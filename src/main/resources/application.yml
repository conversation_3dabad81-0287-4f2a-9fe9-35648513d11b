# Tomcat
server:
  tomcat:
    uri-encoding: UTF-8
    max-threads: 1000
    min-spare-threads: 30
  port: 8090
  connection-timeout: 5000ms
  servlet:
    context-path: /cjrone

spring:
  # 环境 dev|test|prod
  profiles:
    active: dev
  # jackson时间格式化
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true
  redis:
    open: true  # 是否开启redis缓存  true开启   false关闭
    database: 2
    host: localhost
#    port: 8001
    port: 6379
#    password: Hmit@1234@_@   # 密码（默认为空）
    timeout: 6000ms  # 连接超时时长（毫秒）
    jedis:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接
  mvc:
    throw-exception-if-no-handler-found: true
    static-path-pattern: /static/**
#  resources:
#    add-mappings: false
  resources:
    static-locations: classpath:/META-INF/resources/,classpath:/resources/,classpath:/webapp/,classpath:/webapp/static/,classpath:/static/,classpath:/public/,file:${cjrone.upload-path},file:${cjrone.download-path},file:${cjrone.signature-path},file:${cjrone.temp-file-path}



#mybatis
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.hmit.kernespring.modules.*.entity
  global-config:
    #数据库相关配置
    db-config:
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: AUTO
      #字段策略 IGNORED:"忽略判断",NOT_NULL:"非 NULL 判断"),NOT_EMPTY:"非空判断"
      field-strategy: NOT_NULL
      #驼峰下划线转换
      column-underline: true
      logic-delete-value: -1
      logic-not-delete-value: 0
    banner: false
  #原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'


cjrone:
  upload-path:  /home/<USER>/cjrone/upload/
  download-path: /home/<USER>/cjrone/download/
  download-zip-path: /home/<USER>/cjrone/download-zip/
  signature-path: /home/<USER>/cjrone/signature/ #D:\filetest\                   #/home/<USER>/cjrone/signature/
  templete-path: /home/<USER>/cjrone/pdf/templates/  # D:\filetest\                   #/home/<USER>/cjrone/pdf/templates/  #
  temp-file-path: /home/<USER>/cjrone/tmpfile/    #D:\filetest\ # /home/<USER>/cjrone/tmpfile/       # /home/<USER>/cjrone/tmpfile/   D:\temp\


  redis:
    open: true
  shiro:
    redis: false
  # APP模块，是通过jwt认证的，如果要使用APP模块，则需要修改【加密秘钥】
  jwt:
    # 加密秘钥
    secret: f4e2e52034348f86b67cde581c0f9eb5[cjrone]
    # token有效时长，7天，单位秒
    expire: 604800
    header: token
