spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        druid:
            driver-class-name: org.postgresql.Driver
            url: ****************************************************************, sys_catalog
            username: hmit_user
            password: HmitUser@2022
            initial-size: 5
            max-active: 5
            min-idle: 5
            max-wait: 60000
            pool-prepared-statements: true
            max-pool-prepared-statement-per-connection-size: 20
            time-between-eviction-runs-millis: 60000
            min-evictable-idle-time-millis: 300000
            #Oracle需要打开注释
            #validation-query: SELECT 1 FROM DUAL
            test-while-idle: true
            test-on-borrow: false
            test-on-return: false
            stat-view-servlet:
                enabled: false
                url-pattern: /druid/*
                login-username: aw43rwewr
                login-password: 234rerewr32
            filter:
                stat:
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: false
                wall:
                    config:
                        multi-statement-allow: true

cjrone:
    upload-path:  D:/tmp/cjrone/upload/
    download-path: D:/tmp/cjrone/download/
    download-zip-path: D:/tmp/cjrone/download-zip/
    signature-path: D:/tmp/cjrone/signature/
    templete-path: D:/tmp/cjrone/pdf/templates/
    temp-file-path: D:/tmp/cjrone/tmpfile//tmpfile/    #D:\filetest\ # /home/<USER>/cjrone/tmpfile/       # /home/<USER>/cjrone/tmpfile/   D:\temp\

##多数据源的配置
#dynamic:
#  datasource:
#    slave1:
#      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
#      url: ***************************************************
#      username: sa
#      password: 123456
#    slave2:
#      driver-class-name: org.postgresql.Driver
#      url: ***************************************
#      username: cjrone
#      password: 123456
