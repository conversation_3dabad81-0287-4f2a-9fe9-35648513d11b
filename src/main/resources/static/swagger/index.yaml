swagger: '2.0'
info:
  description: 开发平台
  version: 1.0.0
  title: 开发平台

basePath: /cjrone

schemes:
  - http

#认证
securityDefinitions:
  api_key:
    type: "apiKey"
    name: "token"
    in: "header"

#定义接口数据
paths:
  /captcha.jpg:
    get:
      tags:
        - 用户登录
      summary: 获取验证码
      produces:
        - application/octet-stream
      parameters:
        - name: uuid
          description: UUID
          in: query
          type: string
          required: true
  /sys/login:
    post:
      tags:
        - 用户登录
      summary: 用户登录
      produces:
        - application/json
      parameters:
        - name: body
          description: 管理员对象
          in: body
          type: string
          schema:
            $ref: '#/definitions/LoginForm'
          required: true
      responses:
        '200':
          schema:
            $ref: '#/definitions/Login'

  /sys/user/list:
    get:
      tags:
        - 管理员管理
      summary: 管理员列表
      produces:
        - application/json
      parameters:
        - name: page
          description: 页码
          in: query
          type: integer
          required: true
        - name: limit
          description: 每页条数
          in: query
          type: integer
          required: true
        - name: sidx
          description: 排序字段
          in: query
          type: string
        - name: order
          description: 排序方式，如：asc、desc
          in: query
          type: string
        - name: username
          description: 用户名
          in: query
          type: string
      responses:
        '200':
          description: 返回管理员列表
          schema:
            $ref: '#/definitions/SysUserEntityList'
  /sys/user/info:
    get:
      tags:
        - 管理员管理
      summary: 当前管理员信息
      produces:
        - application/json
      responses:
        '200':
          description: 返回当前管理员信息
          schema:
            type: object
            properties:
              code:
                description: 状态码 0：成功  非0：失败
                type: integer
                format: int32
              user:
                $ref: '#/definitions/SysUserEntity'
  /sys/user/info/{userId}:
    get:
      tags:
        - 管理员管理
      summary: 获取管理员信息
      produces:
        - application/json
      parameters:
        - name: userId
          description: 用户ID
          in: path
          type: integer
          required: true
      responses:
        '200':
          description: 返回管理员信息
          schema:
            type: object
            properties:
              code:
                description: 状态码 0：成功  非0：失败
                type: integer
                format: int32
              user:
                $ref: '#/definitions/SysUserEntity'
  /sys/user/password:
    post:
      tags:
        - 管理员管理
      summary: 修改密码
      produces:
        - application/json
      parameters:
        - name: body
          description: 管理员对象
          in: body
          type: string
          schema:
            $ref: '#/definitions/PasswordForm'
          required: true
      responses:
        '200':
          schema:
            $ref: '#/definitions/R'
  /sys/user/save:
    post:
      tags:
        - 管理员管理
      summary: 添加管理员
      produces:
        - application/json
      parameters:
        - name: body
          description: 管理员对象
          in: body
          type: string
          schema:
            $ref: '#/definitions/SysUserEntityEdit'
          required: true
      responses:
        '200':
          schema:
            $ref: '#/definitions/R'
  /sys/user/update:
    post:
      tags:
        - 管理员管理
      summary: 修改管理员
      produces:
        - application/json
      parameters:
        - name: body
          description: 管理员对象
          in: body
          type: string
          schema:
            $ref: '#/definitions/SysUserEntityEdit'
          required: true
      responses:
        '200':
          schema:
            $ref: '#/definitions/R'
  /sys/user/delete:
    post:
      tags:
        - 管理员管理
      summary: 删除管理员
      produces:
        - application/json
      parameters:
        - name: body
          description: 用户ID列表
          in: body
          type: array
          items:
            type: integer
            format: int64
            default: 0
          required: true
      responses:
        '200':
          schema:
            $ref: '#/definitions/R'

  /sys/role/list:
    get:
      tags:
        - 角色管理
      summary: 角色列表
      produces:
        - application/json
      parameters:
        - name: page
          description: 页码
          in: query
          type: integer
          required: true
        - name: limit
          description: 每页条数
          in: query
          type: integer
          required: true
        - name: sidx
          description: 排序字段
          in: query
          type: string
        - name: order
          description: 排序方式，如：asc、desc
          in: query
          type: string
        - name: roleName
          description: 角色名
          in: query
          type: string
      responses:
        '200':
          description: 返回角色列表
          schema:
            $ref: '#/definitions/SysRoleEntityList'
  /sys/role/select:
    get:
      tags:
        - 角色管理
      summary: 当前账号角色列表
      description: 如果是超级管理员，则能查询所有的角色列表
      produces:
        - application/json
      responses:
        '200':
          description: 返回角色列表
          schema:
            type: object
            properties:
              code:
                description: 状态码 0：成功  非0：失败
                type: integer
                format: int32
              list:
                type: array
                items:
                  $ref: '#/definitions/SysRoleEntity'
  /sys/role/info/{roleId}:
    get:
      tags:
        - 角色管理
      summary: 获取角色信息
      produces:
        - application/json
      parameters:
        - name: roleId
          description: 角色ID
          in: path
          type: integer
          required: true
      responses:
        '200':
          description: 返回角色信息
          schema:
            type: object
            properties:
              code:
                description: 状态码 0：成功  非0：失败
                type: integer
                format: int32
              role:
                $ref: '#/definitions/SysRoleEntity'
  /sys/role/save:
    post:
      tags:
        - 角色管理
      summary: 添加角色
      produces:
        - application/json
      parameters:
        - name: body
          description: 角色对象
          in: body
          type: string
          schema:
            $ref: '#/definitions/SysRoleEntityEdit'
          required: true
      responses:
        '200':
          schema:
            $ref: '#/definitions/R'
  /sys/role/update:
    post:
      tags:
        - 角色管理
      summary: 修改角色
      produces:
        - application/json
      parameters:
        - name: body
          description: 角色对象
          in: body
          type: string
          schema:
            $ref: '#/definitions/SysRoleEntityEdit'
          required: true
      responses:
        '200':
          schema:
            $ref: '#/definitions/R'
  /sys/role/delete:
    post:
      tags:
        - 角色管理
      summary: 删除角色
      produces:
        - application/json
      parameters:
        - name: body
          description: 角色ID列表
          in: body
          type: array
          items:
            type: integer
            format: int64
            default: 0
          required: true
      responses:
        '200':
          schema:
            $ref: '#/definitions/R'

  /sys/menu/nav:
    get:
      tags:
        - 菜单管理
      summary: 导航菜单列表
      produces:
        - application/json
      responses:
        '200':
          description: 返回导航菜单列表
          schema:
            type: object
            properties:
              code:
                description: 状态码 0：成功  非0：失败
                type: integer
                format: int32
              menuList:
                description: 菜单列表
                type: array
                items:
                  $ref: '#/definitions/SysMenuEntity'
              permissions:
                description: 权限列表
                type: array
                items:
                  type: string
  /sys/menu/list:
    get:
      tags:
        - 菜单管理
      summary: 菜单列表
      produces:
        - application/json
      responses:
        '200':
          description: 返回菜单列表
          schema:
            type: array
            items:
              $ref: '#/definitions/SysMenuEntity'
  /sys/menu/select:
    get:
      tags:
        - 菜单管理
      summary: 选择菜单
      description: 添加、修改菜单的时候，选择上级菜单接口
      produces:
        - application/json
      responses:
        '200':
          description: 返回菜单列表
          schema:
            type: object
            properties:
              code:
                description: 状态码 0：成功  非0：失败
                type: integer
                format: int32
              menuList:
                description: 菜单列表
                type: array
                items:
                  $ref: '#/definitions/SysMenuEntity'
  /sys/menu/info/{menuId}:
    get:
      tags:
        - 菜单管理
      summary: 获取菜单信息
      produces:
        - application/json
      parameters:
        - name: menuId
          description: 菜单ID
          in: path
          type: integer
          required: true
      responses:
        '200':
          description: 返回菜单信息
          schema:
            type: object
            properties:
              code:
                description: 状态码 0：成功  非0：失败
                type: integer
                format: int32
              menu:
                description: 菜单信息
                $ref: '#/definitions/SysMenuEntity'
  /sys/menu/save:
    post:
      tags:
        - 菜单管理
      summary: 添加菜单
      produces:
        - application/json
      parameters:
        - name: body
          description: 菜单对象
          in: body
          type: string
          schema:
            $ref: '#/definitions/SysMenuEntityEdit'
          required: true
      responses:
        '200':
          schema:
            $ref: '#/definitions/R'
  /sys/menu/update:
    post:
      tags:
        - 菜单管理
      summary: 修改菜单
      produces:
        - application/json
      parameters:
        - name: body
          description: 菜单对象
          in: body
          type: string
          schema:
            $ref: '#/definitions/SysMenuEntityEdit'
          required: true
      responses:
        '200':
          schema:
            $ref: '#/definitions/R'
  /sys/menu/delete/{menuId}:
    post:
      tags:
        - 菜单管理
      summary: 删除菜单
      produces:
        - application/json
      parameters:
        - name: menuId
          description: 菜单ID
          in: path
          type: integer
          required: true
      responses:
        '200':
          schema:
            $ref: '#/definitions/R'

  /sys/log/list:
    get:
      tags:
        - 系统日志
      summary: 日志列表
      produces:
        - application/json
      parameters:
        - name: page
          description: 页码
          in: query
          type: integer
          required: true
        - name: limit
          description: 每页条数
          in: query
          type: integer
          required: true
        - name: sidx
          description: 排序字段
          in: query
          type: string
        - name: order
          description: 排序方式，如：asc、desc
          in: query
          type: string
        - name: key
          description: 用户名或用户操作
          in: query
          type: string
      responses:
        '200':
          description: 返回日志列表
          schema:
            $ref: '#/definitions/SysLogEntityList'

  /sys/config/list:
    get:
      tags:
        - 参数管理
      summary: 参数列表
      produces:
        - application/json
      parameters:
        - name: page
          description: 页码
          in: query
          type: integer
          required: true
        - name: limit
          description: 每页条数
          in: query
          type: integer
          required: true
        - name: sidx
          description: 排序字段
          in: query
          type: string
        - name: order
          description: 排序方式，如：asc、desc
          in: query
          type: string
        - name: key
          description: 参数名
          in: query
          type: string
      responses:
        '200':
          description: 返回参数列表
          schema:
            $ref: '#/definitions/SysConfigEntityList'
  /sys/config/info/{id}:
    get:
      tags:
        - 参数管理
      summary: 获取参数信息
      produces:
        - application/json
      parameters:
        - name: id
          description: 参数ID
          in: path
          type: integer
          required: true
      responses:
        '200':
          description: 返回参数信息
          schema:
            type: object
            properties:
              code:
                description: 状态码 0：成功  非0：失败
                type: integer
                format: int32
              config:
                description: 返回参数信息
                $ref: '#/definitions/SysConfigEntity'
  /sys/config/save:
    post:
      tags:
        - 参数管理
      summary: 添加参数
      produces:
        - application/json
      parameters:
        - name: body
          description: 参数对象
          in: body
          type: string
          schema:
            $ref: '#/definitions/SysConfigEntity'
          required: true
      responses:
        '200':
          schema:
            $ref: '#/definitions/R'
  /sys/config/update:
    post:
      tags:
        - 参数管理
      summary: 修改参数
      produces:
        - application/json
      parameters:
        - name: body
          description: 参数对象
          in: body
          type: string
          schema:
            $ref: '#/definitions/SysConfigEntity'
          required: true
      responses:
        '200':
          schema:
            $ref: '#/definitions/R'
  /sys/config/delete:
    post:
      tags:
        - 参数管理
      summary: 删除参数
      produces:
        - application/json
      parameters:
        - name: body
          description: 参数ID列表
          in: body
          type: array
          items:
            type: integer
            format: int64
            default: 0
          required: true
      responses:
        '200':
          schema:
            $ref: '#/definitions/R'

  /sys/oss/list:
    get:
      tags:
        - 文件服务
      summary: 文件列表
      produces:
        - application/json
      parameters:
        - name: page
          description: 页码
          in: query
          type: integer
          required: true
        - name: limit
          description: 每页条数
          in: query
          type: integer
          required: true
        - name: sidx
          description: 排序字段
          in: query
          type: string
        - name: order
          description: 排序方式，如：asc、desc
          in: query
          type: string
      responses:
        '200':
          description: 返回文件列表
          schema:
            $ref: '#/definitions/SysOssEntityList'
  /sys/oss/config:
    get:
      tags:
        - 文件服务
      summary: 云存储配置信息
      produces:
        - application/json
      responses:
        '200':
          description: 返回云存储配置信息
          schema:
            type: object
            properties:
              code:
                description: 状态码 0：成功  非0：失败
                type: integer
                format: int32
              config:
                description: 云存储配置信息
                $ref: '#/definitions/SysCloudStorageEntity'
  /sys/oss/saveConfig:
    post:
      tags:
        - 文件服务
      summary: 保存云存储配置信息
      produces:
        - application/json
      parameters:
        - name: body
          description: 参数对象
          in: body
          type: string
          schema:
            $ref: '#/definitions/SysCloudStorageEntity'
          required: true
      responses:
        '200':
          schema:
            $ref: '#/definitions/R'
  /sys/oss/upload:
    post:
      tags:
        - 文件服务
      summary: 上传文件
      consumes:
        - multipart/form-data
      produces:
        - application/json
      parameters:
        - name: file
          description: 文件
          in: formData
          type: file
          required: true
      responses:
        '200':
          description: 返回文件列表
          schema:
            $ref: '#/definitions/FileUpload'
  /sys/oss/delete:
    post:
      tags:
        - 文件服务
      summary: 删除文件
      produces:
        - application/json
      parameters:
        - name: body
          description: 文件ID列表
          in: body
          type: array
          items:
            type: integer
            format: int64
            default: 0
          required: true
      responses:
        '200':
          schema:
            $ref: '#/definitions/R'

  /sys/schedule/list:
    get:
      tags:
        - 定时任务
      summary: 定时任务列表
      produces:
        - application/json
      parameters:
        - name: page
          description: 页码
          in: query
          type: integer
          required: true
        - name: limit
          description: 每页条数
          in: query
          type: integer
          required: true
        - name: sidx
          description: 排序字段
          in: query
          type: string
        - name: order
          description: 排序方式，如：asc、desc
          in: query
          type: string
        - name: beanName
          description: spring bean名称
          in: query
          type: string
      responses:
        '200':
          description: 返回定时任务列表
          schema:
            $ref: '#/definitions/ScheduleJobEntityList'
  /sys/schedule/info/{jobId}:
    get:
      tags:
        - 定时任务
      summary: 获取定时任务信息
      produces:
        - application/json
      parameters:
        - name: jobId
          description: 定时任务ID
          in: path
          type: integer
          required: true
      responses:
        '200':
          description: 返回定时任务信息
          schema:
            type: object
            properties:
              code:
                description: 状态码 0：成功  非0：失败
                type: integer
                format: int32
              schedule:
                description: 定时任务信息
                $ref: '#/definitions/ScheduleJobEntity'
  /sys/schedule/save:
    post:
      tags:
        - 定时任务
      summary: 添加定时任务
      produces:
        - application/json
      parameters:
        - name: body
          description: 定时任务对象
          in: body
          type: string
          schema:
            $ref: '#/definitions/ScheduleJobEntity'
          required: true
      responses:
        '200':
          schema:
            $ref: '#/definitions/R'
  /sys/schedule/update:
    post:
      tags:
        - 定时任务
      summary: 修改定时任务
      produces:
        - application/json
      parameters:
        - name: body
          description: 定时任务对象
          in: body
          type: string
          schema:
            $ref: '#/definitions/ScheduleJobEntity'
          required: true
      responses:
        '200':
          schema:
            $ref: '#/definitions/R'
  /sys/schedule/delete:
    post:
      tags:
        - 定时任务
      summary: 删除定时任务
      produces:
        - application/json
      parameters:
        - name: body
          description: 定时任务ID列表
          in: body
          type: array
          items:
            type: integer
            format: int64
            default: 0
          required: true
      responses:
        '200':
          schema:
            $ref: '#/definitions/R'
  /sys/schedule/run:
    post:
      tags:
        - 定时任务
      summary: 立即执行任务
      produces:
        - application/json
      parameters:
        - name: body
          description: 定时任务ID列表
          in: body
          type: array
          items:
            type: integer
            format: int64
            default: 0
          required: true
      responses:
        '200':
          schema:
            $ref: '#/definitions/R'
  /sys/schedule/pause:
    post:
      tags:
        - 定时任务
      summary: 暂停定时任务
      produces:
        - application/json
      parameters:
        - name: body
          description: 定时任务ID列表
          in: body
          type: array
          items:
            type: integer
            format: int64
            default: 0
          required: true
      responses:
        '200':
          schema:
            $ref: '#/definitions/R'
  /sys/schedule/resume:
    post:
      tags:
        - 定时任务
      summary: 恢复定时任务
      produces:
        - application/json
      parameters:
        - name: body
          description: 定时任务ID列表
          in: body
          type: array
          items:
            type: integer
            format: int64
            default: 0
          required: true
      responses:
        '200':
          schema:
            $ref: '#/definitions/R'

  /sys/scheduleLog/list:
    get:
      tags:
        - 定时任务
      summary: 定时任务日志列表
      produces:
        - application/json
      parameters:
        - name: page
          description: 页码
          in: query
          type: integer
          required: true
        - name: limit
          description: 每页条数
          in: query
          type: integer
          required: true
        - name: sidx
          description: 排序字段
          in: query
          type: string
        - name: order
          description: 排序方式，如：asc、desc
          in: query
          type: string
        - name: beanName
          description: spring bean名称
          in: query
          type: string
      responses:
        '200':
          description: 返回定时任务日志列表
          schema:
            $ref: '#/definitions/ScheduleJobLogEntityList'
  /sys/scheduleLog/info/{logId}:
    get:
      tags:
        - 定时任务
      summary: 获取定时任务日志信息
      produces:
        - application/json
      parameters:
        - name: logId
          description: 日志ID
          in: path
          type: integer
          required: true
      responses:
        '200':
          description: 返回定时任务日志信息
          schema:
            type: object
            properties:
              code:
                description: 状态码 0：成功  非0：失败
                type: integer
                format: int32
              schedule:
                description: 定时任务日志信息
                $ref: '#/definitions/ScheduleJobLogEntity'

#定义数据模型
definitions:
  R:
      type: object
      properties:
        code:
          description: 状态码 0：成功  非0：失败
          type: integer
          format: int32
        msg:
          description: 失败原因
          type: string
  Login:
      type: object
      properties:
        code:
          description: 状态码 0：成功  非0：失败
          type: integer
          format: int32
        token:
          description: token
          type: string
        expire:
          description: 过期时长
          type: integer
          format: int32
        msg:
          description: 失败原因
          type: string
  LoginForm:
    type: object
    properties:
      username:
        description: 用户名
        type: string
      password:
        description: 密码
        type: string
      captcha:
        description: 验证码
        type: string
      uuid:
        description: UUID
        type: string
  PasswordForm:
    type: object
    properties:
      password:
        description: 原密码
        type: string
      newPassword:
        description: 新密码
        type: string
  SysUserEntity:
    type: object
    properties:
      userId:
        description: 用户ID
        type: integer
        format: int64
      username:
        description: 用户名
        type: string
      password:
        description: 密码
        type: string
      email:
        description: 邮箱
        type: string
      mobile:
        description: 手机号
        type: string
      status:
        description: 状态  0：禁用   1：正常
        type: integer
        format: int32
      roleIdList:
        description: 角色ID列表
        type: array
        items:
          type: integer
          format: int64
      createUserId:
        description: 创建者ID
        type: integer
        format: int64
      createTime:
        description: 创建时间
        type: string
        format: date-time
  SysUserEntityList:
    type: object
    properties:
      code:
        description: 状态码 0：成功  非0：失败
        type: integer
        format: int32
      page:
        type: object
        properties:
          totalCount:
            description: 总记录数
            type: integer
            format: int32
          pageSize:
            description: 每页记录数
            type: integer
            format: int32
          totalPage:
            description: 总页数
            type: integer
            format: int32
          currPage:
            description: 当前页数
            type: integer
            format: int32
          list:
            type: array
            items:
              $ref: '#/definitions/SysUserEntity'
  SysUserEntityEdit:
      type: object
      properties:
        userId:
          description: 用户ID
          type: integer
          format: int64
        username:
          description: 用户名
          type: string
        password:
          description: 密码
          type: string
        email:
          description: 邮箱
          type: string
        mobile:
          description: 手机号
          type: string
        status:
          description: 状态  0：禁用   1：正常
          type: integer
          format: int32
        roleIdList:
          description: 角色ID列表
          type: array
          items:
            type: integer
            format: int32

  SysRoleEntity:
    type: object
    properties:
      roleId:
        description: 角色ID
        type: integer
        format: int64
      roleName:
        description: 角色名称
        type: string
      remark:
        description: 备注
        type: string
      menuIdList:
        description: 菜单ID列表
        type: array
        items:
          type: integer
          format: int64
      createUserId:
        description: 创建者ID
        type: integer
        format: int64
      createTime:
        description: 创建时间
        type: string
        format: date-time
  SysRoleEntityList:
    type: object
    properties:
      code:
        description: 状态码 0：成功  非0：失败
        type: integer
        format: int32
      page:
        type: object
        properties:
          totalCount:
            description: 总记录数
            type: integer
            format: int32
          pageSize:
            description: 每页记录数
            type: integer
            format: int32
          totalPage:
            description: 总页数
            type: integer
            format: int32
          currPage:
            description: 当前页数
            type: integer
            format: int32
          list:
            type: array
            items:
              $ref: '#/definitions/SysRoleEntity'
  SysRoleEntityEdit:
    type: object
    properties:
      roleId:
        description: 角色ID
        type: integer
        format: int64
      roleName:
        description: 角色名称
        type: string
      remark:
        description: 备注
        type: string
      menuIdList:
        description: 菜单ID列表
        type: array
        items:
          type: integer
          format: int64

  SysMenuEntity:
    type: object
    properties:
      menuId:
        description: 菜单ID
        type: integer
        format: int64
      name:
        description: 菜单名称
        type: string
      parentId:
        description: 父菜单ID，一级菜单为0
        type: integer
        format: int64
      parentName:
        description: 父菜单名称
        type: string
      url:
        description: 菜单URL
        type: string
      perms:
        description: 授权标识
        type: string
      type:
        description: 类型     0：目录   1：菜单   2：按钮
        type: integer
        format: int32
      icon:
        description: 菜单图标
        type: string
      orderNum:
        description: 排序
        type: integer
        format: int32
      open:
        description: 是否展开   true：展开   false：不展开
        type: boolean
        format: int32
  SysMenuEntityEdit:
    type: object
    properties:
      menuId:
        description: 菜单ID
        type: integer
        format: int64
      name:
        description: 菜单名称
        type: string
      parentId:
        description: 父菜单ID，一级菜单为0
        type: integer
        format: int64
      url:
        description: 菜单URL
        type: string
      perms:
        description: 授权标识
        type: string
      type:
        description: 类型     0：目录   1：菜单   2：按钮
        type: integer
        format: int32
      icon:
        description: 菜单图标
        type: string
      orderNum:
        description: 排序
        type: integer
        format: int32

  SysLogEntity:
    type: object
    properties:
      id:
        description: 日志ID
        type: integer
        format: int64
      username:
        description: 用户名
        type: string
      operation:
        description: 用户操作
        type: string
      method:
        description: 请求方法
        type: string
      params:
        description: 请求参数
        type: string
      time:
        description: 执行时长(毫秒)
        type: integer
        format: int64
      ip:
        description: IP地址
        type: string
      createTime:
        description: 创建时间
        type: string
        format: date-time
  SysLogEntityList:
    type: object
    properties:
      code:
        description: 状态码 0：成功  非0：失败
        type: integer
        format: int32
      page:
        type: object
        properties:
          totalCount:
            description: 总记录数
            type: integer
            format: int32
          pageSize:
            description: 每页记录数
            type: integer
            format: int32
          totalPage:
            description: 总页数
            type: integer
            format: int32
          currPage:
            description: 当前页数
            type: integer
            format: int32
          list:
            type: array
            items:
              $ref: '#/definitions/SysLogEntity'

  SysConfigEntity:
    type: object
    properties:
      id:
        description: 参数ID
        type: integer
        format: int64
      key:
        description: 参数名
        type: string
      value:
        description: 参数值
        type: string
      remark:
        description: 备注
        type: string
  SysConfigEntityList:
    type: object
    properties:
      code:
        description: 状态码 0：成功  非0：失败
        type: integer
        format: int32
      page:
        type: object
        properties:
          totalCount:
            description: 总记录数
            type: integer
            format: int32
          pageSize:
            description: 每页记录数
            type: integer
            format: int32
          totalPage:
            description: 总页数
            type: integer
            format: int32
          currPage:
            description: 当前页数
            type: integer
            format: int32
          list:
            type: array
            items:
              $ref: '#/definitions/SysConfigEntity'

  SysOssEntity:
    type: object
    properties:
      id:
        description: ID
        type: integer
        format: int64
      url:
        description: URL地址
        type: string
      createTime:
        description: 创建时间
        type: string
        format: date-time
  SysOssEntityList:
    type: object
    properties:
      code:
        description: 状态码 0：成功  非0：失败
        type: integer
        format: int32
      page:
        type: object
        properties:
          totalCount:
            description: 总记录数
            type: integer
            format: int32
          pageSize:
            description: 每页记录数
            type: integer
            format: int32
          totalPage:
            description: 总页数
            type: integer
            format: int32
          currPage:
            description: 当前页数
            type: integer
            format: int32
          list:
            type: array
            items:
              $ref: '#/definitions/SysOssEntity'
  SysCloudStorageEntity:
    type: object
    properties:
      type:
        description: 类型 1：七牛  2：阿里云  3：腾讯云
        type: integer
        format: int32
      qiniuDomain:
        description: 七牛绑定的域名
        type: string
      qiniuPrefix:
        description: 七牛路径前缀
        type: string
      qiniuAccessKey:
        description: 七牛ACCESS_KEY
        type: string
      qiniuSecretKey:
        description: 七牛SECRET_KEY
        type: string
      qiniuBucketName:
        description: 七牛存储空间名
        type: string
      aliyunDomain:
        description: 阿里云绑定的域名
        type: string
      aliyunPrefix:
        description: 阿里云路径前缀
        type: string
      aliyunEndPoint:
        description: 阿里云EndPoint
        type: string
      aliyunAccessKeyId:
        description: 阿里云AccessKeyId
        type: string
      aliyunAccessKeySecret:
        description: 阿里云AccessKeySecret
        type: string
      aliyunBucketName:
        description: 阿里云BucketName
        type: string
      qcloudDomain:
        description: 腾讯云绑定的域名
        type: string
      qcloudPrefix:
        description: 腾讯云路径前缀
        type: string
      qcloudAppId:
        description: 腾讯云AppId
        type: string
      qcloudSecretId:
        description: 腾讯云SecretId
        type: string
      qcloudSecretKey:
        description: 腾讯云SecretKey
        type: string
      qcloudBucketName:
        description: 腾讯云BucketName
        type: string
      qcloudRegion:
        description: 腾讯云COS所属地区
        type: string
  FileUpload:
      type: object
      properties:
        code:
          description: 状态码 0：成功  非0：失败
          type: integer
          format: int32
        url:
          description: 文件URL地址
          type: string
        msg:
          description: 失败原因
          type: string

  ScheduleJobEntity:
    type: object
    properties:
      jobId:
        description: 任务ID
        type: integer
        format: int64
      beanName:
        description: spring bean名称
        type: string
      methodName:
        description: 方法名
        type: string
      params:
        description: 参数
        type: string
      cronExpression:
        description: cron表达式
        type: string
      status:
        description: 任务状态  0：正常  1：暂停
        type: integer
        format: int32
      remark:
        description: 备注
        type: string
      createTime:
        description: 创建时间
        type: string
        format: date-time
  ScheduleJobEntityList:
    type: object
    properties:
      code:
        description: 状态码 0：成功  非0：失败
        type: integer
        format: int32
      page:
        type: object
        properties:
          totalCount:
            description: 总记录数
            type: integer
            format: int32
          pageSize:
            description: 每页记录数
            type: integer
            format: int32
          totalPage:
            description: 总页数
            type: integer
            format: int32
          currPage:
            description: 当前页数
            type: integer
            format: int32
          list:
            type: array
            items:
              $ref: '#/definitions/ScheduleJobEntity'

  ScheduleJobLogEntity:
    type: object
    properties:
      logId:
        description: 日志id
        type: integer
        format: int64
      jobId:
        description: 任务id
        type: integer
        format: int64
      beanName:
        description: spring bean名称
        type: string
      methodName:
        description: 方法名
        type: string
      params:
        description: 参数
        type: string
      status:
        description: 任务状态    0：成功    1：失败
        type: integer
        format: int32
      error:
        description: 失败信息
        type: string
      times:
        description: 耗时(单位：毫秒)
        type: integer
        format: int32
      createTime:
        description: 创建时间
        type: string
        format: date-time
  ScheduleJobLogEntityList:
    type: object
    properties:
      code:
        description: 状态码 0：成功  非0：失败
        type: integer
        format: int32
      page:
        type: object
        properties:
          totalCount:
            description: 总记录数
            type: integer
            format: int32
          pageSize:
            description: 每页记录数
            type: integer
            format: int32
          totalPage:
            description: 总页数
            type: integer
            format: int32
          currPage:
            description: 当前页数
            type: integer
            format: int32
          list:
            type: array
            items:
              $ref: '#/definitions/ScheduleJobLogEntity'