<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
    <meta http-equiv="X-UA-Compatible" content="IE=10" />
    <title>电子签章</title>
    <style type="text/css">
        form{
            width: 1200px;
        }
        table{
            margin: 0 auto;
        }
        table input{
            font-size: 30px;
            margin: 0 10px;
        }
        .form-btn{
            position: fixed;
            top: 20px;
            right: 0;
            width: auto;
        }
    </style>
    <script type="text/javascript">
	//文件头这句话要申明，不然IE11调用会有问题 <meta http-equiv="X-UA-Compatible" content="IE=10" />
        var TGPDFPlugIn;
        function load() {

        }

        function isIEBrowser() {
            var isAtLeastIE11 = !!(navigator.userAgent.match(/Trident/) && !navigator.userAgent.match(/MSIE/));
            if (isAtLeastIE11) {
                return true;
            }

            return (navigator.appName.indexOf("Microsoft Internet") != -1);
        }

        function getPluginObjcet(objname) {
            if (!isIEBrowser()) {
                if (document.embeds && document.embeds[objname])
                    return document.embeds[objname];
            }
            else {
                return document.getElementById(objname);
            }
        }

        function attach_event() {
            try {
                if (!isIEBrowser()) {
                    TGPDFPlugIn.attachEvent("OnEventFinish", "jsOnEventFinish");
                }
                else {
                    TGPDFPlugIn.attachEvent("OnEventFinish", jsOnEventFinish);
                }
            }
            catch (e) {
                return false;
            }
            return true;
        }

        function ShowResultMessage(msg, result) {
            var resultMsg = "";
            try{
                resultMsg = TGPDFPlugIn.GetErrorMsg(result);
            }catch(e){

            }
            alert( msg + ":" + result.toString() + resultMsg + "\n");
         
        }

        function jsOnEventFinish(eventID, errorCode, result)
        {
            switch(eventID)
            {
                case 11201:
                    ShowResultMessage("获取当前文件路径" + errorCode, result)
                    break;
                case 11202:
                    if (0 == errorCode)
					{
                   //上传文件接口：第一个参数文件上传接收的服务器地址,第二个参数是文件缓存路径，第三个参数：是文件的表单名，第四个参数是附加参数
				   //上传文件成功则在回调函数中进行下一步的处理
                   TGPDFPlugIn.UploadFileToServer("https://cl.fh.gov.cn/cjrone/cjrone/cjronedocument/uploadNeSignature",result,"file","fileName=" + fileName + ",signId="+signId + ",actionType="+actionType+ ",userName="+token);
					}
                    break;
                case 11205:
                    ShowResultMessage("签章个数" + errorCode, result)
                    break;
                case 11207:
                    ShowResultMessage("签章信息文件路径" + errorCode, result)
                    break;
                    break;
                case 12600:
				//这里处理上传文件成功以后的事件
                    ShowResultMessage("上传文件" + errorCode, result)
                    break;
                case 12700:
                    ShowResultMessage("转换文件" + errorCode, result)
                    break;
                default:
                    break;
            }
        }


        //签章
        function sign() {
            // TGPDFPlugIn.ControlPDF(483);
            TGPDFPlugIn.ControlPDF(801);
        }
       //签章成功以后的事件
        function check() {
            var outInfo;
            var outInfoLen;
            //获取签完章的pdf缓存文件地址,在回调函数里面成功获取到文件地址则上传文件
            TGPDFPlugIn.GetCurrentDocInfo(2, outInfo, outInfoLen);
            window.close();
        }
    function GetQueryString(name) {
        var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if(r!=null)return  unescape(r[2]); return null;
    }
    var fileName = null;
    var signId = null;
    var actionType = null;
    var token = null;
    setTimeout(function() {
        TGPDFPlugIn = getPluginObjcet("TGPDFPlugIn");
        attach_event();
        TGPDFPlugIn.DisplayToolBar(0, 0);
        var params = GetQueryString('fileName');
        var params_tmp = params.split(',');
        fileName = params_tmp[0];
        signId = params_tmp[1];
        actionType = params_tmp[2];
        token = params_tmp[3];
        TGPDFPlugIn.LoadPDF("https://cl.fh.gov.cn/cjrone/static/" + fileName);
    }, 20);
    </script>
</head>
<body onload="load();">
  
    <form>
        <object id="TGPDFPlugIn" classid="clsid:04DDDFAA-0AC0-4D47-9315-9F442F65D403" width="1200" height="800">
            <embed name="TGPDFPlugIn" type="application/nptgpdfplugin" width="1200" height="800"></embed>
        </object>
    </form>
 <form name="form1" enctype="multipart/form-data" class="form-btn">
        <table>
            <tr style="margin-bottom: 20px;">
                <td><input type="button" value="签章" onclick="sign()" /></td>
            </tr>
            <tr>
                <td><input type="button" value="确认" onclick="check()" /></td>
            </tr>
        </table>
    </form>
</body>
</html>
