<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.matter_application.dao.CjroneMentalIllnessSubsidyDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.matter_application.entity.CjroneMentalIllnessSubsidyEntity" id="cjroneMentalIllnessSubsidyMap">
        <result property="id" column="id"/>
        <result property="annualAccumulatedAmount" column="annual_accumulated_amount"/>
        <result property="isPsychiatric" column="is_psychiatric"/>
        <result property="hospitalName" column="hospital_name"/>
        <result property="serialNumber" column="serial_number"/>
        <result property="applicationDate" column="application_date"/>
        <result property="hospitalizationStartDate" column="hospitalization_start_date"/>
        <result property="hospitalizationEndDate" column="hospitalization_end_date"/>
        <result property="subsidyYear" column="subsidy_year"/>
        <result property="hospitalizationCount" column="hospitalization_count"/>
        <result property="personalPayment" column="personal_payment"/>
        <result property="classBPayment" column="class_b_payment"/>
        <result property="insuranceOverageLimit" column="insurance_overage_limit"/>
        <result property="effectiveOopMedicalExpense" column="effective_oop_medical_expense"/>
        <result property="subsidyAmount" column="subsidy_amount"/>
        <result property="applyRemarks" column="apply_remarks"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="attach" column="attach"/>
        <result property="reviewRemarks" column="review_remarks"/>
        <result property="welfareMatterApplicationId" column="welfare_matter_application_id"/>
        <result property="operatorId" column="operator_id"/>
        <result property="operatorName" column="operator_name"/>
        <result property="auditorId" column="auditor_id"/>
        <result property="auditorName" column="auditor_name"/>
        <result property="status" column="status"/>
        <result property="diagnosisCertificateImage" column="diagnosis_certificate_image"/>
        <result property="diagnosisCertificateStartTime" column="diagnosis_certificate_start_time"/>
        <result property="diagnosisCertificateEndTime" column="diagnosis_certificate_end_time"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.matter_application.entity.CjroneMentalIllnessSubsidyEntity">
		SELECT * FROM cjrone_mental_illness_subsidy order by id desc
	</select>

    <select id="queryStatistics" resultType="java.util.Map">
        select count(distinct id_card) as count, COALESCE(sum(subsidy_amount),0) as sum, count(*) as 'personTime'
        from cjrone_mental_illness_subsidy
        <where>
            status=8
            <if test="approvalYear!=null">
                AND TO_CHAR(approval_time, 'YYYY')=#{approvalYear}
            </if>

        </where>
    </select>
    
    <select id="calculateYearTotalAmount" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(subsidy_amount), 0)
        FROM cjrone_mental_illness_subsidy
        WHERE id_card = #{idCard}
          AND subsidy_year = #{subsidyYear}
          AND status = '8'
    </select>
</mapper>