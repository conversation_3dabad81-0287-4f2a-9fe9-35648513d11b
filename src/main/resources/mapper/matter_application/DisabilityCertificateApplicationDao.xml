<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.matter_application.dao.DisabilityCertificateApplicationDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity" id="disabilityCertificateApplicationMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="sex" column="sex"/>
        <result property="nationality" column="nationality"/>
        <result property="maritalStatus" column="marital_status"/>
        <result property="birthday" column="birthday"/>
        <result property="nativePlace" column="native_place"/>
        <result property="educationDegree" column="education_degree"/>
        <result property="idCard" column="id_card"/>
        <result property="nativeZhen" column="native_zhen"/>
        <result property="nativeCun" column="native_cun"/>
        <result property="nativeAddress" column="native_address"/>
        <result property="presentZhen" column="present_zhen"/>
        <result property="presentCun" column="present_cun"/>
        <result property="presentAddress" column="present_address"/>
        <result property="postcode" column="postcode"/>
        <result property="mobilePhone" column="mobile_phone"/>
        <result property="guardianName" column="guardian_name"/>
        <result property="guardianPhone" column="guardian_phone"/>
        <result property="guardianIdcard" column="guardian_idcard"/>
        <result property="guardianRelation" column="guardian_relation"/>
        <result property="photo" column="photo"/>
        <result property="applicationType" column="application_type"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="applicationTime" column="application_time"/>
        <result property="createId" column="create_id"/>
        <result property="disabilityType" column="disability_type"/>
        <result property="disableId" column="disable_id"/>
        <result property="isPczlQq" column="is_pczl_qq"/>
        <result property="statusOptions" column="status_options"/>
        <result property="disabilityTypeName" column="disability_type_name"/>
        <result property="pingCanAddress" column="ping_can_address"/>
        <result property="signStatus" column="sign_status"/>
        <result property="pingCanTime" column="ping_can_time"/>
        <result property="backStatus" column="back_status"/>

        <result property="zhenOperator" column="zhen_operator"/>
        <result property="zhenOperateTime" column="zhen_operate_time"/>
        <result property="qclOperator" column="qcl_operator"/>
        <result property="qclOperateTime" column="qcl_operate_time"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity">
		SELECT * FROM disability_certificate_application where 1=1
        <if test="status != null and status.trim() != ''">
            and status = #{status}
        </if>
        <if test="applicationType != null and applicationType.trim() != ''">
            and application_type = #{applicationType}
        </if>
        <if test="name != null and name.trim() != ''">
            and name = #{name}
        </if>
        <if test="idCard != null and idCard.trim() != ''">
            and id_card = #{idCard}
        </if>
		order by id desc
	</select>



    <select id="listDouble" resultType="com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity">
        SELECT * FROM disability_certificate_application
        where 1=1 and id_card = #{id_card} and (disability_type= #{disability_type}
        <if test="disability_type2 != null and disability_type2.trim() != ''">
            or  disability_type = #{disability_type2}
        </if>
        )
        order by complete_time desc
    </select>


    <select id="queryExportDataFz" resultType="com.hmit.kernespring.modules.data_management.entity.DisabilityCertificateApplicationZCLEntity">
        SELECT a.*,b.disable_reason,concat(a.native_zhen_name,a.native_cun_name) as native_address_name,b.hospital,b.audit_date,b.disability_degree,b.assessment_date,b.physician,b.audit_person FROM disability_certificate_application a,cjrone_disability_hospital b where a.disability_type = b.disability_category
        and a.id_card = b.id_card and (a.status = '5' or a.status = '9') and (b.status = '4' or b.status = '9') and is_qcl_show=1 and b.assessment_date is not null
        <if test="name != null and name.trim() != ''">
            and a.name = #{name}
        </if>
        <if test="idCard != null and idCard.trim() != ''">
            and a.id_card = #{idCard}
        </if>
        <if test="nativeZhen != null and nativeZhen.trim() != ''">
            and a.native_zhen = #{nativeZhen}
        </if>
        <if test="nativeCun != null and nativeCun.trim() != ''">
            and a.native_cun = #{nativeCun}
        </if>
        ORDER BY b.audit_date ASC
	</select>

     <select id="exportDataZjdFz" resultType="com.hmit.kernespring.modules.data_management.entity.DisabilityCertificateApplicationZCLEntity">
         SELECT a.*,b.disable_reason,concat(a.native_zhen_name,a.native_cun_name) as native_address_name,b.hospital,b.audit_date,b.disability_degree FROM disability_certificate_application a,cjrone_disability_hospital b where a.disability_type = b.disability_category
         and a.id_card = b.id_card and (a.status = '6' or a.status = '9') and a.is_show=1
        <if test="name != null and name.trim() != ''">
            and a.name = #{name}
        </if>
        <if test="idCard != null and idCard.trim() != ''">
            and a.id_card = #{idCard}
        </if>
         <if test="nativeZhen != null and nativeZhen.trim() != ''">
             and a.native_zhen = #{nativeZhen}
         </if>
         <if test="nativeCun != null and nativeCun.trim() != ''">
             and a.native_cun = #{nativeCun}
         </if>
        ORDER BY b.audit_date ASC
	</select>

    <select id="queryExportData1" resultType="com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity">
        SELECT a.*,b.status as datastatus FROM disability_certificate_application a,data_disability_certificate b where a.id_card=b.id_card and a.disability_type=b.disability_category
        <if test="status != null ">
            and b.status = #{status}
        </if>
        <if test="applicationType != null and applicationType.trim() != ''">
            and b.application_type = #{applicationType}
        </if>
        <if test="name != null and name.trim() != ''">
            and b.name = #{name}
        </if>
        <if test="idCard != null and idCard.trim() != ''">
            and b.id_card = #{idCard}
        </if>
        order by id desc
    </select>

    <select id="queryOrderNum" resultType="string">
        SELECT order_num from cjrone_disability_hospital where disability_category= #{disabilityCategory} and id_card = #{idCard} and (status='1' OR status='2')
    </select>

    <select id="getByIDCard" resultType="com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity">
	    SELECT disable_id,id,name,sex,nationality,marital_status,birthday,native_place,education_degree,id_card,disability_type_name,present_zhen_name,native_zhen_name,native_cun_name,
native_zhen,native_cun,native_address,present_zhen,present_cun,present_address,postcode,mobile_phone,
guardian_name,guardian_phone,guardian_idcard,guardian_relation,photo,application_type,status,
create_time,application_time,create_id,disability_type,bank_account,bank_name,
IFNULL((SELECT medical_insurance from disability_certificate_sync_data where id_card= #{idCard}  ),'无') as health_care_condition ,
IFNULL((SELECT family_econo_condition from disability_certificate_sync_data where id_card= #{idCard} ),'无') as family_econo_condition,
IFNULL((SELECT is_fx from disability_certificate_sync_data where id_card= #{idCard} ),'无') as is_fx,
IFNULL((SELECT is_dead from disability_certificate_sync_data where id_card= #{idCard} ),'无') as is_dead
FROM disability_certificate_application WHERE id_card= #{idCard} and status='6' order by create_time desc limit 1 offset 0
    </select>

    <select id="getByIDCardN" resultType="com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity">
	    SELECT disable_id,id,name,sex,nationality,marital_status,birthday,native_place,education_degree,id_card,disability_type_name,present_zhen_name,
native_zhen,native_cun,native_address,present_zhen,present_cun,present_address,postcode,mobile_phone,
guardian_name,guardian_phone,guardian_idcard,guardian_relation,photo,application_type,status,
create_time,application_time,create_id,disability_type,bank_account,bank_name,
IFNULL((SELECT medical_insurance from disability_certificate_sync_data where id_card= #{idCard}  ),'无') as health_care_condition ,
IFNULL((SELECT family_econo_condition from disability_certificate_sync_data where id_card= #{idCard} ),'无') as family_econo_condition,
IFNULL((SELECT is_fx from disability_certificate_sync_data where id_card= #{idCard} ),'无') as is_fx,
IFNULL((SELECT is_dead from disability_certificate_sync_data where id_card= #{idCard} ),'无') as is_dead
FROM disability_certificate_application WHERE id_card= #{idCard} and status='6' order by create_time desc limit 1 offset 0
    </select>
    <select id="getByMap" resultType="com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity">
	    SELECT disability_type_name,id,name,sex,nationality,marital_status,birthday,native_place,education_degree,id_card,
    native_zhen,native_cun,native_address,present_zhen,present_cun,present_address,postcode,mobile_phone,
    guardian_name,guardian_phone,guardian_idcard,guardian_relation,photo,application_type,status,is_remote,
    create_time,application_time,create_id,disability_type,(
        SELECT medical_insurance from disability_certificate_sync_data where id_card= #{idCard}  ) as health_care_condition ,(
        SELECT family_econo_condition from disability_certificate_sync_data where id_card= #{idCard} ) as family_econo_condition FROM disability_certificate_application
    WHERE id_card= #{idCard}
        <if test="disabilityType != null and disabilityType.trim() != ''">
            and disability_type = #{disabilityType}
        </if>
        ORDER BY application_time DESC limit 1 offset 0
    </select>
    <select id="getMapByMap" resultType="com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity">
	    select  * from disability_certificate_application where status = #{status}
	    and disability_type = #{disabilityType} and id_card = #{idCard}
    </select>

    <select id="getListByMap" resultType="com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity">
        SELECT id,name,sex,nationality,marital_status,birthday,native_place,education_degree,id_card,
        native_zhen,native_cun,native_address,present_zhen,present_cun,present_address,postcode,mobile_phone,
        guardian_name,guardian_phone,guardian_idcard,guardian_relation,photo,application_type,status,
        create_time,application_time,create_id,disability_type,(
        SELECT medical_insurance from disability_certificate_sync_data where id_card= #{idCard}  ) as health_care_condition ,(
        SELECT family_econo_condition from disability_certificate_sync_data where id_card= #{idCard} ) as family_econo_condition FROM disability_certificate_application
        WHERE id_card= #{idCard} and status=#{status}
        <if test="disabilityType != null and disabilityType.trim() != ''">
            and disability_type = #{disabilityType}
        </if>
    </select>

    <select id="updateByMap"  parameterType="map">
	   update disability_certificate_application set status = #{new_status}, status_options = #{status_options},back_status=#{back_status}
        <if test="pingCanCompleteTime != null and pingCanCompleteTime.trim() != ''">
            , ping_can_complete_time = #{pingCanCompleteTime}
        </if>
	   <if test="isQclShow != null and isQclShow.trim() != ''">
            , is_qcl_show = #{isQclShow}
        </if>
	   where id_card = (SELECT id_card from cjrone_disability_hospital where id = #{hospital_id})
	   and disability_type = (SELECT disability_category from cjrone_disability_hospital where id = #{hospital_id})
	   and status = #{old_status}
    </select>
    <select id="updateStatusById"  parameterType="map">
	   update disability_certificate_application set status = #{status}
        <if test="statusOptions != null and statusOptions.trim() != ''">
            ,status_options = #{statusOptions}
        </if>
	   <if test="zhenOperator != null and zhenOperator.trim() != ''">
            ,zhen_operator = #{zhenOperator}
        </if>
	   <if test="zhenOperateTime != null and zhenOperateTime.trim() != ''">
            ,zhen_operate_time = #{zhenOperateTime}
        </if>
	   <if test="qclOperator != null and qclOperator.trim() != ''">
            ,qcl_operator = #{qclOperator}
        </if>
	   <if test="qclOperateTime != null and qclOperateTime.trim() != ''">
            ,qcl_operate_time = #{qclOperateTime}
        </if>
        <if test="backStatus != null and backStatus.trim() != '' ">
            ,back_status = #{backStatus}
        </if>
	   where id = #{id}
    </select>
    <select id="updateStatusOptions"  parameterType="com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity">
	   update disability_certificate_application set status_options = #{statusOptions}
	   where id = #{id}
    </select>
    <select id="updateSignStatusById"  parameterType="map">
        update disability_certificate_application set sign_status = #{signStatus}
        where id = #{id}
    </select>
    <select id="updateSignatureStatusById"  parameterType="map">
        update disability_certificate_application set signature_status = #{signatureStatus}
        where id = #{id}
    </select>
    <select id="faZheng"  parameterType="com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity">
        update disability_certificate_application set is_show = 1
        <if test="disableId != null and disableId.trim() != ''">
            ,disable_id = #{disableId}
        </if>
        <if test="completeTime != null and completeTime.trim() != ''">
            ,complete_time = #{completeTime}
        </if>
        where id = #{id}
    </select>


</mapper>
