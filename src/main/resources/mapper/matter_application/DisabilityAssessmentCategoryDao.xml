<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.matter_application.dao.DisabilityAssessmentCategoryDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.matter_application.entity.DisabilityAssessmentCategoryEntity" id="disabilityAssessmentCategoryMap">
        <result property="id" column="id"/>
        <result property="disabilityType" column="disability_type"/>
        <result property="ratingTime" column="rating_time"/>
        <result property="ratingLocation" column="rating_location"/>
        <result property="material" column="material"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.matter_application.entity.DisabilityAssessmentCategoryEntity">
		SELECT * FROM disability_assessment_category order by id desc
	</select>

</mapper>