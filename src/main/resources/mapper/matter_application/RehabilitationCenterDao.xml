<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.matter_application.dao.RehabilitationCenterDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.matter_application.entity.RehabilitationCenterEntity" id="rehabilitationCenterMap">
        <result property="id" column="id"/>
        <result property="district" column="district"/>
        <result property="institutionName" column="institution_name"/>
        <result property="institutionType" column="institution_type"/>
        <result property="institutionQualification" column="institution_qualification"/>
        <result property="address" column="address"/>
        <result property="serviceCategory" column="service_category"/>
        <result property="agreementPeriod" column="agreement_period"/>
    </resultMap>

    <!-- 查询所有康复中心数据 -->
    <select id="queryAll" resultType="com.hmit.kernespring.modules.matter_application.entity.RehabilitationCenterEntity">
		SELECT * FROM rehabilitation_center ORDER BY id DESC
	</select>

    <!-- 根据区域查询康复中心 -->
    <select id="queryByDistrict" parameterType="string" resultType="com.hmit.kernespring.modules.matter_application.entity.RehabilitationCenterEntity">
        SELECT * FROM rehabilitation_center 
        WHERE district = #{district}
        ORDER BY id DESC
    </select>

    <!-- 根据机构类型查询康复中心 -->
    <select id="queryByInstitutionType" parameterType="string" resultType="com.hmit.kernespring.modules.matter_application.entity.RehabilitationCenterEntity">
        SELECT * FROM rehabilitation_center 
        WHERE institution_type = #{institutionType}
        ORDER BY id DESC
    </select>

    <!-- 根据服务类别查询康复中心 -->
    <select id="queryByServiceCategory" parameterType="string" resultType="com.hmit.kernespring.modules.matter_application.entity.RehabilitationCenterEntity">
        SELECT * FROM rehabilitation_center 
        WHERE service_category LIKE CONCAT('%', #{serviceCategory}, '%')
        ORDER BY id DESC
    </select>



</mapper>
