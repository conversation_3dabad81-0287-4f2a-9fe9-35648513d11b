<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.matter_application.dao.CjroneWelfareMatterApplicationDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.matter_application.entity.CjroneWelfareMatterApplicationEntity" id="cjroneWelfareMatterApplicationMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="idCard" column="id_card"/>
        <result property="mobilePhone" column="mobile_phone"/>
        <result property="disableId" column="disable_id"/>
        <result property="matterName" column="matter_name"/>
        <result property="applicationTime" column="application_time"/>
        <result property="verifyTime" column="verify_time"/>
        <result property="matterId" column="matter_id"/>
        <result property="createTime" column="create_time"/>
        <result property="status" column="status"/>
        <result property="statusOptions" column="status_options"/>
        <result property="signStatus" column="sign_status"/>
        <result property="signatureStatus" column="signature_status"/>
        <result property="nativeCun" column="native_cun"/>
        <result property="nativeZhen" column="native_zhen"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.matter_application.entity.CjroneWelfareMatterApplicationEntity">
		SELECT * FROM cjrone_welfare_matter_application where 1=1
        <if test="zhen != null and zhen.trim() != ''">
            and native_zhen = #{zhen}
        </if>
        <if test="cun != null and cun.trim() != ''">
            and native_cun = #{cun}
        </if>
        <if test="matterName != null and matterName.trim() != ''">
            and matter_name = #{matterName}
        </if>
        <if test="idCard != null and idCard.trim() != ''">
            and id_card = #{idCard}
        </if>
        <if test="disableId != null and disableId.trim() != ''">
            and disable_id = #{disableId}
        </if>
		<if test="name != null and name.trim() != ''">
            and name = #{name}
        </if>
		<if test="status != null and status.trim() != ''">
            and status = #{status}
        </if>
		order by id desc
	</select>
    <select id="updateStatusByMap" parameterType="map">
		update cjrone_welfare_matter_application set status = #{status},verify_time = #{verify_time}
        <if test="signatureStatus != null">
            ,signature_status = #{signatureStatus}
        </if>
        <if test="statusOptions != null">
            ,status_options = #{statusOptions}
        </if>
		where matter_id = #{matter_id} and matter_name = #{matter_name}

	</select>
    <select id="queryMattersByMap" resultType="map" parameterType="map">
SELECT a.name,
(SELECT count(*) from cjronebl_business_grant where status = '8' and id_card = #{idCard}) as  isCybz,
(SELECT count(*) from cjronebl_childedu where status = '8' and id_card = #{idCard}) as  isChildedu,
(SELECT count(*) from cjronebl_collegeedu where status='8' and id_card = #{idCard}) as isCollegeedu,
(SELECT count(*) from cjronebl_cxjmyanglao where status='8' and id_card = #{idCard}) as isCxjmyl,
(SELECT count(*) from cjronebl_cxjmyiliao where status='8' and id_card = #{idCard}) as isCxjbylbx,
(SELECT count(*) from cjronebl_disability_certificate_redemption where status = '8' and id_card = #{idCard}) as isCjzhl,
(SELECT count(*) from cjronebl_hospitalization_allowance where status = '8' and id_card = #{idCard}) as isZybz,
(SELECT count(*) from cjronebl_living_allowance where status = '8' and id_card = #{idCard}) as  isShbzj,
(SELECT count(*) from cjronebl_living_subsidy where status = '8' and id_card = #{idCard}) as  isShbt,
(SELECT count(*) from cjronebl_medical_support where status = '8' and id_card = #{idCard}) as  isYljz,
(SELECT count(*) from cjronebl_nursing_subsidy where status = '8' and id_card = #{idCard}) as  isHlbt,
(SELECT count(*) from cjronebl_rehabilitation_subsidy where status = '8' and id_card = #{idCard}) as  isKfbz,
(SELECT count(*) from cjronebl_temporary_assistance where status = '8' and id_card = #{idCard}) as  isLsjz,
(SELECT count(*) from cjronebl_zgjbyanglao where status = '8' and id_card = #{idCard}) as  isZgjbyl,
(SELECT count(*) from cjronebl_zgjbyiliao where status = '8' and id_card = #{idCard}) as  isZgjbylbx,
(SELECT count(*) from cjronebl_love24 where status = '8' and id_card = #{idCard}) as  isLove24,
(SELECT count(*) from cjronebl_love24 where  application_type = '电信' and id_card = #{idCard}) as  isLove24dx,
(SELECT count(*) from cjronebl_love24 where  application_type = '移动' and id_card = #{idCard}) as  isLove24yd,
IFNULL((SELECT medical_insurance from disability_certificate_sync_data where id_card = #{idCard}),'无') as medicalInsurance,
IFNULL((SELECT pension_insurance from disability_certificate_sync_data where id_card = #{idCard}),'无') as pensionInsurance,
IFNULL((SELECT family_econo_condition from disability_certificate_sync_data where id_card = #{idCard}),'无') as familyEcho,
IFNULL((SELECT is_tk from disability_certificate_sync_data where id_card = #{idCard}),0) as isTk,
IFNULL((SELECT is_fx from disability_certificate_sync_data where id_card = #{idCard}),0) as isFx,
IFNULL((SELECT count(*) from data_distressed_child where id_card = #{idCard}),0) as isKjEt,
IFNULL((SELECT count(*) from data_work_injury_insurance where id_card = #{idCard}),0) as isGongS,
IFNULL((SELECT count(*) from data_pension_subsidy where id_card = #{idCard}),0) as isYangL,
IFNULL((SELECT count(*) from data_yanglaobx where id_card = #{idCard} and type='职工基本养老' ),0) as iszhigongYangLaobx,
IFNULL((SELECT count(*) from data_yanglaobx where id_card = #{idCard} and type='城乡居民养老' ),0) as ischengxiangYangLaobx,
IFNULL((SELECT count(*) from data_yiliaobx where id_card = #{idCard} and type='职工基本医疗'),0) as iszhigongYiLiaobx,
IFNULL((SELECT count(*) from data_yiliaobx where id_card = #{idCard} and type='城乡基本医疗'),0) as ischengxiangYiLiaobx
 from data_disability_certificate a where a.id_card = #{idCard} limit 1 offset 0
	</select>
    <select id="queryMattersByMaB" resultType="map" parameterType="map">
SELECT a.name,(SELECT count(*) from cjrone_living_allowance where id_card = #{idCard}) as  isShbt,
(SELECT count(*) from cjrone_love_bus_card where id_card = #{idCard}) as isAxgjk,
(SELECT count(*) from cjrone_rehabilitation_subsidy where id_card = #{idCard}) as isKhbz,
(SELECT count(*) from cjrone_resident_pension_insurance where id_card = #{idCard}) as isYlbx,
(SELECT count(*) from cjrone_nursing_subsidy where id_card = #{idCard}) as isHlbt,
(SELECT count(*) from cjrone_welfare_matter_application where id_card = #{idCard} and matter_name='城乡居民养老补贴') as isCxjm,
(SELECT insured_situation from data_medical_insurance where id_card = #{idCard}) as medicalInsurance,
(SELECT subsidiary_category from data_low_security where apply_id_card = #{idCard}
	 UNION
	 SELECT personnel_category from data_low_security_margin where apply_id_card = #{idCard}) as familyEcho
 from disability_certificate_application a where a.id_card = #{idCard} limit 1 offset 0
	</select>
<select id="queryMattersIdByMap" resultType="map" parameterType="map">

SELECT
IFNULL((SELECT id from cjrone_living_allowance where id_card = #{idCard} and sign_status = '1'),0) as isShbt,
IFNULL((SELECT id from cjrone_rehabilitation_subsidy where id_card = #{idCard} and sign_status = '1'),0) as isKhbz,
IFNULL((SELECT id from cjrone_resident_pension_insurance where id_card = #{idCard}),0) as isYlbx,
IFNULL((SELECT id from cjrone_nursing_subsidy where id_card = #{idCard} and sign_status = '1'),0) as isHlbt,
IFNULL((SELECT id from cjrone_employment_subsidy where id_card = #{idCard} and sign_status = '1'),0) as isjycybt
from DUAL
	</select>

    <update id="updateStatusById"  parameterType="map">
    update cjrone_welfare_matter_application set status = #{status}
    <if test="statusOptions != null and statusOptions.trim() != ''">
        ,status_options = #{statusOptions}
    </if>
    where id = #{id}
    </update>
    <update id="updateSignStatusById"  parameterType="map">
        update cjrone_welfare_matter_application set status=#{status}
        <if test="signStatus != null and signStatus.trim() != ''">
            ,sign_status = #{signStatus}
        </if>
        <if test="signatureStatus != null and signatureStatus.trim() != ''">
            ,signature_status = #{signatureStatus}
        </if>
    where matter_id = #{matterId} and matter_name=#{matterName}
    </update>
    <select id="queryWelfareMatterByEnt"  resultType="com.hmit.kernespring.modules.matter_application.entity.CjroneWelfareMatterApplicationEntity" parameterType="com.hmit.kernespring.modules.matter_application.entity.CjroneWelfareMatterApplicationEntity">
    select  * from cjrone_welfare_matter_application
    where matter_id = #{matterId} and matter_name=#{matterName}
    </select>
    <select id="queryFamilyEconoCondition"  resultType="map" parameterType="map">
        SELECT id_card as id_card ,'低保' as name from data_low_security where id_card = #{idCard}
    UNION
    SELECT id_card as id_card ,'低保边缘' as name from data_low_security_margin where id_card = #{idCard}
    </select>
</mapper>
