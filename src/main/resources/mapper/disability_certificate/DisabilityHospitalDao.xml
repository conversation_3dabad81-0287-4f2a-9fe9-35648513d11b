<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.disability_certificate.dao.DisabilityHospitalDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.disability_certificate.entity.DisabilityHospitalEntity" id="disabilityHospitalMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="idCard" column="id_card"/>
        <result property="mobilePhone" column="mobile_phone"/>
        <result property="disableId" column="disable_id"/>
        <result property="guardianName" column="guardian_name"/>
        <result property="guardianPhone" column="guardian_phone"/>
        <result property="applicationType" column="application_type"/>
        <result property="disabilityLevel" column="disability_level"/>
        <result property="disabilityType" column="disability_type"/>
    </resultMap>

    <select id="queryExportData" resultType="com.hmit.kernespring.modules.disability_certificate.entity.DisabilityHospitalEntity">
		SELECT * FROM disability_hospital order by id desc
	</select>

    <select id="queryExportDataToZCL" resultType="com.hmit.kernespring.modules.disability_certificate.entity.DisabilityExportEntity">

       SELECT a.name,a.id_card,b.sex,b.nationality,"" as educationDegree,b.marital_status,"" as accountNature,
        b.birthday,b.mobile_phone,"" as fixedTelephone,b.present_Zhen,b.present_Cun,b.native_Address,b.present_Address,
        a.disability_category as typename,b.guardian_name,b.guardian_phone,"" as guardianFixedPhone,b.guardian_relation,
        b.photo
        FROM   cjrone_disability_hospital a left join disability_certificate_application b
        on a.id_card=b.id_card
        WHERE  a.status='1'
        order by a.id desc

    </select>

</mapper>