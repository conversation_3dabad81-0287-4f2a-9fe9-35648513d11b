<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.disability_certificate.dao.DisabilityCertificateDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.disability_certificate.entity.DisabilityCertificateEntity" id="disabilityCertificateMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="idCard" column="id_card"/>
        <result property="mobilePhone" column="mobile_phone"/>
        <result property="disableId" column="disable_id"/>
        <result property="guardianName" column="guardian_name"/>
        <result property="guardianPhone" column="guardian_phone"/>
        <result property="applicationType" column="application_type"/>
        <result property="familyEconoCondition" column="family_econo_condition"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.disability_certificate.entity.DisabilityCertificateEntity">
		SELECT * FROM disability_certificate order by id desc
	</select>

</mapper>