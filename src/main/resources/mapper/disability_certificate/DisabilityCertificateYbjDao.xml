<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.disability_certificate.dao.DisabilityCertificateYbjDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.disability_certificate.entity.DisabilityCertificateYbjEntity" id="disabilityCertificateYbjMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="idCard" column="id_card"/>
        <result property="mobilePhone" column="mobile_phone"/>
        <result property="disableId" column="disable_id"/>
        <result property="guardianName" column="guardian_name"/>
        <result property="guardianPhone" column="guardian_phone"/>
        <result property="applicationType" column="application_type"/>
        <result property="healthCareCondition" column="health_care_condition"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.disability_certificate.entity.DisabilityCertificateYbjEntity">
		SELECT * FROM disability_certificate_ybj order by id desc
	</select>

</mapper>