<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.app.dao.AppDisabilityCretificateDao">

    <select id="getApplyList" resultType="map">
        SELECT * FROM data_rehabilitation_subsidy_category where parent_id=#{parentId} order by category_id desc
    </select>


    <select id="getQueueNum" resultType="integer">
        select count(*) from disability_certificate_application
        where status='2' and disability_type=#{disabilitytype}
        <if test='idcard != null'>
            and application_time &lt;
            (
            select application_time
            from disability_certificate_application
            where id_card=#{idcard}
            )
        </if>


    </select>

</mapper>