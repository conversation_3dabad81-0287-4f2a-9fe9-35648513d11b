<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.cjrone_bl.dao.CjroneblChildeduDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblChildeduEntity" id="cjroneblChildeduMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="idCard" column="id_card"/>
        <result property="birthday" column="birthday"/>
        <result property="telephoe" column="telephoe"/>
        <result property="liveAddress" column="live_address"/>
        <result property="disableId" column="disable_id"/>
        <result property="currentSchool" column="current_school"/>
        <result property="grade" column="grade"/>
        <result property="admissionTime" column="admission_time"/>
        <result property="applicantType" column="applicant_type"/>
        <result property="fatherName" column="father_name"/>
        <result property="fatherIdcard" column="father_idcard"/>
        <result property="motherName" column="mother_name"/>
        <result property="motherIdcard" column="mother_idcard"/>
        <result property="subsidyMoney" column="subsidy_money"/>
        <result property="isHuJiRation" column="is_hu_ji_ration"/>
        <result property="createId" column="create_id"/>
        <result property="createTime" column="create_time"/>
        <result property="createName" column="create_name"/>
        <result property="status" column="status"/>
        <result property="statusOptions" column="status_options"/>
        <result property="signStatus" column="sign_status"/>
        <result property="signatureStatus" column="signature_status"/>
        <result property="fatherDisid" column="father_disid"/>
        <result property="motherDisid" column="mother_disid"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblChildeduEntity">
		SELECT * FROM cjronebl_childedu order by id desc
	</select>

</mapper>