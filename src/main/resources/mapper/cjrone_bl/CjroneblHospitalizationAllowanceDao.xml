<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.cjrone_bl.dao.CjroneblHospitalizationAllowanceDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblHospitalizationAllowanceEntity" id="cjroneblHospitalizationAllowanceMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="sex" column="sex"/>
        <result property="birthday" column="birthday"/>
        <result property="disableId" column="disable_id"/>
        <result property="zyTime" column="zy_time"/>
        <result property="telephone" column="telephone"/>
        <result property="guardianName" column="guardian_name"/>
        <result property="liveAddress" column="live_address"/>
        <result property="createId" column="create_id"/>
        <result property="createTime" column="create_time"/>
        <result property="createName" column="create_name"/>
        <result property="status" column="status"/>
        <result property="statusOptions" column="status_options"/>
        <result property="signStatus" column="sign_status"/>
        <result property="signatureStatus" column="signature_status"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblHospitalizationAllowanceEntity">
		SELECT * FROM cjronebl_hospitalization_allowance order by id desc
	</select>

</mapper>