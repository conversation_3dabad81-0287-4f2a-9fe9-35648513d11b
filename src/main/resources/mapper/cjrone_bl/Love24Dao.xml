<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.cjrone_bl.dao.Love24Dao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.cjrone_bl.entity.Love24Entity" id="love24Map">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="sex" column="sex"/>
        <result property="idCard" column="id_card"/>
        <result property="disableId" column="disable_id"/>
        <result property="disableType" column="disable_type"/>
        <result property="guardian" column="guardian"/>
        <result property="liveAddress" column="live_address"/>
        <result property="telephone" column="telephone"/>
        <result property="relation1" column="relation1"/>
        <result property="name1" column="name1"/>
        <result property="idcard1" column="idcard1"/>
        <result property="tel1" column="tel1"/>
        <result property="relation2" column="relation2"/>
        <result property="name2" column="name2"/>
        <result property="idcard2" column="idcard2"/>
        <result property="tel2" column="tel2"/>
        <result property="relation3" column="relation3"/>
        <result property="name3" column="name3"/>
        <result property="idcard3" column="idcard3"/>
        <result property="tel3" column="tel3"/>
        <result property="relation4" column="relation4"/>
        <result property="name4" column="name4"/>
        <result property="idcard4" column="idcard4"/>
        <result property="tel4" column="tel4"/>
        <result property="type" column="type"/>
        <result property="typeTelephone" column="type_telephone"/>
        <result property="typeTelephone2" column="type_telephone2"/>
        <result property="relation5" column="relation5"/>
        <result property="name5" column="name5"/>
        <result property="tel5" column="tel5"/>
        <result property="relation6" column="relation6"/>
        <result property="name6" column="name6"/>
        <result property="tel6" column="tel6"/>
        <result property="relation7" column="relation7"/>
        <result property="name7" column="name7"/>
        <result property="tel7" column="tel7"/>
        <result property="relation8" column="relation8"/>
        <result property="name8" column="name8"/>
        <result property="tel8" column="tel8"/>
        <result property="createId" column="create_id"/>
        <result property="createTime" column="create_time"/>
        <result property="createName" column="create_name"/>
        <result property="status" column="status"/>
        <result property="statusOptions" column="status_options"/>
        <result property="signStatus" column="sign_status"/>
        <result property="signatureStatus" column="signature_status"/>
        <result property="returnStatus" column="return_status"/>
        <result property="familyLove" column="family_love"/>
        <result property="applicationType" column="application_type"/>
        <result property="dianxinTelephone" column="dianxin_telephone"/>
        <result property="isDisable" column="is_disable"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.cjrone_bl.entity.Love24Entity">
		SELECT * FROM cjronebl_love24 order by id desc
	</select>

</mapper>
