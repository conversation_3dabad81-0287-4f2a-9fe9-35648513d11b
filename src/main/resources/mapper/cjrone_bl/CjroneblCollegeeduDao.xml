<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.cjrone_bl.dao.CjroneblCollegeeduDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblCollegeeduEntity" id="cjroneblCollegeeduMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="sex" column="sex"/>
        <result property="birthday" column="birthday"/>
        <result property="collegeName" column="college_name"/>
        <result property="majorName" column="major_name"/>
        <result property="collegeTime" column="college_time"/>
        <result property="disableId" column="disable_id"/>
        <result property="tuition" column="tuition"/>
        <result property="actuallyTuition" column="actually_tuition"/>
        <result property="accommodationFee" column="accommodation_fee"/>
        <result property="actuallyAccommodationFee" column="actually_accommodation_fee"/>
        <result property="hukouNature" column="hukou_nature"/>
        <result property="familyCount" column="family_count"/>
        <result property="liveAddress" column="live_address"/>
        <result property="telephone" column="telephone"/>
        <result property="postcode" column="postcode"/>
        <result property="familyFinances" column="family_finances"/>
        <result property="familyIncome" column="family_income"/>
        <result property="subsidyReason" column="subsidy_reason"/>
        <result property="createId" column="create_id"/>
        <result property="createTime" column="create_time"/>
        <result property="createName" column="create_name"/>
        <result property="status" column="status"/>
        <result property="statusOptions" column="status_options"/>
        <result property="signStatus" column="sign_status"/>
        <result property="signatureStatus" column="signature_status"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblCollegeeduEntity">
		SELECT * FROM cjronebl_collegeedu order by id desc
	</select>

</mapper>