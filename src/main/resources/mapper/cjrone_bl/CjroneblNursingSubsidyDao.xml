<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.cjrone_bl.dao.CjroneblNursingSubsidyDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblNursingSubsidyEntity" id="cjroneblNursingSubsidyMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="sex" column="sex"/>
        <result property="birthday" column="birthday"/>
        <result property="nationality" column="nationality"/>
        <result property="idCard" column="id_card"/>
        <result property="mobilePhone" column="mobile_phone"/>
        <result property="guardianName" column="guardian_name"/>
        <result property="guardianPhone" column="guardian_phone"/>
        <result property="disabilityCategory" column="disability_category"/>
        <result property="disabilityDegree" column="disability_degree"/>
        <result property="disableId" column="disable_id"/>
        <result property="bankAccount" column="bank_account"/>
        <result property="bankName" column="bank_name"/>
        <result property="careType" column="care_type"/>
        <result property="applicationDate" column="application_date"/>
        <result property="auditPerson" column="audit_person"/>
        <result property="auditDate" column="audit_date"/>
        <result property="status" column="status"/>
        <result property="createId" column="create_id"/>
        <result property="createTime" column="create_time"/>
        <result property="familyEconoCondition" column="family_econo_condition"/>
        <result property="lifeStatus" column="life_status"/>
        <result property="standardSubsidy" column="standard_subsidy"/>
        <result property="nativeAddress" column="native_address"/>
        <result property="actuallySubsidy" column="actually_subsidy"/>
        <result property="liveAddress" column="live_address"/>
        <result property="remark" column="remark"/>
        <result property="signStatus" column="sign_status"/>
        <result property="signatureStatus" column="signature_status"/>
        <result property="sixMonth" column="six_month"/>
        <result property="careMonths" column="care_months"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblNursingSubsidyEntity">
		SELECT * FROM cjronebl_nursing_subsidy order by id desc
	</select>

</mapper>