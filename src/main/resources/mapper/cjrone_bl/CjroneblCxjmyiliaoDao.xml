<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.cjrone_bl.dao.CjroneblCxjmyiliaoDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblCxjmyiliaoEntity" id="cjroneblCxjmyiliaoMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="sex" column="sex"/>
        <result property="age" column="age"/>
        <result property="disableId" column="disable_id"/>
        <result property="telephone" column="telephone"/>
        <result property="liveAddress" column="live_address"/>
        <result property="insuredStatus" column="insured_status"/>
        <result property="otherSubsidy" column="other_subsidy"/>
        <result property="seTime" column="se_time"/>
        <result property="payMoney" column="pay_money"/>
        <result property="subsidyMoney" column="subsidy_money"/>
        <result property="createId" column="create_id"/>
        <result property="createTime" column="create_time"/>
        <result property="createName" column="create_name"/>
        <result property="status" column="status"/>
        <result property="statusOptions" column="status_options"/>
        <result property="signStatus" column="sign_status"/>
        <result property="signatureStatus" column="signature_status"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblCxjmyiliaoEntity">
		SELECT * FROM cjronebl_cxjmyiliao order by id desc
	</select>

</mapper>