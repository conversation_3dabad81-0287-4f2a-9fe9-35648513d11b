<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.cjrone_bl.dao.CjroneblTemporaryAssistanceDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblTemporaryAssistanceEntity" id="cjroneblTemporaryAssistanceMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="sex" column="sex"/>
        <result property="birthday" column="birthday"/>
        <result property="disabilityType" column="disability_type"/>
        <result property="disabilityDegree" column="disability_degree"/>
        <result property="disableId" column="disable_id"/>
        <result property="idCard" column="id_card"/>
        <result property="liveAddress" column="live_address"/>
        <result property="telephone" column="telephone"/>
        <result property="familyEconomy" column="family_economy"/>
        <result property="mingzhenSubsidy" column="mingzhen_subsidy"/>
        <result property="payMoney" column="pay_money"/>
        <result property="subsidyMoney" column="subsidy_money"/>
        <result property="applyReason" column="apply_reason"/>
        <result property="createId" column="create_id"/>
        <result property="createTime" column="create_time"/>
        <result property="createName" column="create_name"/>
        <result property="status" column="status"/>
        <result property="statusOptions" column="status_options"/>
        <result property="signStatus" column="sign_status"/>
        <result property="signatureStatus" column="signature_status"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblTemporaryAssistanceEntity">
		SELECT * FROM cjronebl_temporary_assistance order by id desc
	</select>

</mapper>