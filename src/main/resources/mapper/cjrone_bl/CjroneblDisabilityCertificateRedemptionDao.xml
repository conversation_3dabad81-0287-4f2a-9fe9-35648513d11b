<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.cjrone_bl.dao.CjroneblDisabilityCertificateRedemptionDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblDisabilityCertificateRedemptionEntity" id="cjroneblDisabilityCertificateRedemptionMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="idCard" column="id_card"/>
        <result property="disableId" column="disable_id"/>
        <result property="disabilityCategory" column="disability_category"/>
        <result property="disabilityDegree" column="disability_degree"/>
        <result property="disabilityInfo" column="disability_info"/>
        <result property="completeTime" column="complete_time"/>
        <result property="status" column="status"/>
        <result property="createId" column="create_id"/>
        <result property="createTime" column="create_time"/>
        <result property="createName" column="create_name"/>
        <result property="verifyTime" column="verify_time"/>
        <result property="mobilePhone" column="mobile_phone"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblDisabilityCertificateRedemptionEntity">
		SELECT * FROM cjronebl_disability_certificate_redemption order by id desc
	</select>

</mapper>