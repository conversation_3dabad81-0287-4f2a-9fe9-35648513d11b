<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.cjrone_bl.dao.LivingAllowanceSyncDataDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.cjrone_bl.entity.LivingAllowanceSyncDataEntity" id="livingAllowanceSyncDataMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="idCard" column="id_card"/>
        <result property="createTime" column="create_time"/>
        <result property="familyEconoCondition" column="family_econo_condition"/>
        <result property="isDead" column="is_dead"/>
        <result property="status" column="status"/>
        <result property="isFx" column="is_fx"/>
        <result property="isHkqy" column="is_hkqy"/>
        <result property="fxInfo" column="fx_info"/>
        <result property="hkqyInfo" column="hkqy_info"/>
        <result property="exceptionReason" column="exception_reason"/>
        <result property="isShbt" column="is_shbt"/>
        <result property="isZgyl" column="is_zgyl"/>
        <result property="ym" column="ym"/>
        <result property="isAge" column="is_age"/>
        <result property="isDisable" column="is_disable"/>
        <result property="nativeZhen" column="native_zhen"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.cjrone_bl.entity.LivingAllowanceSyncDataEntity">
		SELECT * FROM cjronebl_living_allowance_sync_data order by id desc
	</select>

</mapper>
