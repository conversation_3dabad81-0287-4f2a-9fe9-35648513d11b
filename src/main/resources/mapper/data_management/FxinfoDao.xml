<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.data_management.dao.FxinfoDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.data_management.entity.FxinfoEntity" id="fxinfoMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="sex" column="sex"/>
        <result property="age" column="age"/>
        <result property="birthplace" column="birthplace"/>
        <result property="idCard" column="id_card"/>
        <result property="fromInfo" column="from_info"/>
        <result property="resultInfo" column="result_info"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.data_management.entity.FxinfoEntity">
		SELECT * FROM data_fxinfo order by id desc
	</select>

</mapper>