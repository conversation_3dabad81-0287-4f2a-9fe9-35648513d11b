<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.data_management.dao.ApiCardIdDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.data_management.entity.ApiCardIdEntity" id="apiCardIdMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="sex" column="sex"/>
        <result property="nationality" column="nationality"/>
        <result property="birthday" column="birthday"/>
        <result property="nativePlace" column="native_place"/>
        <result property="idCard" column="id_card"/>
        <result property="nativeAddress" column="native_address"/>
        <result property="photo" column="photo"/>
        <result property="qfjg" column="qfjg"/>
        <result property="jgbh" column="jgbh"/>
        <result property="maritalStatus" column="marital_status"/>
        <result property="educationDegree" column="education_degree"/>
        <result property="nativeZhen" column="native_zhen"/>
        <result property="nativeCun" column="native_cun"/>
        <result property="presentZhen" column="present_zhen"/>
        <result property="presentCun" column="present_cun"/>
        <result property="presentAddress" column="present_address"/>
        <result property="postcode" column="postcode"/>
        <result property="mobilePhone" column="mobile_phone"/>
        <result property="guardianName" column="guardian_name"/>
        <result property="guardianPhone" column="guardian_phone"/>
        <result property="guardianIdcard" column="guardian_idcard"/>
        <result property="guardianRelation" column="guardian_relation"/>
        <result property="bankAccount" column="bank_account"/>
        <result property="bankName" column="bank_name"/>
        <result property="disabilityType" column="disability_type"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.data_management.entity.ApiCardIdEntity">
		SELECT * FROM api_card_id order by id desc
	</select>

</mapper>