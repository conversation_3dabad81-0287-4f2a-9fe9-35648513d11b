<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.data_management.dao.DataSmsTemplateDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.data_management.entity.DataSmsTemplateEntity" id="dataSmsTemplateMap">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="time" column="time"/>
        <result property="address" column="address"/>
        <result property="material" column="material"/>
        <result property="precautions" column="precautions"/>
        <result property="status" column="status"/>
        <result property="memo" column="memo"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.data_management.entity.DataSmsTemplateEntity">
		SELECT * FROM data_sms_template order by id desc
	</select>

</mapper>