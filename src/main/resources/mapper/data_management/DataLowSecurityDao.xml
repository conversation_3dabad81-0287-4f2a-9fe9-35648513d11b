<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.data_management.dao.DataLowSecurityDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.data_management.entity.DataLowSecurityEntity" id="dataLowSecurityMap">
        <result property="id" column="id"/>
        <result property="administrativeDivision" column="administrative_division"/>
        <result property="applicant" column="applicant"/>
        <result property="applyIdCard" column="apply_id_card"/>
        <result property="name" column="name"/>
        <result property="relationshipWithHousehold" column="relationship_with_household"/>
        <result property="idCard" column="id_card"/>
        <result property="personnelInformationCategory" column="personnel_information_category"/>
        <result property="sex" column="sex"/>
        <result property="birthday" column="birthday"/>
        <result property="age" column="age"/>
        <result property="applicationCategory" column="application_category"/>
        <result property="rescueCertificateNumber" column="rescue_certificate_number"/>
        <result property="bankName" column="bank_name"/>
        <result property="personBankAccount" column="person_bank_account"/>
        <result property="educationalLevel" column="educational_level"/>
        <result property="politicalStatus" column="political_status"/>
        <result property="maritalStatus" column="marital_status"/>
        <result property="nationality" column="nationality"/>
        <result property="monthlyPerCapitaIncome" column="monthly_per_capita_income"/>
        <result property="guaranteeTheTotalPopulation" column="guarantee_the_total_population"/>
        <result property="householdSecurity" column="household_security"/>
        <result property="otherSubsidies" column="other_subsidies"/>
        <result property="totalAmountOfProtection" column="total_amount_of_protection"/>
        <result property="accountNature" column="account_nature"/>
        <result property="personnelCategory" column="personnel_category"/>
        <result property="healthStatus" column="health_status"/>
        <result property="careerStatus" column="career_status"/>
        <result property="disabilityCategory" column="disability_category"/>
        <result property="disabilityLevel" column="disability_level"/>
        <result property="disableId" column="disable_id"/>
        <result property="dateOfSalvage" column="date_of_salvage"/>
        <result property="subsidiaryCategory" column="subsidiary_category"/>
        <result property="telephone" column="telephone"/>
        <result property="mobilePhone" column="mobile_phone"/>
        <result property="bankAccount" column="bank_account"/>
        <result property="perCapitaSecurity" column="per_capita_security"/>
        <result property="createId" column="create_id"/>
        <result property="createTime" column="create_time"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.data_management.entity.DataLowSecurityEntity">
		SELECT * FROM data_low_security order by id desc
	</select>

    <delete id="deleteAllData">
        DELETE  FROM  data_low_security
    </delete>

</mapper>