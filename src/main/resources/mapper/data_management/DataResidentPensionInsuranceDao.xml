<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.data_management.dao.DataResidentPensionInsuranceDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.data_management.entity.DataResidentPensionInsuranceEntity" id="dataResidentPensionInsuranceMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="idCard" column="id_card"/>
        <result property="projectName" column="project_name"/>
        <result property="subsidy" column="subsidy"/>
        <result property="mobilePhone" column="mobile_phone"/>
        <result property="address" column="address"/>
        <result property="status" column="status"/>
        <result property="createId" column="create_id"/>
        <result property="createTime" column="create_time"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.data_management.entity.DataResidentPensionInsuranceEntity">
		SELECT * FROM data_resident_pension_insurance order by id desc
	</select>

</mapper>