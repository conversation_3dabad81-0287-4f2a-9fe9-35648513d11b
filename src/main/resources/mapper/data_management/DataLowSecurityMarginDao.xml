<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.data_management.dao.DataLowSecurityMarginDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.data_management.entity.DataLowSecurityMarginEntity" id="dataLowSecurityMarginMap">
        <result property="id" column="id"/>
        <result property="administrativeDivision" column="administrative_division"/>
        <result property="applicant" column="applicant"/>
        <result property="applyIdCard" column="apply_id_card"/>
        <result property="applicantAccountNature" column="applicant_account_nature"/>
        <result property="telephone" column="telephone"/>
        <result property="mobilePhone" column="mobile_phone"/>
        <result property="causeOfPoverty" column="cause_of_poverty"/>
        <result property="residenceAddress" column="residence_address"/>
        <result property="lowSideCategory" column="low_side_category"/>
        <result property="residentialAddress" column="residential_address"/>
        <result property="reasonForApplying" column="reason_for_applying"/>
        <result property="applicationCategory" column="application_category"/>
        <result property="bankName" column="bank_name"/>
        <result property="accountHolder" column="account_holder"/>
        <result property="accountHolderIdCard" column="account_holder_id_card"/>
        <result property="bankAccount" column="bank_account"/>
        <result property="cumulativeMonth" column="cumulative_month"/>
        <result property="cumulativeIncome" column="cumulative_income"/>
        <result property="cumulativeExpenditure" column="cumulative_expenditure"/>
        <result property="monthlyPerCapitaIncome" column="monthly_per_capita_income"/>
        <result property="inclusionOfRescueStandards" column="inclusion_of_rescue_standards"/>
        <result property="totalFamilyPopulation" column="total_family_population"/>
        <result property="guaranteeTotalPopulation" column="guarantee_total_population"/>
        <result property="generalNumberOfPeople" column="general_number_of_people"/>
        <result property="livingAllowance" column="living_allowance"/>
        <result property="otherSubsidies" column="other_subsidies"/>
        <result property="totalAmountOfProtection" column="total_amount_of_protection"/>
        <result property="dateOfSalvage" column="date_of_salvage"/>
        <result property="relationshipWithHousehold" column="relationship_with_household"/>
        <result property="name" column="name"/>
        <result property="idCard" column="id_card"/>
        <result property="sex" column="sex"/>
        <result property="birthday" column="birthday"/>
        <result property="age" column="age"/>
        <result property="rescueCertificateNumber" column="rescue_certificate_number"/>
        <result property="whetherToEnjoy" column="whether_to_enjoy"/>
        <result property="memberAccountNature" column="member_account_nature"/>
        <result property="personnelCategory" column="personnel_category"/>
        <result property="healthStatus" column="health_status"/>
        <result property="maritalStatus" column="marital_status"/>
        <result property="nationality" column="nationality"/>
        <result property="careerStatus" column="career_status"/>
        <result property="politicalStatus" column="political_status"/>
        <result property="specificGuaranteeObject" column="specific_guarantee_object"/>
        <result property="disabilityCategory" column="disability_category"/>
        <result property="disabilityLevel" column="disability_level"/>
        <result property="disableId" column="disable_id"/>
        <result property="personBankAccount" column="person_bank_account"/>
        <result property="bankNameTwo" column="bank_name_two"/>
        <result property="monthlyGuaranteeAmount" column="monthly_guarantee_amount"/>
        <result property="educationalLevel" column="educational_level"/>
        <result property="laborAbility" column="labor_ability"/>
        <result property="workUnit" column="work_unit"/>
        <result property="severeDisabilitiesNum" column="severe_disabilities_num"/>
        <result property="createId" column="create_id"/>
        <result property="createTime" column="create_time"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.data_management.entity.DataLowSecurityMarginEntity">
		SELECT * FROM data_low_security_margin order by id desc
	</select>

    <delete id="deleteAllData">
        DELETE  FROM  data_low_security_margin
    </delete>

</mapper>