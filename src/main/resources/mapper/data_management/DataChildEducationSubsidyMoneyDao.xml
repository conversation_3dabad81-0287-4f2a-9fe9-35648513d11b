<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.data_management.dao.DataChildEducationSubsidyMoneyDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.data_management.entity.DataChildEducationSubsidyMoneyEntity" id="dataChildEducationSubsidyMoneyMap">
        <result property="id" column="id"/>
        <result property="familyEconomicSituation" column="family_economic_situation"/>
        <result property="education" column="education"/>
        <result property="subsidyMoney" column="subsidy_money"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.data_management.entity.DataChildEducationSubsidyMoneyEntity">
		SELECT * FROM data_child_education_subsidy_money order by id desc
	</select>

    <select id="queryByMap" resultType="com.hmit.kernespring.modules.data_management.entity.DataChildEducationSubsidyMoneyEntity">
        SELECT *
        FROM data_child_education_subsidy_money
        where family_economic_situation=#{family_economic_situation}
        and education=#{education}
        order by id desc
    </select>

</mapper>