<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.data_management.dao.DisabilityCertificateSyncDataDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.data_management.entity.DisabilityCertificateSyncDataEntity" id="disabilityCertificateSyncDataMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="idCard" column="id_card"/>
        <result property="createTime" column="create_time"/>
        <result property="familyEconoCondition" column="family_econo_condition"/>
        <result property="medicalInsurance" column="medical_insurance"/>
        <result property="isDead" column="is_dead"/>
        <result property="isTk" column="is_tk"/>
        <result property="isSixty" column="is_sixty"/>
        <result property="isWorkinjury" column="is_workinjury"/>
        <result property="isDischild" column="is_dischild"/>
        <result property="isResident" column="is_resident"/>
        <result property="isClork" column="is_clork"/>
        <result property="isApplyWelfareMatter" column="is_apply_welfare_matter"/>
        <result property="status" column="status"/>
        <result property="isFx" column="is_fx"/>
        <result property="isHkqy" column="is_hkqy"/>
        <result property="hkqyInfo" column="hkqy_info"/>
        <result property="fxInfo" column="fx_info"/>
        <result property="isShbt" column="is_shbt"/>
        <result property="isZgyl" column="is_zgyl"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.data_management.entity.DisabilityCertificateSyncDataEntity">
        SELECT id,name,id_card,create_time,family_econo_condition,medical_insurance,
        if(is_dead=1,"是","否") as is_dead_name,
        if(is_tk=1,"是","否") as is_tk_name,
        if(is_sixty=1,"是","否") as is_sixty_name,
        if(is_workinjury=1,"是","否") as is_workinjury_name,
        if(is_dischild=1,"是","否") as is_dischild_name,
        if(is_resident=1,"是","否") as is_resident_name,
        if(is_clork=1,"是","否") as is_clork_name,
        if(status=1,"正常","异常") status_name
        FROM disability_certificate_sync_data WHERE 1=1
        <if test="status != null and status.trim() != ''">
            and status = #{status}
        </if>
        order by create_time desc
	</select>
    <select id="queryStaticsData" resultType="com.hmit.kernespring.modules.data_management.entity.DisabilityCertificateSyncDataEntity">
		SELECT
	(SELECT count(*) from data_yanglaobx where type='职工基本养老' and id_card = #{idCard}) as is_zgyl,
    (SELECT count(*) from cjronebl_living_subsidy where id_card = #{idCard}) as is_shbt,
    (SELECT count(*) from data_fxinfo where id_card = #{idCard}) as is_fx,
    (SELECT count(*) from data_low_security where id_card = #{idCard}) as data_low_security,
    (SELECT count(*) from data_low_security_margin where id_card = #{idCard}) as data_low_security_margin,
    (SELECT count(*) from data_medical_insurance where id_card = #{idCard}) as medical_insurance,
    (SELECT count(*) from data_deadinfo where id_card = #{idCard}) as is_dead,
    (SELECT count(*) from data_particularly_poor where id_card = #{idCard}) as is_tk,
    (SELECT count(*) from data_pension_subsidy where id_card = #{idCard}) as is_sixty,
    (SELECT count(*) from data_work_injury_insurance where id_card = #{idCard}) as is_workinjury,
    (SELECT count(*) from data_distressed_child where id_card = #{idCard}) as is_dischild,
    (SELECT count(*) from data_resident_pension_insurance where id_card = #{idCard}) as is_resident,
    (SELECT count(*) from data_employee_social_security_insured where id_card = #{idCard}) as is_clork,
    (SELECT count(*) from cjrone_welfare_matter_application where id_card = #{idCard}) as is_apply_welfare_matter
    FROM DUAL
	</select>
    <select id="queryNursingYcDataTotalByMap" resultType="int">
        select count(*) from cjrone_nursing_subsidy
    </select>
    <select id="queryLivingYcDataTotalByMap" resultType="int">
        select count(*) from cjrone_living_allowance
    </select>
    <select id="queryNursingYcDataByMap" resultType="com.hmit.kernespring.modules.data_management.entity.DisabilityCertificateSyncDataEntity">
        select  a.* from disability_certificate_sync_data a,cjrone_nursing_subsidy b where a.id_card = b.id_card
        order by b.create_time desc
        <if test="page != null and limit != null">
            limit #{limit} offset #{page}
        </if>
    </select>
    <select id="queryLivingYcDataByMap" resultType="com.hmit.kernespring.modules.data_management.entity.DisabilityCertificateSyncDataEntity">
        select  a.* from disability_certificate_sync_data a,cjrone_living_allowance b where a.id_card = b.id_card
        order by b.create_time desc
        <if test="page != null and limit != null">
            limit #{limit} offset #{page}
        </if>
    </select>
    <select id="queryStaticsLivingData" resultType="int">
        select count(*) from cjrone_living_allowance_import where id_card = #{idCard}
    </select>
    <select id="queryStaticsNursingData" resultType="int">
        select count(*) from cjrone_nursing_subsidy_import where id_card = #{idCard}
    </select>
</mapper>
