<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.data_management.dao.DataParticularlyPoorDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.data_management.entity.DataParticularlyPoorEntity" id="dataParticularlyPoorMap">
        <result property="id" column="id"/>
        <result property="administrativeDivision" column="administrative_division"/>
        <result property="name" column="name"/>
        <result property="relationshipWithHousehold" column="relationship_with_household"/>
        <result property="idCard" column="id_card"/>
        <result property="mobilePhone" column="mobile_phone"/>
        <result property="telephone" column="telephone"/>
        <result property="bankAccount" column="bank_account"/>
        <result property="bankName" column="bank_name"/>
        <result property="personBankAccount" column="person_bank_account"/>
        <result property="applicationCategory" column="application_category"/>
        <result property="numberOfSupport" column="number_of_support"/>
        <result property="amountOfSupport" column="amount_of_support"/>
        <result property="healthStatus" column="health_status"/>
        <result property="nursingLevel" column="nursing_level"/>
        <result property="nursingStandard" column="nursing_standard"/>
        <result property="rescueCertificateNumber" column="rescue_certificate_number"/>
        <result property="agedInstitution" column="aged_institution"/>
        <result property="sex" column="sex"/>
        <result property="birthday" column="birthday"/>
        <result property="reasonForApplying" column="reason_for_applying"/>
        <result property="createId" column="create_id"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <select id="queryExportData" resultType="com.hmit.kernespring.modules.data_management.entity.DataParticularlyPoorEntity">
		SELECT * FROM data_particularly_poor order by id desc
	</select>

    <delete id="deleteAllData">
        delete  from  data_particularly_poor
    </delete>

</mapper>