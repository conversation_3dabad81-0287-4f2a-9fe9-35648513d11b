<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.data_management.dao.DataChildEducationSubsidyDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.data_management.entity.DataChildEducationSubsidyEntity" id="dataChildEducationSubsidyMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="idCard" column="id_card"/>
        <result property="sex" column="sex"/>
        <result property="mobilePhone" column="mobile_phone"/>
        <result property="presentAddress" column="present_address"/>
        <result property="disableId" column="disable_id"/>
        <result property="familyEconoCondition" column="family_econo_condition"/>
        <result property="currentSchool" column="current_school"/>
        <result property="grade" column="grade"/>
        <result property="admissionTime" column="admission_time"/>
        <result property="applicantType" column="applicant_type"/>
        <result property="applicationTime" column="application_time"/>
        <result property="fatherName" column="father_name"/>
        <result property="fatherIdcard" column="father_idcard"/>
        <result property="motherName" column="mother_name"/>
        <result property="motherIdcard" column="mother_idcard"/>
        <result property="subsidyMoney" column="subsidy_money"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.data_management.entity.DataChildEducationSubsidyEntity">
		SELECT *
		FROM data_child_education_subsidy
		WHERE status='8'
		order by id desc
	</select>

</mapper>