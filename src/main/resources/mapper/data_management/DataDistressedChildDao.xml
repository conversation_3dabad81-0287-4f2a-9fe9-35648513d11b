<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.data_management.dao.DataDistressedChildDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.data_management.entity.DataDistressedChildEntity" id="dataDistressedChildMap">
        <result property="id" column="id"/>
        <result property="registrationDate" column="registration_date"/>
        <result property="name" column="name"/>
        <result property="idCard" column="id_card"/>
        <result property="sex" column="sex"/>
        <result property="birthday" column="birthday"/>
        <result property="distressedChildrenCategory" column="distressed_children_category"/>
        <result property="householdRegistration" column="household_registration"/>
        <result property="currentAddress" column="current_address"/>
        <result property="familySituation" column="family_situation"/>
        <result property="bankAccount" column="bank_account"/>
        <result property="guardianName" column="guardian_name"/>
        <result property="guardianIdCard" column="guardian_id_card"/>
        <result property="guardianPhone" column="guardian_phone"/>
        <result property="guardianRelationship" column="guardian_relationship"/>
        <result property="livingTogether" column="living_together"/>
        <result property="remark" column="remark"/>
        <result property="createId" column="create_id"/>
        <result property="createTime" column="create_time"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.data_management.entity.DataDistressedChildEntity">
		SELECT * FROM data_distressed_child order by id desc
	</select>

</mapper>