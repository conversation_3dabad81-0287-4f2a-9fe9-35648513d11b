<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.data_management.dao.DataDeadinfoDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.data_management.entity.DataDeadinfoEntity" id="dataDeadinfoMap">
        <result property="id" column="id"/>
        <result property="businessNumber" column="business_number"/>
        <result property="name" column="name"/>
        <result property="sex" column="sex"/>
        <result property="age" column="age"/>
        <result property="birthplace" column="birthplace"/>
        <result property="idCard" column="id_card"/>
        <result property="residenceAddress" column="residence_address"/>
        <result property="deadDate" column="dead_date"/>
        <result property="cremationDate" column="cremation_date"/>
        <result property="getDate" column="get_date"/>
        <result property="getAddress" column="get_address"/>
        <result property="familyName" column="family_name"/>
        <result property="relation" column="relation"/>
        <result property="phone" column="phone"/>
        <result property="fromInfo" column="from_info"/>
        <result property="resultInfo" column="result_info"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.data_management.entity.DataDeadinfoEntity">
		SELECT * FROM data_deadinfo order by id desc
	</select>

</mapper>
