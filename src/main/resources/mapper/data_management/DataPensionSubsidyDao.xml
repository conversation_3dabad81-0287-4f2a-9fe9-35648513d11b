<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.data_management.dao.DataPensionSubsidyDao">



    <select id="queryExportData" resultType="com.hmit.kernespring.modules.data_management.entity.DataPensionSubsidyEntity">
        SELECT * FROM data_pension_subsidy order by id desc
    </select>

    <delete id="deleteAllData">
        delete  from  data_pension_subsidy
    </delete>

</mapper>