<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.cjrone.dao.CjroneWelfareApplyDocDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.cjrone.entity.CjroneWelfareApplyDocEntity" id="cjroneWelfareApplyDocMap">
        <result property="id" column="id"/>
        <result property="welfareMatterApplicationId" column="welfare_matter_application_id"/>
        <result property="documentId" column="document_id"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.cjrone.entity.CjroneWelfareApplyDocEntity">
		SELECT * FROM cjrone_welfare_apply_doc order by id desc
	</select>

</mapper>