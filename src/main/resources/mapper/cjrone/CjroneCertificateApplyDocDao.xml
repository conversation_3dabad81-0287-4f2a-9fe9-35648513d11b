<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.cjrone.dao.CjroneCertificateApplyDocDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.cjrone.entity.CjroneCertificateApplyDocEntity" id="cjroneCertificateApplyDocMap">
        <result property="id" column="id"/>
        <result property="disabilityCertificateApplicationId" column="disability_certificate_application_id"/>
        <result property="disabilityAssessmentId" column="disability_assessment_id"/>
        <result property="disabilityAssessmentDetailName" column="disability_assessment_detail_name"/>
        <result property="documentId" column="document_id"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.cjrone.entity.CjroneCertificateApplyDocEntity">
		SELECT * FROM cjrone_certificate_apply_doc order by id desc
	</select>
    <select id="listDocumentByMap" resultType="com.hmit.kernespring.modules.cjrone.entity.CjroneDocumentEntity">
    SELECT * from cjrone_document where id IN (
    SELECT document_id FROM cjrone_certificate_apply_doc  where disability_certificate_application_id = #{disabilityCertificateApplicationId})
	</select>
    <select id="queryApplyDocByMap" resultType="com.hmit.kernespring.modules.cjrone.entity.CjroneCertificateApplyDocEntity">
        SELECT a.id,a.disability_certificate_application_id,b.disability_assessment_id,a.document_id,b.file_name as fileName,b.file_path as filePath,b.disability_assessment_detail_name
        FROM cjrone_certificate_apply_doc a,cjrone_document b
        where a.disability_certificate_application_id = #{apply_id} and a.document_id = b.id order by a.id desc
	</select>
    <delete id="deleteByApplicationId" parameterType="map">
        delete from cjrone_certificate_apply_doc where disability_certificate_application_id = #{disabilityCertificateApplicationId}
    </delete>

</mapper>