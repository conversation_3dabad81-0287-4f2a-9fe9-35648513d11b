<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.cjrone.dao.CjroneMessageHistoryDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.cjrone.entity.CjroneMessageHistoryEntity" id="cjroneMessageHistoryMap">
        <result property="id" column="id"/>
        <result property="receiveName" column="receive_name"/>
        <result property="receiveIdCard" column="receive_id_card"/>
        <result property="messageContent" column="message_content"/>
        <result property="matterType" column="matter_type"/>
        <result property="sendTime" column="send_time"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.cjrone.entity.CjroneMessageHistoryEntity">
		SELECT * FROM cjrone_message_history order by id desc
	</select>

</mapper>