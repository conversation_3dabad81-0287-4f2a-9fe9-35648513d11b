<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.cjrone.dao.CjroneStreetDao">

    <select id="getAdministrativeDivisionList" resultType="com.hmit.kernespring.modules.cjrone.entity.CjroneAdministrativeDivisionEntity">
        select code as value,name as label
        from cjrone_street
        where areacode=#{code}
    </select>

    <select id="getAdministrativeDivisionListShen" resultType="com.hmit.kernespring.modules.cjrone.entity.CjroneAdministrativeDivisionEntity">
        select code as value,name as label
        from cjrone_province where 1=1
        <if test="code != null">
            code=#{code}
        </if>
    </select>

    <select id="getAdministrativeDivisionListShi" resultType="com.hmit.kernespring.modules.cjrone.entity.CjroneAdministrativeDivisionEntity">
        select code as value,name as label
        from cjrone_city
        where provinceCode=#{code}
    </select>


    <select id="getAdministrativeDivisionListQu" resultType="com.hmit.kernespring.modules.cjrone.entity.CjroneAdministrativeDivisionEntity">
        select code as value,name as label
        from cjrone_area
        where cityCode=#{code}
    </select>


    <select id="getAdministrativeDivisionListForAPP" resultType="map">
        select code as id,name as value
        from cjrone_street
        where areacode=#{code}
    </select>


    <select id="getStreetById" resultType="com.hmit.kernespring.modules.cjrone.entity.CjroneAdministrativeDivisionEntity">
        select code as value,name as label
        from cjrone_street
        where code=#{code}
    </select>

    <select id="queryNames" resultType="map">
         select b.name as areaname,c.name as cityname,d.name as provincename
        from cjrone_street a left join cjrone_area b on a.areaCode=b.code
        left join cjrone_city c on a.cityCode=c.code
        left join cjrone_province d on a.provinceCode=d.code
        where a.code=#{code}
    </select>

    <select id="queryNames2" resultType="map">
        select b.name as areaname,c.name as cityname,d.name as provincename,a.name as villagename,e.name as streetname
        from cjrone_village a  left join cjrone_street e on a.streetCode=e.code
        left join cjrone_area b on a.areaCode=b.code
        left join cjrone_city c on a.cityCode=c.code
        left join cjrone_province d on a.provinceCode=d.code
        where a.code=#{code}
    </select>


</mapper>