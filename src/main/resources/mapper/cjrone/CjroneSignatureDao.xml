<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.cjrone.dao.CjroneSignatureDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.cjrone.entity.CjroneSignatureEntity" id="cjroneSignatureMap">
        <result property="id" column="id"/>
        <result property="createDate" column="create_date"/>
        <result property="createId" column="create_id"/>
        <result property="typeId" column="type_id"/>
        <result property="type" column="type"/>
        <result property="url" column="url"/>
        <result property="fileName" column="file_name"/>
        <result property="accountId" column="account_id"/>
        <result property="accountName" column="account_name"/>
        <result property="status" column="status"/>
        <result property="fileActUrl" column="file_act_url"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.cjrone.entity.CjroneSignatureEntity">
		SELECT * FROM cjrone_signature order by id desc
	</select>
    <select id="queryDataByMap" resultType="com.hmit.kernespring.modules.cjrone.entity.CjroneSignatureEntity">
        SELECT * FROM cjrone_signature where type='医院评残表' and type_id = #{applyId} and status='1'
        UNION
        SELECT * FROM cjrone_signature where type='残疾证申请残疾人手签' and type_id = #{applyId} and status='1'
	</select>

</mapper>