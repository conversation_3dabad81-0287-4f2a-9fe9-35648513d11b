<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.cjrone.dao.CjroneblLove24SybcDataDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.cjrone.entity.CjroneblLove24SybcDataEntity" id="cjroneblLove24SybcDataMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="idCard" column="id_card"/>
        <result property="createTime" column="create_time"/>
        <result property="isDead" column="is_dead"/>
        <result property="status" column="status"/>
        <result property="applicationType" column="application_type"/>
        <result property="isDisable" column="is_disable"/>
        <result property="isHkqy" column="is_hkqy"/>
        <result property="hkqyInfo" column="hkqy_info"/>
        <result property="liveAddress" column="live_address"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.cjrone.entity.CjroneblLove24SybcDataEntity">
		SELECT * FROM cjronebl_love24_sybc_data order by id desc
	</select>

</mapper>