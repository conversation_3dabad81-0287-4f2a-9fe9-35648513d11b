<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.cjrone.dao.DataRehabilitationSubsidyCategoryDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.cjrone.entity.DataRehabilitationSubsidyCategoryEntity" id="dataRehabilitationSubsidyCategoryMap">
        <result property="categoryId" column="category_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="name" column="name"/>
        <result property="createTime" column="create_time"/>
        <result property="createId" column="create_id"/>
        <result property="conditionName" column="condition_name"/>
        <result property="serviceStandard" column="service_standard"/>
        <result property="memo" column="memo"/>
        <result property="leafnode" column="leafnode"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.cjrone.entity.DataRehabilitationSubsidyCategoryEntity">
		SELECT * FROM data_rehabilitation_subsidy_category order by id desc
	</select>

    <select id="queryListByParentId" resultType="com.hmit.kernespring.modules.cjrone.entity.DataRehabilitationSubsidyCategoryEntity">
        SELECT category_id as value,name as label,condition_name
        FROM data_rehabilitation_subsidy_category
        where parent_id=#{parentId} order by category_id desc
    </select>

</mapper>