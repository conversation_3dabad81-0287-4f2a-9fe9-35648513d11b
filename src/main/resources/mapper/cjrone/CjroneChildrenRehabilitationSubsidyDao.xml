<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.cjrone.dao.CjroneChildrenRehabilitationSubsidyDao">

<!--	&lt;!&ndash; 可根据自己的需求，是否要使用 &ndash;&gt;-->
<!--    <resultMap type="com.hmit.kernespring.modules.matter_application.entity.CjroneChildrenRehabilitationSubsidyEntity" id="cjroneChildrenRehabilitationSubsidyMap">-->
<!--        <result property="id" column="id"/>-->
<!--        <result property="welfareMatterApplicationId" column="welfare_matter_application_id"/>-->
<!--        <result property="attach" column="attach"/>-->
<!--        <result property="yearTotalAmount" column="year_total_amount"/>-->
<!--        <result property="no" column="no"/>-->
<!--        <result property="date" column="date"/>-->
<!--        <result property="rehabilitationCenterName" column="rehabilitation_center_name"/>-->
<!--        <result property="designatedInstitution" column="designated_institution"/>-->
<!--        <result property="rehabilitationType" column="rehabilitation_type"/>-->
<!--        <result property="subsidyStandard" column="subsidy_standard"/>-->
<!--        <result property="subsidyYear" column="subsidy_year"/>-->
<!--        <result property="subsidyMonth" column="subsidy_month"/>-->
<!--        <result property="actualInvoiceAmount" column="actual_invoice_amount"/>-->
<!--        <result property="districtDisabilityAuditAmount" column="district_disability_audit_amount"/>-->
<!--        <result property="operatorId" column="operator_id"/>-->
<!--        <result property="operatorName" column="operator_name"/>-->
<!--        <result property="fundsSource" column="funds_source"/>-->
<!--        <result property="applyRemark" column="apply_remark"/>-->
<!--        <result property="approvalRemark" column="approval_remark"/>-->
<!--        <result property="createTime" column="create_time"/>-->
<!--        <result property="auditorId" column="auditor_id"/>-->
<!--        <result property="auditorName" column="auditor_name"/>-->
<!--    </resultMap>-->
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.cjrone.entity.CjroneChildrenRehabilitationSubsidyEntity">
		SELECT * FROM cjrone_children_rehabilitation_subsidy order by id desc
	</select>

    <select id="queryStatistics" resultType="java.util.Map">
        select  count(distinct id_card) as count,
                count(*) as 'personTime',
                COALESCE(sum(district_disability_audit_amount),0) as sum
                from cjrone_children_rehabilitation_subsidy
                <where>
                    status=8
                    <if test="approvalYear!=null">
                        AND TO_CHAR(approval_time, 'YYYY')=#{approvalYear}
                    </if>
                    
                </where>
    </select>
    
    <select id="calculateYearTotalAmount" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(district_disability_audit_amount), 0)
        FROM cjrone_children_rehabilitation_subsidy
        WHERE id_card = #{idCard}
          AND subsidy_year = #{subsidyYear}
          AND status = '8'
    </select>
</mapper>