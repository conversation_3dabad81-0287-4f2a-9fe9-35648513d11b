<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.cjrone.dao.CjroneLivingAllowanceDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.cjrone.entity.CjroneLivingAllowanceEntity" id="cjroneLivingAllowanceMap">
        <result property="name" column="name"/>
        <result property="sex" column="sex"/>
        <result property="birthday" column="birthday"/>
        <result property="nativePlace" column="native_place"/>
        <result property="idCard" column="id_card"/>
        <result property="mobilePhone" column="mobile_phone"/>
        <result property="guardianName" column="guardian_name"/>
        <result property="guardianPhone" column="guardian_phone"/>
        <result property="disabilityCategory" column="disability category"/>
        <result property="disabilityDegree" column="disability_degree"/>
        <result property="disableId" column="disable_id"/>
        <result property="bankAccount" column="bank_account"/>
        <result property="bankName" column="bank_name"/>
        <result property="familyEconoCondition" column="family_econo_condition"/>
        <result property="applicationDate" column="application_date"/>
        <result property="auditPerson" column="audit_person"/>
        <result property="auditDate" column="audit_date"/>
        <result property="status" column="status"/>
        <result property="createId" column="create_id"/>
        <result property="createTime" column="create_time"/>
        <result property="signStatus" column="sign_status"/>
        <result property="signatureStatus" column="signature_status"/>
        <result property="standardSubsidy" column="standard_subsidy"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.cjrone.entity.CjroneLivingAllowanceEntity">
		SELECT a.id,a.name,a.sex,a.birthday,CONCAT(b.native_zhen_name,b.native_cun_name) as native_place,a.id_card,a.mobile_phone,a.guardian_name,a.guardian_phone,a.disability_category,a.disability_degree,a.disable_id,a.bank_account,a.bank_name,a.family_econo_condition,a.application_date,a.audit_person,a.audit_date,a.status,a.create_id,a.create_time,a.sign_status,a.signature_status,a.standard_subsidy
		FROM cjrone_living_allowance a LEFT JOIN disability_certificate_application b on a.id_card = b.id_card and b.status='6'
		where a.is_qcl_show=1
		GROUP BY a.id
	</select>

</mapper>