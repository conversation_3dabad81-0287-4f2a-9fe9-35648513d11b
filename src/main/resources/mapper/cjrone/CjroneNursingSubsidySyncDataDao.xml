<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.cjrone.dao.CjroneNursingSubsidySyncDataDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.cjrone.entity.CjroneNursingSubsidySyncDataEntity" id="cjroneNursingSubsidySyncDataMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="idCard" column="id_card"/>
        <result property="createTime" column="create_time"/>
        <result property="familyEconoCondition" column="family_econo_condition"/>
        <result property="medicalInsurance" column="medical_insurance"/>
        <result property="isDead" column="is_dead"/>
        <result property="isTk" column="is_tk"/>
        <result property="isSixty" column="is_sixty"/>
        <result property="isWorkinjury" column="is_workinjury"/>
        <result property="isDischild" column="is_dischild"/>
        <result property="isResident" column="is_resident"/>
        <result property="isClork" column="is_clork"/>
        <result property="status" column="status"/>
        <result property="isApplyWelfareMatter" column="is_apply_welfare_matter"/>
        <result property="isFx" column="is_fx"/>
        <result property="isHkqy" column="is_hkqy"/>
        <result property="fxInfo" column="fx_info"/>
        <result property="hkqyInfo" column="hkqy_info"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.cjrone.entity.CjroneNursingSubsidySyncDataEntity">
		SELECT * FROM cjrone_nursing_subsidy_sync_data
		where status = #{status}
		order by id desc
	</select>

</mapper>