<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.cjrone.dao.CjroneRehabilitationSubsidyDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.cjrone.entity.CjroneRehabilitationSubsidyEntity" id="cjroneRehabilitationSubsidyMap">
        <result property="name" column="name"/>
        <result property="sex" column="sex"/>
        <result property="nationality" column="nationality"/>
        <result property="birthday" column="birthday"/>
        <result property="idCard" column="id_card"/>
        <result property="disableId" column="disable_id"/>
        <result property="disabilityCategory" column="disability category"/>
        <result property="disabilityDegree" column="disability_degree"/>
        <result property="presentZhen" column="present_zhen"/>
        <result property="presentCun" column="present_cun"/>
        <result property="presentAddress" column="present_address"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="mobilePhone" column="mobile_phone"/>
        <result property="familyEconoCondition" column="family_econo_condition"/>
        <result property="medicalInsurance" column="medical_insurance"/>
        <result property="rehabilitationProject" column="rehabilitation_project"/>
        <result property="rehabilitationProjectName" column="rehabilitation_project_name"/>
        <result property="applicationDate" column="application_date"/>
        <result property="auditPerson" column="audit_person"/>
        <result property="auditDate" column="audit_date"/>
        <result property="status" column="status"/>
        <result property="createId" column="create_id"/>
        <result property="createTime" column="create_time"/>
        <result property="signStatus" column="sign_status"/>
        <result property="signatureStatus" column="signature_status"/>
        <result property="guardianName" column="guardian_name"/>
        <result property="guardianPhone" column="guardian_phone"/>
        <result property="guardianIdcard" column="guardian_idcard"/>
        <result property="guardianRelation" column="guardian_relation"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.cjrone.entity.CjroneRehabilitationSubsidyEntity">
		SELECT * FROM cjrone_rehabilitation_subsidy order by id desc
	</select>
    <select id="queryCategoryList" resultType="String">
        SELECT category_id from data_rehabilitation_subsidy_category where allow_use = #{roleName}
    </select>

</mapper>