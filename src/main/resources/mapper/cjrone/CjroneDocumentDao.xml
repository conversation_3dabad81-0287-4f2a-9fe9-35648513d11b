<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.cjrone.dao.CjroneDocumentDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.cjrone.entity.CjroneDocumentEntity" id="cjroneDocumentMap">
        <result property="id" column="id"/>
        <result property="disabilityCertificateApplicationId" column="disability_certificate_application_id"/>
        <result property="fileName" column="file_name"/>
        <result property="fileSize" column="file_size"/>
        <result property="createId" column="create_id"/>
        <result property="createTime" column="create_time"/>
        <result property="filePath" column="file_path"/>
        <result property="filePathAct" column="file_path_act"/>
        <result property="fileType" column="file_type"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="disabilityAssessmentId" column="disability_assessment_id"/>
        <result property="disabilityAssessmentDetailName" column="disability_assessment_detail_name"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.cjrone.entity.CjroneDocumentEntity">
		SELECT * FROM cjrone_document order by id desc
	</select>
    <select id="queryDocumentDataByMap" resultType="com.hmit.kernespring.modules.cjrone.entity.CjroneDocumentEntity">
        SELECT b.* from cjrone_certificate_apply_doc a,cjrone_document b where a.document_id = b.id and a.disability_certificate_application_id = #{disability_certificate_application_id}
	</select>
    <select id="queryDocumentDataByMapN" resultType="com.hmit.kernespring.modules.cjrone.entity.CjroneDocumentEntity">

    SELECT b.* from cjrone_certificate_apply_doc a,cjrone_document b where a.document_id = b.id
    and a.disability_certificate_application_id = (
	SELECT id from disability_certificate_application where disability_type_name = #{disabilityCategoryName}
	and id_card = #{idCard} and status='6' limit 1 offset 0)
	</select>
    <select id="queryDocumentDocDataByMap" resultType="com.hmit.kernespring.modules.cjrone.entity.CjroneCertificateApplyDocEntity">
        SELECT a.* from cjrone_certificate_apply_doc a where a.disability_certificate_application_id = #{disability_certificate_application_id}
	</select>

</mapper>