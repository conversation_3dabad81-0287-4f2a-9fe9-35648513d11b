<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.cjrone.dao.TempDiffDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.cjrone.entity.TempDiffEntity" id="tempDiffMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="idCard" column="id_card"/>
        <result property="disableId" column="disable_id"/>
        <result property="error" column="error"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.cjrone.entity.TempDiffEntity">
		SELECT * FROM temp_diff order by id desc
	</select>

</mapper>