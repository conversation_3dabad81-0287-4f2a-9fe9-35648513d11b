<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.cjrone.dao.CjroneEmploymentSubsidyDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.cjrone.entity.CjroneEmploymentSubsidyEntity" id="cjroneEmploymentSubsidyMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="idCard" column="id_card"/>
        <result property="familyEconoCondition" column="family_econo_condition"/>
        <result property="sex" column="sex"/>
        <result property="educationDegree" column="education_degree"/>
        <result property="disabilityType" column="disability_type"/>
        <result property="telephone" column="telephone"/>
        <result property="presentAddress" column="present_address"/>
        <result property="managementType" column="management_type"/>
        <result property="managementAddress" column="management_address"/>
        <result property="subsidyMoney" column="subsidy_money"/>
        <result property="applyReason" column="apply_reason"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.cjrone.entity.CjroneEmploymentSubsidyEntity">
		SELECT * FROM cjrone_employment_subsidy order by id desc
	</select>

</mapper>