<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.cjrone.dao.CjroneVillageDao">

    <select id="getAdministrativeDivisionList" resultType="com.hmit.kernespring.modules.cjrone.entity.CjroneAdministrativeDivisionEntity">
        select code as value,name as label
        from cjrone_village
        where streetcode=#{code}
    </select>

    <select id="getAdministrativeDivisionListForAPP" resultType="map">
        select code as id,name as value
        from cjrone_village
        where streetcode=#{code}
    </select>

    <select id="getVillageById" resultType="com.hmit.kernespring.modules.cjrone.entity.CjroneAdministrativeDivisionEntity">
        select code as value,name as label
        from cjrone_village
        where code=#{code}
    </select>


</mapper>