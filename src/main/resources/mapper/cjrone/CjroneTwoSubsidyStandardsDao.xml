<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.cjrone.dao.CjroneTwoSubsidyStandardsDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.cjrone.entity.CjroneTwoSubsidyStandardsEntity" id="cjroneTwoSubsidyStandardsMap">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="disabilityType" column="disability_type"/>
        <result property="disabilityDegree" column="disability_degree"/>
        <result property="isConcentratedCare" column="is_concentrated_care"/>
        <result property="money" column="money"/>
    </resultMap>

    <select id="queryExportData" resultType="com.hmit.kernespring.modules.cjrone.entity.CjroneTwoSubsidyStandardsEntity">
		SELECT * FROM cjrone_two_subsidy_standards order by id desc
	</select>

    <select id="queryByMap" resultType="com.hmit.kernespring.modules.cjrone.entity.CjroneTwoSubsidyStandardsEntity">
        select * from cjrone_two_subsidy_standards
        where type=#{type}
        <if test="disabilityType != null and '' != disabilityType">
            and disability_type=#{disabilityType}
        </if>
        <if test="disabilityDegree != null and '' != disabilityDegree">
            and disability_degree=#{disabilityDegree}
        </if>
        <if test="isConcentratedCare != null and '' != isConcentratedCare">
            and is_concentrated_care=#{isConcentratedCare}
        </if>

    </select>



</mapper>