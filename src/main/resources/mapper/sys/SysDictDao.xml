<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.hmit.kernespring.modules.sys.dao.SysDictDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.hmit.kernespring.modules.sys.entity.SysDictEntity" id="sysDictMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="code" column="code"/>
        <result property="value" column="value"/>
        <result property="orderNum" column="order_num"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
        <result property="label" column="label"/>
    </resultMap>
    <select id="queryExportData" resultType="com.hmit.kernespring.modules.sys.entity.SysDictEntity">
		SELECT * FROM sys_dict order by id desc
	</select>
    <select id="queryDataByMap" resultType="com.hmit.kernespring.modules.sys.entity.SysDictEntity">
		SELECT * FROM sys_dict where 1=1
        <if test="code != null and code.trim() != ''">
            and code = #{code} and value != 0
        </if>
		order by id desc
	</select>
    <select id="querySysDictByMap" resultType="string">
		SELECT label FROM sys_dict where 1=1
        <if test="code != null and code.trim() != ''">
            and code = #{code} and value != 0 and value= #{value}
        </if>
		order by id desc
	</select>

</mapper>