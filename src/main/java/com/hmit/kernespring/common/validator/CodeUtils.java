/**
* Project: yui-common-utils
 * Class CodeUtils
 * Version 1.0
 * File Created at 2018年4月15日
 * $Id$
 *
 * Copyright 2010-2015 Yui.com Corporation Limited.
 * All rights reserved.
 *
 * This software is the confidential and proprietary information of
 * Yui Personal. ("Confidential Information").  You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with Yui.com.
 */
package com.hmit.kernespring.common.validator;

import java.util.Random;

/**
 * 编码生成器
 * <AUTHOR> (<EMAIL>)
 */
public class CodeUtils {

    @SuppressWarnings("unused")
    private static String letter = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"; //去除1l和0o，防止0和o,1和l混合不清
    @SuppressWarnings("unused")
    private static String digital = "**********"; //去除1l和0o，防止0和o,1和l混合不清
    private static String letterDigital = "ABCDEFGHIJKMNPQRSTUVWXYZ**********";
    public static char[] numSequence = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9' };


    public static int getRandom(int count) {
        return (int) Math.round(Math.random() * count);
    }


    public static int getRandomThreeeDigit() {
        int i=(int)(Math.random()*900)+100;
        return i;
    }

    public static String getNumCode(int len) {
        StringBuffer randomCode = new StringBuffer();
        Random random = new Random();
        for (int i = 0; i < len; i++) {
            randomCode.append(String.valueOf(numSequence[random.nextInt(10)]));
        }
        return randomCode.toString();
    }

}
