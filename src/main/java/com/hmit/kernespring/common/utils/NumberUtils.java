/**
* Project: yui3-common-utils
 * Class NumberUtils
 * Version 1.0
 * File Created at 2017年11月22日
 * $Id$
 *
 * Copyright 2010-2015 Yui.com Corporation Limited.
 * All rights reserved.
 *
 * This software is the confidential and proprietary information of
 * Yui Personal. ("Confidential Information").  You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with Yui.com.
 */
package com.hmit.kernespring.common.utils;

import java.math.BigDecimal;

/**
 * 数字计算
 * <AUTHOR> (<EMAIL>)
 */
public class NumberUtils {

    public static int initInt(Object obj) {
        if (null == obj) {
            return 0;
        }
        try {
            return Integer.parseInt(obj.toString());
        } catch (Exception e) {
            return 0;
        }
    }

    public static boolean isLong(String str) {
        try {
            Long.parseLong(str);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static boolean equal(Long n1, Long n2) {
        if (null == n1 || null == n2) {
            return false;
        }
        if (n1.longValue() == n2.longValue()) {
            return true;
        }
        return false;
    }

    public static boolean equal(Integer n1, Integer n2) {
        if (null == n1 || null == n2) {
            return false;
        }
        if (n1.intValue() == n2.intValue()) {
            return true;
        }
        return false;
    }

    public static BigDecimal add(BigDecimal d1, BigDecimal d2) {
        return d1.add(d2);
    }

    public static BigDecimal subtract(BigDecimal d1, BigDecimal d2) {
        return d1.subtract(d2);
    }

    public static BigDecimal multiply(BigDecimal d1, int modulus, int scale) {
        return d1.multiply(new BigDecimal(modulus)).setScale(scale, BigDecimal.ROUND_HALF_UP);
    }

    public static BigDecimal multiply(BigDecimal d1, BigDecimal modulus, int scale) {
        return d1.multiply(modulus).setScale(scale, BigDecimal.ROUND_HALF_UP);
    }

    public static BigDecimal divide(BigDecimal numerator, int denominator, int scale) {
        return numerator.divide(new BigDecimal(denominator), BigDecimal.ROUND_HALF_UP);//.setScale(scale, BigDecimal.ROUND_HALF_UP);
    }

    public static BigDecimal divide(BigDecimal numerator, BigDecimal denominator, int scale) {
        return numerator.divide(denominator, BigDecimal.ROUND_HALF_UP);//.setScale(scale, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 提供精确的乘法运算。
     *
     * @param value1 被乘数
     * @param value2 乘数
     * @return 两个参数的积
     */
     public static Double mul(Double value1, Double value2) {
         if (null == value1) {
             return 0D;
         }
         if (null == value2) {
             return 0D;
         }
         BigDecimal b1 = new BigDecimal(Double.toString(value1));
         BigDecimal b2 = new BigDecimal(Double.toString(value2));
         return b1.multiply(b2).doubleValue();
     }

     public static Double mul(Double value1, Integer value2) {
         if (null == value1) {
             return 0D;
         }
         if (null == value2) {
             return 0D;
         }
         BigDecimal b1 = new BigDecimal(Double.toString(value1));
         BigDecimal b2 = new BigDecimal(Integer.toString(value2));
         return b1.multiply(b2).doubleValue();
     }

     /**
      * 提供（相对）精确的除法运算，当发生除不尽的情况时， 精确到小数点以后10位，以后的数字四舍五入。
      *
      * @param dividend 被除数
      * @param divisor  除数
      * @return 两个参数的商
      */
     public static Double divide(Double dividend, Double divisor) {
         if (null == dividend) {
             return 0D;
         }
         return divide(dividend, divisor, BigDecimal.ROUND_HALF_UP);
     }

     /**
       * 提供（相对）精确的除法运算。 当发生除不尽的情况时，由scale参数指定精度，以后的数字四舍五入。
       *
       * @param dividend 被除数
       * @param divisor  除数
       * @param scale    表示表示需要精确到小数点以后几位。
       * @return 两个参数的商
       */
      public static Double divide(Double dividend, Double divisor, Integer scale) {
          if (scale < 0) {
              throw new IllegalArgumentException("The scale must be a positive integer or zero");
          }
          BigDecimal b1 = new BigDecimal(Double.toString(dividend));
          BigDecimal b2 = new BigDecimal(Double.toString(divisor));
          return b1.divide(b2, scale,BigDecimal.ROUND_HALF_UP).doubleValue();
      }

      /**
        * 提供精确的加法运算。
        *
        * @param value1 被加数
        * @param value2 加数
        * @return 两个参数的和
        */
       public static Double add(Double value1, Double value2) {
           if (null == value1) {
               value1 = 0D;
           }
           if (null == value2) {
               value2 = 0D;
           }
           BigDecimal b1 = new BigDecimal(Double.toString(value1));
           BigDecimal b2 = new BigDecimal(Double.toString(value2));
           return b1.add(b2).doubleValue();
       }

       /**
        * 提供精确的减法运算。
        *
        * @param value1 被减数
        * @param value2 减数
        * @return 两个参数的差
        */
       public static double sub(Double value1, Double value2) {
           if (null == value1) {
               value1 = 0D;
           }
           if (null == value2) {
               value2 = 0D;
           }
           BigDecimal b1 = new BigDecimal(Double.toString(value1));
           BigDecimal b2 = new BigDecimal(Double.toString(value2));
           return b1.subtract(b2).doubleValue();
       }

}
