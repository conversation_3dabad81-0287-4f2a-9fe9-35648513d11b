/**
 * Project: yui-common-utils
 * Class RedisConstant
 * Version 1.0
 * File Created at 2020年7月27日
 * $Id$
 * author yuyi
 * email <EMAIL>
 */
package com.hmit.kernespring.common.utils;

/**
 * <p>
 * Redis key 前缀
 * </p>
 *
 * <AUTHOR> (<EMAIL>)
 */
public class RedisConstant {

    public static final String PREFIX = "cjrone";

    public static final String LOGIN_CAPTCHA = PREFIX + ":login-captcha:"; // 登录IP限定

    public static final String LOGIN_IP = PREFIX + ":login-ip:"; // 登录IP限定

    public static final String LOGIN_USERNAME = PREFIX + ":login-username:"; // 登录用户名限定

    public static final String LOGIN_CRYPTO_KEY = PREFIX + ":login-crypto-key:"; // 登录用户名限定

    public static final String ENUM_HSET = PREFIX + ":enum"; // 枚举类

}
