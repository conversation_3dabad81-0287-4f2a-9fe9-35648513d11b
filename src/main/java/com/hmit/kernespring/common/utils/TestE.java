package com.hmit.kernespring.common.utils;

import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-17 21:56
 */
public class TestE {

    /**
     * hsicrmProductcategorycode : GT
     * hsicrmFactoryid : 9261
     * hsicrmProductcategoryname : 滚筒
     * hsicrmIndustrycode : XYJ
     * hsicrmSalesdate : 2016-12-10
     * hsicrmWarrantyexpirationdate : 2019-12-11
     * hsicrmProductfactory : 顺德滚筒
     * hsicrmCfgIndustryid : 12A9C5DB-DD97-4394-A2DE-CB938461F0DB
     * hsicrmProductmodelcode : CEAAJ601700
     * hsicrmManufacturedate : 2016-11-17
     * hsicrmIndustryname : 洗涤
     * hsicrmProductprice : 0
     * hsicrmProductmodelname : XQG100-HBDX14756GU1
     * hsicrmCfgProductcategoryid : BEF14B94-CF85-4BEB-BD64-9693591DBE9E
     */
    @Excel(name = "", height = 20, width = 30, isImportField = "true_st")
    private String hsicrmProductcategorycode;
    private String hsicrmFactoryid;
    @Excel(name = "产品类别", height = 20, width = 30, isImportField = "true_st")
    private String hsicrmProductcategoryname;
    private String hsicrmIndustrycode;
    @Excel(name = "购买日期", height = 20, width = 30, isImportField = "true_st")
    private String hsicrmSalesdate;
    @Excel(name = "保修到期日", height = 20, width = 30, isImportField = "true_st")
    private String hsicrmWarrantyexpirationdate;
    private String hsicrmProductfactory;
    private String hsicrmCfgIndustryid;
    private String hsicrmProductmodelcode;
    @Excel(name = "生产日期", height = 20, width = 30, isImportField = "true_st")
    private String hsicrmManufacturedate;
    private String hsicrmIndustryname;
    private String hsicrmProductprice;
    private String hsicrmProductmodelname;
    private String hsicrmCfgProductcategoryid;
    @Excel(name = "机器编码是否可使用", height = 20, width = 30, isImportField = "true_st")
    private String isAllowUse;
    @Excel(name = "分析比对结果", height = 20, width = 30, isImportField = "true_st")
    private String result;

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getIsAllowUse() {
        return isAllowUse;
    }

    public void setIsAllowUse(String isAllowUse) {
        this.isAllowUse = isAllowUse;
    }

    public String getHsicrmProductcategorycode() {
        return hsicrmProductcategorycode;
    }

    public void setHsicrmProductcategorycode(String hsicrmProductcategorycode) {
        this.hsicrmProductcategorycode = hsicrmProductcategorycode;
    }

    public String getHsicrmFactoryid() {
        return hsicrmFactoryid;
    }

    public void setHsicrmFactoryid(String hsicrmFactoryid) {
        this.hsicrmFactoryid = hsicrmFactoryid;
    }

    public String getHsicrmProductcategoryname() {
        return hsicrmProductcategoryname;
    }

    public void setHsicrmProductcategoryname(String hsicrmProductcategoryname) {
        this.hsicrmProductcategoryname = hsicrmProductcategoryname;
    }

    public String getHsicrmIndustrycode() {
        return hsicrmIndustrycode;
    }

    public void setHsicrmIndustrycode(String hsicrmIndustrycode) {
        this.hsicrmIndustrycode = hsicrmIndustrycode;
    }

    public String getHsicrmSalesdate() {
        return hsicrmSalesdate;
    }

    public void setHsicrmSalesdate(String hsicrmSalesdate) {
        this.hsicrmSalesdate = hsicrmSalesdate;
    }

    public String getHsicrmWarrantyexpirationdate() {
        return hsicrmWarrantyexpirationdate;
    }

    public void setHsicrmWarrantyexpirationdate(String hsicrmWarrantyexpirationdate) {
        this.hsicrmWarrantyexpirationdate = hsicrmWarrantyexpirationdate;
    }

    public String getHsicrmProductfactory() {
        return hsicrmProductfactory;
    }

    public void setHsicrmProductfactory(String hsicrmProductfactory) {
        this.hsicrmProductfactory = hsicrmProductfactory;
    }

    public String getHsicrmCfgIndustryid() {
        return hsicrmCfgIndustryid;
    }

    public void setHsicrmCfgIndustryid(String hsicrmCfgIndustryid) {
        this.hsicrmCfgIndustryid = hsicrmCfgIndustryid;
    }

    public String getHsicrmProductmodelcode() {
        return hsicrmProductmodelcode;
    }

    public void setHsicrmProductmodelcode(String hsicrmProductmodelcode) {
        this.hsicrmProductmodelcode = hsicrmProductmodelcode;
    }

    public String getHsicrmManufacturedate() {
        return hsicrmManufacturedate;
    }

    public void setHsicrmManufacturedate(String hsicrmManufacturedate) {
        this.hsicrmManufacturedate = hsicrmManufacturedate;
    }

    public String getHsicrmIndustryname() {
        return hsicrmIndustryname;
    }

    public void setHsicrmIndustryname(String hsicrmIndustryname) {
        this.hsicrmIndustryname = hsicrmIndustryname;
    }

    public String getHsicrmProductprice() {
        return hsicrmProductprice;
    }

    public void setHsicrmProductprice(String hsicrmProductprice) {
        this.hsicrmProductprice = hsicrmProductprice;
    }

    public String getHsicrmProductmodelname() {
        return hsicrmProductmodelname;
    }

    public void setHsicrmProductmodelname(String hsicrmProductmodelname) {
        this.hsicrmProductmodelname = hsicrmProductmodelname;
    }

    public String getHsicrmCfgProductcategoryid() {
        return hsicrmCfgProductcategoryid;
    }

    public void setHsicrmCfgProductcategoryid(String hsicrmCfgProductcategoryid) {
        this.hsicrmCfgProductcategoryid = hsicrmCfgProductcategoryid;
    }
}
