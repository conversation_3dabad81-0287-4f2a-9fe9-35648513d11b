package com.hmit.kernespring.common.utils;


import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.*;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/** 
 * Http请求工具类 
 * <AUTHOR> 
 * @since 2017-06-23 13:30:56 
 */ 
public class HttpRequestUtil {
	
	static boolean proxySet = false;
	static String proxyHost = "127.0.0.1";
	static int proxyPort = 8087;
	/** 
     * 编码 
     * @param source 
     * @return 
     */  
    public static String urlEncode(String source, String encode) {
        String result = source;
        try {  
            result = java.net.URLEncoder.encode(source,encode);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            return "0";
        }
        return result;
    }
    public static String urlEncodeGBK(String source) {
        String result = source;
        try {
            result = java.net.URLEncoder.encode(source,"GBK");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            return "0";
        }
        return result;
    }
    /**
     * 发起http请求获取返回结果
     * @param req_url 请求地址
     * @return
     */
    public static String httpRequest(String req_url) {
        StringBuffer buffer = new StringBuffer();
        try {
            URL url = new URL(req_url);
            HttpURLConnection httpUrlConn = (HttpURLConnection) url.openConnection();

            httpUrlConn.setDoOutput(false);
            httpUrlConn.setDoInput(true);
            httpUrlConn.setUseCaches(false);

            httpUrlConn.setRequestMethod("GET");
            httpUrlConn.connect();

            // 将返回的输入流转换成字符串
            InputStream inputStream = httpUrlConn.getInputStream();
            InputStreamReader inputStreamReader = new InputStreamReader(inputStream, "utf-8");
            BufferedReader bufferedReader = new BufferedReader(inputStreamReader);

            String str = null;
            while ((str = bufferedReader.readLine()) != null) {
                buffer.append(str);
            }
            bufferedReader.close();
            inputStreamReader.close();
            // 释放资源
            inputStream.close();
            inputStream = null;
            httpUrlConn.disconnect();

        } catch (Exception e) {
            System.out.println(e.getStackTrace());
        }
        return buffer.toString();
    }

    /**
     * 发送http请求取得返回的输入流
     * @param requestUrl 请求地址
     * @return InputStream
     */
    public static InputStream httpRequestIO(String requestUrl) {
        InputStream inputStream = null;
        try {
            URL url = new URL(requestUrl);
            HttpURLConnection httpUrlConn = (HttpURLConnection) url.openConnection();
            httpUrlConn.setDoInput(true);
            httpUrlConn.setRequestMethod("GET");
            httpUrlConn.connect();
            // 获得返回的输入流
            inputStream = httpUrlConn.getInputStream();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return inputStream;
    }


    /**
     * 向指定URL发送GET方法的请求
     *
     * @param url
     *            发送请求的URL
     * @param param
     *            请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
     * @return URL 所代表远程资源的响应结果
     */
    public static String sendGet(String url, String param) {
        String result = "";
        BufferedReader in = null;
        try {
            String urlNameString = null;

            if (url.indexOf("ticketValidation") != -1 || url.indexOf("getUserInfo") != -1 ){
                urlNameString = url  + param;
            }else {
                urlNameString = url + "?" + param;
            }
            System.out.println(urlNameString);
            URL realUrl = new URL(urlNameString);
            // 打开和URL之间的连接
            URLConnection connection = realUrl.openConnection();
            // 设置通用的请求属性
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("user-agent",
                    "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            // 建立实际的连接
            connection.connect();
            // 获取所有响应头字段
            Map<String, List<String>> map = connection.getHeaderFields();
            // 遍历所有的响应头字段
            for (String key : map.keySet()) {
                System.out.println(key + "--->" + map.get(key));
            }
            // 定义 BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(
                    connection.getInputStream(),"UTF-8"));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
        } catch (Exception e) {
            System.out.println("发送GET请求出现异常！" + e);
            e.printStackTrace();
        }
        // 使用finally块来关闭输入流
        finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (Exception e2) {
                e2.printStackTrace();
            }
        }
        return result;
    }

    /**
     * 向指定 URL 发送POST方法的请求
     *
     * @param url
     *            发送请求的 URL
     * @param param
     *            请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
     * @param isproxy
     * 			     是否使用代理模式
     * @return 所代表远程资源的响应结果
     */
    public static Map<String,Object> sendPost(String url, String param, boolean isproxy, boolean isconfirm) {
        DataOutputStream out = null;
        BufferedReader in = null;
        String result = "";
        Map<String,Object> result_map = new HashMap<>();
        try {
            URL realUrl = new URL(url);
            if (isconfirm){
                String token = CjroneAppUtil.getAccessToken().get("resultmsg").toString();
                param = param+"&token="+token+ "&requestKey="+CjroneApp.APP_KEY;
                //param = param+"&token="+token.substring(1,token.length()-1)+ "&requestKey="+CjroneApp.APP_KEY;
            }
            HttpURLConnection conn = null;
            if(isproxy){//使用代理模式
            	@SuppressWarnings("static-access")
                Proxy proxy = new Proxy(Proxy.Type.DIRECT.HTTP, new InetSocketAddress(proxyHost, proxyPort));
            	conn = (HttpURLConnection) realUrl.openConnection(proxy);
            }else{
            	conn = (HttpURLConnection) realUrl.openConnection();
            }
            // 打开和URL之间的连接

            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setRequestMethod("POST");    // POST方法

            // 设置通用的请求属性

            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent",
                    "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8");
            conn.setRequestProperty("Accept-Charset", "UTF-8");
            // conn.setRequestProperty("Content-Type","application/json");// 设置发送数据的格式
            //conn.setRequestProperty("token","02613bdc56bf6e700b2b6bcd735e512d");
            //conn.setRequestProperty("Transfer-Encoding", "chunked");//设置传输编码
            conn.setConnectTimeout(15000);
            conn.connect();
            System.out.println(url);
            System.out.println(param);
            out = new DataOutputStream(conn.getOutputStream());

            // 获取URLConnection对象对应的输出流
            //out = new OutputStreamWriter(conn.getOutputStream(), "UTF-8");
            // 发送请求参数
            out.write(param.getBytes("UTF-8"));
            // flush输出流的缓冲
            out.flush();
            // 定义BufferedReader输入流来读取URL的响应
            in = new BufferedReader(
                    new InputStreamReader(conn.getInputStream(),"UTF-8"));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
        } catch (Exception e) {
            System.out.println("发送 POST 请求出现异常！"+e);
            e.printStackTrace();
            result_map.put("code","010");
            result_map.put("msg","网络链接超时,请稍后重试");
            return result_map;
        }
        //使用finally块来关闭输出流、输入流
        finally{
            try{
                if(out!=null){
                    out.close();
                }
                if(in!=null){
                    in.close();
                }
            }
            catch(IOException ex){
                ex.printStackTrace();
            }
        }
        System.out.println("xxxxxxx+"+result);
        result_map = new Gson().fromJson(result,new TypeToken<Map<String, Object>>() {
        }.getType());
        return result_map;
    }

    public static Map<String,Object> sendPostNew(String url, String param, boolean isproxy, boolean isconfirm) {
        DataOutputStream out = null;
        BufferedReader in = null;
        String result = "";
        Map<String,Object> result_map = new HashMap<>();
        try {
            URL realUrl = new URL(url);
            if (isconfirm){
                String token = CjroneAppUtil.getAccessToken().get("resultmsg").toString();
                param = param+"&token="+token+ "&requestKey="+CjroneApp.APP_KEY;
                //param = param+"&token="+token.substring(1,token.length()-1)+ "&requestKey="+CjroneApp.APP_KEY;
            }
            HttpURLConnection conn = null;
            if(isproxy){//使用代理模式
            	@SuppressWarnings("static-access")
                Proxy proxy = new Proxy(Proxy.Type.DIRECT.HTTP, new InetSocketAddress(proxyHost, proxyPort));
            	conn = (HttpURLConnection) realUrl.openConnection(proxy);
            }else{
            	conn = (HttpURLConnection) realUrl.openConnection();
            }
            // 打开和URL之间的连接

            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setRequestMethod("POST");    // POST方法

            // 设置通用的请求属性

            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent",
                    "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8");
            conn.setRequestProperty("Accept-Charset", "UTF-8");
            conn.setRequestProperty("Content-Type","application/json");// 设置发送数据的格式
            conn.setRequestProperty("token","********************************");
            //conn.setRequestProperty("Transfer-Encoding", "chunked");//设置传输编码
            conn.setConnectTimeout(15000);
            conn.connect();
            System.out.println(url);
            System.out.println(param);
            out = new DataOutputStream(conn.getOutputStream());

            // 获取URLConnection对象对应的输出流
            //out = new OutputStreamWriter(conn.getOutputStream(), "UTF-8");
            // 发送请求参数
            out.write(param.getBytes("UTF-8"));
            // flush输出流的缓冲
            out.flush();
            // 定义BufferedReader输入流来读取URL的响应
            in = new BufferedReader(
                    new InputStreamReader(conn.getInputStream(),"UTF-8"));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
        } catch (Exception e) {
            System.out.println("发送 POST 请求出现异常！"+e);
            e.printStackTrace();
            result_map.put("code","010");
            result_map.put("msg","网络链接超时,请稍后重试");
            return result_map;
        }
        //使用finally块来关闭输出流、输入流
        finally{
            try{
                if(out!=null){
                    out.close();
                }
                if(in!=null){
                    in.close();
                }
            }
            catch(IOException ex){
                ex.printStackTrace();
            }
        }
        result_map = new Gson().fromJson(result,new TypeToken<Map<String, Object>>() {
        }.getType());
        return result_map;
    }

    public static String sendHeaderPost(String url, String param, boolean isproxy, boolean isconfirm) {
    	OutputStreamWriter out = null;
        BufferedReader in = null;
        String result = "";
        try {
            trustAllHosts();//HTTPS请求需建立信任链接
            URL realUrl = new URL(url);
            System.out.println(url+"?"+param);
            if (isconfirm){
                param = param+"&token="+CjroneAppUtil.getAccessToken()+ "&requestKey="+CjroneApp.APP_KEY;
            }
            HttpURLConnection conn = null;
            if(isproxy){//使用代理模式
            	@SuppressWarnings("static-access")
                Proxy proxy = new Proxy(Proxy.Type.DIRECT.HTTP, new InetSocketAddress(proxyHost, proxyPort));
            	conn = (HttpURLConnection) realUrl.openConnection(proxy);
            }else{
            	conn = (HttpURLConnection) realUrl.openConnection();
            }
            // 打开和URL之间的连接

            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setRequestMethod("POST");    // POST方法


            // 设置通用的请求属性

            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent",
                    "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            conn.setRequestProperty("x-esso-project-id", "1111564169");
            conn.setRequestProperty("x-esso-signature", "55ea54dac3f497c43344a9904f4aa1ae");
            conn.setRequestProperty("Content-Type", "application/json");
            conn.setRequestProperty("Charset", "UTF-8");

            conn.connect();

            // 获取URLConnection对象对应的输出流
            out = new OutputStreamWriter(conn.getOutputStream(), "UTF-8");
            // 发送请求参数
            out.write(param);
            // flush输出流的缓冲
            out.flush();
            // 定义BufferedReader输入流来读取URL的响应
            in = new BufferedReader(
                    new InputStreamReader(conn.getInputStream(),"UTF-8"));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
        } catch (Exception e) {
            System.out.println("发送 POST 请求出现异常！"+e);
            e.printStackTrace();
        }
        //使用finally块来关闭输出流、输入流
        finally{
            try{
                if(out!=null){
                    out.close();
                }
                if(in!=null){
                    in.close();
                }
            }
            catch(IOException ex){
                ex.printStackTrace();
            }
        }
        return result;
    }
    private static void trustAllHosts() {
        // Create a trust manager that does not validate certificate chains
        TrustManager[] trustAllCerts = new TrustManager[]{new X509TrustManager() {
            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return new X509Certificate[]{};
            }

            @Override
            public void checkClientTrusted(X509Certificate[] chain, String authType) {
            }

            @Override
            public void checkServerTrusted(X509Certificate[] chain, String authType) {
            }
        }};
        // Install the all-trusting trust manager
        try {
            SSLContext sc = SSLContext.getInstance("TLS");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    //链接url下载图片
    public static String downloadPicture(String urlList,String savePath,String fileName) {
        URL url = null;
        int imageNumber = 0;
        String imageName = null;

        try {
            url = new URL(urlList);
            DataInputStream dataInputStream = new DataInputStream(url.openStream());

            imageName =  savePath+fileName;

            File file = new File(imageName);
            if (!file.getParentFile().exists()){
                file.getParentFile().mkdirs();
            }

            FileOutputStream fileOutputStream = new FileOutputStream(file);
            ByteArrayOutputStream output = new ByteArrayOutputStream();

            byte[] buffer = new byte[1024];
            int length;

            while ((length = dataInputStream.read(buffer)) > 0) {
                output.write(buffer, 0, length);
            }
            byte[] context=output.toByteArray();
            fileOutputStream.write(output.toByteArray());
            dataInputStream.close();
            fileOutputStream.close();
        } catch (MalformedURLException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            return imageName;
        }
    }
    /**
     * 以流的形式下载文件
     *
     * @param file
     * @param response
     * @return
     */
    public static HttpServletResponse downloadZip(File file, HttpServletResponse response) {
        try {
            // 以流的形式下载文件。
            InputStream fis = new BufferedInputStream(new FileInputStream(file.getPath()));
            byte[] buffer = new byte[fis.available()];
            fis.read(buffer);
            fis.close();
            // 清空response
            response.reset();
            OutputStream toClient = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/octet-stream");
            //如果输出的是中文名的文件，在此处就要用URLEncoder.encode方法进行处理
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(file.getName(), "UTF-8"));
            toClient.write(buffer);
            toClient.flush();
            toClient.close();
        } catch (IOException ex) {
            ex.printStackTrace();
        } finally {
            try {
                File f = new File(file.getPath());
//                f.delete();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return response;
    }
    public static void main(String[] args) throws UnsupportedEncodingException, NoSuchAlgorithmException {


        System.out.println("sendGet");
        List<String> list = new ArrayList<>();
        list.add("CEAAJ601700C4GBH0512");
        list.add("CEAAJ601700C4GBH1618");
        list.add("CEAAJ601700C4GBH2240");
        list.add("CEAAJ601700C4GBH1120");
        list.add("CEAAJ601700C4GBH3418");
        list.add("CEAAJ601700C4GBH3289");
        list.add("CEAAJ601700C4GBH1209");
        list.add("CEAAJ601700C4GBH3123");
        list.add("CEAAJ601700C4GBH4182");
        list.add("CEAAJ601700C4GBH1232");
        String swqq = "{hsicrmProductcategorycode=GT,hsicrmFactoryid=1K,hsicrmProductcategoryname=滚筒,hsicrmIndustrycode=XYJ,hsicrmProductfactory=海梅事业部,hsicrmProductseriescode=null,hsicrmProductseriesname=null,hsicrmCfgIndustryid=12A9C5DB-DD97-4394-A2DE-CB938461F0DB,hsicrmProductmodelcode=CEAAJ601700,hsicrmManufacturedate=null,hsicrmIndustryname=洗涤,hsicrmProductmodelname=XQG100-HBDX14756GU1,hsicrmCfgProductcategoryid=BEF14B94-CF85-4BEB-BD64-9693591DBE9E}";
        TestE testEa = new Gson() .fromJson(swqq,TestE.class);
        list.forEach(serialNumber ->{
            String abc = sendGet("http://hcc.haier.net/ordinaryPath/ordinaryOrder/getProductInfoBySerial?serialNumber=" +serialNumber +"&productModel=CEAAJ601700&registrationTime=2019-05-17+17:28:00",null);
            Map<String, Object> params = new Gson().fromJson(abc,Map.class);
            System.out.println(params);
            if ("true".equals(params.get("success").toString())){
                String arg = params.get("data").toString().replaceAll("=,","=null,");
                arg = arg.replaceAll(" ","").replaceAll("=}","=null}").replaceAll("00:00:00.0","").replaceAll("00:00:00","");
                System.out.println(" aaa "+arg);
                TestE testE = new Gson() .fromJson(arg,TestE.class);
                if (testE != null){
                    if(testE.getHsicrmManufacturedate() != null && testE.getHsicrmSalesdate() == null){
                        System.out.println("当前有生产日期，无购买日期！当前可以用！");
                        System.out.println("生产日期： "+testE.getHsicrmManufacturedate());
                        System.out.println("购买日期： "+testE.getHsicrmSalesdate());
                    }else if(testE.getHsicrmManufacturedate() != null && testE.getHsicrmSalesdate() != null){
                        System.out.println("当前有生产日期、购买日期！当前不可用！");
                        System.out.println("生产日期： "+testE.getHsicrmManufacturedate());
                        System.out.println("购买日期： "+testE.getHsicrmSalesdate());
                    }else {
                        System.out.println("生产日期，购买日期 都么有查到");
                    }
                }
            }
            try {
                Thread.sleep(10000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        });


       /* System.out.println(java.net.URLEncoder.encode("中文参数","UTF-8"));



        String url = "https://cl.fh.gov.cn/cjrone/static/%E6%96%97%E5%9B%BE%E7%A5%9E%E5%99%A8.jpg";
        downloadPicture(url,"/Users/<USER>/resources/upload/cjrone/","斗图.jpg");
		*///demo:代理访问
		/*String url = "http://localhost:9999/sl/selectByQlName/060000701959159264111000205";
		String para = "";
		
		*//*String sr=HttpRequestUtil.sendGet(url, para);
		System.out.println(sr);*//*
		Long current_time = System.currentTimeMillis();
        System.out.println(current_time);
        para = "requestKey="+CjroneApp.APP_KEY+"&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+CjroneApp.APP_SECRET+current_time);
        String sp=HttpRequestUtil.sendPost(CjroneApp.TOKEN_URL, para,false,false);
       Map<String,Object> map = new Gson().fromJson(sp,Map.class);
        System.out.println(map.get("resultmsg"));
        String params = "baseInfoXml="+"&attrXml="+"&apasPostXml="+"&token="+map.get("resultmsg")+ "&requestKey="+CjroneApp.APP_KEY;
        String aaa = HttpRequestUtil.sendPost(CjroneApp.COMMIT_DATA,params,false,false);
        System.out.println(aaa);*/
	}
    
}