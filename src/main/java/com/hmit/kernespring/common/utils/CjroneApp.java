package com.hmit.kernespring.common.utils;

/**
 *
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-16 15:39:39
 */
public class CjroneApp {

    // 获取请求app_secret的地址  宁波市
    public  static final String APP_REQUEST_SECRET_URL = "http://10.68.138.194/gateway/app/refreshTokenByKey.htm";
    public  static final String APP_REQUEST_SECRET_URL_REFRESH = "http://10.68.138.194/gateway/app/refreshTokenBySec.htm";
    public  static final String APP_KEY = "8ff525860800453e87cfce97032bc598";
    public static final String APP_SECRET = "ccf892da4f584999b0474e3ddcac5fb1";
    // 获取请求app_secret的地址  IRS
    public  static final String APP_REQUEST_SECRET_URL_NEW = "http://getkey.zjzwfw.gov.cn/gateway/app/refreshTokenByKey.htm";
    public  static final String APP_REQUEST_SECRET_URL_REFRESH_NEW = "http://getkey.zjzwfw.gov.cn/gateway/app/refreshTokenBySec.htm";
    public  static final String APP_KEY_NEW = "A330206284802202105001534";
    public static final String APP_SECRET_NEW = "9eb1715bd8774f4e88e2c95743fcee7c";

    //public  static final String APP_KEY = "8ff525860800453e87cfce97032bc598";
    //public static final String APP_SECRET = "ccf892da4f584999b0474e3ddcac5fb1";
//    public static final String APP_SECRET = "384564cd732245c2be928c5a8c38e10b";
//    public static final String APP_SECRET = "552318dac4514bfa9dec6f534a14d02f";

    // 获取请求公安身份证信息的请求地址
    public  static final String APP_CAERID_INFO_URL = "http://10.68.138.194/gateway/api/001008002016003/dataSharing/vdjb19VHdIh27Rc5.htm";
    // 获取请求公安身份证信息(老)的请求地址
    public  static final String APP_CAERID_OLD_INFO_URL = "http://10.68.138.194/gateway/api/001008002016003/dataSharing/vdjb19VHdIh27Rc5.htm";
    // 获取请求公安户籍信息的请求地址
    public  static final String APP_HUJI_INFO_URL = "http://10.68.138.194/gateway/api/001008002016003/dataSharing/U8tmD1UJe22fMeO3.htm";
    // 公安接口替换接口 - 省公安厅居民户口簿（个人）  http://10.68.138.194/gateway/api/001008002016003/dataSharing/eR4nJy7WuBcoc114.htm
    public static final String APP_HUKOUBEN_INFO_URL = "https://interface.zjzwfw.gov.cn/gateway/api/001003001029/dataSharing/bnpmrbd3c4945G03.htm";

    // 获取请求公安户籍迁出信息的请求地址
    public  static final String APP_HUJI_QC_INFO_URL = "http://10.68.138.194/gateway/api/001008002016006/dataSharing/3d709Pj90ieK9fDc.htm";
    // 获取出生证明信息的请求地址
    public  static final String APP_CSZM_INFO_URL = "http://10.68.138.194/gateway/api/001008002016003/dataSharing/GaW0f87M6TefKLfe.htm";
    // 获取服刑信息的请求地址
    public  static final String APP_FX_INFO_URL = "http://10.68.138.194/gateway/api/001008002016003/dataSharing/3a51FTA9c9TS3TZ3.htm";
    // 获取火化信息的请求地址
    public  static final String APP_HH_INFO_URL = "http://10.68.138.194/gateway/api/001008002016003/dataSharing/Wf4J8sVGZfce7aj0.htm";
    // 获取治安信息的请求地址
    public  static final String APP_ZA_INFO_URL = "http://10.68.138.194/gateway/api/001008002016006/dataSharing/3d709Pj90ieK9fDc.htm";
    // 获取出生号调取父母信息的请求地址
    public  static final String APP_FMCARD_INFO_URL = "http://10.68.138.194/gateway/api/001008002016003/dataSharing/accX0ef3f0ug0NH5.htm";
    // 获取法定代表人信息的请求地址  http://10.68.138.194/gateway/api/001008002016003/dataSharing/l1qmc7ymPN9da6hc.htm
    public  static final String APP_FDDBR_INFO_URL = "https://interface.zjzwfw.gov.cn/gateway/api/001003033/dataSharing/legalRepresentativeTwoInfo.htm";
    // 企业养老保险
    public  static final String APP_QYYL_INFO_URL = "http://10.68.138.194/gateway/api/001008002016003/dataSharing/Sevf8a9D8twhb8K4.htm";
    // 低保信息救助 http://10.68.138.194/gateway/api/001008002016003/dataSharing/53dlf09Xa4Lf50n3.htm
    public static final String APP_DIBAO_INFO_URL = "https://interface.zjzwfw.gov.cn/gateway/api/001003010/dataSharing/lowestRescueNewInfo.htm";
    // 大学生补助  http://10.68.138.194/gateway/api/001008002016003/dataSharing/3nbd8I5c94uBq142.htm
    public  static final String APP_DXS_INFO_URL = "https://interface.zjzwfw.gov.cn/gateway/api/001003001029/dataSharing/974M4fpP6kF938k1.htm";
    //浙江省学籍  http://10.68.138.194/gateway/api/001008002016003/dataSharing/8cBb7F7YaiaW1zbc.htm
    public  static final String APP_ZJSXJ_INFO_URL = "https://interface.zjzwfw.gov.cn/gateway/api/001003004/dataSharing/adb04dQeT5at490e.htm";
    // 特困对象 http://10.68.138.194/gateway/api/001008002016003/dataSharing/6ELh1xC2Pcf241K7.htm
    public static final String APP_TK="https://interface.zjzwfw.gov.cn/gateway/api/001003010/dataSharing/extremelyPoorBasicInfo.htm";
    // 死亡数据  http://10.68.138.194/gateway/api/001008002016003/dataSharing/80d8w8Gda381ATeb.htm
    public static final String APP_DEAD="https://interface.zjzwfw.gov.cn/gateway/api/001008002016127/dataSharing/75cS9338ifpamDWf.htm";
    // 医疗保险
    public static final String APP_YLBX="http://10.68.138.194/gateway/api/001008002016003/dataSharing/WaH3Nf3yoXI481ac.htm";
    // 灵活就业
    public static final String APP_LHJY="http://10.68.138.194/gateway/api/001008002016011/dataSharing/Xd174k5Pf6fWcb98.htm";

    // 获取电子照片的请求地址  http://10.68.138.194/gateway/api/001008002016003/dataSharing/tfbd4Kha4RBO0a17.htm
    public  static final  String APP_CARD_PHO_INFO_URL = "https://interface.zjzwfw.gov.cn/gateway/api/001003007/dataSharing/populationphotopathInfo.htm";
    // 获取家庭信息的请求地址
    public  static final  String APP_FAMILY_INFO_URL = "http://10.68.138.194/gateway/api/001008002016003/dataSharing/H4d7fbJPaQ4bdB2d.htm";
    // 医疗保险参保人员信息  http://10.68.138.194/gateway/api/001008002016003/dataSharing/Swa22cdcl4Pe09d0.htm
    public static final String APP_YLBXCB_INFO_URL = "https://interface.zjzwfw.gov.cn/gateway/api/001003148/dataSharing/3D0yp5ca52ucUbda.htm";
    // 市治安户籍迁出 http://10.68.138.194/gateway/api/001008002016003/dataSharing/L9gfW8cdFZobVfLf.htm
    public static final String APP_SHIHUJIQC_INFO_URL = "https://interface.zjzwfw.gov.cn/gateway/api/001008002016006/dataSharing/8b0470abtUaIcspf.htm";
    // 省户口本
    public static final String APP_SHENGHUKOU_INFO_URL = "http://10.68.138.194/gateway/api/001008002016003/dataSharing/eR4nJy7WuBcoc114.htm";
    // 社会保险个人参保信息   http://10.68.138.194/gateway/api/001008002016003/dataSharing/nojcPfb7efAdf854.htm
    public static final String APP_SHEBAO_INFO_URL = "https://interface.zjzwfw.gov.cn/gateway/api/001003001/dataSharing/fA9d5cbdPOej72Q4.htm";
    // 人口信息
    public static final String APP_RENKOU_INFO_URL = "http://10.68.138.194/gateway/api/001008002016003/dataSharing/7VKla4cX3UO3XWb6.htm";
    // 特困信息
    public static final String  APP_TEKUN_INFO_URL = "http://10.68.138.194/gateway/api/001008002016008/dataSharing/8e5RYbafwmLWf8f4.htm";
    // 殡葬
    public static final String APP_BINZANG_INFO_URL = "http://10.68.138.194/gateway/api/001008002016003/dataSharing/D7u54a5Gf9arxsS9.htm";
    // 流动人口 http://10.68.138.194/gateway/api/001008002016003/dataSharing/H3k93er0G673beW2.htm
    public static final String APP_LIUDONG_INFO_URL = "https://interface.zjzwfw.gov.cn/gateway/api/floPopBasicInfo.htm";


    public static final String SIGN = "";
    public  static final  String APP_ID = "4438763045";
    public  static final  String APP_KEY_T = "249f09c69fe38bf51fe73976cbed2ead";
    public static final String TOKEN_URL = "";
    public static final String BASE_URL = "http://a.com/";

    public static final String AUTH_LOGIN_URL = BASE_URL + "";
    public static final String PUSH_MESSAGE_SIMPLE = BASE_URL + "";
    public static final String GET_USER_INFO_BY_MOBILE = BASE_URL + "";
    public static final String COMMIT_DATA = BASE_URL + "";



    //手机
    public static final String SERVICE_CODE = "fhcjrzdpyc";
    public static final String SERVICE_PASS = "fhcjrzdpycpwd";
    public static final String APP_BASE_URL = "https://puser.zjzwfw.gov.cn/sso/servlet/simpleauth?method=";
    public static final String VALIDATION_TICKET = "ticketValidation";

    public static final String USER_INFO = "getUserInfo";

    //法人
    public static final String ROOT_USER_INFO = "";
    public static final String ROOT_PROJECT_ID =  "";
    public static final String ROOT_PROJECT_SECRET = "";


}
