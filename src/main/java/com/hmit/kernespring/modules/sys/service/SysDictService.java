package com.hmit.kernespring.modules.sys.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;

import java.util.Map;

import java.util.List;

/**
 * 数据字典表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-03 10:52:39
 */
public interface SysDictService extends IService<SysDictEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<SysDictEntity> queryExportData(Map<String, Object> params);
    List<SysDictEntity> queryDataByMap(Map<String, Object> params);
    String querySysDictByMap(Map<String, Object> params);
    String generaterDisableID(boolean isDc,String id_card,String disabilityCategory,String disabilityDegree);
}

