package com.hmit.kernespring.modules.sys.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.google.gson.reflect.TypeToken;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;
import com.hmit.kernespring.common.utils.RedisUtils;
import com.hmit.kernespring.modules.sys.dao.SysDictDao;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;


@Service("sysDictService")
public class SysDictServiceImpl extends ServiceImpl<SysDictDao, SysDictEntity> implements SysDictService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private SysDictDao sysDictDao;
    @Autowired
    private RedisUtils redisUtils;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        SysDictEntity sysDictEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, SysDictEntity.class);
        IPage<SysDictEntity> page = this.page(
                new Query<SysDictEntity>().getPage(params),
                new QueryWrapper<SysDictEntity>()
            .eq(StringUtils.isNotBlank(sysDictEntity.getId ()!=null && !"".equals(sysDictEntity.getId ().toString())? sysDictEntity.getId ().toString():null),"id", sysDictEntity.getId ())
        .eq(StringUtils.isNotBlank(sysDictEntity.getName ()!=null && !"".equals(sysDictEntity.getName ().toString())? sysDictEntity.getName ().toString():null),"name", sysDictEntity.getName ())
        .eq(StringUtils.isNotBlank(sysDictEntity.getType ()!=null && !"".equals(sysDictEntity.getType ().toString())? sysDictEntity.getType ().toString():null),"type", sysDictEntity.getType ())
        .eq(StringUtils.isNotBlank(sysDictEntity.getCode ()!=null && !"".equals(sysDictEntity.getCode ().toString())? sysDictEntity.getCode ().toString():null),"code", sysDictEntity.getCode ())
        .eq(StringUtils.isNotBlank(sysDictEntity.getValue ()!=null && !"".equals(sysDictEntity.getValue ().toString())? sysDictEntity.getValue ().toString():null),"value", sysDictEntity.getValue ())
        .eq(StringUtils.isNotBlank(sysDictEntity.getOrderNum ()!=null && !"".equals(sysDictEntity.getOrderNum ().toString())? sysDictEntity.getOrderNum ().toString():null),"order_num", sysDictEntity.getOrderNum ())
        .eq(StringUtils.isNotBlank(sysDictEntity.getRemark ()!=null && !"".equals(sysDictEntity.getRemark ().toString())? sysDictEntity.getRemark ().toString():null),"remark", sysDictEntity.getRemark ())
        .eq(StringUtils.isNotBlank(sysDictEntity.getDelFlag ()!=null && !"".equals(sysDictEntity.getDelFlag ().toString())? sysDictEntity.getDelFlag ().toString():null),"del_flag", sysDictEntity.getDelFlag ())
        .eq(StringUtils.isNotBlank(sysDictEntity.getLabel ()!=null && !"".equals(sysDictEntity.getLabel ().toString())? sysDictEntity.getLabel ().toString():null),"label", sysDictEntity.getLabel ())
                .groupBy("code")
        );

        return new PageUtils(page);
    }
    @Override
    public List<SysDictEntity> queryExportData(Map<String, Object> params) {
        return sysDictDao.queryExportData(params);
    }
    @Override
    public List<SysDictEntity> queryDataByMap(Map<String, Object> params) {
        if (redisUtils.get(params.get("redis_key").toString()) != null){
            System.out.println("缓存中获取数据key--------> "+params.get("redis_key").toString());
            return gson.fromJson(redisUtils.get(params.get("redis_key").toString()),new TypeToken<List<SysDictEntity>>() {
            }.getType());
        }else{
            List<SysDictEntity> list = sysDictDao.queryDataByMap(params);
            redisUtils.set(params.get("redis_key").toString(),gson.toJson(list));
            return list;
        }
    }
    @Override
    public String querySysDictByMap(Map<String, Object> params) {
        if (redisUtils.get(params.get("redis_key").toString()) != null){
            System.out.println("缓存中获取数据key--------> "+params.get("redis_key").toString());
            /*return gson.fromJson(redisUtils.get(params.get("redis_key").toString()),new TypeToken<List<SysDictEntity>>() {
            }.getType());*/
            return redisUtils.get(params.get("redis_key").toString());
        }else{
            String sysDictEntity = sysDictDao.querySysDictByMap(params);
            redisUtils.set(params.get("redis_key").toString(),gson.toJson(sysDictEntity));
            return sysDictEntity;
        }
    }

    @Override
    public String generaterDisableID(boolean isDc,String id_card,String disabilityCategory, String disabilityDegree) {

        String disableId = null;
        if (!isDc) {
            if (disabilityCategory.indexOf("视力") != -1) {
                disableId = id_card + "1" + disabilityDegree;
            } else if (disabilityCategory.indexOf("听力") != -1) {
                disableId = id_card + "2" + disabilityDegree;
            } else if (disabilityCategory.indexOf("言语") != -1) {
                disableId = id_card + "3" + disabilityDegree;
            } else if (disabilityCategory.indexOf("肢体") != -1) {
                disableId = id_card + "4" + disabilityDegree;
            } else if (disabilityCategory.indexOf("智力") != -1) {
                disableId = id_card + "5" + disabilityDegree;
            } else if (disabilityCategory.indexOf("精神") != -1) {
                disableId = id_card + "6" + disabilityDegree;
            }
        }else {
            disableId = id_card + "7" + disabilityDegree;
        }
        return disableId;
    }

}