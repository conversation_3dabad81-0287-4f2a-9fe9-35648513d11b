

package com.hmit.kernespring.modules.sys.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 系统日志
 *
 * @<NAME_EMAIL>
 */
@Data
@TableName("sys_log")
public class SysLogEntity implements Serializable {
	private static final long serialVersionUID = 1L;
	@TableId
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
	private Long id;
	//用户名
	@Excel(name = "用户名", height = 20, width = 30, isImportField = "true_st")
	private String username;
	//用户操作
	@Excel(name = "用户操作", height = 20, width = 30, isImportField = "true_st")
	private String operation;
	//请求方法
	@Excel(name = "请求方法", height = 20, width = 30, isImportField = "true_st")
	private String method;
	//请求参数
	@Excel(name = "请求参数", height = 20, width = 30, isImportField = "true_st")
	private String params;
	//执行时长(毫秒)
	@Excel(name = "执行时长", height = 20, width = 30, isImportField = "true_st")
	private Long time;
	//IP地址
	@Excel(name = "IP地址", height = 20, width = 30, isImportField = "true_st")
	private String ip;
	//创建时间
	@Excel(name = "创建时间", height = 20, width = 30, isImportField = "true_st")
	private Date createDate;

}
