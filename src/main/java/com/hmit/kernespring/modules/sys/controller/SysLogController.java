

package com.hmit.kernespring.modules.sys.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.hmit.kernespring.common.annotation.SysLog;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblLivingAllowanceEntity;
import com.hmit.kernespring.modules.sys.entity.SysLogEntity;
import com.hmit.kernespring.modules.sys.service.SysLogService;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;


/**
 * 系统日志
 *
 * @<NAME_EMAIL>
 */
@Controller
@RequestMapping("/sys/log")
public class SysLogController {
	@Autowired
	private SysLogService sysLogService;

	/**
	 * 列表
	 */
	@ResponseBody
	@GetMapping("/list")
	@RequiresPermissions("sys:log:list")
	public R list(@RequestParam Map<String, Object> params){
		PageUtils page = sysLogService.queryPage(params);

		return R.ok().put("page", page);
	}


	/**
	 * 导出数据
	 */
	@SysLog("导出日志数据")
	@RequestMapping("/exportData")
	public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		List<SysLogEntity> sysLogEntities = sysLogService.list();

		ExportParams params = new ExportParams("操作日志记录表", null, "操作日志记录表");
		Workbook workbook = ExcelExportUtil.exportExcel(params, SysLogEntity.class, sysLogEntities);

		response.setContentType("application/vnd.ms-excel");
		String fileName = "操作日志记录表" ;
		response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
		OutputStream ouputStream = response.getOutputStream();
		workbook.write(ouputStream);
		ouputStream.flush();
		ouputStream.close();
	}


}
