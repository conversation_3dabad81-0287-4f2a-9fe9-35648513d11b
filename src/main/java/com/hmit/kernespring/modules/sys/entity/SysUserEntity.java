

package com.hmit.kernespring.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hmit.kernespring.common.validator.group.AddGroup;
import com.hmit.kernespring.common.validator.group.UpdateGroup;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 系统用户
 *
 * @<NAME_EMAIL>
 */
@Data
@TableName("sys_user")
public class SysUserEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 用户ID
	 */
	@TableId
	private Long userId;

	/**
	 * 用户名
	 */
	@NotBlank(message="用户名不能为空", groups = {AddGroup.class, UpdateGroup.class})
	private String username;

	/**
	 * 密码
	 */
	@NotBlank(message="密码不能为空", groups = AddGroup.class)
	@Size(groups = {AddGroup.class, UpdateGroup.class}, min = 8,message = "密码长度不能小于8位")
	private String password;

	/**
	 * 盐
	 */
	private String salt;

	/**
	 * 邮箱
	 */
	@NotBlank(message="邮箱不能为空", groups = {AddGroup.class, UpdateGroup.class})
	@Email(message="邮箱格式不正确", groups = {AddGroup.class, UpdateGroup.class})
	private String email;

	/**
	 * 手机号
	 */
	private String mobile;

	/**
	 * 状态  0：禁用   1：正常
	 */
	private Integer status;

	/**
	 * 角色ID列表
	 */
	@TableField(exist=false)
	private List<Long> roleIdList;

	/**
	 * 创建者ID
	 */
	private Long createUserId;

	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 镇
	 */
	private String zhen;
	/**
	 * 村
	 */
	private String cun;
	/**
	 * 所属医院
	 */
	private String hospital;
	/**
	 * 是否区残联
	 */
	private String isQcl;
	/**
	 * 角色类别
	 */
	private String roleName;
	/**
	 * 姓名 签字用
	 */
	private String name;

	/**
	 * 身份证 签字用
	 */
	private String idNo;

	/**
	 * 类别
	 */
	private String disabilityType;


}
