

package com.hmit.kernespring.modules.sys.controller;

import com.hmit.kernespring.common.annotation.SysLog;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.common.utils.*;
import com.hmit.kernespring.common.validator.AESUtils;
import com.hmit.kernespring.common.validator.CodeUtils;
import com.hmit.kernespring.modules.sys.entity.SysLogEntity;
import com.hmit.kernespring.modules.sys.entity.SysUserEntity;
import com.hmit.kernespring.modules.sys.form.SysLoginForm;
import com.hmit.kernespring.modules.sys.service.SysCaptchaService;
import com.hmit.kernespring.modules.sys.service.SysLogService;
import com.hmit.kernespring.modules.sys.service.SysUserService;
import com.hmit.kernespring.modules.sys.service.SysUserTokenService;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.crypto.hash.Sha256Hash;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.Date;
import java.util.Map;

/**
 * 登录相关
 *
 * @<NAME_EMAIL>
 */
@RestController
public class SysLoginController extends AbstractController {
	private static final int LOGIN_IP_LIMIT_TIME = 3;          // 登录失败三次，需要进行验证码验证
	private static final int LOGIN_USERNAME_DISABLE_TIME = 10; // 同一个用户名登录失败10次，账号将被冻结
	private static final int LOGIN_USERNAME_PROMPT_TIME = 5;   // 同一个账号错误5次，提醒用户超过10次，账号将被冻结
	private static final long LOGIN_EXPIRE_TIME = 1800;        // 登录失败记录时长
	private static final long LOGIN_CRYPTO_KEY_EXPIRE_TIME = 60 * 60 * 24;        // 登录失败记录时长

	@Autowired
	private RedisUtils redisUtils;


	@Autowired
	private SysUserService sysUserService;
	@Autowired
	private SysUserTokenService sysUserTokenService;
	@Autowired
	private SysCaptchaService sysCaptchaService;
	@Autowired
	private SysLogService sysLogService;

	/**
	 * 验证码
	 */
	@GetMapping("captcha.jpg")
	public void captcha(HttpServletResponse response, String uuid)throws IOException {
		response.setHeader("Cache-Control", "no-store, no-cache");
		response.setContentType("image/jpeg");

		//获取图片验证码
		BufferedImage image = sysCaptchaService.getCaptcha(uuid);

		ServletOutputStream out = response.getOutputStream();
		ImageIO.write(image, "jpg", out);
		IOUtils.closeQuietly(out);
	}

	@ApiOperation(value = "获取加密的KEY")
	@GetMapping("sys/getCryptoKey")
	public R getCryptoKey() throws Exception {
		String key = CodeUtils.getNumCode(16);
		redisUtils.set(RedisConstant.LOGIN_CRYPTO_KEY + key, key, LOGIN_CRYPTO_KEY_EXPIRE_TIME);
		return R.ok().put("key",AESUtils.encrypt(key));
	}


	/**
	 * 登录
	 */
	@PostMapping("/sys/login")
	public Map<String, Object> login(@RequestBody SysLoginForm form, HttpServletRequest request) throws Exception {
		/*boolean captcha = sysCaptchaService.validate(form.getUuid(), form.getCaptcha());
		if(!captcha){
			return R.error("验证码不正确");
		}*/

		// 登陆前检查
		checkLogin(request, form);

		String username = form.getUsername();
		String password = form.getPassword();

		//用户信息
		SysUserEntity user = sysUserService.queryByUserName(username);


		//账号不存在、密码错误
		if(user == null || !user.getPassword().equals(new Sha256Hash(password, user.getSalt()).toHex())) {
			addFailureLogin(form, request);
			return R.error("账号或密码不正确");
		}

		//账号锁定
		if(user.getStatus() == 0){
			return R.error("账号已被锁定,请联系管理员");
		}

		SysLogEntity sysLog = new SysLogEntity();
		sysLog.setMethod("SysLoginController.login()");
		//设置IP地址
		sysLog.setIp(IPUtils.getIpAddr(request));
		//用户名
		sysLog.setUsername(username);
		sysLog.setTime(1L);
		sysLog.setParams("");

		sysLog.setCreateDate(new Date());
		sysLog.setOperation("用户登录");
		//保存系统日志
		sysLogService.save(sysLog);


		//生成token，并保存到数据库
		R r = sysUserTokenService.createToken(user.getUserId());
		// 返回给前端token值及过期时间，在之后的请求中，前端必须携带token
		return r;
	}


	/**
	 * 退出
	 */
	@PostMapping("/sys/logout")
	public R logout() {

		//设置退出日志
		SysLogEntity sysLog = new SysLogEntity();

		long beginTime = System.currentTimeMillis();
		sysUserTokenService.logout(getUserId());
		long time = System.currentTimeMillis() - beginTime;

		sysLog.setMethod("SysLoginController.logout()");
		//获取request
		HttpServletRequest request = HttpContextUtils.getHttpServletRequest();
		//设置IP地址
		sysLog.setIp(IPUtils.getIpAddr(request));
		//用户名
		String username = ((SysUserEntity) SecurityUtils.getSubject().getPrincipal()).getUsername();
		sysLog.setUsername(username);

		sysLog.setTime(time);
		sysLog.setCreateDate(new Date());
		sysLog.setOperation("用户登出");
		sysLog.setParams("");
		//保存系统日志
		sysLogService.save(sysLog);

		return R.ok();
	}

	private void checkLogin(HttpServletRequest request, SysLoginForm form) throws Exception {
		// 检查cryptoKey有效性,超过特定时间需要刷新页面再操作
		String cryptoKey = AESUtils.decrypt(form.getCryptoKey());
		if (null == redisUtils.get(RedisConstant.LOGIN_CRYPTO_KEY + cryptoKey)) {
			throw new RRException("刷新页面重新登陆");
		}

		form.setUsername(AESUtils.decrypt(form.getUsername(), cryptoKey));
		form.setPassword(AESUtils.decrypt(form.getPassword(), cryptoKey));
		checkUsernameLogin(request, form, true);

	}

	private void checkUsernameLogin(HttpServletRequest request, SysLoginForm form, boolean prompt) throws Exception {
		int unCount = NumberUtils.initInt(redisUtils.get(RedisConstant.LOGIN_USERNAME + form.getUsername()));
		if (unCount >= LOGIN_USERNAME_DISABLE_TIME) {
			throw new RRException("用户名登录次数超过" + LOGIN_USERNAME_DISABLE_TIME + "次，被锁定，解冻剩余时间 "
					+ DateUtils.formatLong(redisUtils.getExpire(RedisConstant.LOGIN_USERNAME + form.getUsername())));
		}

		if (prompt && unCount >= LOGIN_USERNAME_PROMPT_TIME) {
			throw new RRException("用户名或密码不正确, 失败登录" + unCount + "次, 超过" + LOGIN_USERNAME_DISABLE_TIME + "次, 账号将被冻结" + LOGIN_EXPIRE_TIME/60 + "分钟");
		}
	}
	private void addFailureLogin(SysLoginForm form, HttpServletRequest request) throws Exception {
		redisUtils.incr(RedisConstant.LOGIN_USERNAME + form.getUsername(), LOGIN_EXPIRE_TIME);
	}

}
