

package com.hmit.kernespring.modules.sys.service;

import com.hmit.kernespring.modules.sys.entity.SysUserEntity;
import com.hmit.kernespring.modules.sys.entity.SysUserTokenEntity;

import java.util.Set;

/**
 * shiro相关接口
 *
 * @<NAME_EMAIL>
 */
public interface ShiroService {
    /**
     * 获取用户权限列表
     */
    Set<String> getUserPermissions(long userId);

    SysUserTokenEntity queryByToken(String token);

    /**
     * 根据用户ID，查询用户
     * @param userId
     */
    SysUserEntity queryUser(Long userId);
}
