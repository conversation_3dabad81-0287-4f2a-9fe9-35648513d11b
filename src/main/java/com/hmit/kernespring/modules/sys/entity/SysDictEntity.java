package com.hmit.kernespring.modules.sys.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 数据字典表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-03 10:52:39
 */
@Data
@TableName("sys_dict")
public class SysDictEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
@TableId
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Long id;
	/**
	 * 字典名称
	 */
	@Excel(name = "字典名称", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 字典类型
	 */
	@Excel(name = "字典类型", height = 20, width = 30, isImportField = "true_st")
private String type;
	/**
	 * 字典码
	 */
	@Excel(name = "字典码", height = 20, width = 30, isImportField = "true_st")
private String code;
	/**
	 * 字典值
	 */
	@Excel(name = "字典值", height = 20, width = 30, isImportField = "true_st")
private String value;
	/**
	 * 排序
	 */
	@Excel(name = "排序", height = 20, width = 30, isImportField = "true_st")
private Integer orderNum;
	/**
	 * 备注
	 */
	@Excel(name = "备注", height = 20, width = 30, isImportField = "true_st")
private String remark;
	/**
	 * 删除标记  -1：已删除  0：正常
	 */
	@Excel(name = "删除标记  -1：已删除  0：正常", height = 20, width = 30, isImportField = "true_st")
private Integer delFlag;
	/**
	 * 字典显示值
	 */
	@Excel(name = "字典显示值", height = 20, width = 30, isImportField = "true_st")
private String label;

}
