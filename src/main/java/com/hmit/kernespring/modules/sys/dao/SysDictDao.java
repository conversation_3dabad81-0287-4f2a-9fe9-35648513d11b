package com.hmit.kernespring.modules.sys.dao;

import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 数据字典表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-03 10:52:39
 */
@Mapper
public interface SysDictDao extends BaseMapper<SysDictEntity> {
    List<SysDictEntity> queryExportData(Map<String, Object> params);
	List<SysDictEntity> queryDataByMap(Map<String, Object> params);
	String querySysDictByMap(Map<String, Object> params);
}
