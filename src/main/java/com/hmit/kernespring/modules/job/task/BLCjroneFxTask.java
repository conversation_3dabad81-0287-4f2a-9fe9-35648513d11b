package com.hmit.kernespring.modules.job.task;


import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.hmit.kernespring.modules.app.service.APIService;
import com.hmit.kernespring.modules.data_management.entity.ApiFuxEntity;
import com.hmit.kernespring.modules.data_management.entity.DataDeadinfoEntity;
import com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity;
import com.hmit.kernespring.modules.data_management.entity.FxinfoEntity;
import com.hmit.kernespring.modules.data_management.service.DataDeadinfoService;
import com.hmit.kernespring.modules.data_management.service.DataDisabilityCertificateService;
import com.hmit.kernespring.modules.data_management.service.FxinfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

// 每日基础数据比对 -- 2、服刑相关接口
@Component("blcjroneFxTask")
public class BLCjroneFxTask implements ITask {

    private Logger logger = LoggerFactory.getLogger(getClass());
    @Autowired
    private APIService apiService;
    @Autowired
    private DataDisabilityCertificateService dataDisabilityCertificateService;
    @Autowired
    private FxinfoService fxinfoService;



    @Override
    public void run(String params) throws InterruptedException {

        // 清空服刑数据
        List<FxinfoEntity> fxinfoEntityList = fxinfoService.list();
        for(FxinfoEntity f : fxinfoEntityList){
            fxinfoService.removeById(f.getId());
        }

        logger.debug("北仑残疾人服刑数据比对开始==================");
        // 1、 获取所有残疾人列表，遍历判断
        List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntityList = dataDisabilityCertificateService.list();
        for (DataDisabilityCertificateEntity d : dataDisabilityCertificateEntityList) {

            // 调用服刑数据接口
            Map<String, Object> doFxInfo = apiService.queryFXInfo(d.getIdCard(), null);
            System.out.println("kkkkkkk------->" + doFxInfo);
            if ("00".equals(doFxInfo.get("code").toString())) {
                System.out.println("kkkkkkk------->" + doFxInfo.get("datas"));
                if (doFxInfo.get("datas") != null && !"null".equals(doFxInfo.get("datas").toString()) && !"[]".equals(doFxInfo.get("datas").toString())) {
                    System.out.println("doFxInfo: " + doFxInfo);
                    List<ApiFuxEntity> list = new Gson().fromJson(doFxInfo.get("datas").toString().replaceAll("00:00:00.0", "").replaceAll(":", "").replaceAll(";", "").replaceAll(" ", ""), new TypeToken<List<ApiFuxEntity>>() {
                    }.getType());
                    if (list.size() != 0) {

                        Map<String,Object> p = new HashMap<>();
                        p.put("name",d.getName());
                        p.put("id_card",d.getIdCard());
                        List<FxinfoEntity> dataFxinfoEntityList = (List<FxinfoEntity>) fxinfoService.listByMap(p);
                        if(dataFxinfoEntityList==null||dataFxinfoEntityList.size()==0){
                            // 原表不存在，插入
                            FxinfoEntity fxinfoEntity = new FxinfoEntity();
                            fxinfoEntity.setName(d.getName());
                            fxinfoEntity.setIdCard(d.getIdCard());
                            fxinfoEntity.setFromInfo("服刑信息");
                            fxinfoEntity.setResultInfo(doFxInfo.toString());
                            fxinfoService.save(fxinfoEntity);
                        }

                        System.out.println("当前存在" + list.size() + "条服刑信息 第一条信息如下---> " + new Gson().toJson(list.get(0)));
                    }
                }
            } else {
                System.out.println("获取数据异常");
            }


        }
    }


}
