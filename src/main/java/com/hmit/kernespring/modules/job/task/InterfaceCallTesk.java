package com.hmit.kernespring.modules.job.task;

import com.hmit.kernespring.modules.app.service.APIService;
import com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity;
import com.hmit.kernespring.modules.data_management.service.DataDisabilityCertificateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-11-16 16:16
 */
@Component("interfaceCallTask")
public class InterfaceCallTesk implements  ITask{

    @Autowired
    private DataDisabilityCertificateService dataDisabilityCertificateService;

    @Autowired
    private APIService apiService;

    @Override
    public void run(String params) {

        //获得残疾人列表
        Map<String,Object> paramsdis=new HashMap<>();
        List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities = (List<DataDisabilityCertificateEntity>) dataDisabilityCertificateService.listByMap(paramsdis);

        // 便利调用接口，提高接口调用量
        dataDisabilityCertificateEntities.forEach(dataDisabilityCertificateEntity -> {

            //1、户籍接口
            Map<String, Object> hh_result = apiService.queryHuJiInfo(dataDisabilityCertificateEntity.getIdCard(), null);
            System.out.println("户籍------->" + hh_result);

            //2、服刑信息接口
            Map<String, Object> doFxInfo = apiService.queryFXInfo(dataDisabilityCertificateEntity.getIdCard(), null);
            System.out.println("服刑------->" + doFxInfo);

            //3、验证是否个体工商户
            Map<String, Object> doFdDbInfo = apiService.queryFDDBRInfo(dataDisabilityCertificateEntity.getIdCard(), null);
            System.out.println("是否个体工商户--------->"+doFdDbInfo);

            //4、获得火化信息
            Map<String, Object> dead_result = apiService.queryHHInfo(dataDisabilityCertificateEntity.getIdCard(), null);
            System.out.println("火化------->"+dead_result);

            //5、治安信息
            Map<String, Object> doZaInfo = apiService.queryZAInfo(dataDisabilityCertificateEntity.getIdCard(), null);
            System.out.println("治安----->"+doZaInfo);

            //6、身份证信息
            Map<String, Object> hh_result2 = apiService.queryCardIDInfo(dataDisabilityCertificateEntity.getName(), dataDisabilityCertificateEntity.getIdCard(), null);
            System.out.println("身份证----->"+hh_result2);




        });

    }
}
