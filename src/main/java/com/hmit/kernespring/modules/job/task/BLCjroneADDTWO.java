package com.hmit.kernespring.modules.job.task;


import com.hmit.kernespring.modules.app.service.APIService;
import com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity;
import com.hmit.kernespring.modules.data_management.service.DataDisabilityCertificateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

// 增加调用量第二页
@Component("blcjroneADDTWO")
public class BLCjroneADDTWO implements  ITask{

    @Autowired
    private APIService apiService;
    @Autowired
    private DataDisabilityCertificateService dataDisabilityCertificateService;


    @Override
    public void run(String params) throws InterruptedException {

        //获得残疾人列表
        Map<String,Object> paramsdis=new HashMap<>();
        List<DataDisabilityCertificateEntity> alldata = new ArrayList<>();
        List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities = (List<DataDisabilityCertificateEntity>) dataDisabilityCertificateService.listByMap(paramsdis);
        alldata.addAll(dataDisabilityCertificateEntities);
        alldata.addAll(dataDisabilityCertificateEntities);
        alldata.addAll(dataDisabilityCertificateEntities);
        alldata.addAll(dataDisabilityCertificateEntities);
        alldata.addAll(dataDisabilityCertificateEntities);

        // 1、灵活就业
        apiService.asynclhjy(alldata);
        // 2、高校学籍查询
        apiService.asyncgdxj(alldata);
        // 3、低保救助
        apiService.asyncdbjz(alldata);
        // 4、出生证明  参数不足
        // 5、省公安厅居民身份证查询  参数不足

        // 6、额外补充
        // 法人信息
        apiService.asyncblfddbr(alldata);



    }
}
