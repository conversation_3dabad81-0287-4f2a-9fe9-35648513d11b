package com.hmit.kernespring.modules.job.task;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.hmit.kernespring.modules.app.service.APIService;
import com.hmit.kernespring.modules.data_management.entity.ApiFuxEntity;
import com.hmit.kernespring.modules.data_management.entity.DataDeadinfoEntity;
import com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity;
import com.hmit.kernespring.modules.data_management.entity.DisabilityCertificateSyncDataEntity;
import com.hmit.kernespring.modules.data_management.service.DataDeadinfoService;
import com.hmit.kernespring.modules.data_management.service.DataDisabilityCertificateService;
import com.hmit.kernespring.modules.data_management.service.DisabilityCertificateSyncDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

// 基本数据同步
// 北仑残疾人  disability_certificate_sync_data (服刑，低保，家庭经济情况)  - 每日定时任务
@Component("blcjroneSyncTask")
public class BLCjroneSyncTask implements  ITask {

    private Logger logger = LoggerFactory.getLogger(getClass());
    @Autowired
    private APIService apiService;
    @Autowired
    private DataDisabilityCertificateService dataDisabilityCertificateService;
    @Autowired
    private DisabilityCertificateSyncDataService disabilityCertificateSyncDataService;
    @Autowired
    private DataDeadinfoService dataDeadinfoService;


    @Override
    public void run(String params) {

        logger.debug("数据比对 定时任务正在执行，参数为：{}", params);


        // 1、 遍历残疾人基础数据
        Map<String, Object> params_tmp = new HashMap<>();
        List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities = (List<DataDisabilityCertificateEntity>) dataDisabilityCertificateService.listByMap(params_tmp);
        dataDisabilityCertificateEntities.forEach(dataDisabilityCertificateEntity -> {

            Map<String, Object> param = new HashMap<>();
            param.put("id_card",dataDisabilityCertificateEntity.getIdCard());
            param.put("name",dataDisabilityCertificateEntity.getName());
            List<DisabilityCertificateSyncDataEntity> dataEntities = (List<DisabilityCertificateSyncDataEntity>) disabilityCertificateSyncDataService.listByMap(param);
            DisabilityCertificateSyncDataEntity dataEntity = null;
            if (dataEntities.size() != 0){
                dataEntity  = dataEntities.get(0);
            }else {
                dataEntity  = new DisabilityCertificateSyncDataEntity();
            }
            dataEntity.setName(dataDisabilityCertificateEntity.getName());
            dataEntity.setIdCard(dataDisabilityCertificateEntity.getIdCard());
            dataEntity.setCreateTime(new Date());
            param.put("idCard",dataDisabilityCertificateEntity.getIdCard());
            // 联合查询基础数据表
            DisabilityCertificateSyncDataEntity disabilityCertificateSyncDataEntity = disabilityCertificateSyncDataService.queryStaticsData(param);
            System.out.println(new Gson().toJson(disabilityCertificateSyncDataEntity));

            // 1、残疾人是否死亡  将 data_deadinfo 表的数据保存至 is_dead 字段
            dataEntity.setIsDead(disabilityCertificateSyncDataEntity.getIsDead());
            // 2、残疾人是否服刑
            dataEntity.setIsFx(disabilityCertificateSyncDataEntity.getIsFx());
            // 3、是否申请了生活补贴
            dataEntity.setIsShbt(disabilityCertificateSyncDataEntity.getIsShbt());
            // 4、是否享受了低保
            if (disabilityCertificateSyncDataEntity.getDataLowSecurity() ==0){
                dataEntity.setFamilyEconoCondition("无");
            } else if (disabilityCertificateSyncDataEntity.getDataLowSecurity() !=0){
                dataEntity.setFamilyEconoCondition("低保");
            }
            // 5、判断是否享受了职工基本养老保险
            dataEntity.setIsZgyl(disabilityCertificateSyncDataEntity.getIsZgyl());


            // 保存到数据库
            if (dataEntity.getId() !=null){
                disabilityCertificateSyncDataService.updateById(dataEntity);
            }else {
                disabilityCertificateSyncDataService.save(dataEntity);
            }

        });




        }
}
