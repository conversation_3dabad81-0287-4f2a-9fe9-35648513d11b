package com.hmit.kernespring.modules.job.task;

import com.hmit.kernespring.modules.app.service.APIService;
import com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity;
import com.hmit.kernespring.modules.data_management.service.DataDisabilityCertificateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

// 增加调用量第一页
@Component("blcjroneADDONE")
public class BLCjroneADDONE implements  ITask {

    @Autowired
    private APIService apiService;
    @Autowired
    private DataDisabilityCertificateService dataDisabilityCertificateService;

    @Override
    public void run(String params) throws InterruptedException {

        //获得残疾人列表
        Map<String,Object> paramsdis=new HashMap<>();
        List<DataDisabilityCertificateEntity> alldata = new ArrayList<>();
        List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities = (List<DataDisabilityCertificateEntity>) dataDisabilityCertificateService.listByMap(paramsdis);
        alldata.addAll(dataDisabilityCertificateEntities);
        alldata.addAll(dataDisabilityCertificateEntities);
        alldata.addAll(dataDisabilityCertificateEntities);
        alldata.addAll(dataDisabilityCertificateEntities);
        alldata.addAll(dataDisabilityCertificateEntities);

        // 1、学生信息
        apiService.asyncxsxx(alldata);
        // 2、特困对象基本信息
        apiService.asynctk(alldata);
        // 3、 死亡信息
        apiService.asyncdead(alldata);
        // 4、医疗保险
        apiService.asyncylbx(alldata);
        // 5、企业养老
        apiService.asyncqyyl(alldata);


    }


}
