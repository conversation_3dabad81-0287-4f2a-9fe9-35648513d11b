package com.hmit.kernespring.modules.job.task;

import com.hmit.kernespring.modules.app.service.APIService;
import com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity;
import com.hmit.kernespring.modules.data_management.service.DataDisabilityCertificateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

// 增加调用量第四页
@Component("blcjroneADDFOUR")
public class BLCjroneADDFOUR implements  ITask {

    @Autowired
    private APIService apiService;
    @Autowired
    private DataDisabilityCertificateService dataDisabilityCertificateService;

    @Override
    public void run(String params) throws InterruptedException {

        //获得残疾人列表
        Map<String,Object> paramsdis=new HashMap<>();
        List<DataDisabilityCertificateEntity> alldata = new ArrayList<>();
        List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities = (List<DataDisabilityCertificateEntity>) dataDisabilityCertificateService.listByMap(paramsdis);
        alldata.addAll(dataDisabilityCertificateEntities);
        alldata.addAll(dataDisabilityCertificateEntities);
        alldata.addAll(dataDisabilityCertificateEntities);
        alldata.addAll(dataDisabilityCertificateEntities);
        alldata.addAll(dataDisabilityCertificateEntities);

        // 1、医疗保险参保
        apiService.asynylbxcb(alldata);
        //2、市治安户籍迁出
        apiService.asynyshihujiqc(alldata);
        // 3、 省户口本
        apiService.asynyshenhukou(alldata);
        // 4、社会保险个人参保
        apiService.asynyshebao(alldata);
        // 5、人口信息
        apiService.asynyrenkou(alldata);
        // 6、特困
        apiService.asynytekun(alldata);
        // 7、 殡葬
        apiService.asynybinzang(alldata);
        // 8、流动人口
        apiService.asynyliudong(alldata);





    }

}
