package com.hmit.kernespring.modules.job.task;

import com.google.gson.Gson;
import com.hmit.kernespring.common.utils.HttpRequestUtil;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.app.service.APIService;
import com.hmit.kernespring.modules.data_management.entity.ApiCardIdEntity;
import com.hmit.kernespring.modules.data_management.entity.ApiHuJiEntity;
import com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity;
import com.hmit.kernespring.modules.data_management.service.ApiCardIdService;
import com.hmit.kernespring.modules.data_management.service.DataDeadinfoService;
import com.hmit.kernespring.modules.data_management.service.DataDisabilityCertificateService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

// 北仑残疾人户籍数据 及 电子证照  -- 每日定时任务 (更新户籍数据及电子证照)
@Component("blcjroneApiCardIdTask")
public class BLCjroneApiCardIdTask implements ITask {

    private Logger logger = LoggerFactory.getLogger(getClass());
    @Autowired
    private APIService apiService;
    @Autowired
    private DataDisabilityCertificateService dataDisabilityCertificateService;
    @Autowired
    private ApiCardIdService apiCardIdService;
    @Autowired
    private CjroneProperties cjroneProperties;


    @Override
    public void run(String params) {

        logger.debug("北仑残疾人户籍数据比对开始-------");

        // 1、 获取所有残疾人列表，遍历判断
        List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntityList = dataDisabilityCertificateService.list();
        for(DataDisabilityCertificateEntity d : dataDisabilityCertificateEntityList){

            // 2、判断表中是否已经有数据
            boolean isapicardid = false;
            // 3、判断是否已经有电子证照
            boolean isphoto = false;

            Map<String, Object> dis_tmp = new HashMap<>();
            dis_tmp.put("id_card", d.getIdCard());
            ApiCardIdEntity apiCardId_result = new ApiCardIdEntity();
            List<ApiCardIdEntity> is_bl_list = (List<ApiCardIdEntity>) apiCardIdService.listByMap(dis_tmp);
            if(is_bl_list!=null && is_bl_list.size()>0){
                isapicardid = true;
                apiCardId_result=is_bl_list.get(0);
                if(apiCardId_result.getPhoto()!=null){
                    isphoto = true;
                }
            }

            try {
                Thread.sleep(300);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

            Map<String, Object> blhh_result = apiService.queryHuJiInfo(d.getIdCard(), null);
            if ("00".equals(blhh_result.get("code").toString()) && blhh_result.get("datas") != null && !"[]".equals(blhh_result.get("datas").toString())) {

                String data = blhh_result.get("datas").toString();
                System.out.println("=====datas===="+data);
                data = data.substring(1,data.length()-1);
                String data_new = data.substring(0,data.indexOf("registrationDate=")+27)+data.substring(data.indexOf("registrationDate=")+36,data.length());
                System.out.println(data_new);
                ApiHuJiEntity apiHuJiEntity = new Gson().fromJson(data_new.replaceAll(" ","").replaceAll("=,","=null,"), ApiHuJiEntity.class);
                if (apiHuJiEntity != null) {
                    ApiCardIdEntity apiCardId = new ApiCardIdEntity();
                    System.out.println(new Gson().toJson(apiHuJiEntity));
                    // 北仑户籍
                    if (apiHuJiEntity.getRegistrationAuthority() != null && apiHuJiEntity.getRegistrationAuthority().indexOf("北仑") != -1){
                        System.out.println("当前是北仑户籍");
                        apiCardId.setQfjg("北仑区公安局");
                    }else {
                        apiCardId.setQfjg(apiHuJiEntity.getRegistrationAuthority());
                    }
                    System.out.println("apiHuJiEntity: "+new Gson().toJson(apiHuJiEntity));

                    // 开始保存数据至本地
                    apiCardId.setName(apiHuJiEntity.getName());
                    apiCardId.setSex(apiHuJiEntity.getSex());
                    apiCardId.setNationality(apiHuJiEntity.getNation());
                    if (apiHuJiEntity.getDateOfBirth().length() == 8){
                        String aabc = apiHuJiEntity.getDateOfBirth();
                        aabc = aabc.substring(0,4)+"-"+aabc.substring(4,6)+"-"+aabc.substring(6,8);
                        apiCardId.setBirthday(aabc);
                    }else {
                        apiCardId.setBirthday(apiHuJiEntity.getDateOfBirth());
                    }
                    apiCardId.setNativePlace("浙江");
                    apiCardId.setIdCard(apiHuJiEntity.getIdcard());
                    apiCardId.setNativeAddress(apiHuJiEntity.getWhereToLocal());
                    apiCardId.setJgbh("长期");

                    System.out.println("dATABean: "+new Gson().toJson(apiHuJiEntity));

                    // 判断是否需要下载电子证照
                    if(!isphoto){
                        // 开始下载电子照片
                        Map<String, Object> photo_result = apiService.queryCardIdPho(d.getIdCard(), apiHuJiEntity.getName(), apiHuJiEntity.getNation(), apiHuJiEntity.getDateOfBirth(), null);
                        System.out.println("photo_result: "+photo_result);
                        if (photo_result.get("datas") == null || "null".equals(photo_result.get("datas"))){
                            apiCardId.setPhoto(null);
                        }else {
                            System.out.println(photo_result.get("datas").toString());
                            String pho_datas = photo_result.get("datas").toString();
                            System.out.println("当前电子照片张数：" + count(pho_datas, "uRL"));
                            String is_exist_path = null;
                            if (count(pho_datas, "uRL") > 0) {
                                is_exist_path = pho_datas.substring(StringUtils.ordinalIndexOf(pho_datas, "uRL", count(pho_datas, "uRL")) + 4, pho_datas.length() - 2);
                            }
                            System.out.println("is_exist_path" + is_exist_path);
                            if (is_exist_path != null) {
                                String sava_path = HttpRequestUtil.downloadPicture(is_exist_path, cjroneProperties.getDownloadPath(), apiCardId.getIdCard() + ".jpg");
                                apiCardId.setPhoto(sava_path);
                            }
                        }
                    }

                    //将接口获得的数据保存至数据库
                    // 判断原先是否有记录
                    if(isapicardid){
                        // 有记录，保存id，设置为修改
                        apiCardId.setId(apiCardId_result.getId());
                        apiCardIdService.updateById(apiCardId);
                    }else{
                        apiCardIdService.save(apiCardId);
                    }
                }
            }
        }
    }


    public static int count(String srcStr, String findStr) {
        int count = 0;
        Pattern pattern = Pattern.compile(findStr);// 通过静态方法compile(String regex)方法来创建,将给定的正则表达式编译并赋予给Pattern类
        Matcher matcher = pattern.matcher(srcStr);//
        while (matcher.find()) {// boolean find() 对字符串进行匹配,匹配到的字符串可以在任何位置
            count++;
        }
        return count;
    }
}
