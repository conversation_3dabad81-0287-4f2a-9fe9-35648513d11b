package com.hmit.kernespring.modules.job.task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.QueryChainWrapper;
import com.hmit.kernespring.modules.cjrone.entity.CjroneblLove24SybcDataEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneblLove24SybcDataService;
import com.hmit.kernespring.modules.cjrone_bl.entity.Love24Entity;
import com.hmit.kernespring.modules.cjrone_bl.service.LivingAllowanceSyncDataService;
import com.hmit.kernespring.modules.cjrone_bl.service.Love24Service;
import com.hmit.kernespring.modules.data_management.entity.ApiCardIdEntity;
import com.hmit.kernespring.modules.data_management.entity.DataDeadinfoEntity;
import com.hmit.kernespring.modules.data_management.entity.DisabilityCertificateSyncDataEntity;
import com.hmit.kernespring.modules.data_management.service.ApiCardIdService;
import com.hmit.kernespring.modules.data_management.service.DataDeadinfoService;
import com.hmit.kernespring.modules.data_management.service.DisabilityCertificateSyncDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

// 爱心24小时异常数据比对
@Component("blcjroneLove24Task")
public class BLCjroneLove24Task implements ITask {

    private Logger logger = LoggerFactory.getLogger(getClass());
    @Autowired
    private CjroneblLove24SybcDataService cjroneblLove24SybcDataService;
    @Autowired
    private DisabilityCertificateSyncDataService disabilityCertificateSyncDataService;
    @Autowired
    private LivingAllowanceSyncDataService livingAllowanceSyncDataService;
    @Autowired
    private Love24Service love24Service;
    @Autowired
    private ApiCardIdService apiCardIdService;
    @Autowired
    private DataDeadinfoService dataDeadinfoService;


    @Override
    public void run(String params) throws InterruptedException {

        logger.debug("爱心24小时数据比对 定时任务正在执行....");


        // 遍历 爱心24小时 享受名单
        List<Love24Entity> love24Entities = love24Service.list();
        for(Love24Entity l : love24Entities){
            CjroneblLove24SybcDataEntity entity = cjroneblLove24SybcDataService.getOne(new QueryWrapper<CjroneblLove24SybcDataEntity>()
            .eq("id_card",l.getIdCard()));

            if(entity == null){
                // 构造需要保存的实体数据
                DateTimeFormatter dateTimeFormat= DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                CjroneblLove24SybcDataEntity love24SybcDataEntity = new CjroneblLove24SybcDataEntity();
                love24SybcDataEntity.setName(l.getName());
                love24SybcDataEntity.setIdCard(l.getIdCard());
                love24SybcDataEntity.setLiveAddress(l.getLiveAddress());
                love24SybcDataEntity.setCreateTime(LocalDateTime.now().format(dateTimeFormat));
                love24SybcDataEntity.setApplicationType(l.getApplicationType());
                love24SybcDataEntity.setIsDisable("0");

                // 1、判断户口迁移
                Map<String,Object> apiparam = new HashMap<>();
                apiparam.put("id_card",l.getIdCard());
                List<ApiCardIdEntity> al = (List<ApiCardIdEntity>)apiCardIdService.listByMap(apiparam);
                if(al!=null && al.size()>0){
                    if(al.get(0).getQfjg().indexOf("北仑") == -1&&al.get(0).getQfjg().indexOf("大榭") == -1){
                        // 户口已迁移
                        love24SybcDataEntity.setStatus("1");
                        love24SybcDataEntity.setIsHkqy("1");
                        love24SybcDataEntity.setHkqyInfo("户口迁移："+al.get(0).getQfjg());

                        cjroneblLove24SybcDataService.save(love24SybcDataEntity);
                        continue;
                    }
                }

                Map<String,Object> dsparam = new HashMap<>();
                dsparam.put("id_card",l.getIdCard());
//                List<DisabilityCertificateSyncDataEntity> dsl = (List<DisabilityCertificateSyncDataEntity>)disabilityCertificateSyncDataService.listByMap(dsparam);
                List<DataDeadinfoEntity> dsl = (List<DataDeadinfoEntity>) dataDeadinfoService.listByMap(dsparam);

                if(dsl!=null && dsl.size()>0){
                    love24SybcDataEntity.setStatus("1");
                    love24SybcDataEntity.setIsDead("1");
                    cjroneblLove24SybcDataService.save(love24SybcDataEntity);
                    continue;

/*
                    // 2、判断是否死亡
                    if(dsl.get(0).getIsDead()==1){
                        love24SybcDataEntity.setStatus("1");
                        love24SybcDataEntity.setIsDead("1");
                        cjroneblLove24SybcDataService.save(love24SybcDataEntity);
                        continue;
                    }
*/

                }

            }

        }


    }
}
