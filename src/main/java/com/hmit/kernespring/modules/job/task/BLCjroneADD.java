package com.hmit.kernespring.modules.job.task;

import com.hmit.kernespring.modules.app.service.APIService;
import com.hmit.kernespring.modules.data_management.dao.DataDisabilityCertificateDao;
import com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity;
import com.hmit.kernespring.modules.data_management.service.DataDisabilityCertificateService;
import com.hmit.kernespring.modules.matter_application.service.DisabilityCertificateApplicationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

// 北仑增加接口调用量
@Component("blcjroneADD")
public class BLCjroneADD implements  ITask {

    @Autowired
    private APIService apiService;
    @Autowired
    private DataDisabilityCertificateService dataDisabilityCertificateService;


    @Override
    public void run(String params)throws InterruptedException {

        //获得残疾人列表
        Map<String,Object> paramsdis=new HashMap<>();
        List<DataDisabilityCertificateEntity> alldata = new ArrayList<>();
        List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities = (List<DataDisabilityCertificateEntity>) dataDisabilityCertificateService.listByMap(paramsdis);
        alldata.addAll(dataDisabilityCertificateEntities);


        // 更新的接口调用
        // 1、社会保险个人参保  ok
        apiService.asynyshebao(alldata);
        // 2、医保个人参保信息   ok
        apiService.asynylbxcb(alldata);
        // 3、宁波市卫生健康委宁波地区死亡数据  异常
        apiService.asyncdead(alldata);
        // 4、教育部_高校学历_姓名及证件号码查询  ok
        apiService.asyncgdxj(alldata);
        // 5、个人实名认证信息查询   ok
        apiService.asyncdzzz(alldata);
        // 6、省公安厅居民户口簿（个人）  ok
        apiService.querrHuKouBenInfo(alldata.get(1).getIdCard(),null);
        // 7、流动人口信息  放弃
        apiService.asynyliudong(alldata);
        // 8、宁波市公安局治安户籍迁出信息查询接口 异常
        apiService.asynyshihujiqc(alldata);
        // 9、学生信息  ok
        apiService.asyncxsxx(alldata);









       /* // 多线程调用接口
        // 11\ 特困信息
        apiService.asynctk(alldata);
        // 12、 死亡数据
        apiService.asyncdead(alldata);
        // 13、 医疗数据
        apiService.asyncylbx(alldata);
        // 14\ 灵活就业数据
        apiService.asynclhjy(alldata);
        //1、法定代表人
        apiService.asyncblfddbr(alldata);
        //2、服刑信息
        apiService.asyncblfx(alldata);
        //3、电子证照
        apiService.asyncdzzz(alldata);
        //4、户籍信息
        apiService.asyncblhj(alldata);
        //5、火化信息
        apiService.asyncblhh(alldata);
        //6、户籍迁出
        apiService.asyncblhjqc(alldata);
        // 7、低保救助信息
        apiService.asyncdbjz(alldata);
        // 8、高校学籍数据
        apiService.asyncgdxj(alldata);
        // 9、企业养老数据
        apiService.asyncqyyl(alldata);
        // 10、 学生信息
        apiService.asyncxsxx(alldata);*/




    }
}
