package com.hmit.kernespring.modules.job.task;

import com.google.gson.Gson;
import com.hmit.kernespring.common.utils.DateUtils;
import com.hmit.kernespring.modules.app.service.APIService;
import com.hmit.kernespring.modules.cjrone.entity.CjroneLivingAllowanceEntity;
import com.hmit.kernespring.modules.cjrone.entity.CjroneLivingAllowanceSyncDataEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneLivingAllowanceService;
import com.hmit.kernespring.modules.cjrone.service.CjroneLivingAllowanceSyncDataService;
import com.hmit.kernespring.modules.data_management.entity.ApiHuJiEntity;
import com.hmit.kernespring.modules.data_management.entity.DisabilityCertificateSyncDataEntity;
import com.hmit.kernespring.modules.data_management.service.DataDisabilityCertificateService;
import com.hmit.kernespring.modules.data_management.service.DisabilityCertificateSyncDataService;
import com.hmit.kernespring.modules.matter_application.service.CjroneWelfareMatterApplicationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-07 16:16
 */
@Component("cjroneLIvingTask")
public class CjroneLIvingTask implements ITask {
    private Logger logger = LoggerFactory.getLogger(getClass());
    @Autowired
    private APIService apiService;
    @Autowired
    private CjroneLivingAllowanceSyncDataService cjroneLivingAllowanceSyncDataService;
    @Autowired
    private DisabilityCertificateSyncDataService disabilityCertificateSyncDataService;
    @Autowired
    private DataDisabilityCertificateService dataDisabilityCertificateService;
    @Autowired
    private CjroneLivingAllowanceService cjroneLivingAllowanceService;

    @Autowired
    private CjroneWelfareMatterApplicationService cjroneWelfareMatterApplicationService;

    @Override
    public void run(String paramss){

        logger.debug("生活补贴数据比对 定时任务正在执行，参数为：{}", paramss);
        Map<String, Object> params_tmp = new Gson().fromJson(paramss,Map.class);
        String is_yzhj = params_tmp.get("is_yzhj") != null ?params_tmp.get("is_yzhj").toString():null;
        String is_yzfx = params_tmp.get("is_yzfx") != null ?params_tmp.get("is_yzfx").toString():null;
        params_tmp.put("is_yzhj",null);
        params_tmp.put("is_yzfx",null);
        List<CjroneLivingAllowanceEntity> dataDisabilityCertificateEntities = (List<CjroneLivingAllowanceEntity>) cjroneLivingAllowanceService.listByMap(params_tmp);
        params_tmp.put("is_yzhj",is_yzhj);
        params_tmp.put("is_yzfx",is_yzfx);
        logger.debug("生活补贴数据比对 比对数据总数：{}", dataDisabilityCertificateEntities.size());
        dataDisabilityCertificateEntities.forEach(dataDisabilityCertificateEntity -> {


            // bob20190911 更改代码逻辑

            //1、获得各种基本数据
            Map<String,Object> nmap = new HashMap<>();
            nmap.put("idCard",dataDisabilityCertificateEntity.getIdCard());
            Map<String,Object> result_map = cjroneWelfareMatterApplicationService.queryMattersByMap(nmap);

            boolean isShbtOption = true; //是否享受护理补贴
            String exceptionReason="";   // 异常理由

            //2、违法犯罪，正在执行监禁刑罚，则不满足
            if ("1".equals(result_map.get("isFx").toString())){
                isShbtOption = false;
                exceptionReason = exceptionReason+"当前属于服刑人员！ ； ";
            }

            //3、已纳入特困人员供养，则不满足
            if ("1".equals(result_map.get("isTk").toString())){
                isShbtOption = false;
                exceptionReason = exceptionReason+"当前属于特困人员！ ； ";
            }

            //4、已领取工伤保险生活护理费的，则不满足
            if ("1".equals(result_map.get("isGongS").toString())){
                isShbtOption = false;
                exceptionReason = exceptionReason+"当前属于享受工伤保险生活护理费人员！ ； ";
            }

          //5、 已享受困境儿童
            if ("1".equals(result_map.get("isKjEt").toString())){
                isShbtOption = false;
                exceptionReason = exceptionReason+"当前属于享受困境儿童基本生活补贴政策！ ； ";
            }

            //6、非低保，低边年龄在18以下，60以上的
            String birthday = dataDisabilityCertificateEntity.getIdCard().substring(6,10)+"-"+dataDisabilityCertificateEntity.getIdCard().substring(10,12)+"-"+dataDisabilityCertificateEntity.getIdCard().substring(12,14)+" 00:00:00";
            Date stringToDate = DateUtils.stringToDate(birthday,DateUtils.DATE_TIME_PATTERN);
            if ("无".equals(result_map.get("familyEcho").toString())){
                if (DateUtils.checkAdultDYEight(stringToDate) && DateUtils.checkAdultXYSix(stringToDate)) {
                }else{
                    isShbtOption = false;
                    exceptionReason = exceptionReason+"当前人员年龄不属于18周岁-60周岁之间！ ； ";
                }
            }

            //以下开始更改代码：

            // 查询家庭经济情况和医保信息
            Map<String, Object> params = new HashMap<>();
            params.put("id_card",dataDisabilityCertificateEntity.getIdCard());
            params.put("name",dataDisabilityCertificateEntity.getName());
            // params.put("status","5");
            List<CjroneLivingAllowanceSyncDataEntity> dataEntities = (List<CjroneLivingAllowanceSyncDataEntity>) cjroneLivingAllowanceSyncDataService.listByMap(params);
            CjroneLivingAllowanceSyncDataEntity dataEntity = null;
            if (dataEntities.size() != 0){
                dataEntity  = dataEntities.get(0);
            }else {
                dataEntity  = new CjroneLivingAllowanceSyncDataEntity();
            }
            dataEntity.setName(dataDisabilityCertificateEntity.getName());
            dataEntity.setIdCard(dataDisabilityCertificateEntity.getIdCard());
            dataEntity.setCreateTime(DateUtils.format(new Date(),DateUtils.DATE_TIME_PATTERN));
            params.put("idCard",dataDisabilityCertificateEntity.getIdCard());
            DisabilityCertificateSyncDataEntity disabilityCertificateSyncDataEntity = disabilityCertificateSyncDataService.queryStaticsData(params);


            if (disabilityCertificateSyncDataEntity.getDataLowSecurity() ==0 && disabilityCertificateSyncDataEntity.getDataLowSecurityMargin() ==0){
                dataEntity.setFamilyEconoCondition("无");
            } else if (disabilityCertificateSyncDataEntity.getDataLowSecurity() !=0 && disabilityCertificateSyncDataEntity.getDataLowSecurityMargin() ==0){
                dataEntity.setFamilyEconoCondition("低保");
            } else if (disabilityCertificateSyncDataEntity.getDataLowSecurity() ==0 && disabilityCertificateSyncDataEntity.getDataLowSecurityMargin() !=0){
                dataEntity.setFamilyEconoCondition("低保边缘");
            } else if (disabilityCertificateSyncDataEntity.getDataLowSecurity() !=0 && disabilityCertificateSyncDataEntity.getDataLowSecurityMargin() !=0){
                dataEntity.setFamilyEconoCondition("低保、低保边缘");
            }else {
                dataEntity.setFamilyEconoCondition("无");
            }
            if (!"0".equals(disabilityCertificateSyncDataEntity.getMedicalInsurance())){
                dataEntity.setMedicalInsurance("参保");
            }else {
                dataEntity.setMedicalInsurance("无");
            }
            // 是否死亡
            if (disabilityCertificateSyncDataEntity.getIsDead() != 0){
                // 数据导入--是否死亡
                dataEntity.setIsDead(1);
                exceptionReason = exceptionReason+"已死亡 ； ";
            }else {
                // 验证公安数据
                dataEntity.setIsDead(0);
            }
            dataEntity.setIsHkqy(0);
            dataEntity.setHkqyInfo("当前是奉化户籍");
            if (params_tmp.get("is_yzhj") != null) {
                // 是否迁移
                Map<String, Object> hh_result = apiService.queryHuJiInfo(dataDisabilityCertificateEntity.getIdCard(), null);
                if ("00".equals(hh_result.get("code").toString())) {
                    if (hh_result.get("datas") != null && !"null".equals(hh_result.get("datas").toString()) && !"[]".equals(hh_result.get("datas").toString())) {
                        String data = hh_result.get("datas").toString();
                        data = data.substring(1, data.length() - 1);
                        String data_new = data.substring(0, data.indexOf("registrationDate=") + 27) + data.substring(data.indexOf("registrationDate=") + 36, data.length());
                        System.out.println(data_new);

                        ApiHuJiEntity apiHuJiEntity = new Gson().fromJson(data_new.replaceAll(" ", "").replaceAll("=,", "=null,"), ApiHuJiEntity.class);
                        if (apiHuJiEntity != null) {
                            System.out.println(new Gson().toJson(apiHuJiEntity));
                            if (apiHuJiEntity.getRegistrationAuthority() != null && apiHuJiEntity.getRegistrationAuthority().indexOf("奉化") != -1) {
                                System.out.println("当前是奉化户籍");
                                dataEntity.setIsHkqy(0);
                                dataEntity.setHkqyInfo("当前是奉化户籍");
                            } else {
                                dataEntity.setIsHkqy(1);
                                dataEntity.setHkqyInfo("户口已经迁移");
                                exceptionReason = exceptionReason+"户口已经迁移！ ； ";
                            }
                        }
                    }
                } else {
                    dataEntity.setHkqyInfo("获取数据异常");
                }
            }


             if (dataEntity.getIsDead() == 1){
                /* Map<String, Object> tmp_params = new HashMap<>();
                 params.put("id_card",dataDisabilityCertificateEntity.getIdCard());
                 params.put("name",dataDisabilityCertificateEntity.getName());
                 List<CjroneWelfareMatterApplicationEntity> welfareMatterApplicationEntities = (List<CjroneWelfareMatterApplicationEntity>) cjroneWelfareMatterApplicationService.listByMap(tmp_params);
                 welfareMatterApplicationEntities.forEach(item ->{
                        item.setStatus("0");
                 });
                 if (welfareMatterApplicationEntities.size()>0) {
                     cjroneWelfareMatterApplicationService.updateBatchById(welfareMatterApplicationEntities);
                 }*/
             }
            // dataEntity.getIsFx() == 1 ||dataEntity.getIsHkqy() == 1 ||(!isShbtOption)
             if ( dataEntity.getIsHkqy() == 1  || dataEntity.getIsDead() == 1 || (!isShbtOption)){
                 dataEntity.setStatus(0);
             }else {
                 dataEntity.setStatus(1);
             }
             if (!"0".equals(disabilityCertificateSyncDataEntity.getIsApplyWelfareMatter())){
                 dataEntity.setIsApplyWelfareMatter("是");
             }else{
                 dataEntity.setIsApplyWelfareMatter("否");
             }
            System.out.println(new Gson().toJson(dataEntity));
            dataEntity.setExceptionReason(exceptionReason);
            // 保存到数据库
            if (dataEntity.getId() !=null){
                cjroneLivingAllowanceSyncDataService.updateById(dataEntity);
            }else {
                cjroneLivingAllowanceSyncDataService.save(dataEntity);
            }
        });
    }
}
