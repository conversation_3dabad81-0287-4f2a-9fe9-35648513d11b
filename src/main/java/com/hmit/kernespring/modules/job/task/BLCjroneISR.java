package com.hmit.kernespring.modules.job.task;

import com.hmit.kernespring.modules.app.service.APIService;
import com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity;
import com.hmit.kernespring.modules.data_management.service.DataDisabilityCertificateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

// ISR接口调用
@Component("blcjroneISR")
public class BLCjroneISR implements  ITask {

    @Autowired
    private APIService apiService;
    @Autowired
    private DataDisabilityCertificateService dataDisabilityCertificateService;


    @Override
    public void run(String params)throws InterruptedException {

        //获得残疾人列表
        Map<String,Object> paramsdis=new HashMap<>();
        List<DataDisabilityCertificateEntity> alldata = new ArrayList<>();
        List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities = (List<DataDisabilityCertificateEntity>) dataDisabilityCertificateService.listByMap(paramsdis);
        alldata.addAll(dataDisabilityCertificateEntities);


        // 更新的接口调用
        // 1、特困对象
        apiService.asynytekun(alldata);
        // 2、低保救助对象
        apiService.asyncdbjz(alldata);
        // 3、宁波市卫生健康委宁波地区死亡数据  异常
        apiService.asyncdead(alldata);
        // 4、宁波市公安局治安户籍迁出信息查询接口 异常
        apiService.asynyshihujiqc(alldata);
        // 5. 法人信息
        apiService.asyncblfddbr(alldata);
        // 7、流动人口信息  放弃
        apiService.asynyliudong(alldata);














    }
}
