package com.hmit.kernespring.modules.job.task;

import com.hmit.kernespring.modules.app.service.APIService;
import com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity;
import com.hmit.kernespring.modules.data_management.service.DataDisabilityCertificateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

// 增加调用量第三页
@Component("blcjroneADDTHREE")
public class BLCjroneADDTHREE implements  ITask{

    @Autowired
    private APIService apiService;
    @Autowired
    private DataDisabilityCertificateService dataDisabilityCertificateService;

    @Override
    public void run(String params) throws InterruptedException {

        //获得残疾人列表
        Map<String,Object> paramsdis=new HashMap<>();
        List<DataDisabilityCertificateEntity> alldata = new ArrayList<>();
        List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities = (List<DataDisabilityCertificateEntity>) dataDisabilityCertificateService.listByMap(paramsdis);
        alldata.addAll(dataDisabilityCertificateEntities);
        alldata.addAll(dataDisabilityCertificateEntities);
        alldata.addAll(dataDisabilityCertificateEntities);
        alldata.addAll(dataDisabilityCertificateEntities);
        alldata.addAll(dataDisabilityCertificateEntities);

        //1、户籍迁出
        apiService.asyncblhjqc(alldata);
        // 2、火化信息
        apiService.asyncblhh(alldata);
        // 3、户籍信息
        apiService.asyncblhj(alldata);
        // 4、个人照片
        apiService.asyncdzzz(alldata);
        // 5、服刑信息
        apiService.asyncblfx(alldata);

    }
}
