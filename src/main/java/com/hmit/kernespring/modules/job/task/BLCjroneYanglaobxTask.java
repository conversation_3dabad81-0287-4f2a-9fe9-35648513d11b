package com.hmit.kernespring.modules.job.task;

import com.hmit.kernespring.modules.app.service.APIService;
import com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity;
import com.hmit.kernespring.modules.data_management.entity.DataLowSecurityEntity;
import com.hmit.kernespring.modules.data_management.entity.YanglaobxEntity;
import com.hmit.kernespring.modules.data_management.service.ApiCardIdService;
import com.hmit.kernespring.modules.data_management.service.DataDisabilityCertificateService;
import com.hmit.kernespring.modules.data_management.service.YanglaobxService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

// 北仑残疾人 职工基本养老保险  -- 每日定时任务
@Component("blcjroneYanglaobxTask")
public class BLCjroneYanglaobxTask implements ITask {

    private Logger logger = LoggerFactory.getLogger(getClass());
    @Autowired
    private APIService apiService;
    @Autowired
    private DataDisabilityCertificateService dataDisabilityCertificateService;
    @Autowired
    private ApiCardIdService apiCardIdService;
    @Autowired
    private YanglaobxService yanglaobxService;

    @Override
    public void run(String params) {

        logger.debug("北仑残疾人职工基本养老数据比对开始--------");

        // 查询的职工基本养老List
        List<YanglaobxEntity> ZGyanglaobxEntityList =new ArrayList<>();

        // 清空原表
        Map<String,Object> deleteParam = new HashMap<>();
        deleteParam.put("type","职工基本养老");
        yanglaobxService.removeByMap(deleteParam);

        // 1、 获取所有残疾人列表，遍历判断
        List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntityList = dataDisabilityCertificateService.list();
        for(DataDisabilityCertificateEntity d : dataDisabilityCertificateEntityList){

            // 查询职工基本养老数据
            Map<String,Object> zgyl_result = apiService.queryQYYLInfo(d.getIdCard(),d.getName(),null);
            // 如果查询有数据，则对数据进行处理
            if("00".equals(zgyl_result.get("code").toString())) {
                // 判断返回数据的数量
                int datacount = Integer.parseInt(zgyl_result.get("dataCount").toString().split("\\.")[0]);
                if (datacount > 0) {
                    // 将数据保存至 data_yanglaobx
                    YanglaobxEntity yanglaobxEntity = new YanglaobxEntity();
                    yanglaobxEntity.setName(d.getName());
                    yanglaobxEntity.setIdCard(d.getIdCard());
                    yanglaobxEntity.setType("职工基本养老");
                    yanglaobxEntity.setRemark(zgyl_result.toString());

                    ZGyanglaobxEntityList.add(yanglaobxEntity);
                }
            }


            /*if("00".equals(zgyl_result.get("code").toString()) && zgyl_result.get("datas")!=null && !"[]".equals(zgyl_result.get("datas").toString())){

                String datas = zgyl_result.get("datas").toString();
                System.out.println("=====datas===="+datas);

                // 判断类型是否为 11
                if(datas.indexOf("\"INSURANCETYPE\":\"11\"")!=-1){
                    // 将数据保存至 data_yanglaobx
                    YanglaobxEntity yanglaobxEntity = new YanglaobxEntity();
                    yanglaobxEntity.setName(d.getName());
                    yanglaobxEntity.setIdCard(d.getIdCard());
                    yanglaobxEntity.setType("职工基本养老");
                    yanglaobxEntity.setRemark(datas);

                    ZGyanglaobxEntityList.add(yanglaobxEntity);
                }
            }*/
        }

        // 批量保存数据
        yanglaobxService.saveBatch(ZGyanglaobxEntityList);

    }
}
