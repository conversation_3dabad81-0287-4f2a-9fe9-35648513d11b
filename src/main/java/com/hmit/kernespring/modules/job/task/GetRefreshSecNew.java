package com.hmit.kernespring.modules.job.task;

import com.google.gson.Gson;
import com.hmit.kernespring.common.utils.CjroneApp;
import com.hmit.kernespring.common.utils.CjroneAppUtil;
import com.hmit.kernespring.modules.sys.entity.SysConfigEntity;
import com.hmit.kernespring.modules.sys.service.SysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

// 根据APP_KEY和APP密钥获取刷新密钥  IRS
@Component("getRefreshSecNew")
public class GetRefreshSecNew implements  ITask {

    @Autowired
    private SysConfigService sysConfigService;

    @Override
    public void run(String params) throws InterruptedException {

        // 获取当前日期
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime nowtime = LocalDateTime.now();
        String nowtimestr = nowtime.format(formatter);

        SysConfigEntity refreshconfig = sysConfigService.getById(5);
        SysConfigEntity requestconfig = sysConfigService.getById(4);
        DateTimeFormatter dateTimeFormatter2=DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");//严格遵守大小写
        LocalDateTime refreshlocalDate=LocalDateTime.parse(refreshconfig.getRemark(),dateTimeFormatter2);
        LocalDateTime requestlocalDate=LocalDateTime.parse(requestconfig.getRemark(),dateTimeFormatter2);

        System.out.println("根据APP_KEY和APP密钥获取刷新密钥");

        Map<String, Object> refresh_key_result = CjroneAppUtil.doOne(CjroneApp.APP_REQUEST_SECRET_URL_NEW, CjroneApp.APP_KEY_NEW, CjroneApp.APP_SECRET_NEW);
        System.out.println("刷新密钥返回："+refresh_key_result);

        String requestSecret="";
        String refreshSecret="";

        if ("00".equals(refresh_key_result.get("code").toString()) && refresh_key_result.get("datas") != null) {
            Map<String, Object> map = new Gson().fromJson(refresh_key_result.get("datas").toString(), Map.class);
            if (map.get("requestSecret") != null) {
                requestSecret = map.get("requestSecret") != null ? map.get("requestSecret").toString() : null;
            }
            if (map.get("refreshSecret") != null) {
                refreshSecret = map.get("refreshSecret") != null ? map.get("refreshSecret").toString() : null;
            }
        }

        LocalDateTime requesttime = LocalDateTime.now().plusMinutes(13);
        String requesttimestr = requesttime.format(formatter);
        LocalDateTime refreshtime = LocalDateTime.now().plusHours(47);
        String refreshtimestr = refreshtime.format(formatter);

        // 判断是否需要更新
        if(null!=requestSecret&&!"".equals(requestSecret)){
            requestconfig.setParamKey("requestSecretNew");
            requestconfig.setParamValue(requestSecret);
            requestconfig.setRemark(requesttimestr);
            sysConfigService.update(requestconfig);
        }

        // 判断是否需要更新
        if(null!=refreshSecret&&!"".equals(refreshSecret)){
            refreshconfig.setParamKey("refreshSecretNew");
            refreshconfig.setParamValue(refreshSecret);
            refreshconfig.setRemark(refreshtimestr);
            sysConfigService.update(refreshconfig);
        }

    }
}
