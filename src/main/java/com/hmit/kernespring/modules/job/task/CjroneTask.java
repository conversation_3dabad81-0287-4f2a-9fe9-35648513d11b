package com.hmit.kernespring.modules.job.task;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.hmit.kernespring.common.utils.HttpRequestUtil;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.app.service.APIService;
import com.hmit.kernespring.modules.data_management.entity.*;
import com.hmit.kernespring.modules.data_management.service.ApiCardIdService;
import com.hmit.kernespring.modules.data_management.service.DataDisabilityCertificateService;
import com.hmit.kernespring.modules.data_management.service.DisabilityCertificateSyncDataService;
import com.hmit.kernespring.modules.matter_application.service.CjroneWelfareMatterApplicationService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-07 16:16
 */
@Component("cjroneTask")
public class CjroneTask implements ITask {
    private Logger logger = LoggerFactory.getLogger(getClass());
    @Autowired
    private APIService apiService;
    @Autowired
    private DisabilityCertificateSyncDataService disabilityCertificateSyncDataService;

    @Autowired
    private DataDisabilityCertificateService dataDisabilityCertificateService;

    @Autowired
    private CjroneWelfareMatterApplicationService cjroneWelfareMatterApplicationService;

    @Autowired
    private ApiCardIdService apiCardIdService;

    @Autowired
    private CjroneProperties cjroneProperties;

    @Override
    public void run(String paramss){
        logger.debug("数据比对 定时任务正在执行，参数为：{}", paramss);

        Map<String, Object> params_tmp = new HashMap<>();

        // <editor-fold> 更新表disability_certificate_sync_data

        // </editor-fold>

        List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities = (List<DataDisabilityCertificateEntity>) dataDisabilityCertificateService.listByMap(params_tmp);
        dataDisabilityCertificateEntities.forEach(dataDisabilityCertificateEntity -> {

            Map<String, Object> params = new HashMap<>();
            params.put("id_card",dataDisabilityCertificateEntity.getIdCard());
            params.put("name",dataDisabilityCertificateEntity.getName());
            // 保存残疾人基础信息
            List<DisabilityCertificateSyncDataEntity> dataEntities = (List<DisabilityCertificateSyncDataEntity>) disabilityCertificateSyncDataService.listByMap(params);
            DisabilityCertificateSyncDataEntity dataEntity = null;
            if (dataEntities.size() != 0){
                dataEntity  = dataEntities.get(0);
            }else {
                dataEntity  = new DisabilityCertificateSyncDataEntity();
            }
            dataEntity.setName(dataDisabilityCertificateEntity.getName());
            dataEntity.setIdCard(dataDisabilityCertificateEntity.getIdCard());
            dataEntity.setCreateTime(new Date());
            params.put("idCard",dataDisabilityCertificateEntity.getIdCard());
            // 联合查询基础数据表
            DisabilityCertificateSyncDataEntity disabilityCertificateSyncDataEntity = disabilityCertificateSyncDataService.queryStaticsData(params);
            System.out.println(new Gson().toJson(disabilityCertificateSyncDataEntity));

            //<editor-fold>  家庭经济情况
            if (disabilityCertificateSyncDataEntity.getDataLowSecurity() ==0 && disabilityCertificateSyncDataEntity.getDataLowSecurityMargin() ==0){
                dataEntity.setFamilyEconoCondition("无");
            } else if (disabilityCertificateSyncDataEntity.getDataLowSecurity() !=0 && disabilityCertificateSyncDataEntity.getDataLowSecurityMargin() ==0){
                dataEntity.setFamilyEconoCondition("低保");
            } else if (disabilityCertificateSyncDataEntity.getDataLowSecurity() ==0 && disabilityCertificateSyncDataEntity.getDataLowSecurityMargin() !=0){
                dataEntity.setFamilyEconoCondition("低保边缘");
            } else if (disabilityCertificateSyncDataEntity.getDataLowSecurity() !=0 && disabilityCertificateSyncDataEntity.getDataLowSecurityMargin() !=0){
                dataEntity.setFamilyEconoCondition("低保、低保边缘");
            }else {
                dataEntity.setFamilyEconoCondition("无");
            }
            //</editor-fold>

            //<editor-fold> 医疗保险情况
           /* if (!"0".equals(disabilityCertificateSyncDataEntity.getMedicalInsurance())){
                dataEntity.setMedicalInsurance("参保");
            }else {
                dataEntity.setMedicalInsurance("无");
            }*/
            //</editor-fold>

            // <editor-fold>是否死亡
            if (disabilityCertificateSyncDataEntity.getIsDead() != 0){
                // 数据导入--是否死亡
                dataEntity.setIsDead(1);
            }else {
                // 验证公安数据
                dataEntity.setIsDead(0);
            }
            //</editor-fold>

            // <editor-fold> 是否户口迁移
            dataEntity.setIsHkqy(0);
            dataEntity.setHkqyInfo("当前是北仑户籍");
                // 是否迁移
                Map<String, Object> hh_result = apiService.queryHuJiInfo(dataDisabilityCertificateEntity.getIdCard(), null);
                if ("00".equals(hh_result.get("code").toString())) {
                    if (hh_result.get("datas") != null && !"null".equals(hh_result.get("datas").toString()) && !"[]".equals(hh_result.get("datas").toString())) {
                        String data = hh_result.get("datas").toString();
                        data = data.substring(1, data.length() - 1);
                        String data_new = data.substring(0, data.indexOf("registrationDate=") + 27) + data.substring(data.indexOf("registrationDate=") + 36, data.length());
                        System.out.println(data_new);

                        ApiHuJiEntity apiHuJiEntity = new Gson().fromJson(data_new.replaceAll(" ", "").replaceAll("=,", "=null,"), ApiHuJiEntity.class);
                        if (apiHuJiEntity != null) {
                            System.out.println(new Gson().toJson(apiHuJiEntity));
                            if (apiHuJiEntity.getRegistrationAuthority() != null && apiHuJiEntity.getRegistrationAuthority().indexOf("北仑") != -1) {
                                System.out.println("当前是北仑户籍");
                                dataEntity.setIsHkqy(0);
                                dataEntity.setHkqyInfo("当前是北仑户籍");
                            } else {
                                dataEntity.setIsHkqy(1);
                                dataEntity.setHkqyInfo("户口已经迁移");
                            }
                        }
                    }
                }else{
                    dataEntity.setHkqyInfo("获取数据异常");
                }
            // </editor-fold>

            // <editor-fold> 是否服刑
            dataEntity.setFxInfo("暂无服刑信息");
            dataEntity.setIsFx(0);
                // 是否服刑
                Map<String, Object> doFxInfo = apiService.queryFXInfo(dataDisabilityCertificateEntity.getIdCard(), null);
                System.out.println("kkkkkkk------->" + doFxInfo);
                if ("00".equals(doFxInfo.get("code").toString())) {
                    System.out.println("kkkkkkk------->" + doFxInfo.get("datas"));
                    if (doFxInfo.get("datas") != null && !"null".equals(doFxInfo.get("datas").toString()) && !"[]".equals(doFxInfo.get("datas").toString())) {
                        System.out.println("doFxInfo: " + doFxInfo);
                        List<ApiFuxEntity> list = new Gson().fromJson(doFxInfo.get("datas").toString().replaceAll("00:00:00.0", "").replaceAll(":", "").replaceAll(";", "").replaceAll(" ", ""), new TypeToken<List<ApiFuxEntity>>() {
                        }.getType());
                        if (list.size() != 0) {
                            System.out.println("当前存在" + list.size() + "条服刑信息 第一条信息如下---> " + new Gson().toJson(list.get(0)));
                            dataEntity.setIsFx(1);
                            dataEntity.setFxInfo(new Gson().toJson(list));
                        } else {
                            dataEntity.setFxInfo("暂无服刑信息");
                            dataEntity.setIsFx(0);
                        }
                    }
                } else {
                    dataEntity.setFxInfo("获取数据异常");
                }
            // </editor-fold>

            // <editor-fold> 是否困境儿童
            if (disabilityCertificateSyncDataEntity.getIsDischild() != 0){
                dataEntity.setIsDischild(1);
            }else {
                dataEntity.setIsDischild(0);
            }
            // </editor-fold>

            // <editor-fold> 是否60周岁养老保险补贴
            if (disabilityCertificateSyncDataEntity.getIsSixty() != 0){
                dataEntity.setIsSixty(1);
            }else {
                dataEntity.setIsSixty(0);
            }
            // </editor-fold>

            // <editor-fold> 是否特困
            if (disabilityCertificateSyncDataEntity.getIsTk() != 0){
                dataEntity.setIsTk(1);
            }else {
                dataEntity.setIsTk(0);
            }
            // </editor-fold>

            // <editor-fold> 是否工伤保险
             if (disabilityCertificateSyncDataEntity.getIsWorkinjury() != 0){
                dataEntity.setIsWorkinjury(1);
            }else {
                 dataEntity.setIsWorkinjury(0);
            }
             // </editor-fold>


             /*if (dataEntity.getIsFx() == 1 ||dataEntity.getIsHkqy() == 1 ||dataEntity.getIsTk() == 1 || dataEntity.getIsSixty() == 1 || dataEntity.getIsDischild() == 1  || dataEntity.getIsWorkinjury() == 1 ){
                 dataEntity.setStatus(0);
             }else {
                 dataEntity.setStatus(1);
             }
             if (!"0".equals(disabilityCertificateSyncDataEntity.getIsApplyWelfareMatter())){
                 dataEntity.setIsApplyWelfareMatter("是");
             }else{
                 dataEntity.setIsApplyWelfareMatter("否");
             }
            System.out.println(new Gson().toJson(dataEntity));*/
            // 保存到数据库
            if (dataEntity.getId() !=null){
                disabilityCertificateSyncDataService.updateById(dataEntity);
            }else {
                disabilityCertificateSyncDataService.save(dataEntity);
            }
        });
    }

    /**
     * 方法2：Java正则表达式Pattern和Matcher类
     *
     * @param srcStr
     * @param findStr
     * @return
     */
    public static int count(String srcStr, String findStr) {
        int count = 0;
        Pattern pattern = Pattern.compile(findStr);// 通过静态方法compile(String regex)方法来创建,将给定的正则表达式编译并赋予给Pattern类
        Matcher matcher = pattern.matcher(srcStr);//
        while (matcher.find()) {// boolean find() 对字符串进行匹配,匹配到的字符串可以在任何位置
            count++;
        }
        return count;
    }

    public static void main(String[] args) {
        Map<String, Object> doFxInfo = new HashMap<>();
        doFxInfo.put("datas","[{releaseTime=2015-04-05 00:00:00.0, address=锦屏街道体育场路98号, endDate=1900-01-01, additional=罚金金额:;剥政年限:000000;没收财产情况:, relatives=胡明娥, imprisonedPalce=3317, commutation=暂无, entryTime=2014-08-14, birthplace=330283, cardID=330224195910110016, name=周林刚, crime=169, startDate=1900-01-01}]");
        if (doFxInfo.get("datas") != null && !"null".equals(doFxInfo.get("datas").toString()) && !"[]".equals(doFxInfo.get("datas").toString())) {
            System.out.println("doFxInfo: " + doFxInfo);
            List<ApiFuxEntity> list = new Gson().fromJson(doFxInfo.get("datas").toString().replaceAll("00:00:00.0","").replaceAll(":","").replaceAll(";","").replaceAll(" ",""), new TypeToken<List<ApiFuxEntity>>() {
            }.getType());
            if (list.size() != 0) {
                System.out.println(list.get(0).getName());
            System.out.println(list.get(0).getReleaseTime());
            System.out.println(list.get(0).getEntryTime());
            }
        }
    }
}
