package com.hmit.kernespring.modules.job.task;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.hmit.kernespring.modules.app.service.APIService;
import com.hmit.kernespring.modules.data_management.entity.DataDeadinfoEntity;
import com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity;
import com.hmit.kernespring.modules.data_management.entity.DeadInfoEntity;
import com.hmit.kernespring.modules.data_management.service.DataDeadinfoService;
import com.hmit.kernespring.modules.data_management.service.DataDisabilityCertificateService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

// 每日基础数据比对 -- 1、死亡相关接口
@Component("blcjroneDeadTask")
public class BLCjroneDeadTask implements ITask {

    private Logger logger = LoggerFactory.getLogger(getClass());
    @Autowired
    private APIService apiService;
    @Autowired
    private DataDisabilityCertificateService dataDisabilityCertificateService;
    @Autowired
    private DataDeadinfoService dataDeadinfoService;

    @Override
    public void run(String params) {

        logger.debug("北仑残疾人死亡数据比对开始-------");

        // data_deadinfo 表保存历史的死亡数据，因此同步之前不需要清空

        // 1、 获取所有残疾人列表，遍历判断
        List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntityList = dataDisabilityCertificateService.list();
        dataDisabilityCertificateEntityList.forEach(d ->{

            // 1、调用火化信息接口
            Map<String, Object> dead_result = apiService.queryHHInfo(d.getIdCard(), null);
            if ("00".equals(dead_result.get("code").toString())) {
                if(dead_result.get("datas") != null) {
                    List<DeadInfoEntity> dead_list = new Gson().fromJson(dead_result.get("datas").toString().replaceAll("=,", "=null,"), new TypeToken<List<DeadInfoEntity>>() {
                    }.getType());
                    if (dead_list.size() != 0) {
                        // 将死亡名单保存至 datadeadinfo
                        Map<String,Object> p = new HashMap<>();
                        p.put("name",d.getName());
                        p.put("id_card",d.getIdCard());
                        List<DataDeadinfoEntity> dataDeadinfoEntityList = (List<DataDeadinfoEntity>) dataDeadinfoService.listByMap(p);
                        if(dataDeadinfoEntityList==null||dataDeadinfoEntityList.size()==0){
                            // 原表不存在，插入
                            DataDeadinfoEntity dataDeadinfoEntity = new DataDeadinfoEntity();
                            dataDeadinfoEntity.setName(d.getName());
                            dataDeadinfoEntity.setIdCard(d.getIdCard());
                            dataDeadinfoEntity.setFromInfo("火化信息");
                            dataDeadinfoEntity.setResultInfo(dead_result.toString());
                            dataDeadinfoService.save(dataDeadinfoEntity);
                        }

                        System.out.println("当前存在" + dead_list.size() + "条火化信息 第一条信息如下---> " + new Gson().toJson(dead_list.get(0)));
                    }
                }
            }else {
                System.out.println("数据获取异常，身份证号码为："+ d.getIdCard());
            }

            // 2、卫健委死亡信息接口
            Map<String,Object> wjw_dead_info = apiService.queryDeadInfo(d.getName(),d.getIdCard(),null);
            if("00".equals(wjw_dead_info.get("code").toString())){
                // 判断返回数据的数量
                int datacount = Integer.parseInt(wjw_dead_info.get("dataCount").toString().split("\\.")[0]);
                if(datacount>0){
                    // 将死亡名单保存至 datadeadinfo
                    Map<String,Object> p = new HashMap<>();
                    p.put("name",d.getName());
                    p.put("id_card",d.getIdCard());
                    List<DataDeadinfoEntity> dataDeadinfoEntityList = (List<DataDeadinfoEntity>) dataDeadinfoService.listByMap(p);
                    if(dataDeadinfoEntityList==null||dataDeadinfoEntityList.size()==0){
                        // 原表不存在，插入
                        DataDeadinfoEntity dataDeadinfoEntity = new DataDeadinfoEntity();
                        dataDeadinfoEntity.setName(d.getName());
                        dataDeadinfoEntity.setIdCard(d.getIdCard());
                        dataDeadinfoEntity.setFromInfo("卫健委死亡信息");
                        dataDeadinfoEntity.setResultInfo(wjw_dead_info.toString());
                        dataDeadinfoService.save(dataDeadinfoEntity);
                    }
                }
            }

        });

        logger.debug("北仑残疾人死亡数据比对结束-------");

    }


}
