package com.hmit.kernespring.modules.job.task;

import com.hmit.kernespring.modules.app.service.APIService;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblLivingAllowanceEntity;
import com.hmit.kernespring.modules.cjrone_bl.entity.LivingAllowanceSyncDataEntity;
import com.hmit.kernespring.modules.cjrone_bl.service.CjroneblLivingAllowanceService;
import com.hmit.kernespring.modules.cjrone_bl.service.LivingAllowanceSyncDataService;
import com.hmit.kernespring.modules.data_management.entity.ApiCardIdEntity;
import com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity;
import com.hmit.kernespring.modules.data_management.entity.DisabilityCertificateSyncDataEntity;
import com.hmit.kernespring.modules.data_management.service.ApiCardIdService;
import com.hmit.kernespring.modules.data_management.service.DataDisabilityCertificateService;
import com.hmit.kernespring.modules.data_management.service.DisabilityCertificateSyncDataService;
import org.joda.time.format.DateTimeFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

// 生活补助金异常数据比对
@Component("blcjroneLivingAllowanceTask")
public class BLCjroneLivingAllowanceTask implements ITask {

    private Logger logger = LoggerFactory.getLogger(getClass());
    @Autowired
    private APIService apiService;
    @Autowired
    private DataDisabilityCertificateService dataDisabilityCertificateService;
    @Autowired
    private DisabilityCertificateSyncDataService disabilityCertificateSyncDataService;
    @Autowired
    private LivingAllowanceSyncDataService livingAllowanceSyncDataService;
    @Autowired
    private CjroneblLivingAllowanceService cjroneblLivingAllowanceService;
    @Autowired
    private ApiCardIdService apiCardIdService;


    @Override
    public void run(String params) throws InterruptedException {

        logger.debug("生活补助金数据比对 定时任务正在执行....");

        // 清除本月的比对数据：
        LocalDate localDate = LocalDate.now();
        int year = localDate.getYear();
        int month = localDate.getMonthValue();
        String ym = year+"-"+month;
        Map<String,Object> deleteParam = new HashMap<>();
        deleteParam.put("ym",ym);
        livingAllowanceSyncDataService.removeByMap(deleteParam);

        // 遍历 生活补助金 享受名单
        List<CjroneblLivingAllowanceEntity> cjroneblLivingAllowanceEntities = cjroneblLivingAllowanceService.list();
        for(CjroneblLivingAllowanceEntity l : cjroneblLivingAllowanceEntities){

            // 构造需要保存的实体数据
            DateTimeFormatter dateTimeFormat= DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LivingAllowanceSyncDataEntity livingAllowanceSyncDataEntity = new LivingAllowanceSyncDataEntity();
            livingAllowanceSyncDataEntity.setName(l.getName());
            livingAllowanceSyncDataEntity.setIdCard(l.getIdCard());
            livingAllowanceSyncDataEntity.setStatus(1);
            livingAllowanceSyncDataEntity.setCreateTime(LocalDateTime.now().format(dateTimeFormat));
            livingAllowanceSyncDataEntity.setYm(ym);

            // 1、判断此人是否有残疾证
            Map<String,Object> disparam = new HashMap<>();
            disparam.put("id_card",l.getIdCard());
            List<DataDisabilityCertificateEntity> dl = (List<DataDisabilityCertificateEntity>)dataDisabilityCertificateService.listByMap(disparam);
            if(dl==null || dl.size()<=0){
                // 此人没有残疾证
                livingAllowanceSyncDataEntity.setStatus(0);
                livingAllowanceSyncDataEntity.setIsDisable(0);
                livingAllowanceSyncDataEntity.setExceptionReason("残疾人库无此人，无残疾证");
                livingAllowanceSyncDataService.save(livingAllowanceSyncDataEntity);
                continue;
            }

            // 2、判断户口迁移
            Map<String,Object> apiparam = new HashMap<>();
            apiparam.put("id_card",l.getIdCard());
            List<ApiCardIdEntity> al = (List<ApiCardIdEntity>)apiCardIdService.listByMap(apiparam);
            if(al!=null && al.size()>0){
                if(al.get(0).getQfjg().indexOf("北仑") == -1&&al.get(0).getQfjg().indexOf("大榭") == -1){
                    // 户口已迁移
                    livingAllowanceSyncDataEntity.setStatus(0);
                    livingAllowanceSyncDataEntity.setIsHkqy(1);
                    livingAllowanceSyncDataEntity.setExceptionReason("户口迁移："+al.get(0).getQfjg());
                    livingAllowanceSyncDataService.save(livingAllowanceSyncDataEntity);
                    continue;
                }
            }

            Map<String,Object> dsparam = new HashMap<>();
            dsparam.put("id_card",l.getIdCard());
            List<DisabilityCertificateSyncDataEntity> dsl = (List<DisabilityCertificateSyncDataEntity>)disabilityCertificateSyncDataService.listByMap(dsparam);

            if(dsl!=null && dsl.size()>0){

                // 3、判断是否死亡
                if(dsl.get(0).getIsDead()==1){
                    livingAllowanceSyncDataEntity.setStatus(0);
                    livingAllowanceSyncDataEntity.setIsDead(1);
                    livingAllowanceSyncDataEntity.setExceptionReason("已死亡");
                    livingAllowanceSyncDataService.save(livingAllowanceSyncDataEntity);
                    continue;
                }

                // 4、判断是否监禁
                if(dsl.get(0).getIsFx()==1){
                    livingAllowanceSyncDataEntity.setStatus(0);
                    livingAllowanceSyncDataEntity.setIsFx(1);
                    livingAllowanceSyncDataEntity.setExceptionReason("监禁");
                    livingAllowanceSyncDataService.save(livingAllowanceSyncDataEntity);
                    continue;
                }

                // 5、判断是否享受生活补贴

                //6、判断是否享受低保

                //7、判断是否享受职工基本养老

            }

            // 8、判断年龄变化




        }


    }
}
