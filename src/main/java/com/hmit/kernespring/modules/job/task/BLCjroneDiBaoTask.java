package com.hmit.kernespring.modules.job.task;

import com.hmit.kernespring.modules.app.service.APIService;
import com.hmit.kernespring.modules.data_management.entity.DataDeadinfoEntity;
import com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity;
import com.hmit.kernespring.modules.data_management.entity.DataLowSecurityEntity;
import com.hmit.kernespring.modules.data_management.entity.FxinfoEntity;
import com.hmit.kernespring.modules.data_management.service.DataDisabilityCertificateService;
import com.hmit.kernespring.modules.data_management.service.DataLowSecurityService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

// 每日基础数据比对 -- 3、低保信息相关接口
@Component("blcjroneDiBaoTask")
public class BLCjroneDiBaoTask implements ITask {

    private Logger logger = LoggerFactory.getLogger(getClass());
    @Autowired
    private APIService apiService;
    @Autowired
    private DataDisabilityCertificateService dataDisabilityCertificateService;
    @Autowired
    private DataLowSecurityService dataLowSecurityService;

    @Override
    public void run(String params) throws InterruptedException {

        // 清空低保数据
        List<DataLowSecurityEntity> dibaoinfoEntityList = dataLowSecurityService.list();
        for(DataLowSecurityEntity d : dibaoinfoEntityList){
            dataLowSecurityService.removeById(d.getId());
        }

        // 低保数据存在延迟性，只能查询上个月的数据
        logger.debug("北仑残疾人低保数据比对开始==================");
        // 1、 获取所有残疾人列表，遍历判断
        List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntityList = dataDisabilityCertificateService.list();
        for (DataDisabilityCertificateEntity d : dataDisabilityCertificateEntityList) {
            // 调用低保数据接口
            LocalDate ld = LocalDate.now().minusMonths(1); // 查询上个月的日期
            String years = ld.getYear()+"";
            int monthsAge = ld.getMonthValue();
            String month="";
            if(monthsAge<10){
                month="0"+monthsAge;
            }else{
                month=""+monthsAge;
            }
            Map<String, Object> doDiBaoInfo = apiService.queryDIBAOInfo(d.getIdCard(),years,month,null);
            if("00".equals(doDiBaoInfo.get("code").toString())) {
                // 判断返回数据的数量
                int datacount = Integer.parseInt(doDiBaoInfo.get("dataCount").toString().split("\\.")[0]);
                if(datacount>0){
                    // 将低保名单保存至 datalowsecurity
                    DataLowSecurityEntity dataLowSecurityEntity = new DataLowSecurityEntity();
                    dataLowSecurityEntity.setName(d.getName());
                    dataLowSecurityEntity.setIdCard(d.getIdCard());
                    dataLowSecurityEntity.setPersonnelInformationCategory(doDiBaoInfo.toString());
                    dataLowSecurityService.save(dataLowSecurityEntity);
                }

            }



        }

        logger.debug("北仑残疾人低保数据比对结束-------");




    }
}
