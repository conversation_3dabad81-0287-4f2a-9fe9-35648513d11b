package com.hmit.kernespring.modules.cjrone_bl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblHospitalizationAllowanceEntity;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblMedicalSupportEntity;

import java.util.Map;

import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-01-13 01:05:40
 */
public interface CjroneblHospitalizationAllowanceService extends IService<CjroneblHospitalizationAllowanceEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneblHospitalizationAllowanceEntity> queryExportData(Map<String, Object> params);

    // 电子盖章专用
    boolean updateById(CjroneblHospitalizationAllowanceEntity cjroneblHospitalizationAllowanceEntity);

    void updateByIdSign(CjroneblHospitalizationAllowanceEntity cjroneblHospitalizationAllowanceEntity);

    void updateAudioById(CjroneblHospitalizationAllowanceEntity cjroneblHospitalizationAllowanceEntity);
}

