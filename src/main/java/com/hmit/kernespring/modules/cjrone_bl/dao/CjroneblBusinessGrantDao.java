package com.hmit.kernespring.modules.cjrone_bl.dao;

import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblBusinessGrantEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-22 19:54:10
 */
@Mapper
public interface CjroneblBusinessGrantDao extends BaseMapper<CjroneblBusinessGrantEntity> {
    List<CjroneblBusinessGrantEntity> queryExportData(Map<String, Object> params);
	
}
