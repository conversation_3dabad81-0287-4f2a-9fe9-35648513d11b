package com.hmit.kernespring.modules.cjrone_bl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblLivingAllowanceEntity;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblRehabilitationSubsidyEntity;

import java.util.Map;

import java.util.List;

/**
 * 康复补助
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-13 16:03:17
 */
public interface CjroneblRehabilitationSubsidyService extends IService<CjroneblRehabilitationSubsidyEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneblRehabilitationSubsidyEntity> queryExportData(Map<String, Object> params);

    // 电子盖章专用
    boolean updateById(CjroneblRehabilitationSubsidyEntity cjroneblRehabilitationSubsidyEntity);

    void updateByIdSign(CjroneblRehabilitationSubsidyEntity cjroneblRehabilitationSubsidyEntity);

    void updateAudioById(CjroneblRehabilitationSubsidyEntity cjroneblRehabilitationSubsidyEntity);
}

