package com.hmit.kernespring.modules.cjrone_bl.service.impl;


import com.google.gson.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.cjrone_bl.dao.CjroneblDisabilityCertificateRedemptionDao;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblDisabilityCertificateRedemptionEntity;
import com.hmit.kernespring.modules.cjrone_bl.service.CjroneblDisabilityCertificateRedemptionService;


@Service("cjroneblDisabilityCertificateRedemptionService")
public class CjroneblDisabilityCertificateRedemptionServiceImpl extends ServiceImpl<CjroneblDisabilityCertificateRedemptionDao, CjroneblDisabilityCertificateRedemptionEntity> implements CjroneblDisabilityCertificateRedemptionService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneblDisabilityCertificateRedemptionDao cjroneblDisabilityCertificateRedemptionDao;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneblDisabilityCertificateRedemptionEntity cjroneblDisabilityCertificateRedemptionEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneblDisabilityCertificateRedemptionEntity.class);
        IPage<CjroneblDisabilityCertificateRedemptionEntity> page = this.page(
                new Query<CjroneblDisabilityCertificateRedemptionEntity>().getPage(params),
                new QueryWrapper<CjroneblDisabilityCertificateRedemptionEntity>()
            .eq(StringUtils.isNotBlank(cjroneblDisabilityCertificateRedemptionEntity.getId ()!=null && !"".equals(cjroneblDisabilityCertificateRedemptionEntity.getId ().toString())? cjroneblDisabilityCertificateRedemptionEntity.getId ().toString():null),"id", cjroneblDisabilityCertificateRedemptionEntity.getId ())
            .eq(StringUtils.isNotBlank(cjroneblDisabilityCertificateRedemptionEntity.getName ()!=null && !"".equals(cjroneblDisabilityCertificateRedemptionEntity.getName ().toString())? cjroneblDisabilityCertificateRedemptionEntity.getName ().toString():null),"name", cjroneblDisabilityCertificateRedemptionEntity.getName ())
            .eq(StringUtils.isNotBlank(cjroneblDisabilityCertificateRedemptionEntity.getIdCard ()!=null && !"".equals(cjroneblDisabilityCertificateRedemptionEntity.getIdCard ().toString())? cjroneblDisabilityCertificateRedemptionEntity.getIdCard ().toString():null),"id_card", cjroneblDisabilityCertificateRedemptionEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(cjroneblDisabilityCertificateRedemptionEntity.getDisableId ()!=null && !"".equals(cjroneblDisabilityCertificateRedemptionEntity.getDisableId ().toString())? cjroneblDisabilityCertificateRedemptionEntity.getDisableId ().toString():null),"disable_id", cjroneblDisabilityCertificateRedemptionEntity.getDisableId ())
            .eq(StringUtils.isNotBlank(cjroneblDisabilityCertificateRedemptionEntity.getDisabilityCategory ()!=null && !"".equals(cjroneblDisabilityCertificateRedemptionEntity.getDisabilityCategory ().toString())? cjroneblDisabilityCertificateRedemptionEntity.getDisabilityCategory ().toString():null),"disability_category", cjroneblDisabilityCertificateRedemptionEntity.getDisabilityCategory ())
            .eq(StringUtils.isNotBlank(cjroneblDisabilityCertificateRedemptionEntity.getDisabilityDegree ()!=null && !"".equals(cjroneblDisabilityCertificateRedemptionEntity.getDisabilityDegree ().toString())? cjroneblDisabilityCertificateRedemptionEntity.getDisabilityDegree ().toString():null),"disability_degree", cjroneblDisabilityCertificateRedemptionEntity.getDisabilityDegree ())
            .eq(StringUtils.isNotBlank(cjroneblDisabilityCertificateRedemptionEntity.getDisabilityInfo ()!=null && !"".equals(cjroneblDisabilityCertificateRedemptionEntity.getDisabilityInfo ().toString())? cjroneblDisabilityCertificateRedemptionEntity.getDisabilityInfo ().toString():null),"disability_info", cjroneblDisabilityCertificateRedemptionEntity.getDisabilityInfo ())
            .eq(StringUtils.isNotBlank(cjroneblDisabilityCertificateRedemptionEntity.getCompleteTime ()!=null && !"".equals(cjroneblDisabilityCertificateRedemptionEntity.getCompleteTime ().toString())? cjroneblDisabilityCertificateRedemptionEntity.getCompleteTime ().toString():null),"complete_time", cjroneblDisabilityCertificateRedemptionEntity.getCompleteTime ())
            .eq(StringUtils.isNotBlank(cjroneblDisabilityCertificateRedemptionEntity.getStatus ()!=null && !"".equals(cjroneblDisabilityCertificateRedemptionEntity.getStatus ().toString())? cjroneblDisabilityCertificateRedemptionEntity.getStatus ().toString():null),"status", cjroneblDisabilityCertificateRedemptionEntity.getStatus ())
            .eq(StringUtils.isNotBlank(cjroneblDisabilityCertificateRedemptionEntity.getCreateId ()!=null && !"".equals(cjroneblDisabilityCertificateRedemptionEntity.getCreateId ().toString())? cjroneblDisabilityCertificateRedemptionEntity.getCreateId ().toString():null),"create_id", cjroneblDisabilityCertificateRedemptionEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(cjroneblDisabilityCertificateRedemptionEntity.getCreateTime ()!=null && !"".equals(cjroneblDisabilityCertificateRedemptionEntity.getCreateTime ().toString())? cjroneblDisabilityCertificateRedemptionEntity.getCreateTime ().toString():null),"create_time", cjroneblDisabilityCertificateRedemptionEntity.getCreateTime ())
            .eq(StringUtils.isNotBlank(cjroneblDisabilityCertificateRedemptionEntity.getCreateName ()!=null && !"".equals(cjroneblDisabilityCertificateRedemptionEntity.getCreateName ().toString())? cjroneblDisabilityCertificateRedemptionEntity.getCreateName ().toString():null),"create_name", cjroneblDisabilityCertificateRedemptionEntity.getCreateName ())
            .eq(StringUtils.isNotBlank(cjroneblDisabilityCertificateRedemptionEntity.getVerifyTime ()!=null && !"".equals(cjroneblDisabilityCertificateRedemptionEntity.getVerifyTime ().toString())? cjroneblDisabilityCertificateRedemptionEntity.getVerifyTime ().toString():null),"verify_time", cjroneblDisabilityCertificateRedemptionEntity.getVerifyTime ())
            .eq(StringUtils.isNotBlank(cjroneblDisabilityCertificateRedemptionEntity.getMobilePhone ()!=null && !"".equals(cjroneblDisabilityCertificateRedemptionEntity.getMobilePhone ().toString())? cjroneblDisabilityCertificateRedemptionEntity.getMobilePhone ().toString():null),"mobile_phone", cjroneblDisabilityCertificateRedemptionEntity.getMobilePhone ())
        );
        return new PageUtils(page);
    }
    @Override
    public List<CjroneblDisabilityCertificateRedemptionEntity> queryExportData(Map<String, Object> params) {
            return cjroneblDisabilityCertificateRedemptionDao.queryExportData(params);
    }

}