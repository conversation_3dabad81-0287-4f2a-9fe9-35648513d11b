package com.hmit.kernespring.modules.cjrone_bl.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-12 16:24:08
 */
@Data
@TableName("cjronebl_zgjbyiliao")
public class CjroneblZgjbyiliaoEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
@TableId
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 姓名
	 */
@Excel(name = "姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 性别
	 */
@Excel(name = "性别", height = 20, width = 30, isImportField = "true_st")
private Integer sex;

@TableField(exist=false)
private String sexName;
	/**
	 * 年龄
	 */
@Excel(name = "年龄", height = 20, width = 30, isImportField = "true_st")
private Integer age;
	/**
	 * 残疾证号
	 */
@Excel(name = "残疾证号", height = 20, width = 30, isImportField = "true_st")
private String disableId;
	/**
	 * 联系电话
	 */
@Excel(name = "联系电话", height = 20, width = 30, isImportField = "true_st")
private String telephone;
	/**
	 * 联系地址
	 */
@Excel(name = "联系地址", height = 20, width = 30, isImportField = "true_st")
private String liveAddress;
	/**
	 * 参保身份
	 */
@Excel(name = "参保身份", height = 20, width = 30, isImportField = "true_st")
private String insuredStatus;
	/**
	 * 其它补助情况
	 */
@Excel(name = "其它补助情况", height = 20, width = 30, isImportField = "true_st")
private String otherSubsidy;
	/**
	 * 参保起止时间
	 */
@Excel(name = "参保起止时间", height = 20, width = 30, isImportField = "true_st")
private String seTime;
	/**
	 * 自缴费用
	 */
@Excel(name = "自缴费用", height = 20, width = 30, isImportField = "true_st")
private String payMoney;
	/**
	 * 补助金额
	 */
@Excel(name = "补助金额", height = 20, width = 30, isImportField = "true_st")
private String subsidyMoney;
	/**
	 * 创建人编号
	 */
@Excel(name = "创建人编号", height = 20, width = 30, isImportField = "true_st")
private Integer createId;
	/**
	 * 创建时间
	 */
@Excel(name = "创建时间", height = 20, width = 30, isImportField = "true_st")
private String createTime;
	/**
	 * 创建人姓名
	 */
@Excel(name = "创建人姓名", height = 20, width = 30, isImportField = "true_st")
private String createName;
	/**
	 * 状态
	 */
@Excel(name = "状态", height = 20, width = 30, isImportField = "true_st")
private String status;
	/**
	 * 
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String statusOptions;
	/**
	 * 
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String signStatus;
	/**
	 * 
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String signatureStatus;

}
