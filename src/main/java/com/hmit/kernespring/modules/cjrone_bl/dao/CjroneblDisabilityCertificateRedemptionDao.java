package com.hmit.kernespring.modules.cjrone_bl.dao;

import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblDisabilityCertificateRedemptionEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 残疾人证换领
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-10 17:20:23
 */
@Mapper
public interface CjroneblDisabilityCertificateRedemptionDao extends BaseMapper<CjroneblDisabilityCertificateRedemptionEntity> {
    List<CjroneblDisabilityCertificateRedemptionEntity> queryExportData(Map<String, Object> params);
	
}
