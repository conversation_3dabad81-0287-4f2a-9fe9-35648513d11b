package com.hmit.kernespring.modules.cjrone_bl.controller;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.time.LocalDate;
import java.util.*;

import java.io.OutputStream;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.cjrone.entity.CjroneSignatureEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneSignatureService;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblLivingAllowanceEntity;
import com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity;
import com.hmit.kernespring.modules.data_management.service.DataDisabilityCertificateService;
import com.hmit.kernespring.modules.matter_application.entity.CjroneWelfareMatterApplicationEntity;
import com.hmit.kernespring.modules.matter_application.service.CjroneWelfareMatterApplicationService;
import com.hmit.kernespring.modules.sys.controller.AbstractController;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.AcroFields;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfCopy;
import com.itextpdf.text.pdf.PdfImportedPage;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hmit.kernespring.modules.cjrone_bl.entity.Love24Entity;
import com.hmit.kernespring.modules.cjrone_bl.service.Love24Service;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 智慧爱心24小时
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-11-25 17:44:36
 */
@RestController
@RequestMapping("cjrone_bl/love24")
public class Love24Controller extends AbstractController {
    @Autowired
    private Love24Service love24Service;
    @Autowired
    private CjroneSignatureService cjroneSignatureService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private DataDisabilityCertificateService dataDisabilityCertificateService;

    @Autowired
    private CjroneWelfareMatterApplicationService cjroneWelfareMatterApplicationService;



    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("cjrone_bl:love24:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = love24Service.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("cjrone_bl:love24:info")
    public R info(@PathVariable("id") Integer id){
		Love24Entity love24 = love24Service.getById(id);

        return R.ok().put("love24", love24);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("cjrone_bl:love24:save")
    public R save(@RequestBody Love24Entity love24){

/*
        if("true".equals(love24.getFamilyLove()) ){
            love24.setType("3");
        }
*/
		love24Service.save(love24);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("cjrone_bl:love24:update")
    public R update(@RequestBody Love24Entity love24){
        // 保存审核的状态
        Love24Entity entity = love24Service.getById(love24.getId());
        System.out.println(new Gson().toJson(entity));
        if (entity != null){
            if (getUser().getRoleName() != null && getUser().getRoleName().indexOf("区残联") != -1){
                if ("8".equals(entity.getSignStatus())){
                    if (!"6".equals(entity.getSignatureStatus())){
                        return R.error().put("code",100).put("msg","审核未通过，区残联未电子公章！").put("applyId",love24.getId());
                    }else {
                        System.out.println("aaaaaaa");
                    }
                }else {
                    return R.error().put("code",100).put("msg","审核未通过，区残联未手签！").put("applyId",love24.getId());
                }

                //判断审核通过与退回
                if("1".equals(love24.getStatus())){
                    // 审核通过
                    love24.setStatus("8");  //通过

                    // 发送短信
                    DataDisabilityCertificateEntity dataDisabilityCertificateEntity=dataDisabilityCertificateService.getByIDCard(love24.getIdCard());
                    try{
                        HttpClient httpclient = new HttpClient();
                        String SerialNumber = "0000000"+String.valueOf(System.currentTimeMillis());
                        PostMethod post = new PostMethod("http://ums.zj165.com:8888/sms/Api/Send.do");
                        post.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET,"gbk");
                        post.addParameter("SpCode", "001715");
                        post.addParameter("LoginName", "canlian");
                        post.addParameter("Password", "uTWFXMGWoNfhr7");
                        post.addParameter("MessageContent", "【北仑区残疾人联合会】您的爱心24小时审批已经通过。");
                        post.addParameter("UserNumber", "18368404744");
                        post.addParameter("SerialNumber", SerialNumber);
                        post.addParameter("ScheduleTime", "");
                        post.addParameter("ExtendAccessNum", "");
                        post.addParameter("f", "1");
                        httpclient.executeMethod(post);

                    }catch (Exception e) {
                        e.printStackTrace();
                    }

                }else{
                    // 审核退回
                    love24.setStatus("12"); //区残联退回至街道
                }

            }
        }
        love24Service.updateAudioById(love24);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("cjrone_bl:love24:delete")
    public R delete(@RequestBody Integer[] ids){

        cjroneWelfareMatterApplicationService.remove(new QueryWrapper<CjroneWelfareMatterApplicationEntity>()
                .in("matter_id",ids).eq("matter_name","智慧爱心24小时"));
        love24Service.removeByIds(Arrays.asList(ids));
        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    @RequiresPermissions("cjrone_bl:love24:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = ""; //bxProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        // 此处的headRows、titleRows 依据导入表格具体的数据行决定
        params_import.setHeadRows(2);
        params_import.setTitleRows(1);
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<Love24Entity> love24List = new ArrayList<>();
        System.out.println("当前导入数据智慧爱心24小时条数：" + list.size());
        list.forEach(item ->{
            if (null != item.get("身份证") && !"".equals(item.get("身份证").toString()) || (null != item.get("身份证号") && !"".equals(item.get("身份证号").toString()))) {
                item.put("id",item.get(""));
                item.put("name",item.get("申请人姓名"));
                item.put("sex",item.get("性别"));
                item.put("disableId",item.get("残疾证号"));
                item.put("disableType",item.get("残疾类别"));
                item.put("guardian",item.get("监护人"));
                item.put("liveAddress",item.get("监护人"));
                item.put("telephone",item.get("联系电话"));
                item.put("relation1",item.get("与申请人关系1"));
                item.put("name1",item.get("姓名1"));
                item.put("idcard1",item.get("身份证号或残疾证号1"));
                item.put("tel1",item.get("联系电话1"));
                item.put("relation2",item.get("与申请人关系2"));
                item.put("name2",item.get("姓名2"));
                item.put("idcard2",item.get("身份证号或残疾证号2"));
                item.put("tel2",item.get("联系电话2"));
                item.put("relation3",item.get("与申请人关系3"));
                item.put("name3",item.get("姓名3"));
                item.put("idcard3",item.get("身份证号或残疾证号3"));
                item.put("tel3",item.get("联系电话3"));
                item.put("relation4",item.get("与申请人关系4"));
                item.put("name4",item.get("姓名4"));
                item.put("idcard4",item.get("身份证号或残疾证号4"));
                item.put("tel4",item.get("联系电话4"));
                item.put("type",item.get("1固定电话，2智能手机"));
                item.put("typeTelephone",item.get("申请电话号码"));
                item.put("relation5",item.get("与申请人关系5"));
                item.put("name5",item.get("姓名5"));
                item.put("tel5",item.get("联系电话5"));
                item.put("relation6",item.get("与申请人关系6"));
                item.put("name6",item.get("姓名6"));
                item.put("tel6",item.get("联系电话6"));
                item.put("relation7",item.get("与申请人关系7"));
                item.put("name7",item.get("姓名7"));
                item.put("tel7",item.get("联系电话7"));
                item.put("relation8",item.get("与申请人关系8"));
                item.put("name8",item.get("姓名8"));
                item.put("tel8",item.get("联系电话8"));
                item.put("createId",item.get("创建人编号"));
                item.put("createTime",item.get("创建时间"));
                item.put("createName",item.get("创建人姓名"));
                item.put("status",item.get("状态"));
                item.put("statusOptions",item.get(""));
                item.put("signStatus",item.get(""));
                item.put("signatureStatus",item.get(""));
                item.put("returnStatus",item.get("退回状态"));
                item.put("familyLove",item.get("亲情互助选项"));
                love24List.add(new Gson().fromJson(new Gson().toJson(item), Love24Entity.class));
            }
        });
        // 保存到数据库
        love24Service.saveBatch(love24List);



        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    @RequiresPermissions("cjrone_bl:love24:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<Love24Entity> love24EntityList = love24Service.queryExportData(mapArgs);

        love24EntityList.forEach(item ->{
            //开始处理性别
            if("1".equals(item.getSex())){
                item.setSex("男");
            }else{
                item.setSex("女");
            }
            if("0".equals(item.getType())){
                item.setType("固定电话+定制手机");
            }
            if("1".equals(item.getType())){
                item.setType("定制CDMA手机一部");
            }
            if("2".equals(item.getType())){
                item.setType("固定电话");
            }
            if("3".equals(item.getType())){
                item.setType("号码定位");
            }
            if("4".equals(item.getType())){
                item.setType("智能机手机定位");
            }
            if("5".equals(item.getType())){
                item.setType("购机优惠");
            }

            if("true".equals(item.getFamilyLove()) ){
                item.setFamilyLove("是");
            }else{
                item.setFamilyLove("否");
            }
        });
        ExportParams params = new ExportParams("智慧爱心24小时", null, "智慧爱心24小时");
        Workbook workbook = ExcelExportUtil.exportExcel(params, Love24Entity.class, love24EntityList);

        response.setContentType("application/vnd.ms-excel");

        //此处的fileName没有用，是用来迷惑人的，请查看前端的filename
        String fileName = "智慧爱心24小时" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }


    @RequestMapping("/printPDF/{id}")
    public R printPDF(@PathVariable("id") Integer id,@RequestParam("type") String type) throws IOException {
        System.out.print("id is :"+id);

        Map<String, Object> tmp_params = new HashMap<>();
        tmp_params.put("type","惠残事项智慧爱心24小时");
        tmp_params.put("status","1");
        tmp_params.put("type_id",id);
        List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
        if (alive_list.size()>0) {
            CjroneSignatureEntity cjroneSignatureEntity = alive_list.get(0);
            if ("电子公章".equals(type)){
                String tmp_file_name = System.currentTimeMillis()+"_"+cjroneSignatureEntity.getFileName();
                Files.copy(new File(cjroneSignatureEntity.getFileActUrl()).toPath(),new File(cjroneProperties.getTempFilePath()+tmp_file_name).toPath());

                return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", tmp_file_name).put("signId",cjroneSignatureEntity.getId());

            }else {
                return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", cjroneSignatureEntity.getFileName()).put("signId",cjroneSignatureEntity.getId());
            }
        }else {

            // 根据编号获得智慧爱心24小时
            Love24Entity love24Entity = love24Service.getById(id);

            // 原pdf模板路径
            String templatePath = cjroneProperties.getTempletePath()+"智慧爱心24小时.pdf";
            // 生成的新文件路径
            String newPDFPath = cjroneProperties.getSignaturePath()+"love24_"+love24Entity.getDisableId()+".pdf";

            // 获得待生成的实体文件
            Map<String, String> map =new HashMap<String, String>();
            map.put("name", love24Entity.getName()==null?"":love24Entity.getName());
            if(love24Entity.getSex()!=null){
                if("0".equals(love24Entity.getSex())){
                    map.put("sex","女");
                }else{
                    map.put("sex","男");
                }
            }else{
                map.put("sex","");
            }

            map.put("disabilityId",love24Entity.getDisableId()==null?"":love24Entity.getDisableId());
            map.put("disableType",love24Entity.getDisableType()==null?"":love24Entity.getDisableType());
            map.put("guardian",love24Entity.getGuardian()==null?"":love24Entity.getGuardian());
            map.put("liveAddress",love24Entity.getLiveAddress()==null?"":love24Entity.getLiveAddress());
            map.put("telephone",love24Entity.getTelephone()==null?"":love24Entity.getTelephone());

            map.put("relation1",love24Entity.getRelation1() ==null?"":love24Entity.getRelation1());
            map.put("name1",love24Entity.getName1()==null?"":love24Entity.getName1());
            map.put("idcard1",love24Entity.getIdcard1()==null?"":love24Entity.getIdcard1());
            map.put("tel1",love24Entity.getTel1()==null?"":love24Entity.getTel1());
            map.put("relation2",love24Entity.getRelation2() ==null?"":love24Entity.getRelation2());
            map.put("name2",love24Entity.getName2()==null?"":love24Entity.getName2());
            map.put("idcard2",love24Entity.getIdcard2()==null?"":love24Entity.getIdcard2());
            map.put("tel2",love24Entity.getTel2()==null?"":love24Entity.getTel2());
            map.put("relation3",love24Entity.getRelation3() ==null?"":love24Entity.getRelation3());
            map.put("name3",love24Entity.getName3()==null?"":love24Entity.getName3());
            map.put("idcard3",love24Entity.getIdcard3()==null?"":love24Entity.getIdcard3());
            map.put("tel3",love24Entity.getTel3()==null?"":love24Entity.getTel3());
            map.put("relation4",love24Entity.getRelation4() ==null?"":love24Entity.getRelation4());
            map.put("name4",love24Entity.getName4()==null?"":love24Entity.getName4());
            map.put("idcard4",love24Entity.getIdcard4()==null?"":love24Entity.getIdcard4());
            map.put("tel4",love24Entity.getTel4()==null?"":love24Entity.getTel4());

            // 判断是固定电话还是智能手机？
            if(love24Entity.getType()!=null && StringUtils.isNotBlank(love24Entity.getType())){

                // 移动申请项目都为多选
                if(love24Entity.getType().contains(",") || Integer.parseInt(love24Entity.getType())>= 10){
                    String [] typeLists =love24Entity.getType().split(",");
                    map.put("yidong0","");
                    map.put("yidong1","");
                    map.put("yidong2","");
                    map.put("yidong3","");

                    Arrays.stream(typeLists).forEach(item ->{
                        System.out.println("移动申请项目值： "+item);
                        //初始化设置
                        if("10".equals(item)){
                            //移动 智能手机定位
                            map.put("yidong0","√");
                        }
                        if("11".equals(item)){
                            //移动 购机优惠
                            map.put("yidong1","√");
                        }
                        if("12".equals(item)){
                            //移动 套餐优惠
                            map.put("yidong2","√");
                        }
                        if("13".equals(item)){
                            //移动 宽带电视
                            map.put("yidong3","√");
                        }
                    });
                    map.put("dianxin0","");
                    map.put("dianxin1","");
                    map.put("dianxin2","");
                    map.put("dianxin3","");
                    map.put("dianxin4","");
                    map.put("dianxinTelphone0","");
                    map.put("dianxinTelphone1","");
                    map.put("yidongTelphone",love24Entity.getTypeTelephone2()==null?"":love24Entity.getTypeTelephone2());

                }

                // 电信为单选，且值都小于10
                if(!love24Entity.getType().contains(",") && Integer.parseInt(love24Entity.getType())<10 ){
                    System.out.println("电信固定电话："+love24Entity.getDianxinTelephone());
                    map.put("dianxin0","");
                    map.put("dianxin1","");
                    map.put("dianxin2","");
                    map.put("dianxin3","");
                    map.put("dianxin4","");

                    if("0".equals(love24Entity.getType())){
                        //电信  固定电话 + 手机
                        map.put("dianxin0","√");
                    }
                    if("1".equals(love24Entity.getType())){
                        //电信 CMDA手机一部
                        map.put("dianxin1","√");
                    }
                    if("2".equals(love24Entity.getType())){
                        //电信 固定电话
                        map.put("dianxin2","√");
                    }
                    if("4".equals(love24Entity.getType())){
                        //电信 但手机用户
                        map.put("dianxin4","√");

                    }
                    if("3".equals(love24Entity.getType())){
                        //电信 号码定位
                        map.put("dianxin3","√");
                    }
                    map.put("yidong0","");
                    map.put("yidong1","");
                    map.put("yidong2","");
                    map.put("yidong3","");
                    map.put("yidongTelphone","");
                    map.put("dianxinTelphone1",love24Entity.getDianxinTelephone()==null?"":love24Entity.getDianxinTelephone());
                    map.put("dianxinTelphone0",love24Entity.getTypeTelephone()==null?"":love24Entity.getTypeTelephone());

                }
            }else{
                map.put("dianxin0","");
                map.put("dianxin1","");
                map.put("dianxin2","");
                map.put("dianxin3","");
                map.put("dianxin4","");
                map.put("yidong0","");
                map.put("yidong1","");
                map.put("yidong2","");
                map.put("yidong3","");
                map.put("dianxinTelphone0","");
                map.put("dianxinTelphone1","");
                map.put("yidongTelphone","");
            }

            // 电信是否勾选了号码定位功能
            if(love24Entity.getFamilyLove()!=null){
                if("true".equals(love24Entity.getFamilyLove())){
                    map.put("dianxin3","√");
                }
            }

            // 申请时间
            LocalDate today = LocalDate.now();
            map.put("applyDate",today.getYear()+"年"+today.getMonthValue() +"月"+ today.getDayOfMonth() +"日");

            FileOutputStream out;
            int num = 1;//页数
            ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
            try {
                out = new FileOutputStream(newPDFPath);// 输出流
                Document doc = new Document();   //新建一个文档
                PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                doc.open();

                for (int i = 0; i < num; i++) {
                    bos[i] = new ByteArrayOutputStream();
                    System.out.println("templatePath--->"+templatePath);
                    PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                    PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                    AcroFields form = stamper.getAcroFields(); //获取文本域
                    // BaseFont
                    // 1、使用iTextAsian.jar中的字体
                    // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                    // 2、使用Windows系统字体(TrueType)
                    // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                    // 3、使用资源字体(ClassPath)
                    // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                    BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                    java.util.Iterator<String> it = form.getFields().keySet().iterator();
                    while (it.hasNext()) {
                        String name = it.next();
                        System.out.println(name + ":"+map.get(name));
                        form.setFieldProperty(name,"textfont",font,null);
                        // form.addSubstitutionFont(font);
                        form.setField(name, map.get(name));
                    }

                    stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                    stamper.close();

                }
                PdfImportedPage page = null;
                for (int i = 0; i < num; i++) {
                    page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                    copy.addPage(page);
                }
                doc.close();
                out.close();
            } catch (IOException e) {
                System.out.println("导出异常");
                System.out.println(e);
            } catch (DocumentException e) {
                System.out.println("文档异常");
            }

            CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
            cjroneSignature.setUrl("/love24_" + love24Entity.getDisableId() + ".pdf");
            cjroneSignature.setFileActUrl(newPDFPath);
            cjroneSignature.setCreateDate(new Date());
            cjroneSignature.setCreateId(getUserId());
            cjroneSignature.setType("惠残事项智慧爱心24小时");
            cjroneSignature.setTypeId(love24Entity.getId());
            cjroneSignature.setFileName("love24_" + love24Entity.getDisableId() + ".pdf");
            cjroneSignature.setAccountId(getUserId().toString());
            cjroneSignature.setAccountName(getUser().getUsername());
            cjroneSignature.setStatus("1");

            cjroneSignatureService.save(cjroneSignature);

            return R.ok().put("fileUrl",cjroneSignature.getUrl());
        }
    }


}
