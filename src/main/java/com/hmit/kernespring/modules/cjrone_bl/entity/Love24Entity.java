package com.hmit.kernespring.modules.cjrone_bl.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 智慧爱心24小时
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-11-25 17:44:36
 */
@Data
@TableName("cjronebl_love24")
public class Love24Entity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 *
	 */

@TableId
	//@ApiModelProperty(value = "")
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 申请人姓名
	 */
	//@ApiModelProperty(value = "申请人姓名")
@Excel(name = "申请人姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 性别
	 */
	//@ApiModelProperty(value = "性别")
@Excel(name = "性别", height = 20, width = 30, isImportField = "true_st")
private String sex;
	/**
	 * 残疾证号
	 */
	//ApiModelProperty(value = "残疾证号")
@Excel(name = "残疾证号", height = 20, width = 30, isImportField = "true_st")
private String disableId;

	// 身份证号码
	@Excel(name = "身份证号", height = 20, width = 30, isImportField = "true_st")
	private String idCard;

	/**
	 * 残疾类别
	 */
	//@ApiModelProperty(value = "残疾类别")
@Excel(name = "残疾类别", height = 20, width = 30, isImportField = "true_st")
private String disableType;
	/**
	 * 监护人
	 */
	//@ApiModelProperty(value = "监护人")
@Excel(name = "监护人", height = 20, width = 30, isImportField = "true_st")
private String guardian;
	/**
	 * 监护人
	 */
	//@ApiModelProperty(value = "监护人")
@Excel(name = "监护人", height = 20, width = 30, isImportField = "true_st")
private String liveAddress;
	/**
	 * 联系电话
	 */
	//@ApiModelProperty(value = "联系电话")
@Excel(name = "联系电话", height = 20, width = 30, isImportField = "true_st")
private String telephone;
	/**
	 * 与申请人关系1
	 */
	//@ApiModelProperty(value = "与申请人关系1")
@Excel(name = "与申请人关系1", height = 20, width = 30, isImportField = "true_st")
private String relation1;
	/**
	 * 姓名1
	 */
	//@ApiModelProperty(value = "姓名1")
@Excel(name = "姓名1", height = 20, width = 30, isImportField = "true_st")
private String name1;
	/**
	 * 身份证号或残疾证号1
	 */
	//@ApiModelProperty(value = "身份证号或残疾证号1")
@Excel(name = "身份证号或残疾证号1", height = 20, width = 30, isImportField = "true_st")
private String idcard1;
	/**
	 * 联系电话1
	 */
	//@ApiModelProperty(value = "联系电话1")
@Excel(name = "联系电话1", height = 20, width = 30, isImportField = "true_st")
private String tel1;
	/**
	 * 与申请人关系2
	 */
	//@ApiModelProperty(value = "与申请人关系2")
@Excel(name = "与申请人关系2", height = 20, width = 30, isImportField = "true_st")
private String relation2;
	/**
	 * 姓名2
	 */
	//@ApiModelProperty(value = "姓名2")
@Excel(name = "姓名2", height = 20, width = 30, isImportField = "true_st")
private String name2;
	/**
	 * 身份证号或残疾证号2
	 */
	//@ApiModelProperty(value = "身份证号或残疾证号2")
@Excel(name = "身份证号或残疾证号2", height = 20, width = 30, isImportField = "true_st")
private String idcard2;
	/**
	 * 联系电话2
	 */
	//@ApiModelProperty(value = "联系电话2")
@Excel(name = "联系电话2", height = 20, width = 30, isImportField = "true_st")
private String tel2;
	/**
	 * 与申请人关系3
	 */
	//@ApiModelProperty(value = "与申请人关系3")
@Excel(name = "与申请人关系3", height = 20, width = 30, isImportField = "true_st")
private String relation3;
	/**
	 * 姓名3
	 */
	//@ApiModelProperty(value = "姓名3")
@Excel(name = "姓名3", height = 20, width = 30, isImportField = "true_st")
private String name3;
	/**
	 * 身份证号或残疾证号3
	 */
	//@ApiModelProperty(value = "身份证号或残疾证号3")
@Excel(name = "身份证号或残疾证号3", height = 20, width = 30, isImportField = "true_st")
private String idcard3;
	/**
	 * 联系电话3
	 */
	//@ApiModelProperty(value = "联系电话3")
@Excel(name = "联系电话3", height = 20, width = 30, isImportField = "true_st")
private String tel3;
	/**
	 * 与申请人关系4
	 */
	//@ApiModelProperty(value = "与申请人关系4")
@Excel(name = "与申请人关系4", height = 20, width = 30, isImportField = "true_st")
private String relation4;
	/**
	 * 姓名4
	 */
	//@ApiModelProperty(value = "姓名4")
@Excel(name = "姓名4", height = 20, width = 30, isImportField = "true_st")
private String name4;
	/**
	 * 身份证号或残疾证号4
	 */
	//@ApiModelProperty(value = "身份证号或残疾证号4")
@Excel(name = "身份证号或残疾证号4", height = 20, width = 30, isImportField = "true_st")
private String idcard4;
	/**
	 * 联系电话4
	 */
	//@ApiModelProperty(value = "联系电话4")
@Excel(name = "联系电话4", height = 20, width = 30, isImportField = "true_st")
private String tel4;
	/**
	 * 0 固定电话+定制手机；
	 * 1 定制CDMA手机一部；
	 * 2 固定电话；
	 * 3 号码定位；
	 * 4 智能机手机定位；
	 * 5 购机优惠；
	 */
@Excel(name = "申请项目类型", height = 20, width = 30, isImportField = "true_st")
private String type;
	/**
	 * 申请电话号码
	 */
	//@ApiModelProperty(value = "申请电话号码")
@Excel(name = "申请电话号码", height = 20, width = 30, isImportField = "true_st")
//电信手机号码
private String typeTelephone;

//移动手机号码
private String typeTelephone2;

//电信固定电话
private String dianxinTelephone;
	/**
	 * 与申请人关系5
	 */
	//@ApiModelProperty(value = "与申请人关系5")
@Excel(name = "与申请人关系5", height = 20, width = 30, isImportField = "true_st")
private String relation5;
	/**
	 * 姓名5
	 */
	//@ApiModelProperty(value = "姓名5")
@Excel(name = "姓名5", height = 20, width = 30, isImportField = "true_st")
private String name5;
	/**
	 * 联系电话5
	 */
	//@ApiModelProperty(value = "联系电话5")
@Excel(name = "联系电话5", height = 20, width = 30, isImportField = "true_st")
private String tel5;
	/**
	 * 与申请人关系6
	 */
	//@ApiModelProperty(value = "与申请人关系6")
@Excel(name = "与申请人关系6", height = 20, width = 30, isImportField = "true_st")
private String relation6;
	/**
	 * 姓名6
	 */
	//@ApiModelProperty(value = "姓名6")
@Excel(name = "姓名6", height = 20, width = 30, isImportField = "true_st")
private String name6;
	/**
	 * 联系电话6
	 */
	//@ApiModelProperty(value = "联系电话6")
@Excel(name = "联系电话6", height = 20, width = 30, isImportField = "true_st")
private String tel6;
	/**
	 * 与申请人关系7
	 */
	//@ApiModelProperty(value = "与申请人关系7")
@Excel(name = "与申请人关系7", height = 20, width = 30, isImportField = "true_st")
private String relation7;
	/**
	 * 姓名7
	 */
	//@ApiModelProperty(value = "姓名7")
@Excel(name = "姓名7", height = 20, width = 30, isImportField = "true_st")
private String name7;
	/**
	 * 联系电话7
	 */
	//@ApiModelProperty(value = "联系电话7")
@Excel(name = "联系电话7", height = 20, width = 30, isImportField = "true_st")
private String tel7;
	/**
	 * 与申请人关系8
	 */
	//@ApiModelProperty(value = "与申请人关系8")
@Excel(name = "与申请人关系8", height = 20, width = 30, isImportField = "true_st")
private String relation8;
	/**
	 * 姓名8
	 */
	//@ApiModelProperty(value = "姓名8")
@Excel(name = "姓名8", height = 20, width = 30, isImportField = "true_st")
private String name8;
	/**
	 * 联系电话8
	 */
	//@ApiModelProperty(value = "联系电话8")
@Excel(name = "联系电话8", height = 20, width = 30, isImportField = "true_st")
private String tel8;
	/**
	 * 创建人编号
	 */
	//@ApiModelProperty(value = "创建人编号")
//@Excel(name = "创建人编号", height = 20, width = 30, isImportField = "true_st")
private Integer createId;
	/**
	 * 创建时间
	 */
	//@ApiModelProperty(value = "创建时间")
@Excel(name = "创建时间", height = 20, width = 30, isImportField = "true_st")
private String createTime;
	/**
	 * 创建人姓名
	 */
	//@ApiModelProperty(value = "创建人姓名")
@Excel(name = "创建人姓名", height = 20, width = 30, isImportField = "true_st")
private String createName;
	/**
	 * 状态
	 */
	//@ApiModelProperty(value = "状态")
//@Excel(name = "状态", height = 20, width = 30, isImportField = "true_st")
private String status;
	/**
	 *
	 */
	//@ApiModelProperty(value = "")
private String statusOptions;
	/**
	 *
	 */
	//@ApiModelProperty(value = "")
private String signStatus;
	/**
	 *
	 */
	//@ApiModelProperty(value = "")
private String signatureStatus;
	/**
	 * 退回状态
	 */
	//@ApiModelProperty(value = "退回状态")
//@Excel(name = "退回状态", height = 20, width = 30, isImportField = "true_st")
private Integer returnStatus;
	/**
	 * 是否禁用
	 * 异常数据（户籍迁出，死亡人员） -- 禁用之后状态设置成1，正常为0
	 */
	//@ApiModelProperty(value = "退回状态")
//@Excel(name = "退回状态", height = 20, width = 30, isImportField = "true_st")
private Integer isDisable;
	/**
	 * 号码定位
	 */
@Excel(name = "号码定位", height = 20, width = 30, isImportField = "true_st")
private String familyLove;

@Excel(name = "运营商", height = 20, width = 30, isImportField = "true_st")
private String applicationType;

@TableField(exist=false)
private String error;

//查询使用
@TableField(exist=false)
private String startDay;

@TableField(exist=false)
private String endDay;

}
