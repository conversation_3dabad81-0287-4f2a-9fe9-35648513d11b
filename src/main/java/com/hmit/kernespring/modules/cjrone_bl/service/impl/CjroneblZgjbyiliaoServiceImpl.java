package com.hmit.kernespring.modules.cjrone_bl.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblZgjbyanglaoEntity;
import com.hmit.kernespring.modules.matter_application.dao.CjroneWelfareMatterApplicationDao;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.cjrone_bl.dao.CjroneblZgjbyiliaoDao;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblZgjbyiliaoEntity;
import com.hmit.kernespring.modules.cjrone_bl.service.CjroneblZgjbyiliaoService;


@Service("cjroneblZgjbyiliaoService")
public class CjroneblZgjbyiliaoServiceImpl extends ServiceImpl<CjroneblZgjbyiliaoDao, CjroneblZgjbyiliaoEntity> implements CjroneblZgjbyiliaoService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneblZgjbyiliaoDao cjroneblZgjbyiliaoDao;
    @Autowired
    private CjroneWelfareMatterApplicationDao cjroneWelfareMatterApplicationDao;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneblZgjbyiliaoEntity cjroneblZgjbyiliaoEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneblZgjbyiliaoEntity.class);
        IPage<CjroneblZgjbyiliaoEntity> page = this.page(
                new Query<CjroneblZgjbyiliaoEntity>().getPage(params),
                new QueryWrapper<CjroneblZgjbyiliaoEntity>()
            .eq(StringUtils.isNotBlank(cjroneblZgjbyiliaoEntity.getId ()!=null && !"".equals(cjroneblZgjbyiliaoEntity.getId ().toString())? cjroneblZgjbyiliaoEntity.getId ().toString():null),"id", cjroneblZgjbyiliaoEntity.getId ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyiliaoEntity.getName ()!=null && !"".equals(cjroneblZgjbyiliaoEntity.getName ().toString())? cjroneblZgjbyiliaoEntity.getName ().toString():null),"name", cjroneblZgjbyiliaoEntity.getName ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyiliaoEntity.getSex ()!=null && !"".equals(cjroneblZgjbyiliaoEntity.getSex ().toString())? cjroneblZgjbyiliaoEntity.getSex ().toString():null),"sex", cjroneblZgjbyiliaoEntity.getSex ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyiliaoEntity.getAge ()!=null && !"".equals(cjroneblZgjbyiliaoEntity.getAge ().toString())? cjroneblZgjbyiliaoEntity.getAge ().toString():null),"age", cjroneblZgjbyiliaoEntity.getAge ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyiliaoEntity.getDisableId ()!=null && !"".equals(cjroneblZgjbyiliaoEntity.getDisableId ().toString())? cjroneblZgjbyiliaoEntity.getDisableId ().toString():null),"disable_id", cjroneblZgjbyiliaoEntity.getDisableId ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyiliaoEntity.getTelephone ()!=null && !"".equals(cjroneblZgjbyiliaoEntity.getTelephone ().toString())? cjroneblZgjbyiliaoEntity.getTelephone ().toString():null),"telephone", cjroneblZgjbyiliaoEntity.getTelephone ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyiliaoEntity.getLiveAddress ()!=null && !"".equals(cjroneblZgjbyiliaoEntity.getLiveAddress ().toString())? cjroneblZgjbyiliaoEntity.getLiveAddress ().toString():null),"live_address", cjroneblZgjbyiliaoEntity.getLiveAddress ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyiliaoEntity.getInsuredStatus ()!=null && !"".equals(cjroneblZgjbyiliaoEntity.getInsuredStatus ().toString())? cjroneblZgjbyiliaoEntity.getInsuredStatus ().toString():null),"insured_status", cjroneblZgjbyiliaoEntity.getInsuredStatus ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyiliaoEntity.getOtherSubsidy ()!=null && !"".equals(cjroneblZgjbyiliaoEntity.getOtherSubsidy ().toString())? cjroneblZgjbyiliaoEntity.getOtherSubsidy ().toString():null),"other_subsidy", cjroneblZgjbyiliaoEntity.getOtherSubsidy ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyiliaoEntity.getSeTime ()!=null && !"".equals(cjroneblZgjbyiliaoEntity.getSeTime ().toString())? cjroneblZgjbyiliaoEntity.getSeTime ().toString():null),"se_time", cjroneblZgjbyiliaoEntity.getSeTime ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyiliaoEntity.getPayMoney ()!=null && !"".equals(cjroneblZgjbyiliaoEntity.getPayMoney ().toString())? cjroneblZgjbyiliaoEntity.getPayMoney ().toString():null),"pay_money", cjroneblZgjbyiliaoEntity.getPayMoney ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyiliaoEntity.getSubsidyMoney ()!=null && !"".equals(cjroneblZgjbyiliaoEntity.getSubsidyMoney ().toString())? cjroneblZgjbyiliaoEntity.getSubsidyMoney ().toString():null),"subsidy_money", cjroneblZgjbyiliaoEntity.getSubsidyMoney ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyiliaoEntity.getCreateId ()!=null && !"".equals(cjroneblZgjbyiliaoEntity.getCreateId ().toString())? cjroneblZgjbyiliaoEntity.getCreateId ().toString():null),"create_id", cjroneblZgjbyiliaoEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyiliaoEntity.getCreateTime ()!=null && !"".equals(cjroneblZgjbyiliaoEntity.getCreateTime ().toString())? cjroneblZgjbyiliaoEntity.getCreateTime ().toString():null),"create_time", cjroneblZgjbyiliaoEntity.getCreateTime ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyiliaoEntity.getCreateName ()!=null && !"".equals(cjroneblZgjbyiliaoEntity.getCreateName ().toString())? cjroneblZgjbyiliaoEntity.getCreateName ().toString():null),"create_name", cjroneblZgjbyiliaoEntity.getCreateName ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyiliaoEntity.getStatus ()!=null && !"".equals(cjroneblZgjbyiliaoEntity.getStatus ().toString())? cjroneblZgjbyiliaoEntity.getStatus ().toString():null),"status", cjroneblZgjbyiliaoEntity.getStatus ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyiliaoEntity.getStatusOptions ()!=null && !"".equals(cjroneblZgjbyiliaoEntity.getStatusOptions ().toString())? cjroneblZgjbyiliaoEntity.getStatusOptions ().toString():null),"status_options", cjroneblZgjbyiliaoEntity.getStatusOptions ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyiliaoEntity.getSignStatus ()!=null && !"".equals(cjroneblZgjbyiliaoEntity.getSignStatus ().toString())? cjroneblZgjbyiliaoEntity.getSignStatus ().toString():null),"sign_status", cjroneblZgjbyiliaoEntity.getSignStatus ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyiliaoEntity.getSignatureStatus ()!=null && !"".equals(cjroneblZgjbyiliaoEntity.getSignatureStatus ().toString())? cjroneblZgjbyiliaoEntity.getSignatureStatus ().toString():null),"signature_status", cjroneblZgjbyiliaoEntity.getSignatureStatus ())
        );

        // 开始为状态赋值
        page.getRecords().forEach( item -> {
            if ("1".equals(item.getStatus())){
                item.setStatus("申请人待手签");
            }else if ("2".equals(item.getStatus())){
                item.setStatus("街道待审核");
            }else if ("6".equals(item.getStatus())){
                item.setStatus("区残联经办人待审核");
            }else if ("7".equals(item.getStatus()) ){
                item.setStatus("区残联负责人待审核");
            } else if ("8".equals(item.getStatus())){
                item.setStatus("通过");
            }else if ("0".equals(item.getStatus())){
                item.setStatus("禁用");
            }else if("12".equals(item.getStatus())){
                item.setStatus("退回");  // 区残联退回至街道
            }

            if("1".equals(item.getSignStatus())){
                item.setSignStatus("申请人待手签");
            }else if("2".equals(item.getSignStatus())){
                item.setSignStatus("镇街道待手签");
            }else if("6".equals(item.getSignStatus())){
                item.setSignStatus("区残联经办人待手签");
            }else if("7".equals(item.getSignStatus())){
                item.setSignStatus("区残联负责人待手签");
            }else if("8".equals(item.getSignStatus())){
                item.setSignStatus("完成手签");
            }

            if("1".equals(item.getSignatureStatus())){
                item.setSignatureStatus("无");
            }else if("2".equals(item.getSignatureStatus())){
                item.setSignatureStatus("镇街道待电子签章");
            }else if("4".equals(item.getSignatureStatus())){
                item.setSignatureStatus("民政待电子签章");
            }else if("5".equals(item.getSignatureStatus())){
                item.setSignatureStatus("区残联待电子签章");
            }else if("6".equals(item.getSignatureStatus())){
                item.setSignatureStatus("完成电子签章");
            }

            //性别
            if(item.getSex()==0){
                item.setSexName("女");
            }else{
                item.setSexName("男");
            }

        });

        return new PageUtils(page);
    }
    @Override
    public List<CjroneblZgjbyiliaoEntity> queryExportData(Map<String, Object> params) {
            return cjroneblZgjbyiliaoDao.queryExportData(params);
    }

    @Override
    public void updateByIdSign(CjroneblZgjbyiliaoEntity cjroneblZgjbyiliaoEntity) {
        super.updateById(cjroneblZgjbyiliaoEntity);
    }

    @Override
    public void updateAudioById(CjroneblZgjbyiliaoEntity cjroneblZgjbyiliaoEntity) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",cjroneblZgjbyiliaoEntity.getId());
        map.put("matter_name","职工基本医疗保险补助");
        map.put("verify_time",new Date());
        map.put("status",cjroneblZgjbyiliaoEntity.getStatus());
        map.put("statusOptions",cjroneblZgjbyiliaoEntity.getStatusOptions());
        if (cjroneblZgjbyiliaoEntity.getStatus() != null){
            cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        }
        super.updateById(cjroneblZgjbyiliaoEntity);
    }

    @Override
    public boolean updateById(CjroneblZgjbyiliaoEntity entity) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",entity.getId());
        map.put("matter_name","职工基本医疗保险补助");
        map.put("status",entity.getStatus());
        map.put("signatureStatus",entity.getSignatureStatus());  // 只需要更改电子印章的状态
        map.put("statusOptions",entity.getStatusOptions());

        cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        return super.updateById(entity);
    }


}