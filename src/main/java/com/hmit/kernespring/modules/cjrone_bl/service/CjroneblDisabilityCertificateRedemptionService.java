package com.hmit.kernespring.modules.cjrone_bl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblDisabilityCertificateRedemptionEntity;

import java.util.Map;

import java.util.List;

/**
 * 残疾人证换领
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-10 17:20:23
 */
public interface CjroneblDisabilityCertificateRedemptionService extends IService<CjroneblDisabilityCertificateRedemptionEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneblDisabilityCertificateRedemptionEntity> queryExportData(Map<String, Object> params);
}

