package com.hmit.kernespring.modules.cjrone_bl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblLivingAllowanceEntity;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblNursingSubsidyEntity;

import java.util.Map;

import java.util.List;

/**
 * 生活补助金表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-11 15:12:44
 */
public interface CjroneblLivingAllowanceService extends IService<CjroneblLivingAllowanceEntity> {

    PageUtils queryPage(Map<String, Object> params);

    List<CjroneblLivingAllowanceEntity> queryExportData(Map<String, Object> params);

    // 电子盖章专用
    boolean updateById(CjroneblLivingAllowanceEntity cjroneblLivingAllowanceEntity);

    void updateByIdSign(CjroneblLivingAllowanceEntity cjroneblLivingAllowanceEntity);

    void updateAudioById(CjroneblLivingAllowanceEntity cjroneblLivingAllowance);
}

