package com.hmit.kernespring.modules.cjrone_bl.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 医疗救助
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-01-13 01:05:40
 */
@Data
@TableName("cjronebl_medical_support")
public class CjroneblMedicalSupportEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
@TableId
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 姓名
	 */
@Excel(name = "姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 性别
	 */
@Excel(name = "性别", height = 20, width = 30, isImportField = "true_st")
private String sex;
	/**
	 * 出生年月
	 */
@Excel(name = "出生年月", height = 20, width = 30, isImportField = "true_st")
private String birthday;
	/**
	 * 残疾证号码
	 */
@Excel(name = "残疾证号码", height = 20, width = 30, isImportField = "true_st")
private String disableId;
	/**
	 * 住院时间
	 */
@Excel(name = "住院时间", height = 20, width = 30, isImportField = "true_st")
private String zyTime;
	/**
	 * 联系电话
	 */
@Excel(name = "联系电话", height = 20, width = 30, isImportField = "true_st")
private String telephone;
	/**
	 * 监护人姓名
	 */
@Excel(name = "监护人姓名", height = 20, width = 30, isImportField = "true_st")
private String guardianName;
	/**
	 * 家庭住址
	 */
@Excel(name = "家庭住址", height = 20, width = 30, isImportField = "true_st")
private String liveAddress;
	/**
	 * 创建人编号
	 */
@Excel(name = "创建人编号", height = 20, width = 30, isImportField = "true_st")
private Integer createId;
	/**
	 * 创建时间
	 */
@Excel(name = "创建时间", height = 20, width = 30, isImportField = "true_st")
private String createTime;
	/**
	 * 创建人姓名
	 */
@Excel(name = "创建人姓名", height = 20, width = 30, isImportField = "true_st")
private String createName;
	/**
	 * 状态
	 */
@Excel(name = "状态", height = 20, width = 30, isImportField = "true_st")
private String status;
	/**
	 * 
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String statusOptions;
	/**
	 * 
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String signStatus;
	/**
	 * 
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String signatureStatus;

}
