package com.hmit.kernespring.modules.cjrone_bl.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 生活补助金表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-11 15:12:44
 */
@Data
@TableName("cjronebl_living_allowance")
public class CjroneblLivingAllowanceEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
@TableId
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 姓名
	 */
@Excel(name = "姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 性别
	 */
@Excel(name = "性别", height = 20, width = 30, isImportField = "true_st")
private String sex;
	/**
	 * 出生年月
	 */
@Excel(name = "出生年月", height = 20, width = 30, isImportField = "true_st")
private String birthday;
	/**
	 * 年龄
	 */
@Excel(name = "年龄", height = 20, width = 30, isImportField = "true_st")
private Integer age;
	/**
	 * 残疾类别
	 */
@Excel(name = "残疾类别", height = 20, width = 30, isImportField = "true_st")
private String disabilityType;
	/**
	 * 残疾等级
	 */
@Excel(name = "残疾等级", height = 20, width = 30, isImportField = "true_st")
private String disabilityDegree;
	/**
	 * 残疾证编号
	 */
@Excel(name = "残疾证编号", height = 20, width = 30, isImportField = "true_st")
private String disableId;
	/**
	 * 身份证号码
	 */
@Excel(name = "身份证号码", height = 20, width = 30, isImportField = "true_st")
private String idCard;
	/**
	 * 详细地址
	 */
@Excel(name = "详细地址", height = 20, width = 30, isImportField = "true_st")
private String liveAddress;
	/**
	 * 联系电话
	 */
@Excel(name = "联系电话", height = 20, width = 30, isImportField = "true_st")
private String telephone;
	/**
	 * 家庭经济情况
	 */
@Excel(name = "家庭经济情况", height = 20, width = 30, isImportField = "true_st")
private String familyEconomy;
	/**
	 * 养老保险
	 */
@Excel(name = "养老保险", height = 20, width = 30, isImportField = "true_st")
private String pension;
	/**
	 * 补助金额
	 */
@Excel(name = "补助金额", height = 20, width = 30, isImportField = "true_st")
private String subsidyMoney;
	/**
	 * 申请补助类型
	 */
@Excel(name = "申请补助类型", height = 20, width = 30, isImportField = "true_st")
private String applyType;
	/**
	 * 创建人编号
	 */
@Excel(name = "创建人编号", height = 20, width = 30, isImportField = "true_st")
private Integer createId;
	/**
	 * 创建时间
	 */
@Excel(name = "创建时间", height = 20, width = 30, isImportField = "true_st")
private String createTime;
	/**
	 * 创建人姓名
	 */
@Excel(name = "创建人姓名", height = 20, width = 30, isImportField = "true_st")
private String createName;
	/**
	 * 状态
	 */
@Excel(name = "状态", height = 20, width = 30, isImportField = "true_st")
private String status;
	/**
	 * 
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String statusOptions;
	/**
	 * 
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String signStatus;
	/**
	 * 
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String signatureStatus;
	/**
	 * 退回状态
	 */
@Excel(name = "退回状态", height = 20, width = 30, isImportField = "true_st")
private Integer returnStatus;


}
