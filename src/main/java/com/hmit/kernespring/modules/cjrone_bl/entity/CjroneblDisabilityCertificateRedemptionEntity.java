package com.hmit.kernespring.modules.cjrone_bl.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 残疾人证换领
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-10 17:20:23
 */
@Data
@TableName("cjronebl_disability_certificate_redemption")
public class CjroneblDisabilityCertificateRedemptionEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 *
	 */

	@TableId
	@ApiModelProperty(value="")
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
	private Integer id;
	/**
	 * 姓名
	 */
	@ApiModelProperty(value="姓名")
	@Excel(name = "姓名", height = 20, width = 30, isImportField = "true_st")
	private String name;
	/**
	 * 身份证号码
	 */
	@ApiModelProperty(value="身份证号码")
	@Excel(name = "身份证号码", height = 20, width = 30, isImportField = "true_st")
	private String idCard;
	/**
	 * 残疾证号
	 */
	@ApiModelProperty(value="残疾证号")
	@Excel(name = "残疾证号", height = 20, width = 30, isImportField = "true_st")
	private String disableId;
	/**
	 * 残疾类别
	 */
	@ApiModelProperty(value="残疾类别")
	@Excel(name = "残疾类别", height = 20, width = 30, isImportField = "true_st")
	private String disabilityCategory;
	/**
	 * 残疾等级
	 */
	@ApiModelProperty(value="残疾等级")
	@Excel(name = "残疾等级", height = 20, width = 30, isImportField = "true_st")
	private String disabilityDegree;
	/**
	 * 残疾详情
	 */
	@ApiModelProperty(value="残疾详情")
	@Excel(name = "残疾详情", height = 20, width = 30, isImportField = "true_st")
	private String disabilityInfo;
	/**
	 * 持证时间
	 */
	@ApiModelProperty(value="持证时间")
	@Excel(name = "持证时间", height = 20, width = 30, isImportField = "true_st")
	private String completeTime;
	/**
	 * 状态
	 */
	@ApiModelProperty(value="状态")
	@Excel(name = "状态", height = 20, width = 30, isImportField = "true_st")
	private String status;
	/**
	 * 创建人编号
	 */
	@ApiModelProperty(value="创建人编号")
	@Excel(name = "创建人编号", height = 20, width = 30, isImportField = "true_st")
	private Integer createId;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value="创建时间")
	@Excel(name = "创建时间", height = 20, width = 30, isImportField = "true_st")
	private String createTime;
	/**
	 * 创建人姓名
	 */
	@ApiModelProperty(value="创建人姓名")
	@Excel(name = "创建人姓名", height = 20, width = 30, isImportField = "true_st")
	private String createName;
	/**
	 * 审核时间
	 */
	@ApiModelProperty(value="审核时间")
	@Excel(name = "审核时间", height = 20, width = 30, isImportField = "true_st")
	private Date verifyTime;
	/**
	 * 手机号
	 */
	@ApiModelProperty(value="手机号")
	@Excel(name = "手机号", height = 20, width = 30, isImportField = "true_st")
	private String mobilePhone;

}
