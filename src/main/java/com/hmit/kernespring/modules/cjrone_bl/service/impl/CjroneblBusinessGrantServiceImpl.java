package com.hmit.kernespring.modules.cjrone_bl.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblRehabilitationSubsidyEntity;
import com.hmit.kernespring.modules.matter_application.dao.CjroneWelfareMatterApplicationDao;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.cjrone_bl.dao.CjroneblBusinessGrantDao;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblBusinessGrantEntity;
import com.hmit.kernespring.modules.cjrone_bl.service.CjroneblBusinessGrantService;


@Service("cjroneblBusinessGrantService")
public class CjroneblBusinessGrantServiceImpl extends ServiceImpl<CjroneblBusinessGrantDao, CjroneblBusinessGrantEntity> implements CjroneblBusinessGrantService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneblBusinessGrantDao cjroneblBusinessGrantDao;
    @Autowired
    private CjroneWelfareMatterApplicationDao cjroneWelfareMatterApplicationDao;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneblBusinessGrantEntity cjroneblBusinessGrantEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneblBusinessGrantEntity.class);
        IPage<CjroneblBusinessGrantEntity> page = this.page(
                new Query<CjroneblBusinessGrantEntity>().getPage(params),
                new QueryWrapper<CjroneblBusinessGrantEntity>()
            .eq(StringUtils.isNotBlank(cjroneblBusinessGrantEntity.getId ()!=null && !"".equals(cjroneblBusinessGrantEntity.getId ().toString())? cjroneblBusinessGrantEntity.getId ().toString():null),"id", cjroneblBusinessGrantEntity.getId ())
            .eq(StringUtils.isNotBlank(cjroneblBusinessGrantEntity.getName ()!=null && !"".equals(cjroneblBusinessGrantEntity.getName ().toString())? cjroneblBusinessGrantEntity.getName ().toString():null),"name", cjroneblBusinessGrantEntity.getName ())
            .eq(StringUtils.isNotBlank(cjroneblBusinessGrantEntity.getSex ()!=null && !"".equals(cjroneblBusinessGrantEntity.getSex ().toString())? cjroneblBusinessGrantEntity.getSex ().toString():null),"sex", cjroneblBusinessGrantEntity.getSex ())
            .eq(StringUtils.isNotBlank(cjroneblBusinessGrantEntity.getAge ()!=null && !"".equals(cjroneblBusinessGrantEntity.getAge ().toString())? cjroneblBusinessGrantEntity.getAge ().toString():null),"age", cjroneblBusinessGrantEntity.getAge ())
            .eq(StringUtils.isNotBlank(cjroneblBusinessGrantEntity.getDisableId ()!=null && !"".equals(cjroneblBusinessGrantEntity.getDisableId ().toString())? cjroneblBusinessGrantEntity.getDisableId ().toString():null),"disable_id", cjroneblBusinessGrantEntity.getDisableId ())
            .eq(StringUtils.isNotBlank(cjroneblBusinessGrantEntity.getTelephone ()!=null && !"".equals(cjroneblBusinessGrantEntity.getTelephone ().toString())? cjroneblBusinessGrantEntity.getTelephone ().toString():null),"telephone", cjroneblBusinessGrantEntity.getTelephone ())
            .eq(StringUtils.isNotBlank(cjroneblBusinessGrantEntity.getFamilyEconomy ()!=null && !"".equals(cjroneblBusinessGrantEntity.getFamilyEconomy ().toString())? cjroneblBusinessGrantEntity.getFamilyEconomy ().toString():null),"family_economy", cjroneblBusinessGrantEntity.getFamilyEconomy ())
            .eq(StringUtils.isNotBlank(cjroneblBusinessGrantEntity.getEducationDegree ()!=null && !"".equals(cjroneblBusinessGrantEntity.getEducationDegree ().toString())? cjroneblBusinessGrantEntity.getEducationDegree ().toString():null),"education_degree", cjroneblBusinessGrantEntity.getEducationDegree ())
            .eq(StringUtils.isNotBlank(cjroneblBusinessGrantEntity.getLiveAddress ()!=null && !"".equals(cjroneblBusinessGrantEntity.getLiveAddress ().toString())? cjroneblBusinessGrantEntity.getLiveAddress ().toString():null),"live_address", cjroneblBusinessGrantEntity.getLiveAddress ())
            .eq(StringUtils.isNotBlank(cjroneblBusinessGrantEntity.getStartTime ()!=null && !"".equals(cjroneblBusinessGrantEntity.getStartTime ().toString())? cjroneblBusinessGrantEntity.getStartTime ().toString():null),"start_time", cjroneblBusinessGrantEntity.getStartTime ())
            .eq(StringUtils.isNotBlank(cjroneblBusinessGrantEntity.getManageAddress ()!=null && !"".equals(cjroneblBusinessGrantEntity.getManageAddress ().toString())? cjroneblBusinessGrantEntity.getManageAddress ().toString():null),"manage_address", cjroneblBusinessGrantEntity.getManageAddress ())
            .eq(StringUtils.isNotBlank(cjroneblBusinessGrantEntity.getManageRange ()!=null && !"".equals(cjroneblBusinessGrantEntity.getManageRange ().toString())? cjroneblBusinessGrantEntity.getManageRange ().toString():null),"manage_range", cjroneblBusinessGrantEntity.getManageRange ())
            .eq(StringUtils.isNotBlank(cjroneblBusinessGrantEntity.getIsCollege ()!=null && !"".equals(cjroneblBusinessGrantEntity.getIsCollege ().toString())? cjroneblBusinessGrantEntity.getIsCollege ().toString():null),"is_college", cjroneblBusinessGrantEntity.getIsCollege ())
            .eq(StringUtils.isNotBlank(cjroneblBusinessGrantEntity.getSubsidyMoney ()!=null && !"".equals(cjroneblBusinessGrantEntity.getSubsidyMoney ().toString())? cjroneblBusinessGrantEntity.getSubsidyMoney ().toString():null),"subsidy_money", cjroneblBusinessGrantEntity.getSubsidyMoney ())
            .eq(StringUtils.isNotBlank(cjroneblBusinessGrantEntity.getSubsidyReason ()!=null && !"".equals(cjroneblBusinessGrantEntity.getSubsidyReason ().toString())? cjroneblBusinessGrantEntity.getSubsidyReason ().toString():null),"subsidy_reason", cjroneblBusinessGrantEntity.getSubsidyReason ())
            .eq(StringUtils.isNotBlank(cjroneblBusinessGrantEntity.getIsEmployment ()!=null && !"".equals(cjroneblBusinessGrantEntity.getIsEmployment ().toString())? cjroneblBusinessGrantEntity.getIsEmployment ().toString():null),"is_employment", cjroneblBusinessGrantEntity.getIsEmployment ())
            .eq(StringUtils.isNotBlank(cjroneblBusinessGrantEntity.getIsFirsttime ()!=null && !"".equals(cjroneblBusinessGrantEntity.getIsFirsttime ().toString())? cjroneblBusinessGrantEntity.getIsFirsttime ().toString():null),"is_firsttime", cjroneblBusinessGrantEntity.getIsFirsttime ())
            .eq(StringUtils.isNotBlank(cjroneblBusinessGrantEntity.getIsSixManage ()!=null && !"".equals(cjroneblBusinessGrantEntity.getIsSixManage ().toString())? cjroneblBusinessGrantEntity.getIsSixManage ().toString():null),"is_six_manage", cjroneblBusinessGrantEntity.getIsSixManage ())
            .eq(StringUtils.isNotBlank(cjroneblBusinessGrantEntity.getCreateId ()!=null && !"".equals(cjroneblBusinessGrantEntity.getCreateId ().toString())? cjroneblBusinessGrantEntity.getCreateId ().toString():null),"create_id", cjroneblBusinessGrantEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(cjroneblBusinessGrantEntity.getCreateTime ()!=null && !"".equals(cjroneblBusinessGrantEntity.getCreateTime ().toString())? cjroneblBusinessGrantEntity.getCreateTime ().toString():null),"create_time", cjroneblBusinessGrantEntity.getCreateTime ())
            .eq(StringUtils.isNotBlank(cjroneblBusinessGrantEntity.getCreateName ()!=null && !"".equals(cjroneblBusinessGrantEntity.getCreateName ().toString())? cjroneblBusinessGrantEntity.getCreateName ().toString():null),"create_name", cjroneblBusinessGrantEntity.getCreateName ())
            .eq(StringUtils.isNotBlank(cjroneblBusinessGrantEntity.getStatus ()!=null && !"".equals(cjroneblBusinessGrantEntity.getStatus ().toString())? cjroneblBusinessGrantEntity.getStatus ().toString():null),"status", cjroneblBusinessGrantEntity.getStatus ())
            .eq(StringUtils.isNotBlank(cjroneblBusinessGrantEntity.getStatusOptions ()!=null && !"".equals(cjroneblBusinessGrantEntity.getStatusOptions ().toString())? cjroneblBusinessGrantEntity.getStatusOptions ().toString():null),"status_options", cjroneblBusinessGrantEntity.getStatusOptions ())
            .eq(StringUtils.isNotBlank(cjroneblBusinessGrantEntity.getSignStatus ()!=null && !"".equals(cjroneblBusinessGrantEntity.getSignStatus ().toString())? cjroneblBusinessGrantEntity.getSignStatus ().toString():null),"sign_status", cjroneblBusinessGrantEntity.getSignStatus ())
            .eq(StringUtils.isNotBlank(cjroneblBusinessGrantEntity.getSignatureStatus ()!=null && !"".equals(cjroneblBusinessGrantEntity.getSignatureStatus ().toString())? cjroneblBusinessGrantEntity.getSignatureStatus ().toString():null),"signature_status", cjroneblBusinessGrantEntity.getSignatureStatus ())
        );

        // 开始为状态赋值
        page.getRecords().forEach( item -> {
            if ("1".equals(item.getStatus())){
                item.setStatus("申请人待手签");
            }else if ("2".equals(item.getStatus())){
                item.setStatus("街道待审核");
            }else if ("6".equals(item.getStatus())){
                item.setStatus("区残联经办人待审核");
            }else if ("7".equals(item.getStatus()) ){
                item.setStatus("区残联负责人待审核");
            } else if ("8".equals(item.getStatus())){
                item.setStatus("通过");
            }else if ("0".equals(item.getStatus())){
                item.setStatus("禁用");
            }else if("12".equals(item.getStatus())){
                item.setStatus("退回");  // 区残联退回至街道
            }

            if("1".equals(item.getSignStatus())){
                item.setSignStatus("申请人待手签");
            }else if("2".equals(item.getSignStatus())){
                item.setSignStatus("镇街道待手签");
            }else if("6".equals(item.getSignStatus())){
                item.setSignStatus("区残联经办人待手签");
            }else if("7".equals(item.getSignStatus())){
                item.setSignStatus("区残联负责人待手签");
            }else if("8".equals(item.getSignStatus())){
                item.setSignStatus("完成手签");
            }

            if("1".equals(item.getSignatureStatus())){
                item.setSignatureStatus("无");
            }else if("2".equals(item.getSignatureStatus())){
                item.setSignatureStatus("镇街道待电子签章");
            }else if("4".equals(item.getSignatureStatus())){
                item.setSignatureStatus("民政待电子签章");
            }else if("5".equals(item.getSignatureStatus())){
                item.setSignatureStatus("区残联待电子签章");
            }else if("6".equals(item.getSignatureStatus())){
                item.setSignatureStatus("完成电子签章");
            }

            //性别
            if("0".equals(item.getSex())){
                item.setSex("女");
            }else{
                item.setSex("男");
            }

        });

        return new PageUtils(page);
    }
    @Override
    public List<CjroneblBusinessGrantEntity> queryExportData(Map<String, Object> params) {
            return cjroneblBusinessGrantDao.queryExportData(params);
    }

    @Override
    public void updateByIdSign(CjroneblBusinessGrantEntity cjroneblBusinessGrantEntity) {
        super.updateById(cjroneblBusinessGrantEntity);
    }

    @Override
    public void updateAudioById(CjroneblBusinessGrantEntity cjroneblBusinessGrantEntity) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",cjroneblBusinessGrantEntity.getId());
        map.put("matter_name","创业补助");
        map.put("verify_time",new Date());
        map.put("status",cjroneblBusinessGrantEntity.getStatus());
        map.put("statusOptions",cjroneblBusinessGrantEntity.getStatusOptions());
        if (cjroneblBusinessGrantEntity.getStatus() != null){
            cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        }
        super.updateById(cjroneblBusinessGrantEntity);
    }

    @Override
    public boolean updateById(CjroneblBusinessGrantEntity entity) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",entity.getId());
        map.put("matter_name","创业补助");
        map.put("status",entity.getStatus());
        map.put("signatureStatus",entity.getSignatureStatus());  // 只需要更改电子印章的状态
        map.put("statusOptions",entity.getStatusOptions());

        cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        return super.updateById(entity);
    }


}