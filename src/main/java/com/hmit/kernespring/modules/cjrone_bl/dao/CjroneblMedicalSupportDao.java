package com.hmit.kernespring.modules.cjrone_bl.dao;

import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblMedicalSupportEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 医疗救助
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-01-13 01:05:40
 */
@Mapper
public interface CjroneblMedicalSupportDao extends BaseMapper<CjroneblMedicalSupportEntity> {
    List<CjroneblMedicalSupportEntity> queryExportData(Map<String, Object> params);
	
}
