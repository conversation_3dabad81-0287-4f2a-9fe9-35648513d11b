package com.hmit.kernespring.modules.cjrone_bl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblLivingAllowanceEntity;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblZgjbyanglaoEntity;

import java.util.Map;

import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-12 11:25:34
 */
public interface CjroneblZgjbyanglaoService extends IService<CjroneblZgjbyanglaoEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneblZgjbyanglaoEntity> queryExportData(Map<String, Object> params);
    // 电子盖章专用
    boolean updateById(CjroneblZgjbyanglaoEntity cjroneblZgjbyanglaoEntity);
    void updateByIdSign(CjroneblZgjbyanglaoEntity cjroneblZgjbyanglaoEntity);

    void updateAudioById(CjroneblZgjbyanglaoEntity cjroneblZgjbyanglao);
}

