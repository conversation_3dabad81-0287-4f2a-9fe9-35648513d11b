package com.hmit.kernespring.modules.cjrone_bl.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblZgjbyiliaoEntity;
import com.hmit.kernespring.modules.matter_application.dao.CjroneWelfareMatterApplicationDao;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.cjrone_bl.dao.CjroneblCxjmyiliaoDao;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblCxjmyiliaoEntity;
import com.hmit.kernespring.modules.cjrone_bl.service.CjroneblCxjmyiliaoService;


@Service("cjroneblCxjmyiliaoService")
public class CjroneblCxjmyiliaoServiceImpl extends ServiceImpl<CjroneblCxjmyiliaoDao, CjroneblCxjmyiliaoEntity> implements CjroneblCxjmyiliaoService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneblCxjmyiliaoDao cjroneblCxjmyiliaoDao;
    @Autowired
    private CjroneWelfareMatterApplicationDao cjroneWelfareMatterApplicationDao;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneblCxjmyiliaoEntity cjroneblCxjmyiliaoEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneblCxjmyiliaoEntity.class);
        IPage<CjroneblCxjmyiliaoEntity> page = this.page(
                new Query<CjroneblCxjmyiliaoEntity>().getPage(params),
                new QueryWrapper<CjroneblCxjmyiliaoEntity>()
            .eq(StringUtils.isNotBlank(cjroneblCxjmyiliaoEntity.getId ()!=null && !"".equals(cjroneblCxjmyiliaoEntity.getId ().toString())? cjroneblCxjmyiliaoEntity.getId ().toString():null),"id", cjroneblCxjmyiliaoEntity.getId ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyiliaoEntity.getName ()!=null && !"".equals(cjroneblCxjmyiliaoEntity.getName ().toString())? cjroneblCxjmyiliaoEntity.getName ().toString():null),"name", cjroneblCxjmyiliaoEntity.getName ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyiliaoEntity.getSex ()!=null && !"".equals(cjroneblCxjmyiliaoEntity.getSex ().toString())? cjroneblCxjmyiliaoEntity.getSex ().toString():null),"sex", cjroneblCxjmyiliaoEntity.getSex ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyiliaoEntity.getAge ()!=null && !"".equals(cjroneblCxjmyiliaoEntity.getAge ().toString())? cjroneblCxjmyiliaoEntity.getAge ().toString():null),"age", cjroneblCxjmyiliaoEntity.getAge ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyiliaoEntity.getDisableId ()!=null && !"".equals(cjroneblCxjmyiliaoEntity.getDisableId ().toString())? cjroneblCxjmyiliaoEntity.getDisableId ().toString():null),"disable_id", cjroneblCxjmyiliaoEntity.getDisableId ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyiliaoEntity.getTelephone ()!=null && !"".equals(cjroneblCxjmyiliaoEntity.getTelephone ().toString())? cjroneblCxjmyiliaoEntity.getTelephone ().toString():null),"telephone", cjroneblCxjmyiliaoEntity.getTelephone ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyiliaoEntity.getLiveAddress ()!=null && !"".equals(cjroneblCxjmyiliaoEntity.getLiveAddress ().toString())? cjroneblCxjmyiliaoEntity.getLiveAddress ().toString():null),"live_address", cjroneblCxjmyiliaoEntity.getLiveAddress ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyiliaoEntity.getInsuredStatus ()!=null && !"".equals(cjroneblCxjmyiliaoEntity.getInsuredStatus ().toString())? cjroneblCxjmyiliaoEntity.getInsuredStatus ().toString():null),"insured_status", cjroneblCxjmyiliaoEntity.getInsuredStatus ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyiliaoEntity.getOtherSubsidy ()!=null && !"".equals(cjroneblCxjmyiliaoEntity.getOtherSubsidy ().toString())? cjroneblCxjmyiliaoEntity.getOtherSubsidy ().toString():null),"other_subsidy", cjroneblCxjmyiliaoEntity.getOtherSubsidy ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyiliaoEntity.getSeTime ()!=null && !"".equals(cjroneblCxjmyiliaoEntity.getSeTime ().toString())? cjroneblCxjmyiliaoEntity.getSeTime ().toString():null),"se_time", cjroneblCxjmyiliaoEntity.getSeTime ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyiliaoEntity.getPayMoney ()!=null && !"".equals(cjroneblCxjmyiliaoEntity.getPayMoney ().toString())? cjroneblCxjmyiliaoEntity.getPayMoney ().toString():null),"pay_money", cjroneblCxjmyiliaoEntity.getPayMoney ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyiliaoEntity.getSubsidyMoney ()!=null && !"".equals(cjroneblCxjmyiliaoEntity.getSubsidyMoney ().toString())? cjroneblCxjmyiliaoEntity.getSubsidyMoney ().toString():null),"subsidy_money", cjroneblCxjmyiliaoEntity.getSubsidyMoney ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyiliaoEntity.getCreateId ()!=null && !"".equals(cjroneblCxjmyiliaoEntity.getCreateId ().toString())? cjroneblCxjmyiliaoEntity.getCreateId ().toString():null),"create_id", cjroneblCxjmyiliaoEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyiliaoEntity.getCreateTime ()!=null && !"".equals(cjroneblCxjmyiliaoEntity.getCreateTime ().toString())? cjroneblCxjmyiliaoEntity.getCreateTime ().toString():null),"create_time", cjroneblCxjmyiliaoEntity.getCreateTime ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyiliaoEntity.getCreateName ()!=null && !"".equals(cjroneblCxjmyiliaoEntity.getCreateName ().toString())? cjroneblCxjmyiliaoEntity.getCreateName ().toString():null),"create_name", cjroneblCxjmyiliaoEntity.getCreateName ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyiliaoEntity.getStatus ()!=null && !"".equals(cjroneblCxjmyiliaoEntity.getStatus ().toString())? cjroneblCxjmyiliaoEntity.getStatus ().toString():null),"status", cjroneblCxjmyiliaoEntity.getStatus ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyiliaoEntity.getStatusOptions ()!=null && !"".equals(cjroneblCxjmyiliaoEntity.getStatusOptions ().toString())? cjroneblCxjmyiliaoEntity.getStatusOptions ().toString():null),"status_options", cjroneblCxjmyiliaoEntity.getStatusOptions ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyiliaoEntity.getSignStatus ()!=null && !"".equals(cjroneblCxjmyiliaoEntity.getSignStatus ().toString())? cjroneblCxjmyiliaoEntity.getSignStatus ().toString():null),"sign_status", cjroneblCxjmyiliaoEntity.getSignStatus ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyiliaoEntity.getSignatureStatus ()!=null && !"".equals(cjroneblCxjmyiliaoEntity.getSignatureStatus ().toString())? cjroneblCxjmyiliaoEntity.getSignatureStatus ().toString():null),"signature_status", cjroneblCxjmyiliaoEntity.getSignatureStatus ())
        );

        // 开始为状态赋值
        page.getRecords().forEach( item -> {
            if ("1".equals(item.getStatus())){
                item.setStatus("申请人待手签");
            }else if ("2".equals(item.getStatus())){
                item.setStatus("街道待审核");
            }else if ("6".equals(item.getStatus())){
                item.setStatus("区残联经办人待审核");
            }else if ("7".equals(item.getStatus()) ){
                item.setStatus("区残联负责人待审核");
            } else if ("8".equals(item.getStatus())){
                item.setStatus("通过");
            }else if ("0".equals(item.getStatus())){
                item.setStatus("禁用");
            }else if("12".equals(item.getStatus())){
                item.setStatus("退回");  // 区残联退回至街道
            }

            if("1".equals(item.getSignStatus())){
                item.setSignStatus("申请人待手签");
            }else if("2".equals(item.getSignStatus())){
                item.setSignStatus("镇街道待手签");
            }else if("6".equals(item.getSignStatus())){
                item.setSignStatus("区残联经办人待手签");
            }else if("7".equals(item.getSignStatus())){
                item.setSignStatus("区残联负责人待手签");
            }else if("8".equals(item.getSignStatus())){
                item.setSignStatus("完成手签");
            }

            if("1".equals(item.getSignatureStatus())){
                item.setSignatureStatus("无");
            }else if("2".equals(item.getSignatureStatus())){
                item.setSignatureStatus("镇街道待电子签章");
            }else if("4".equals(item.getSignatureStatus())){
                item.setSignatureStatus("民政待电子签章");
            }else if("5".equals(item.getSignatureStatus())){
                item.setSignatureStatus("区残联待电子签章");
            }else if("6".equals(item.getSignatureStatus())){
                item.setSignatureStatus("完成电子签章");
            }

            //性别
            if(item.getSex()==0){
                item.setSexName("女");
            }else{
                item.setSexName("男");
            }

        });

        return new PageUtils(page);
    }
    @Override
    public List<CjroneblCxjmyiliaoEntity> queryExportData(Map<String, Object> params) {
            return cjroneblCxjmyiliaoDao.queryExportData(params);
    }

    @Override
    public void updateByIdSign(CjroneblCxjmyiliaoEntity cjroneblCxjmyiliaoEntity) {
        super.updateById(cjroneblCxjmyiliaoEntity);
    }

    @Override
    public void updateAudioById(CjroneblCxjmyiliaoEntity cjroneblCxjmyiliaoEntity) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",cjroneblCxjmyiliaoEntity.getId());
        map.put("matter_name","城乡基本医疗保险补助");
        map.put("verify_time",new Date());
        map.put("status",cjroneblCxjmyiliaoEntity.getStatus());
        map.put("statusOptions",cjroneblCxjmyiliaoEntity.getStatusOptions());
        if (cjroneblCxjmyiliaoEntity.getStatus() != null){
            cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        }
        super.updateById(cjroneblCxjmyiliaoEntity);
    }

    @Override
    public boolean updateById(CjroneblCxjmyiliaoEntity entity) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",entity.getId());
        map.put("matter_name","城乡基本医疗保险补助");
        map.put("status",entity.getStatus());
        map.put("signatureStatus",entity.getSignatureStatus());  // 只需要更改电子印章的状态
        map.put("statusOptions",entity.getStatusOptions());

        cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        return super.updateById(entity);
    }


}