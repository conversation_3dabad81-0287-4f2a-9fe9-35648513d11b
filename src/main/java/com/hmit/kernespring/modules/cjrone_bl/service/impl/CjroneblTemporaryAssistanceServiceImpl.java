package com.hmit.kernespring.modules.cjrone_bl.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblLivingAllowanceEntity;
import com.hmit.kernespring.modules.matter_application.dao.CjroneWelfareMatterApplicationDao;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.cjrone_bl.dao.CjroneblTemporaryAssistanceDao;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblTemporaryAssistanceEntity;
import com.hmit.kernespring.modules.cjrone_bl.service.CjroneblTemporaryAssistanceService;


@Service("cjroneblTemporaryAssistanceService")
public class CjroneblTemporaryAssistanceServiceImpl extends ServiceImpl<CjroneblTemporaryAssistanceDao, CjroneblTemporaryAssistanceEntity> implements CjroneblTemporaryAssistanceService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneblTemporaryAssistanceDao cjroneblTemporaryAssistanceDao;
    @Autowired
    private CjroneWelfareMatterApplicationDao cjroneWelfareMatterApplicationDao;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneblTemporaryAssistanceEntity cjroneblTemporaryAssistanceEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneblTemporaryAssistanceEntity.class);
        IPage<CjroneblTemporaryAssistanceEntity> page = this.page(
                new Query<CjroneblTemporaryAssistanceEntity>().getPage(params),
                new QueryWrapper<CjroneblTemporaryAssistanceEntity>()
            .eq(StringUtils.isNotBlank(cjroneblTemporaryAssistanceEntity.getId ()!=null && !"".equals(cjroneblTemporaryAssistanceEntity.getId ().toString())? cjroneblTemporaryAssistanceEntity.getId ().toString():null),"id", cjroneblTemporaryAssistanceEntity.getId ())
            .eq(StringUtils.isNotBlank(cjroneblTemporaryAssistanceEntity.getName ()!=null && !"".equals(cjroneblTemporaryAssistanceEntity.getName ().toString())? cjroneblTemporaryAssistanceEntity.getName ().toString():null),"name", cjroneblTemporaryAssistanceEntity.getName ())
            .eq(StringUtils.isNotBlank(cjroneblTemporaryAssistanceEntity.getSex ()!=null && !"".equals(cjroneblTemporaryAssistanceEntity.getSex ().toString())? cjroneblTemporaryAssistanceEntity.getSex ().toString():null),"sex", cjroneblTemporaryAssistanceEntity.getSex ())
            .eq(StringUtils.isNotBlank(cjroneblTemporaryAssistanceEntity.getBirthday ()!=null && !"".equals(cjroneblTemporaryAssistanceEntity.getBirthday ().toString())? cjroneblTemporaryAssistanceEntity.getBirthday ().toString():null),"birthday", cjroneblTemporaryAssistanceEntity.getBirthday ())
            .eq(StringUtils.isNotBlank(cjroneblTemporaryAssistanceEntity.getDisabilityType ()!=null && !"".equals(cjroneblTemporaryAssistanceEntity.getDisabilityType ().toString())? cjroneblTemporaryAssistanceEntity.getDisabilityType ().toString():null),"disability_type", cjroneblTemporaryAssistanceEntity.getDisabilityType ())
            .eq(StringUtils.isNotBlank(cjroneblTemporaryAssistanceEntity.getDisabilityDegree ()!=null && !"".equals(cjroneblTemporaryAssistanceEntity.getDisabilityDegree ().toString())? cjroneblTemporaryAssistanceEntity.getDisabilityDegree ().toString():null),"disability_degree", cjroneblTemporaryAssistanceEntity.getDisabilityDegree ())
            .eq(StringUtils.isNotBlank(cjroneblTemporaryAssistanceEntity.getDisableId ()!=null && !"".equals(cjroneblTemporaryAssistanceEntity.getDisableId ().toString())? cjroneblTemporaryAssistanceEntity.getDisableId ().toString():null),"disable_id", cjroneblTemporaryAssistanceEntity.getDisableId ())
            .eq(StringUtils.isNotBlank(cjroneblTemporaryAssistanceEntity.getIdCard ()!=null && !"".equals(cjroneblTemporaryAssistanceEntity.getIdCard ().toString())? cjroneblTemporaryAssistanceEntity.getIdCard ().toString():null),"id_card", cjroneblTemporaryAssistanceEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(cjroneblTemporaryAssistanceEntity.getLiveAddress ()!=null && !"".equals(cjroneblTemporaryAssistanceEntity.getLiveAddress ().toString())? cjroneblTemporaryAssistanceEntity.getLiveAddress ().toString():null),"live_address", cjroneblTemporaryAssistanceEntity.getLiveAddress ())
            .eq(StringUtils.isNotBlank(cjroneblTemporaryAssistanceEntity.getTelephone ()!=null && !"".equals(cjroneblTemporaryAssistanceEntity.getTelephone ().toString())? cjroneblTemporaryAssistanceEntity.getTelephone ().toString():null),"telephone", cjroneblTemporaryAssistanceEntity.getTelephone ())
            .eq(StringUtils.isNotBlank(cjroneblTemporaryAssistanceEntity.getFamilyEconomy ()!=null && !"".equals(cjroneblTemporaryAssistanceEntity.getFamilyEconomy ().toString())? cjroneblTemporaryAssistanceEntity.getFamilyEconomy ().toString():null),"family_economy", cjroneblTemporaryAssistanceEntity.getFamilyEconomy ())
            .eq(StringUtils.isNotBlank(cjroneblTemporaryAssistanceEntity.getMingzhenSubsidy ()!=null && !"".equals(cjroneblTemporaryAssistanceEntity.getMingzhenSubsidy ().toString())? cjroneblTemporaryAssistanceEntity.getMingzhenSubsidy ().toString():null),"mingzhen_subsidy", cjroneblTemporaryAssistanceEntity.getMingzhenSubsidy ())
            .eq(StringUtils.isNotBlank(cjroneblTemporaryAssistanceEntity.getPayMoney ()!=null && !"".equals(cjroneblTemporaryAssistanceEntity.getPayMoney ().toString())? cjroneblTemporaryAssistanceEntity.getPayMoney ().toString():null),"pay_money", cjroneblTemporaryAssistanceEntity.getPayMoney ())
            .eq(StringUtils.isNotBlank(cjroneblTemporaryAssistanceEntity.getSubsidyMoney ()!=null && !"".equals(cjroneblTemporaryAssistanceEntity.getSubsidyMoney ().toString())? cjroneblTemporaryAssistanceEntity.getSubsidyMoney ().toString():null),"subsidy_money", cjroneblTemporaryAssistanceEntity.getSubsidyMoney ())
            .eq(StringUtils.isNotBlank(cjroneblTemporaryAssistanceEntity.getApplyReason ()!=null && !"".equals(cjroneblTemporaryAssistanceEntity.getApplyReason ().toString())? cjroneblTemporaryAssistanceEntity.getApplyReason ().toString():null),"apply_reason", cjroneblTemporaryAssistanceEntity.getApplyReason ())
            .eq(StringUtils.isNotBlank(cjroneblTemporaryAssistanceEntity.getCreateId ()!=null && !"".equals(cjroneblTemporaryAssistanceEntity.getCreateId ().toString())? cjroneblTemporaryAssistanceEntity.getCreateId ().toString():null),"create_id", cjroneblTemporaryAssistanceEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(cjroneblTemporaryAssistanceEntity.getCreateTime ()!=null && !"".equals(cjroneblTemporaryAssistanceEntity.getCreateTime ().toString())? cjroneblTemporaryAssistanceEntity.getCreateTime ().toString():null),"create_time", cjroneblTemporaryAssistanceEntity.getCreateTime ())
            .eq(StringUtils.isNotBlank(cjroneblTemporaryAssistanceEntity.getCreateName ()!=null && !"".equals(cjroneblTemporaryAssistanceEntity.getCreateName ().toString())? cjroneblTemporaryAssistanceEntity.getCreateName ().toString():null),"create_name", cjroneblTemporaryAssistanceEntity.getCreateName ())
            .eq(StringUtils.isNotBlank(cjroneblTemporaryAssistanceEntity.getStatus ()!=null && !"".equals(cjroneblTemporaryAssistanceEntity.getStatus ().toString())? cjroneblTemporaryAssistanceEntity.getStatus ().toString():null),"status", cjroneblTemporaryAssistanceEntity.getStatus ())
            .eq(StringUtils.isNotBlank(cjroneblTemporaryAssistanceEntity.getStatusOptions ()!=null && !"".equals(cjroneblTemporaryAssistanceEntity.getStatusOptions ().toString())? cjroneblTemporaryAssistanceEntity.getStatusOptions ().toString():null),"status_options", cjroneblTemporaryAssistanceEntity.getStatusOptions ())
            .eq(StringUtils.isNotBlank(cjroneblTemporaryAssistanceEntity.getSignStatus ()!=null && !"".equals(cjroneblTemporaryAssistanceEntity.getSignStatus ().toString())? cjroneblTemporaryAssistanceEntity.getSignStatus ().toString():null),"sign_status", cjroneblTemporaryAssistanceEntity.getSignStatus ())
            .eq(StringUtils.isNotBlank(cjroneblTemporaryAssistanceEntity.getSignatureStatus ()!=null && !"".equals(cjroneblTemporaryAssistanceEntity.getSignatureStatus ().toString())? cjroneblTemporaryAssistanceEntity.getSignatureStatus ().toString():null),"signature_status", cjroneblTemporaryAssistanceEntity.getSignatureStatus ())
        );

        // 开始为状态赋值
        page.getRecords().forEach( item -> {
            if ("1".equals(item.getStatus())){
                item.setStatus("申请人待手签");
            }else if ("2".equals(item.getStatus())){
                item.setStatus("街道待审核");
            }else if ("6".equals(item.getStatus())){
                item.setStatus("区残联经办人待审核");
            }else if ("7".equals(item.getStatus()) ){
                item.setStatus("区残联负责人待审核");
            } else if ("8".equals(item.getStatus())){
                item.setStatus("通过");
            }else if ("0".equals(item.getStatus())){
                item.setStatus("禁用");
            }else if("12".equals(item.getStatus())){
                item.setStatus("退回");  // 区残联退回至街道
            }

            if("1".equals(item.getSignStatus())){
                item.setSignStatus("申请人待手签");
            }else if("2".equals(item.getSignStatus())){
                item.setSignStatus("镇街道待手签");
            }else if("6".equals(item.getSignStatus())){
                item.setSignStatus("区残联经办人待手签");
            }else if("7".equals(item.getSignStatus())){
                item.setSignStatus("区残联负责人待手签");
            }else if("8".equals(item.getSignStatus())){
                item.setSignStatus("完成手签");
            }

            if("1".equals(item.getSignatureStatus())){
                item.setSignatureStatus("无");
            }else if("2".equals(item.getSignatureStatus())){
                item.setSignatureStatus("镇街道待电子签章");
            }else if("4".equals(item.getSignatureStatus())){
                item.setSignatureStatus("民政待电子签章");
            }else if("5".equals(item.getSignatureStatus())){
                item.setSignatureStatus("区残联待电子签章");
            }else if("6".equals(item.getSignatureStatus())){
                item.setSignatureStatus("完成电子签章");
            }

            //性别
            if("0".equals(item.getSex())){
                item.setSex("女");
            }else{
                item.setSex("男");
            }

        });

        return new PageUtils(page);
    }
    @Override
    public List<CjroneblTemporaryAssistanceEntity> queryExportData(Map<String, Object> params) {
            return cjroneblTemporaryAssistanceDao.queryExportData(params);
    }

    @Override
    public void updateByIdSign(CjroneblTemporaryAssistanceEntity cjroneblTemporaryAssistanceEntity) {
        super.updateById(cjroneblTemporaryAssistanceEntity);
    }

    @Override
    public void updateAudioById(CjroneblTemporaryAssistanceEntity cjroneblTemporaryAssistanceEntity) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",cjroneblTemporaryAssistanceEntity.getId());
        map.put("matter_name","残疾人临时救助");
        map.put("verify_time",new Date());
        map.put("status",cjroneblTemporaryAssistanceEntity.getStatus());
        map.put("statusOptions",cjroneblTemporaryAssistanceEntity.getStatusOptions());
        if (cjroneblTemporaryAssistanceEntity.getStatus() != null){
            cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        }
        super.updateById(cjroneblTemporaryAssistanceEntity);
    }

    @Override
    public boolean updateById(CjroneblTemporaryAssistanceEntity entity) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",entity.getId());
        map.put("matter_name","残疾人临时救助");
        map.put("status",entity.getStatus());
        map.put("signatureStatus",entity.getSignatureStatus());  // 只需要更改电子印章的状态
        map.put("statusOptions",entity.getStatusOptions());

        cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        return super.updateById(entity);
    }



}