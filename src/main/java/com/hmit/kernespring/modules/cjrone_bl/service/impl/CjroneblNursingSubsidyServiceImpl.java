package com.hmit.kernespring.modules.cjrone_bl.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblLivingSubsidyEntity;
import com.hmit.kernespring.modules.matter_application.dao.CjroneWelfareMatterApplicationDao;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.cjrone_bl.dao.CjroneblNursingSubsidyDao;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblNursingSubsidyEntity;
import com.hmit.kernespring.modules.cjrone_bl.service.CjroneblNursingSubsidyService;


@Service("cjroneblNursingSubsidyService")
public class CjroneblNursingSubsidyServiceImpl extends ServiceImpl<CjroneblNursingSubsidyDao, CjroneblNursingSubsidyEntity> implements CjroneblNursingSubsidyService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneblNursingSubsidyDao cjroneblNursingSubsidyDao;
    @Autowired
    private CjroneWelfareMatterApplicationDao cjroneWelfareMatterApplicationDao;


    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneblNursingSubsidyEntity cjroneblNursingSubsidyEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneblNursingSubsidyEntity.class);
        IPage<CjroneblNursingSubsidyEntity> page = this.page(
                new Query<CjroneblNursingSubsidyEntity>().getPage(params),
                new QueryWrapper<CjroneblNursingSubsidyEntity>()
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getId ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getId ().toString())? cjroneblNursingSubsidyEntity.getId ().toString():null),"id", cjroneblNursingSubsidyEntity.getId ())
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getName ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getName ().toString())? cjroneblNursingSubsidyEntity.getName ().toString():null),"name", cjroneblNursingSubsidyEntity.getName ())
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getSex ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getSex ().toString())? cjroneblNursingSubsidyEntity.getSex ().toString():null),"sex", cjroneblNursingSubsidyEntity.getSex ())
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getBirthday ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getBirthday ().toString())? cjroneblNursingSubsidyEntity.getBirthday ().toString():null),"birthday", cjroneblNursingSubsidyEntity.getBirthday ())
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getNationality ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getNationality ().toString())? cjroneblNursingSubsidyEntity.getNationality ().toString():null),"nationality", cjroneblNursingSubsidyEntity.getNationality ())
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getIdCard ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getIdCard ().toString())? cjroneblNursingSubsidyEntity.getIdCard ().toString():null),"id_card", cjroneblNursingSubsidyEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getMobilePhone ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getMobilePhone ().toString())? cjroneblNursingSubsidyEntity.getMobilePhone ().toString():null),"mobile_phone", cjroneblNursingSubsidyEntity.getMobilePhone ())
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getGuardianName ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getGuardianName ().toString())? cjroneblNursingSubsidyEntity.getGuardianName ().toString():null),"guardian_name", cjroneblNursingSubsidyEntity.getGuardianName ())
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getGuardianPhone ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getGuardianPhone ().toString())? cjroneblNursingSubsidyEntity.getGuardianPhone ().toString():null),"guardian_phone", cjroneblNursingSubsidyEntity.getGuardianPhone ())
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getDisabilityCategory ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getDisabilityCategory ().toString())? cjroneblNursingSubsidyEntity.getDisabilityCategory ().toString():null),"disability_category", cjroneblNursingSubsidyEntity.getDisabilityCategory ())
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getDisabilityDegree ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getDisabilityDegree ().toString())? cjroneblNursingSubsidyEntity.getDisabilityDegree ().toString():null),"disability_degree", cjroneblNursingSubsidyEntity.getDisabilityDegree ())
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getDisableId ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getDisableId ().toString())? cjroneblNursingSubsidyEntity.getDisableId ().toString():null),"disable_id", cjroneblNursingSubsidyEntity.getDisableId ())
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getBankAccount ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getBankAccount ().toString())? cjroneblNursingSubsidyEntity.getBankAccount ().toString():null),"bank_account", cjroneblNursingSubsidyEntity.getBankAccount ())
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getBankName ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getBankName ().toString())? cjroneblNursingSubsidyEntity.getBankName ().toString():null),"bank_name", cjroneblNursingSubsidyEntity.getBankName ())
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getCareType ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getCareType ().toString())? cjroneblNursingSubsidyEntity.getCareType ().toString():null),"care_type", cjroneblNursingSubsidyEntity.getCareType ())
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getApplicationDate ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getApplicationDate ().toString())? cjroneblNursingSubsidyEntity.getApplicationDate ().toString():null),"application_date", cjroneblNursingSubsidyEntity.getApplicationDate ())
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getAuditPerson ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getAuditPerson ().toString())? cjroneblNursingSubsidyEntity.getAuditPerson ().toString():null),"audit_person", cjroneblNursingSubsidyEntity.getAuditPerson ())
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getAuditDate ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getAuditDate ().toString())? cjroneblNursingSubsidyEntity.getAuditDate ().toString():null),"audit_date", cjroneblNursingSubsidyEntity.getAuditDate ())
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getStatus ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getStatus ().toString())? cjroneblNursingSubsidyEntity.getStatus ().toString():null),"status", cjroneblNursingSubsidyEntity.getStatus ())
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getCreateId ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getCreateId ().toString())? cjroneblNursingSubsidyEntity.getCreateId ().toString():null),"create_id", cjroneblNursingSubsidyEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getCreateTime ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getCreateTime ().toString())? cjroneblNursingSubsidyEntity.getCreateTime ().toString():null),"create_time", cjroneblNursingSubsidyEntity.getCreateTime ())
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getFamilyEconoCondition ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getFamilyEconoCondition ().toString())? cjroneblNursingSubsidyEntity.getFamilyEconoCondition ().toString():null),"family_econo_condition", cjroneblNursingSubsidyEntity.getFamilyEconoCondition ())
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getLifeStatus ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getLifeStatus ().toString())? cjroneblNursingSubsidyEntity.getLifeStatus ().toString():null),"life_status", cjroneblNursingSubsidyEntity.getLifeStatus ())
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getStandardSubsidy ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getStandardSubsidy ().toString())? cjroneblNursingSubsidyEntity.getStandardSubsidy ().toString():null),"standard_subsidy", cjroneblNursingSubsidyEntity.getStandardSubsidy ())
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getNativeAddress ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getNativeAddress ().toString())? cjroneblNursingSubsidyEntity.getNativeAddress ().toString():null),"native_address", cjroneblNursingSubsidyEntity.getNativeAddress ())
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getActuallySubsidy ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getActuallySubsidy ().toString())? cjroneblNursingSubsidyEntity.getActuallySubsidy ().toString():null),"actually_subsidy", cjroneblNursingSubsidyEntity.getActuallySubsidy ())
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getLiveAddress ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getLiveAddress ().toString())? cjroneblNursingSubsidyEntity.getLiveAddress ().toString():null),"live_address", cjroneblNursingSubsidyEntity.getLiveAddress ())
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getRemark ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getRemark ().toString())? cjroneblNursingSubsidyEntity.getRemark ().toString():null),"remark", cjroneblNursingSubsidyEntity.getRemark ())
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getSignStatus ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getSignStatus ().toString())? cjroneblNursingSubsidyEntity.getSignStatus ().toString():null),"sign_status", cjroneblNursingSubsidyEntity.getSignStatus ())
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getSignatureStatus ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getSignatureStatus ().toString())? cjroneblNursingSubsidyEntity.getSignatureStatus ().toString():null),"signature_status", cjroneblNursingSubsidyEntity.getSignatureStatus ())
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getSixMonth ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getSixMonth ().toString())? cjroneblNursingSubsidyEntity.getSixMonth ().toString():null),"six_month", cjroneblNursingSubsidyEntity.getSixMonth ())
            .eq(StringUtils.isNotBlank(cjroneblNursingSubsidyEntity.getCareMonths ()!=null && !"".equals(cjroneblNursingSubsidyEntity.getCareMonths ().toString())? cjroneblNursingSubsidyEntity.getCareMonths ().toString():null),"care_months", cjroneblNursingSubsidyEntity.getCareMonths ())
        );

        // 开始为状态赋值
        page.getRecords().forEach( item -> {
            if ("1".equals(item.getStatus())){
                item.setStatus("申请人待手签");
            }else if ("2".equals(item.getStatus())){
                item.setStatus("街道待审核");
            }else if ("6".equals(item.getStatus())){
                item.setStatus("区残联经办人待审核");
            }else if ("7".equals(item.getStatus()) ){
                item.setStatus("区残联负责人待审核");
            } else if ("8".equals(item.getStatus())){
                item.setStatus("通过");
            }else if ("0".equals(item.getStatus())){
                item.setStatus("禁用");
            }else if("12".equals(item.getStatus())){
                item.setStatus("退回");  // 区残联退回至街道
            }

            if("1".equals(item.getSignStatus())){
                item.setSignStatus("申请人待手签");
            }else if("2".equals(item.getSignStatus())){
                item.setSignStatus("镇街道待手签");
            }else if("6".equals(item.getSignStatus())){
                item.setSignStatus("区残联经办人待手签");
            }else if("7".equals(item.getSignStatus())){
                item.setSignStatus("区残联负责人待手签");
            }else if("8".equals(item.getSignStatus())){
                item.setSignStatus("完成手签");
            }

            if("1".equals(item.getSignatureStatus())){
                item.setSignatureStatus("无");
            }else if("2".equals(item.getSignatureStatus())){
                item.setSignatureStatus("镇街道待电子签章");
            }else if("4".equals(item.getSignatureStatus())){
                item.setSignatureStatus("民政待电子签章");
            }else if("5".equals(item.getSignatureStatus())){
                item.setSignatureStatus("区残联待电子签章");
            }else if("6".equals(item.getSignatureStatus())){
                item.setSignatureStatus("完成电子签章");
            }

            // 照料方式
            if("1".equals(item.getCareType())){
                item.setCareType("居家安养");
            } else if("2".equals(item.getCareType())){
                item.setCareType("日间照料");
            }else if("3".equals(item.getCareType())){
                item.setCareType("集中托养");
            }else if("4".equals(item.getCareType())){
                item.setCareType("项目服务");
            }

        });

        return new PageUtils(page);
    }
    @Override
    public List<CjroneblNursingSubsidyEntity> queryExportData(Map<String, Object> params) {
            return cjroneblNursingSubsidyDao.queryExportData(params);
    }

    @Override
    public void updateAudioById(CjroneblNursingSubsidyEntity cjroneblNursingSubsidy) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",cjroneblNursingSubsidy.getId());
        map.put("matter_name","护理补贴");
        map.put("verify_time",new Date());
        map.put("status",cjroneblNursingSubsidy.getStatus());
        map.put("statusOptions",cjroneblNursingSubsidy.getStatusOptions());
        if (cjroneblNursingSubsidy.getStatus() != null){
            cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        }
        super.updateById(cjroneblNursingSubsidy);
    }

    @Override
    public void updateByIdSign(CjroneblNursingSubsidyEntity cjroneblNursingSubsidyEntity) {
        super.updateById(cjroneblNursingSubsidyEntity);
    }

    @Override
    public boolean updateById(CjroneblNursingSubsidyEntity entity) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",entity.getId());
        map.put("matter_name","护理补贴");
        map.put("status",entity.getStatus());
        map.put("signatureStatus",entity.getSignatureStatus());  // 只需要更改电子印章的状态
        map.put("statusOptions",entity.getStatusOptions());

        cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        return super.updateById(entity);
    }

}