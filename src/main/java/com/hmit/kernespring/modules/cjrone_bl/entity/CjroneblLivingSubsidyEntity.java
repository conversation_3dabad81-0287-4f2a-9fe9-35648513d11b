package com.hmit.kernespring.modules.cjrone_bl.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-02 13:01:22
 */
@Data
@TableName("cjronebl_living_subsidy")
public class CjroneblLivingSubsidyEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
@TableId
private Integer id;
	/**
	 * 姓名
	 */
@Excel(name = "姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 性别
	 */
@Excel(name = "性别", height = 20, width = 30, isImportField = "true_st")
private String sex;
	/**
	 * 出生年月
	 */
@Excel(name = "出生年月", height = 20, width = 30, isImportField = "true_st")
private String birthday;
	/**
	 * 残疾类别
	 */
@Excel(name = "残疾类别", height = 20, width = 30, isImportField = "true_st")
private String disabilityType;
	/**
	 * 残疾等级
	 */
@Excel(name = "残疾等级", height = 20, width = 30, isImportField = "true_st")
private String disabilityDegree;
	/**
	 * 残疾证编号
	 */
@Excel(name = "残疾证编号", height = 20, width = 30, isImportField = "true_st")
private String disableId;
	/**
	 * 身份证号码
	 */
@Excel(name = "身份证号码", height = 20, width = 30, isImportField = "true_st")
private String idCard;
	/**
	 * 详细地址
	 */
@Excel(name = "详细地址", height = 20, width = 30, isImportField = "true_st")
private String liveAddress;
	/**
	 * 户籍地址
	 */
@Excel(name = "户籍地址", height = 20, width = 30, isImportField = "true_st")
private String nativeAddress;
	/**
	 * 联系电话
	 */
@Excel(name = "联系电话", height = 20, width = 30, isImportField = "true_st")
private String telephone;
	/**
	 * 家庭经济情况
	 */
@Excel(name = "家庭经济情况", height = 20, width = 30, isImportField = "true_st")
private String familyEconomy;
	/**
	 * 补助金额
	 */
@Excel(name = "补助金额", height = 20, width = 30, isImportField = "true_st")
private String subsidyMoney;
	/**
	 * 申请补助类型
	 */
@Excel(name = "申请补助类型", height = 20, width = 30, isImportField = "true_st")
private String applyType;
	/**
	 * 创建人编号
	 */
@Excel(name = "创建人编号", height = 20, width = 30, isImportField = "true_st")
private Integer createId;
	/**
	 * 创建时间
	 */
@Excel(name = "创建时间", height = 20, width = 30, isImportField = "true_st")
private String createTime;
	/**
	 * 创建人姓名
	 */
@Excel(name = "创建人姓名", height = 20, width = 30, isImportField = "true_st")
private String createName;
	/**
	 * 状态
	 */
@Excel(name = "状态", height = 20, width = 30, isImportField = "true_st")
private String status;
	/**
	 * 
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String statusOptions;
	/**
	 * 
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String signStatus;
	/**
	 * 
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String signatureStatus;
	/**
	 * 退回状态
	 */
@Excel(name = "退回状态", height = 20, width = 30, isImportField = "true_st")
private Integer returnStatus;
	/**
	 * 
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String bankName;
	/**
	 * 
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String bankAccount;
	/**
	 * 本人收入
	 */
@Excel(name = "本人收入", height = 20, width = 30, isImportField = "true_st")
private String income;
	/**
	 * 监护人姓名
	 */
@Excel(name = "监护人姓名", height = 20, width = 30, isImportField = "true_st")
private String guardianName;
	/**
	 * 监护人电话
	 */
@Excel(name = "监护人电话", height = 20, width = 30, isImportField = "true_st")
private String guardianPhone;
	/**
	 * 
	 */
@Excel(name = "村", height = 20, width = 30, isImportField = "true_st")
private String nativeCun;
	/**
	 * 
	 */
@Excel(name = "街道", height = 20, width = 30, isImportField = "true_st")
private String nativeZhen;

}
