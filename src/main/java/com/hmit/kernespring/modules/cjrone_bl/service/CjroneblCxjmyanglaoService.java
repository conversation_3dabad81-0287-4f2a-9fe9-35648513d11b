package com.hmit.kernespring.modules.cjrone_bl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblCxjmyanglaoEntity;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblZgjbyanglaoEntity;

import java.util.Map;

import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-12 17:40:46
 */
public interface CjroneblCxjmyanglaoService extends IService<CjroneblCxjmyanglaoEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneblCxjmyanglaoEntity> queryExportData(Map<String, Object> params);

    // 电子盖章专用
    boolean updateById(CjroneblCxjmyanglaoEntity cjroneblCxjmyanglaoEntity);
    void updateByIdSign(CjroneblCxjmyanglaoEntity cjroneblCxjmyanglaoEntity);

    void updateAudioById(CjroneblCxjmyanglaoEntity cjroneblCxjmyanglaoEntity);
}

