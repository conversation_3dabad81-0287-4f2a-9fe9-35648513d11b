package com.hmit.kernespring.modules.cjrone_bl.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-24 14:31:01
 */
@Data
@TableName("cjronebl_childedu")
public class CjroneblChildeduEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
@TableId
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 残疾人姓名
	 */
@Excel(name = "残疾人姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 身份证号码
	 */
@Excel(name = "身份证号码", height = 20, width = 30, isImportField = "true_st")
private String idCard;
	/**
	 * 出生日期
	 */
@Excel(name = "出生日期", height = 20, width = 30, isImportField = "true_st")
private String birthday;
	/**
	 * 联系电话
	 */
@Excel(name = "联系电话", height = 20, width = 30, isImportField = "true_st")
private String telephoe;
	/**
	 * 家庭住址
	 */
@Excel(name = "家庭住址", height = 20, width = 30, isImportField = "true_st")
private String liveAddress;
	/**
	 * 残疾证号
	 */
@Excel(name = "残疾证号", height = 20, width = 30, isImportField = "true_st")
private String disableId;
	/**
	 * 就读学校
	 */
@Excel(name = "就读学校", height = 20, width = 30, isImportField = "true_st")
private String currentSchool;
	/**
	 * 年级
	 */
@Excel(name = "年级", height = 20, width = 30, isImportField = "true_st")
private String grade;
	/**
	 * 入学时间
	 */
@Excel(name = "入学时间", height = 20, width = 30, isImportField = "true_st")
private String admissionTime;
	/**
	 * 申请人类型
	 */
@Excel(name = "申请人类型", height = 20, width = 30, isImportField = "true_st")
private String applicantType;
	/**
	 * 父亲姓名
	 */
@Excel(name = "父亲姓名", height = 20, width = 30, isImportField = "true_st")
private String fatherName;
	/**
	 * 父亲身份证
	 */
@Excel(name = "父亲身份证", height = 20, width = 30, isImportField = "true_st")
private String fatherIdcard;
	/**
	 * 母亲姓名
	 */
@Excel(name = "母亲姓名", height = 20, width = 30, isImportField = "true_st")
private String motherName;
	/**
	 * 母亲身份证
	 */
@Excel(name = "母亲身份证", height = 20, width = 30, isImportField = "true_st")
private String motherIdcard;
	/**
	 * 补贴金额
	 */
@Excel(name = "补贴金额", height = 20, width = 30, isImportField = "true_st")
private Double subsidyMoney;
	/**
	 * 
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String isHuJiRation;
	/**
	 * 创建人编号
	 */
@Excel(name = "创建人编号", height = 20, width = 30, isImportField = "true_st")
private Integer createId;
	/**
	 * 创建时间
	 */
@Excel(name = "创建时间", height = 20, width = 30, isImportField = "true_st")
private String createTime;
	/**
	 * 创建人姓名
	 */
@Excel(name = "创建人姓名", height = 20, width = 30, isImportField = "true_st")
private String createName;
	/**
	 * 状态
	 */
@Excel(name = "状态", height = 20, width = 30, isImportField = "true_st")
private String status;
	/**
	 * 
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String statusOptions;
	/**
	 * 
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String signStatus;
	/**
	 * 
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String signatureStatus;
	/**
	 * 
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String fatherDisid;
	/**
	 * 
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String motherDisid;

}
