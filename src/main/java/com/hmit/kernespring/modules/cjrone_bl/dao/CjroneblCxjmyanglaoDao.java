package com.hmit.kernespring.modules.cjrone_bl.dao;

import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblCxjmyanglaoEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-12 17:40:46
 */
@Mapper
public interface CjroneblCxjmyanglaoDao extends BaseMapper<CjroneblCxjmyanglaoEntity> {
    List<CjroneblCxjmyanglaoEntity> queryExportData(Map<String, Object> params);
	
}
