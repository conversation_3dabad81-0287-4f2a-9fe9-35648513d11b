package com.hmit.kernespring.modules.cjrone_bl.dao;

import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblLivingAllowanceEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 生活补助金表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-11 15:12:44
 */
@Mapper
public interface CjroneblLivingAllowanceDao extends BaseMapper<CjroneblLivingAllowanceEntity> {
    List<CjroneblLivingAllowanceEntity> queryExportData(Map<String, Object> params);
	
}
