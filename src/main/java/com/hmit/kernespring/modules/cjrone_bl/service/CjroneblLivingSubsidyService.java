package com.hmit.kernespring.modules.cjrone_bl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone.entity.CjroneLivingAllowanceEntity;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblLivingSubsidyEntity;

import java.util.Map;

import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-11-29 09:37:43
 */
public interface CjroneblLivingSubsidyService extends IService<CjroneblLivingSubsidyEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneblLivingSubsidyEntity> queryExportData(Map<String, Object> params);
    // 电子盖章专用
    boolean updateById(CjroneblLivingSubsidyEntity entity);
    boolean updateAudio(CjroneblLivingSubsidyEntity entity);
    // 签章专用
    void updateByIdSign(CjroneblLivingSubsidyEntity cjroneblLivingSubsidyEntity);

    void updateAudioById(CjroneblLivingSubsidyEntity cjroneblLivingSubsidy);
}

