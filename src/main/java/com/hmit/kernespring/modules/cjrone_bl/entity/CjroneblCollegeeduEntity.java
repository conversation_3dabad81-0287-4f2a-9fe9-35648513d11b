package com.hmit.kernespring.modules.cjrone_bl.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-22 21:13:55
 */
@Data
@TableName("cjronebl_collegeedu")
public class CjroneblCollegeeduEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
@TableId
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 姓名
	 */
@Excel(name = "姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 性别
	 */
@Excel(name = "性别", height = 20, width = 30, isImportField = "true_st")
private String sex;
	/**
	 * 出生日期
	 */
@Excel(name = "出生日期", height = 20, width = 30, isImportField = "true_st")
private String birthday;
	/**
	 * 所在学校
	 */
@Excel(name = "所在学校", height = 20, width = 30, isImportField = "true_st")
private String collegeName;
	/**
	 * 所在专业
	 */
@Excel(name = "所在专业", height = 20, width = 30, isImportField = "true_st")
private String majorName;
	/**
	 * 入学时间
	 */
@Excel(name = "入学时间", height = 20, width = 30, isImportField = "true_st")
private String collegeTime;
	/**
	 * 残疾证号
	 */
@Excel(name = "残疾证号", height = 20, width = 30, isImportField = "true_st")
private String disableId;
	/**
	 * 应缴学费
	 */
@Excel(name = "应缴学费", height = 20, width = 30, isImportField = "true_st")
private String tuition;
	/**
	 * 实缴学费
	 */
@Excel(name = "实缴学费", height = 20, width = 30, isImportField = "true_st")
private String actuallyTuition;
	/**
	 * 应缴住宿费
	 */
@Excel(name = "应缴住宿费", height = 20, width = 30, isImportField = "true_st")
private String accommodationFee;
	/**
	 * 实缴住宿费
	 */
@Excel(name = "实缴住宿费", height = 20, width = 30, isImportField = "true_st")
private String actuallyAccommodationFee;
	/**
	 * 户口性质
	 */
@Excel(name = "户口性质", height = 20, width = 30, isImportField = "true_st")
private String hukouNature;
	/**
	 * 家庭总人口
	 */
@Excel(name = "家庭总人口", height = 20, width = 30, isImportField = "true_st")
private String familyCount;
	/**
	 * 家庭住址
	 */
@Excel(name = "家庭住址", height = 20, width = 30, isImportField = "true_st")
private String liveAddress;
	/**
	 * 联系电话
	 */
@Excel(name = "联系电话", height = 20, width = 30, isImportField = "true_st")
private String telephone;
	/**
	 * 邮编
	 */
@Excel(name = "邮编", height = 20, width = 30, isImportField = "true_st")
private String postcode;
	/**
	 * 家庭经济来源
	 */
@Excel(name = "家庭经济来源", height = 20, width = 30, isImportField = "true_st")
private String familyFinances;
	/**
	 * 家庭年收入
	 */
@Excel(name = "家庭年收入", height = 20, width = 30, isImportField = "true_st")
private String familyIncome;
	/**
	 * 申请理由
	 */
@Excel(name = "申请理由", height = 20, width = 30, isImportField = "true_st")
private String subsidyReason;
	/**
	 * 创建人编号
	 */
@Excel(name = "创建人编号", height = 20, width = 30, isImportField = "true_st")
private Integer createId;
	/**
	 * 创建时间
	 */
@Excel(name = "创建时间", height = 20, width = 30, isImportField = "true_st")
private String createTime;
	/**
	 * 创建人姓名
	 */
@Excel(name = "创建人姓名", height = 20, width = 30, isImportField = "true_st")
private String createName;
	/**
	 * 状态
	 */
@Excel(name = "状态", height = 20, width = 30, isImportField = "true_st")
private String status;
	/**
	 * 
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String statusOptions;
	/**
	 * 
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String signStatus;
	/**
	 * 
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String signatureStatus;

}
