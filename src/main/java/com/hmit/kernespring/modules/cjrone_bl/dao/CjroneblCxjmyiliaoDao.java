package com.hmit.kernespring.modules.cjrone_bl.dao;

import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblCxjmyiliaoEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-13 09:59:33
 */
@Mapper
public interface CjroneblCxjmyiliaoDao extends BaseMapper<CjroneblCxjmyiliaoEntity> {
    List<CjroneblCxjmyiliaoEntity> queryExportData(Map<String, Object> params);
	
}
