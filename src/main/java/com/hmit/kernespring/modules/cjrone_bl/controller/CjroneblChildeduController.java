package com.hmit.kernespring.modules.cjrone_bl.controller;

import java.io.*;
import java.nio.file.Files;
import java.util.*;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.common.utils.DateUtils;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.app.service.APIService;
import com.hmit.kernespring.modules.cjrone.entity.CjroneSignatureEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneSignatureService;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblRehabilitationSubsidyEntity;
import com.hmit.kernespring.modules.data_management.entity.ApiZCszmEntity;
import com.hmit.kernespring.modules.data_management.entity.DataChildEducationSubsidyEntity;
import com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity;
import com.hmit.kernespring.modules.data_management.service.DataDisabilityCertificateService;
import com.hmit.kernespring.modules.matter_application.entity.CjroneWelfareMatterApplicationEntity;
import com.hmit.kernespring.modules.matter_application.service.CjroneWelfareMatterApplicationService;
import com.hmit.kernespring.modules.sys.controller.AbstractController;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.*;
import org.apache.poi.ss.usermodel.Workbook;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblChildeduEntity;
import com.hmit.kernespring.modules.cjrone_bl.service.CjroneblChildeduService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-24 14:31:01
 */
@RestController
@RequestMapping("cjrone_bl/cjroneblchildedu")
public class CjroneblChildeduController extends AbstractController {
    @Autowired
    private CjroneblChildeduService cjroneblChildeduService;
    @Autowired
    private DataDisabilityCertificateService dataDisabilityCertificateService;
    @Autowired
    private CjroneWelfareMatterApplicationService cjroneWelfareMatterApplicationService;
    @Autowired
    private APIService apiService;
    @Autowired
    private CjroneSignatureService cjroneSignatureService;
    @Autowired
    private CjroneProperties cjroneProperties;


    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("cjrone_bl:cjroneblchildedu:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = cjroneblChildeduService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("cjrone_bl:cjroneblchildedu:info")
    public R info(@PathVariable("id") Integer id){
		CjroneblChildeduEntity cjroneblChildedu = cjroneblChildeduService.getById(id);

        return R.ok().put("cjroneblChildedu", cjroneblChildedu);
    }



    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("cjrone_bl:cjroneblchildedu:save")
    public R save(@RequestBody CjroneblChildeduEntity cjroneblChildedu){

		cjroneblChildeduService.save(cjroneblChildedu);

        return R.ok();
    }

    /**
     * 保存
     */
    @RequestMapping("/saveZljy")
    // @RequiresPermissions("data_management:datachildeducationsubsidy:saveZljy")
    public R saveZljy(@RequestBody CjroneblChildeduEntity cjroneblChildeduEntity){
        // 判断是否存在申请记录
        Map<String, Object> params_alive = new HashMap<>();
        params_alive.put("id_card",cjroneblChildeduEntity.getIdCard());
        List<CjroneblChildeduEntity> is_alive = (List<CjroneblChildeduEntity>) cjroneblChildeduService.listByMap(params_alive);
        if (is_alive.size() == 0) {
            Date date = new Date();
            String dateTime = DateUtils.format(date, DateUtils.DATE_TIME_PATTERN);
            cjroneblChildeduEntity.setCreateTime(DateUtils.format(date, DateUtils.DATE_TIME_PATTERN));

            if (cjroneblChildeduEntity.getApplicantType().indexOf("本人") != -1) {
                // 判断本人是否为残疾人
                Map<String, Object> params = new HashMap<>();
                params.put("id_card", cjroneblChildeduEntity.getIdCard());
                List<DataDisabilityCertificateEntity> entities = (List<DataDisabilityCertificateEntity>) dataDisabilityCertificateService.listByMap(params);
                System.out.println(entities.size() + "aaa");
                if (entities.size() != 0) {
                    DataDisabilityCertificateEntity entity = entities.get(0);
                    // 保存至本表
                    cjroneblChildeduEntity.setCreateId(Integer.parseInt(getUserId().toString()));
                    cjroneblChildeduEntity.setCreateName(getUser().getName());
                    cjroneblChildeduEntity.setStatus("1");
                    cjroneblChildeduEntity.setSignStatus("1");
                    cjroneblChildeduEntity.setSignatureStatus("1");
                    cjroneblChildeduService.save(cjroneblChildeduEntity);
                    // 保存到惠残事项表
                    CjroneWelfareMatterApplicationEntity welfareMatterApplicationEntity = new CjroneWelfareMatterApplicationEntity();
                    welfareMatterApplicationEntity.setMatterName("残疾人子女教育补贴");
                    welfareMatterApplicationEntity.setMatterId(cjroneblChildeduEntity.getId());
                    welfareMatterApplicationEntity.setIdCard(entity.getIdCard());
                    welfareMatterApplicationEntity.setName(entity.getName());
                    welfareMatterApplicationEntity.setMobilePhone(cjroneblChildeduEntity.getTelephoe());
                    welfareMatterApplicationEntity.setDisableId(entity.getDisableId());
                    welfareMatterApplicationEntity.setSignStatus("1");
                    welfareMatterApplicationEntity.setSignatureStatus("1");
                    welfareMatterApplicationEntity.setStatus("1");
                    welfareMatterApplicationEntity.setApplicationTime(DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN));
                    welfareMatterApplicationEntity.setNativeZhen(getUser().getZhen());
                    welfareMatterApplicationEntity.setCreateId(getUserId());
                    welfareMatterApplicationEntity.setCreateTime(dateTime);
                    cjroneWelfareMatterApplicationService.saveZljf(welfareMatterApplicationEntity);
                } else {
                    System.out.println("aaaaaaa");
                    return R.error().put("msg", "未找到残疾证相关信息，请检查身份证号码是否输入正确！");
                }
            } else if (cjroneblChildeduEntity.getApplicantType().indexOf("子女") != -1) {
                // 查询父母哪一位残疾
                Map<String, Object> params = new HashMap<>();
                params.put("id_card", cjroneblChildeduEntity.getFatherIdcard());
                List<DataDisabilityCertificateEntity> entitiesf = (List<DataDisabilityCertificateEntity>) dataDisabilityCertificateService.listByMap(params);
                Map<String, Object> params_m = new HashMap<>();
                params_m.put("id_card", cjroneblChildeduEntity.getMotherIdcard());
                List<DataDisabilityCertificateEntity> entitiesm = (List<DataDisabilityCertificateEntity>) dataDisabilityCertificateService.listByMap(params_m);
                if (entitiesf.size() > 0 && entitiesm.size() > 0) {
                    // 同时残疾
                    cjroneblChildeduEntity.setApplicantType("学生父母");
                    cjroneblChildeduEntity.setFatherDisid(entitiesf.get(0).getDisableId());
                    cjroneblChildeduEntity.setMotherDisid(entitiesm.get(0).getDisableId());

                } else if (entitiesf.size() > 0 && entitiesm.size() == 0) {
                    //父亲残疾
                    cjroneblChildeduEntity.setApplicantType("学生父亲");
                    cjroneblChildeduEntity.setFatherDisid(entitiesf.get(0).getDisableId());

                } else if (entitiesf.size() == 0 && entitiesm.size() > 0) {
                    // 母亲
                    cjroneblChildeduEntity.setApplicantType("学生母亲");
                    cjroneblChildeduEntity.setMotherDisid(entitiesm.get(0).getDisableId());

                } else {
                    // 都无
                    return R.error().put("msg", "通过父母双方身份证，未找到相关残疾证信息，请检查身份证是否填写错误！");
                }
                //开始验证户籍
                String is_childs = "否";
                Map<String, Object> doCszmInfo = apiService.queryCSZMInfo(cjroneblChildeduEntity.getFatherName(),cjroneblChildeduEntity.getMotherName(),cjroneblChildeduEntity.getFatherIdcard(),cjroneblChildeduEntity.getMotherIdcard(),null);
                if ("00".equals(doCszmInfo.get("code").toString())) {
                    if (doCszmInfo.get("datas") != null) {
                        System.out.println("doCszmInfo: " + doCszmInfo);
                        List<ApiZCszmEntity> list1 = new Gson().fromJson(doCszmInfo.get("datas").toString().replaceAll(" ", "").replaceAll(":", "").replaceAll("-", ""), new TypeToken<List<ApiZCszmEntity>>() {
                        }.getType());
                        if (list1.size() != 0) {
                            System.out.println(new Gson().toJson("list1---->" + list1));
                            is_childs = "是";
                        }else {
                            //return R.error().put("msg","省公安户籍验证接口显示，申请人与所输入父母信息未查到子女关系！请检查后再提交。");
                        }
                    }else {
                        System.out.println("aaaaaaa");
                        //return R.error().put("msg","省公安户籍验证接口显示，申请人与所输入父母信息未查到子女关系！请检查后再提交。");
                    }
                }else {
                    is_childs = "异常";
                    return R.error().put("msg","省公安户籍验证接口验证异常，请稍后再试！请检查后再提交。");
                }
                // 保存到本表
                cjroneblChildeduEntity.setCreateId(Integer.parseInt(getUserId().toString()));
                cjroneblChildeduEntity.setCreateName(getUser().getName());
                cjroneblChildeduEntity.setStatus("1");
                cjroneblChildeduEntity.setSignStatus("1");
                cjroneblChildeduEntity.setSignatureStatus("1");
                cjroneblChildeduService.save(cjroneblChildeduEntity);
                // 保存到惠残事项表
                CjroneWelfareMatterApplicationEntity welfareMatterApplicationEntity = new CjroneWelfareMatterApplicationEntity();
                welfareMatterApplicationEntity.setMatterName("残疾人子女教育补贴");
                welfareMatterApplicationEntity.setMatterId(cjroneblChildeduEntity.getId());
                welfareMatterApplicationEntity.setIdCard(cjroneblChildeduEntity.getIdCard());
                welfareMatterApplicationEntity.setName(cjroneblChildeduEntity.getName());
                welfareMatterApplicationEntity.setMobilePhone(cjroneblChildeduEntity.getTelephoe());
                welfareMatterApplicationEntity.setDisableId(cjroneblChildeduEntity.getDisableId());
                welfareMatterApplicationEntity.setSignStatus("1");
                welfareMatterApplicationEntity.setSignatureStatus("1");
                welfareMatterApplicationEntity.setStatus("1");
                welfareMatterApplicationEntity.setApplicationTime(DateUtils.format(date, DateUtils.DATE_TIME_PATTERN));
                welfareMatterApplicationEntity.setNativeZhen(getUser().getZhen());
                welfareMatterApplicationEntity.setCreateTime(dateTime);
                welfareMatterApplicationEntity.setCreateId(getUserId());
                cjroneWelfareMatterApplicationService.saveZljf(welfareMatterApplicationEntity);
            }
            return R.ok().put("applyId",cjroneblChildeduEntity.getId());
        }else {
            return R.error().put("msg","该学生已申请过残疾人子女教育补贴！");
        }
    }


    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("cjrone_bl:cjroneblchildedu:update")
    public R update(@RequestBody CjroneblChildeduEntity cjroneblChildedu){
		cjroneblChildeduService.updateById(cjroneblChildedu);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("cjrone_bl:cjroneblchildedu:delete")
    public R delete(@RequestBody Integer[] ids){
		cjroneblChildeduService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    @RequiresPermissions("cjrone_bl:cjroneblchildedu:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = ""; //bxProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        // 此处的headRows、titleRows 依据导入表格具体的数据行决定
        params_import.setHeadRows(2);
        params_import.setTitleRows(1);
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<CjroneblChildeduEntity> cjroneblChildeduList = new ArrayList<>();
        System.out.println("当前导入数据条数：" + list.size());
        list.forEach(item ->{
            if (null != item.get("身份证") && !"".equals(item.get("身份证").toString()) || (null != item.get("身份证号") && !"".equals(item.get("身份证号").toString()))) {
                item.put("id",item.get(""));
                item.put("name",item.get("残疾人姓名"));
                item.put("idCard",item.get("身份证号码"));
                item.put("birthday",item.get("出生日期"));
                item.put("telephoe",item.get("联系电话"));
                item.put("liveAddress",item.get("家庭住址"));
                item.put("disableId",item.get("残疾证号"));
                item.put("currentSchool",item.get("就读学校"));
                item.put("grade",item.get("年级"));
                item.put("admissionTime",item.get("入学时间"));
                item.put("applicantType",item.get("申请人类型"));
                item.put("fatherName",item.get("父亲姓名"));
                item.put("fatherIdcard",item.get("父亲身份证"));
                item.put("motherName",item.get("母亲姓名"));
                item.put("motherIdcard",item.get("母亲身份证"));
                item.put("subsidyMoney",item.get("补贴金额"));
                item.put("isHuJiRation",item.get(""));
                item.put("createId",item.get("创建人编号"));
                item.put("createTime",item.get("创建时间"));
                item.put("createName",item.get("创建人姓名"));
                item.put("status",item.get("状态"));
                item.put("statusOptions",item.get(""));
                item.put("signStatus",item.get(""));
                item.put("signatureStatus",item.get(""));
                item.put("fatherDisid",item.get(""));
                item.put("motherDisid",item.get(""));
                cjroneblChildeduList.add(new Gson().fromJson(new Gson().toJson(item), CjroneblChildeduEntity.class));
            }
        });
        // 保存到数据库
        cjroneblChildeduService.saveBatch(cjroneblChildeduList);



        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    @RequiresPermissions("cjrone_bl:cjroneblchildedu:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<CjroneblChildeduEntity> cjroneblChildeduEntityList = cjroneblChildeduService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("", null, "");
        Workbook workbook = ExcelExportUtil.exportExcel(params, CjroneblChildeduEntity.class, cjroneblChildeduEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

    /**
     * 生成电子签章 pdf
     */
    @RequestMapping("/printPDF/{id}")
    public R printPDF(@PathVariable("id") Integer id,@RequestParam("type") String type) throws IOException {
        System.out.print("id is :"+id);

        Map<String, Object> tmp_params = new HashMap<>();
        tmp_params.put("type","惠残事项残疾人子女教育补贴");
        tmp_params.put("status","1");
        tmp_params.put("type_id",id);
        List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
        if (alive_list.size()>0) {
            CjroneSignatureEntity cjroneSignatureEntity = alive_list.get(0);
            if ("电子公章".equals(type)){
                String tmp_file_name = System.currentTimeMillis()+"_"+cjroneSignatureEntity.getFileName();
                Files.copy(new File(cjroneSignatureEntity.getFileActUrl()).toPath(),new File(cjroneProperties.getTempFilePath()+tmp_file_name).toPath());

                return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", tmp_file_name).put("signId",cjroneSignatureEntity.getId());

            }else {
                return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", cjroneSignatureEntity.getFileName()).put("signId",cjroneSignatureEntity.getId());
            }
        }else {

            //根据编号获得实体
            CjroneblChildeduEntity cjroneblChildeduEntity =cjroneblChildeduService.getById(id);

            Calendar now = Calendar.getInstance();

            // 原pdf模板路径
            String templatePath = cjroneProperties.getTempletePath()+"高中补助模板.pdf";
            // 生成的新文件路径
            String newPDFPath = cjroneProperties.getSignaturePath()+"child_edu_"+cjroneblChildeduEntity.getIdCard()+".pdf";

            // 获得待生成的实体文件
            Map<String, String> map =new HashMap<String, String>();
            map.put("name", cjroneblChildeduEntity.getName()==null?"":cjroneblChildeduEntity.getName());
            map.put("birthday",cjroneblChildeduEntity.getBirthday()==null?"":cjroneblChildeduEntity.getBirthday());
            map.put("disableId",cjroneblChildeduEntity.getDisableId()==null?"":cjroneblChildeduEntity.getDisableId());
            map.put("idCard",cjroneblChildeduEntity.getIdCard()==null?"":cjroneblChildeduEntity.getIdCard());
            map.put("telephone",cjroneblChildeduEntity.getTelephoe()==null?"":cjroneblChildeduEntity.getTelephoe());
            map.put("liveAddress",cjroneblChildeduEntity.getLiveAddress()==null?"":cjroneblChildeduEntity.getLiveAddress());
            map.put("admissionTime",cjroneblChildeduEntity.getAdmissionTime()==null?"":cjroneblChildeduEntity.getAdmissionTime());
            map.put("currentSchool",cjroneblChildeduEntity.getCurrentSchool()==null?"":cjroneblChildeduEntity.getCurrentSchool());
            map.put("grade",cjroneblChildeduEntity.getGrade()==null?"":cjroneblChildeduEntity.getGrade());
            map.put("subsidyMoney",cjroneblChildeduEntity.getSubsidyMoney()==null?"":cjroneblChildeduEntity.getSubsidyMoney().toString());
            map.put("applicantType",cjroneblChildeduEntity.getApplicantType()==null?"":cjroneblChildeduEntity.getApplicantType());
            map.put("fatherName",cjroneblChildeduEntity.getFatherName()==null?"":cjroneblChildeduEntity.getFatherName());
            map.put("motherName",cjroneblChildeduEntity.getMotherName()==null?"":cjroneblChildeduEntity.getMotherName());
            map.put("motherDisid",cjroneblChildeduEntity.getMotherDisid()==null?"":cjroneblChildeduEntity.getMotherDisid());
            map.put("FatherDisid",cjroneblChildeduEntity.getFatherDisid()==null?"":cjroneblChildeduEntity.getFatherDisid());


            map.put("createTime",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");



            FileOutputStream out;
            int num = 1;//页数
            ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
            try {
                out = new FileOutputStream(newPDFPath);// 输出流
                Document doc = new Document();   //新建一个文档
                PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                doc.open();

                for (int i = 0; i < num; i++) {
                    bos[i] = new ByteArrayOutputStream();
                    System.out.println("templatePath--->"+templatePath);
                    PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                    PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                    AcroFields form = stamper.getAcroFields(); //获取文本域
                    // BaseFont
                    // 1、使用iTextAsian.jar中的字体
                    // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                    // 2、使用Windows系统字体(TrueType)
                    // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                    // 3、使用资源字体(ClassPath)
                    // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                    BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                    java.util.Iterator<String> it = form.getFields().keySet().iterator();
                    while (it.hasNext()) {
                        String name = it.next();
                        System.out.println(name + ":"+map.get(name));
                        form.setFieldProperty(name,"textfont",font,null);
                        // form.addSubstitutionFont(font);
                        form.setField(name, map.get(name));
                    }

                    stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                    stamper.close();

                }
                PdfImportedPage page = null;
                for (int i = 0; i < num; i++) {
                    page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                    copy.addPage(page);
                }
                doc.close();
                out.close();
            } catch (IOException e) {
                System.out.println("导出异常");
            } catch (DocumentException e) {
                System.out.println("文档异常");
            }

            CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
            cjroneSignature.setUrl("/child_edu_" + cjroneblChildeduEntity.getIdCard() + ".pdf");
            cjroneSignature.setFileActUrl(newPDFPath);
            cjroneSignature.setCreateDate(new Date());
            cjroneSignature.setCreateId(getUserId());
            cjroneSignature.setType("惠残事项残疾人子女教育补贴");
            cjroneSignature.setTypeId(cjroneblChildeduEntity.getId());
            cjroneSignature.setFileName("child_edu_" + cjroneblChildeduEntity.getIdCard() + ".pdf");
            cjroneSignature.setAccountId(getUserId().toString());
            cjroneSignature.setAccountName(getUser().getUsername());
            cjroneSignature.setStatus("1");

            cjroneSignatureService.save(cjroneSignature);

            return R.ok().put("signTotal",1).put("matterId",id).put("matterName","残疾人子女教育补贴").put("fileUrl",cjroneSignature.getUrl());

        }



    }




}
