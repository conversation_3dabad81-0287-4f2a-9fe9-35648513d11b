package com.hmit.kernespring.modules.cjrone_bl.controller;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.*;

import java.io.OutputStream;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.cjrone.entity.CjroneSignatureEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneSignatureService;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblLivingAllowanceEntity;
import com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity;
import com.hmit.kernespring.modules.sys.controller.AbstractController;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.apache.poi.ss.usermodel.Workbook;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblRehabilitationSubsidyEntity;
import com.hmit.kernespring.modules.cjrone_bl.service.CjroneblRehabilitationSubsidyService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 康复补助
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-13 16:03:17
 */
@RestController
@RequestMapping("cjrone_bl/cjroneblrehabilitationsubsidy")
public class CjroneblRehabilitationSubsidyController extends AbstractController {
    @Autowired
    private CjroneblRehabilitationSubsidyService cjroneblRehabilitationSubsidyService;
    @Autowired
    private CjroneSignatureService cjroneSignatureService;
    @Autowired
    private CjroneProperties cjroneProperties;



    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("cjrone_bl:cjroneblrehabilitationsubsidy:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = cjroneblRehabilitationSubsidyService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("cjrone_bl:cjroneblrehabilitationsubsidy:info")
    public R info(@PathVariable("id") Integer id){
		CjroneblRehabilitationSubsidyEntity cjroneblRehabilitationSubsidy = cjroneblRehabilitationSubsidyService.getById(id);

        return R.ok().put("cjroneblRehabilitationSubsidy", cjroneblRehabilitationSubsidy);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("cjrone_bl:cjroneblrehabilitationsubsidy:save")
    public R save(@RequestBody CjroneblRehabilitationSubsidyEntity cjroneblRehabilitationSubsidy){

		cjroneblRehabilitationSubsidyService.save(cjroneblRehabilitationSubsidy);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("cjrone_bl:cjroneblrehabilitationsubsidy:update")
    public R update(@RequestBody CjroneblRehabilitationSubsidyEntity cjroneblRehabilitationSubsidy){
        // 保存审核的状态
        CjroneblRehabilitationSubsidyEntity entity = cjroneblRehabilitationSubsidyService.getById(cjroneblRehabilitationSubsidy.getId());
        System.out.println(new Gson().toJson(entity));
        if (entity != null){
            if (getUser().getRoleName() != null && getUser().getRoleName().indexOf("区残联") != -1){
                if ("8".equals(entity.getSignStatus())){
                    if (!"6".equals(entity.getSignatureStatus())){
                        return R.error().put("code",100).put("msg","审核未通过，区残联未电子公章！").put("applyId",cjroneblRehabilitationSubsidy.getId());
                    }else {
                        System.out.println("aaaaaaa");
                    }
                }else {
                    return R.error().put("code",100).put("msg","审核未通过，区残联未手签！").put("applyId",cjroneblRehabilitationSubsidy.getId());
                }

                //判断审核通过与退回
                if("1".equals(cjroneblRehabilitationSubsidy.getStatus())){
                    // 审核通过
                    cjroneblRehabilitationSubsidy.setStatus("8");  //通过

                    // 发送短信
                    //DataDisabilityCertificateEntity dataDisabilityCertificateEntity=dataDisabilityCertificateService.getByIDCard(cjroneblLivingSubsidy.getIdCard());
                    try{
                        HttpClient httpclient = new HttpClient();
                        String SerialNumber = "0000000"+String.valueOf(System.currentTimeMillis());
                        PostMethod post = new PostMethod("http://ums.zj165.com:8888/sms/Api/Send.do");
                        post.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET,"gbk");
                        post.addParameter("SpCode", "001715");
                        post.addParameter("LoginName", "canlian");
                        post.addParameter("Password", "uTWFXMGWoNfhr7");
                        post.addParameter("MessageContent", "【北仑区残疾人联合会】您的康复补助审批已经通过。");
                        post.addParameter("UserNumber", "18368404744");
                        post.addParameter("SerialNumber", SerialNumber);
                        post.addParameter("ScheduleTime", "");
                        post.addParameter("ExtendAccessNum", "");
                        post.addParameter("f", "1");
                        httpclient.executeMethod(post);

                    }catch (Exception e) {
                        e.printStackTrace();
                    }
                }else{
                    // 审核退回
                    cjroneblRehabilitationSubsidy.setStatus("12"); //区残联退回至街道
                }

            }
        }
        cjroneblRehabilitationSubsidyService.updateAudioById(cjroneblRehabilitationSubsidy);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("cjrone_bl:cjroneblrehabilitationsubsidy:delete")
    public R delete(@RequestBody Integer[] ids){
		cjroneblRehabilitationSubsidyService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    @RequiresPermissions("cjrone_bl:cjroneblrehabilitationsubsidy:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = "";//bxProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        // 此处的headRows、titleRows 依据导入表格具体的数据行决定
        params_import.setHeadRows(2);
        params_import.setTitleRows(1);
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<CjroneblRehabilitationSubsidyEntity> cjroneblRehabilitationSubsidyList = new ArrayList<>();
        System.out.println("当前导入数据康复补助条数：" + list.size());
        list.forEach(item ->{
            if (null != item.get("身份证") && !"".equals(item.get("身份证").toString()) || (null != item.get("身份证号") && !"".equals(item.get("身份证号").toString()))) {
                item.put("id",item.get(""));
                item.put("name",item.get("姓名"));
                item.put("sex",item.get("性别"));
                item.put("nationailty",item.get("民族"));
                item.put("birthday",item.get("出生年月"));
                item.put("idCard",item.get("身份证号"));
                item.put("disabileId",item.get("残疾证号码"));
                item.put("disabilityType",item.get("残疾类别"));
                item.put("disabilityDegree",item.get("残疾等级"));
                item.put("liveAddress",item.get("家庭住址"));
                item.put("guardianName",item.get("监护人姓名"));
                item.put("guardianTelephone",item.get("监护人电话"));
                item.put("familyEconomy",item.get("家庭经济情况"));
                item.put("telephone",item.get("联系电话"));
                item.put("ylbx",item.get("享受医疗保险情况"));
                item.put("createId",item.get("创建人编号"));
                item.put("createTime",item.get("创建时间"));
                item.put("createName",item.get("创建人姓名"));
                item.put("status",item.get("状态"));
                item.put("statusOptions",item.get(""));
                item.put("signStatus",item.get(""));
                item.put("signatureStatus",item.get(""));
                cjroneblRehabilitationSubsidyList.add(new Gson().fromJson(new Gson().toJson(item), CjroneblRehabilitationSubsidyEntity.class));
            }
        });
        // 保存到数据库
        cjroneblRehabilitationSubsidyService.saveBatch(cjroneblRehabilitationSubsidyList);



        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    @RequiresPermissions("cjrone_bl:cjroneblrehabilitationsubsidy:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<CjroneblRehabilitationSubsidyEntity> cjroneblRehabilitationSubsidyEntityList = cjroneblRehabilitationSubsidyService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("康复补助", null, "康复补助");
        Workbook workbook = ExcelExportUtil.exportExcel(params, CjroneblRehabilitationSubsidyEntity.class, cjroneblRehabilitationSubsidyEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "康复补助" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

    /**
     * 生成电子签章 pdf
     */
    @RequestMapping("/printPDF/{id}")
    public R printPDF(@PathVariable("id") Integer id,@RequestParam("type") String type) throws IOException {
        System.out.print("id is :"+id);

        Map<String, Object> tmp_params = new HashMap<>();
        tmp_params.put("type","惠残事项康复补助");
        tmp_params.put("status","1");
        tmp_params.put("type_id",id);
        List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
        if (alive_list.size()>0) {
            CjroneSignatureEntity cjroneSignatureEntity = alive_list.get(0);
            if ("电子公章".equals(type)){
                String tmp_file_name = System.currentTimeMillis()+"_"+cjroneSignatureEntity.getFileName();
                Files.copy(new File(cjroneSignatureEntity.getFileActUrl()).toPath(),new File(cjroneProperties.getTempFilePath()+tmp_file_name).toPath());

                return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", tmp_file_name).put("signId",cjroneSignatureEntity.getId());

            }else {
                return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", cjroneSignatureEntity.getFileName()).put("signId",cjroneSignatureEntity.getId());
            }
        }else {
/*

            //根据编号获得详细信息
            CjroneLivingAllowanceEntity cjroneLivingAllowance = cjroneLivingAllowanceService.getById(id);
            //获得残疾证信息
            DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity = disabilityCertificateApplicationService.getByIDCard(cjroneLivingAllowance.getIdCard());

            String nums[] = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
            Calendar now = Calendar.getInstance();

            // 原pdf模板路径
            String templatePath = cjroneProperties.getTempletePath()+"生活补助模板.pdf";
            // 生成的新文件路径
            String newPDFPath = cjroneProperties.getSignaturePath()+"living_allowance_"+cjroneLivingAllowance.getIdCard()+".pdf";

            // 获得待生成的实体文件
            Map<String, String> map =new HashMap<String, String>();
            map.put("name", cjroneLivingAllowance.getName()==null?"":cjroneLivingAllowance.getName());
            if (cjroneLivingAllowance.getSex() == 1)
                map.put("sex", "男");
            else
                map.put("sex", "女");
            map.put("birthday", cjroneLivingAllowance.getBirthday()==null?"":cjroneLivingAllowance.getBirthday());
            map.put("mobile", cjroneLivingAllowance.getMobilePhone()==null?"":cjroneLivingAllowance.getMobilePhone());
            map.put("idCard", cjroneLivingAllowance.getIdCard()==null?"":cjroneLivingAllowance.getIdCard());

            if(cjroneLivingAllowance.getDisabilityCategory()==null){
                map.put("disabilityType", "");
            }
            else{
                if("1".equals(cjroneLivingAllowance.getDisabilityCategory())){
                    map.put("disabilityType", "视力残疾");
                }
                else if("2".equals(cjroneLivingAllowance.getDisabilityCategory())){
                    map.put("disabilityType", "听力残疾");
                }
                else if("3".equals(cjroneLivingAllowance.getDisabilityCategory())){
                    map.put("disabilityType", "智力残疾");
                }
                else if("4".equals(cjroneLivingAllowance.getDisabilityCategory())){
                    map.put("disabilityType", "精神残疾");
                }
                else if("5".equals(cjroneLivingAllowance.getDisabilityCategory())){
                    map.put("disabilityType", "肢体");
                }
                else if("6".equals(cjroneLivingAllowance.getDisabilityCategory())){
                    map.put("disabilityType", "言语残疾");
                }
                else if("7".equals(cjroneLivingAllowance.getDisabilityCategory())){
                    map.put("disabilityType", "肢体");
                }
                else{
                    map.put("disabilityType", cjroneLivingAllowance.getDisabilityCategory());
                }
            }
            map.put("disabilityDegree", cjroneLivingAllowance.getDisabilityDegree()==null?"无等":nums[Integer.parseInt(cjroneLivingAllowance.getDisabilityDegree())] + "级");
            map.put("disableId", cjroneLivingAllowance.getDisableId()==null?"":cjroneLivingAllowance.getDisableId());
            // 从残疾证表中获得详细的镇街道，村社区地址
            if(disabilityCertificateApplicationEntity!=null)
                map.put("nativeAddress", disabilityCertificateApplicationEntity.getNativeZhenName()==null?"":disabilityCertificateApplicationEntity.getNativeZhenName()+disabilityCertificateApplicationEntity.getNativeCunName());
            else
                map.put("nativeAddress", cjroneLivingAllowance.getNativePlace()==null?"":cjroneLivingAllowance.getNativePlace());
            map.put("bankName", cjroneLivingAllowance.getBankName()==null?"":cjroneLivingAllowance.getBankName());
            map.put("bankAccount", cjroneLivingAllowance.getBankAccount()==null?"":cjroneLivingAllowance.getBankAccount());
            map.put("guardianName", cjroneLivingAllowance.getGuardianName()==null?"":cjroneLivingAllowance.getGuardianName());
            map.put("guardianPhone", cjroneLivingAllowance.getGuardianPhone()==null?"":cjroneLivingAllowance.getGuardianPhone());
            map.put("economicSituation", cjroneLivingAllowance.getFamilyEconoCondition()==null?"":cjroneLivingAllowance.getFamilyEconoCondition());
            map.put("applicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
            //map.put("zhenApplicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
            //map.put("shiApplicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
            //开始关联补助金额
            Map<String, Object> mapparams =new HashMap<String, Object>();
            mapparams.put("type","生活补贴");
            List<CjroneTwoSubsidyStandardsEntity> cjroneTwoSubsidyStandardsEntityList=cjroneTwoSubsidyStandardsService.queryByMap(mapparams);
            if(cjroneTwoSubsidyStandardsEntityList!=null&&cjroneTwoSubsidyStandardsEntityList.size()>0){
                map.put("subsidymoney",cjroneTwoSubsidyStandardsEntityList.get(0).getMoney().toString());
            }
            else{
                map.put("subsidymoney"," ");
            }


            FileOutputStream out;
            int num = 1;//页数
            ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
            try {
                out = new FileOutputStream(newPDFPath);// 输出流
                Document doc = new Document();   //新建一个文档
                PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                doc.open();

                for (int i = 0; i < num; i++) {
                    bos[i] = new ByteArrayOutputStream();
                    PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                    PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                    AcroFields form = stamper.getAcroFields(); //获取文本域
                    // BaseFont
                    // 1、使用iTextAsian.jar中的字体
                    // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                    // 2、使用Windows系统字体(TrueType)
                    // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                    // 3、使用资源字体(ClassPath)
                    // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                    BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                    java.util.Iterator<String> it = form.getFields().keySet().iterator();
                    while (it.hasNext()) {
                        String name = it.next();
                        System.out.println(name + ":"+map.get(name));
                        form.setFieldProperty(name,"textfont",font,null);
                        // form.addSubstitutionFont(font);
                        form.setField(name, map.get(name));
                    }

                    stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                    stamper.close();

                }
                PdfImportedPage page = null;
                for (int i = 0; i < num; i++) {
                    page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                    copy.addPage(page);
                }
                doc.close();
                out.close();
            } catch (IOException e) {
                System.out.println("导出异常");
            } catch (DocumentException e) {
                System.out.println("文档异常");
            }

            // 生成电子签章pdf 并保存
            //String filePath = pdfUtils.pdfLivingAllowanceApplyToSignature(cjroneProperties.getTempletePath(), cjroneProperties.getSignaturePath(), livingentity);
            CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
            cjroneSignature.setUrl("/living_allowance_" + cjroneLivingAllowance.getIdCard()+".pdf");
            cjroneSignature.setFileActUrl(newPDFPath);
            cjroneSignature.setCreateDate(new Date());
            cjroneSignature.setCreateId(getUserId());
            cjroneSignature.setType("惠残事项生活补贴");
            cjroneSignature.setTypeId(cjroneLivingAllowance.getId());
            cjroneSignature.setFileName("living_allowance_"+cjroneLivingAllowance.getIdCard()+".pdf");
            cjroneSignature.setAccountId(getUserId().toString());
            cjroneSignature.setAccountName(getUser().getUsername());
            cjroneSignature.setStatus("1");

            cjroneSignatureService.save(cjroneSignature);
            // return R.ok().put("fileName", "signature_"+ PinYinUtil.getFullSpell(cjroneDisabilityHospital.getName())+".pdf").put("signId",cjroneSignature.getId());
            return R.ok().put("fileUrl", cjroneSignature.getUrl()).put("fileName", cjroneSignature.getFileName()).put("signId",cjroneSignature.getId());*/
            return R.ok();
        }
    }


}
