package com.hmit.kernespring.modules.cjrone_bl.controller;

import java.io.File;
import java.io.IOException;
import java.util.*;

import java.io.OutputStream;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.modules.sys.controller.AbstractController;
import org.apache.poi.ss.usermodel.Workbook;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblDisabilityCertificateRedemptionEntity;
import com.hmit.kernespring.modules.cjrone_bl.service.CjroneblDisabilityCertificateRedemptionService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 残疾人证换领
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-10 17:20:23
 */
@RestController
@RequestMapping("cjrone_bl/cjronebldisabilitycertificateredemption")
public class CjroneblDisabilityCertificateRedemptionController extends AbstractController {
    @Autowired
    private CjroneblDisabilityCertificateRedemptionService cjroneblDisabilityCertificateRedemptionService;



    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("cjrone_bl:cjronebldisabilitycertificateredemption:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = cjroneblDisabilityCertificateRedemptionService.queryPage(params);

        return R.ok().put("page", page);
    }

    @RequestMapping("/auditPass")
    @RequiresPermissions("cjrone_bl:cjronebldisabilitycertificateredemption:list")
    public R auditPass(@RequestParam Map<String, Object> params){

        Integer id = Integer.parseInt(params.get("id").toString());
        CjroneblDisabilityCertificateRedemptionEntity cd=new CjroneblDisabilityCertificateRedemptionEntity();
        cd.setId(id);
        cd.setStatus("审核通过");
        cjroneblDisabilityCertificateRedemptionService.updateById(cd);

        return R.ok();
    }




    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("cjrone_bl:cjronebldisabilitycertificateredemption:info")
    public R info(@PathVariable("id") Integer id){
		CjroneblDisabilityCertificateRedemptionEntity cjroneblDisabilityCertificateRedemption = cjroneblDisabilityCertificateRedemptionService.getById(id);

        return R.ok().put("cjroneblDisabilityCertificateRedemption", cjroneblDisabilityCertificateRedemption);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("cjrone_bl:cjronebldisabilitycertificateredemption:save")
    public R save(@RequestBody CjroneblDisabilityCertificateRedemptionEntity cjroneblDisabilityCertificateRedemption){

		cjroneblDisabilityCertificateRedemptionService.save(cjroneblDisabilityCertificateRedemption);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("cjrone_bl:cjronebldisabilitycertificateredemption:update")
    public R update(@RequestBody CjroneblDisabilityCertificateRedemptionEntity cjroneblDisabilityCertificateRedemption){
		cjroneblDisabilityCertificateRedemptionService.updateById(cjroneblDisabilityCertificateRedemption);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("cjrone_bl:cjronebldisabilitycertificateredemption:delete")
    public R delete(@RequestBody Integer[] ids){
		cjroneblDisabilityCertificateRedemptionService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    @RequiresPermissions("cjrone_bl:cjronebldisabilitycertificateredemption:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = "";
        //String file_path = bxProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        // 此处的headRows、titleRows 依据导入表格具体的数据行决定
        params_import.setHeadRows(2);
        params_import.setTitleRows(1);
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<CjroneblDisabilityCertificateRedemptionEntity> cjroneblDisabilityCertificateRedemptionList = new ArrayList<>();
        System.out.println("当前导入数据残疾人证换领条数：" + list.size());
        list.forEach(item ->{
            if (null != item.get("身份证") && !"".equals(item.get("身份证").toString()) || (null != item.get("身份证号") && !"".equals(item.get("身份证号").toString()))) {
                item.put("id",item.get(""));
                item.put("name",item.get("姓名"));
                item.put("idCard",item.get("身份证号码"));
                item.put("disableId",item.get("残疾证号"));
                item.put("disabilityCategory",item.get("残疾类别"));
                item.put("disabilityDegree",item.get("残疾等级"));
                item.put("disabilityInfo",item.get("残疾详情"));
                item.put("completeTime",item.get("持证时间"));
                item.put("status",item.get("状态"));
                item.put("createId",item.get("创建人编号"));
                item.put("createTime",item.get("创建时间"));
                item.put("createName",item.get("创建人姓名"));
                item.put("verifyTime",item.get("审核时间"));
                item.put("mobilePhone",item.get("手机号"));
                cjroneblDisabilityCertificateRedemptionList.add(new Gson().fromJson(new Gson().toJson(item), CjroneblDisabilityCertificateRedemptionEntity.class));
            }
        });
        // 保存到数据库
        cjroneblDisabilityCertificateRedemptionService.saveBatch(cjroneblDisabilityCertificateRedemptionList);



        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    @RequiresPermissions("cjrone_bl:cjronebldisabilitycertificateredemption:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<CjroneblDisabilityCertificateRedemptionEntity> cjroneblDisabilityCertificateRedemptionEntityList = cjroneblDisabilityCertificateRedemptionService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("残疾人证换领", null, "残疾人证换领");
        Workbook workbook = ExcelExportUtil.exportExcel(params, CjroneblDisabilityCertificateRedemptionEntity.class, cjroneblDisabilityCertificateRedemptionEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "残疾人证换领" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

}
