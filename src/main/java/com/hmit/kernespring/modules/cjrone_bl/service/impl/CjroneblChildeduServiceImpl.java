package com.hmit.kernespring.modules.cjrone_bl.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblRehabilitationSubsidyEntity;
import com.hmit.kernespring.modules.matter_application.dao.CjroneWelfareMatterApplicationDao;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.cjrone_bl.dao.CjroneblChildeduDao;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblChildeduEntity;
import com.hmit.kernespring.modules.cjrone_bl.service.CjroneblChildeduService;


@Service("cjroneblChildeduService")
public class CjroneblChildeduServiceImpl extends ServiceImpl<CjroneblChildeduDao, CjroneblChildeduEntity> implements CjroneblChildeduService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneblChildeduDao cjroneblChildeduDao;
    @Autowired
    private CjroneWelfareMatterApplicationDao cjroneWelfareMatterApplicationDao;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneblChildeduEntity cjroneblChildeduEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneblChildeduEntity.class);
        IPage<CjroneblChildeduEntity> page = this.page(
                new Query<CjroneblChildeduEntity>().getPage(params),
                new QueryWrapper<CjroneblChildeduEntity>()
            .eq(StringUtils.isNotBlank(cjroneblChildeduEntity.getId ()!=null && !"".equals(cjroneblChildeduEntity.getId ().toString())? cjroneblChildeduEntity.getId ().toString():null),"id", cjroneblChildeduEntity.getId ())
            .eq(StringUtils.isNotBlank(cjroneblChildeduEntity.getName ()!=null && !"".equals(cjroneblChildeduEntity.getName ().toString())? cjroneblChildeduEntity.getName ().toString():null),"name", cjroneblChildeduEntity.getName ())
            .eq(StringUtils.isNotBlank(cjroneblChildeduEntity.getIdCard ()!=null && !"".equals(cjroneblChildeduEntity.getIdCard ().toString())? cjroneblChildeduEntity.getIdCard ().toString():null),"id_card", cjroneblChildeduEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(cjroneblChildeduEntity.getBirthday ()!=null && !"".equals(cjroneblChildeduEntity.getBirthday ().toString())? cjroneblChildeduEntity.getBirthday ().toString():null),"birthday", cjroneblChildeduEntity.getBirthday ())
            .eq(StringUtils.isNotBlank(cjroneblChildeduEntity.getTelephoe ()!=null && !"".equals(cjroneblChildeduEntity.getTelephoe ().toString())? cjroneblChildeduEntity.getTelephoe ().toString():null),"telephoe", cjroneblChildeduEntity.getTelephoe ())
            .eq(StringUtils.isNotBlank(cjroneblChildeduEntity.getLiveAddress ()!=null && !"".equals(cjroneblChildeduEntity.getLiveAddress ().toString())? cjroneblChildeduEntity.getLiveAddress ().toString():null),"live_address", cjroneblChildeduEntity.getLiveAddress ())
            .eq(StringUtils.isNotBlank(cjroneblChildeduEntity.getDisableId ()!=null && !"".equals(cjroneblChildeduEntity.getDisableId ().toString())? cjroneblChildeduEntity.getDisableId ().toString():null),"disable_id", cjroneblChildeduEntity.getDisableId ())
            .eq(StringUtils.isNotBlank(cjroneblChildeduEntity.getCurrentSchool ()!=null && !"".equals(cjroneblChildeduEntity.getCurrentSchool ().toString())? cjroneblChildeduEntity.getCurrentSchool ().toString():null),"current_school", cjroneblChildeduEntity.getCurrentSchool ())
            .eq(StringUtils.isNotBlank(cjroneblChildeduEntity.getGrade ()!=null && !"".equals(cjroneblChildeduEntity.getGrade ().toString())? cjroneblChildeduEntity.getGrade ().toString():null),"grade", cjroneblChildeduEntity.getGrade ())
            .eq(StringUtils.isNotBlank(cjroneblChildeduEntity.getAdmissionTime ()!=null && !"".equals(cjroneblChildeduEntity.getAdmissionTime ().toString())? cjroneblChildeduEntity.getAdmissionTime ().toString():null),"admission_time", cjroneblChildeduEntity.getAdmissionTime ())
            .eq(StringUtils.isNotBlank(cjroneblChildeduEntity.getApplicantType ()!=null && !"".equals(cjroneblChildeduEntity.getApplicantType ().toString())? cjroneblChildeduEntity.getApplicantType ().toString():null),"applicant_type", cjroneblChildeduEntity.getApplicantType ())
            .eq(StringUtils.isNotBlank(cjroneblChildeduEntity.getFatherName ()!=null && !"".equals(cjroneblChildeduEntity.getFatherName ().toString())? cjroneblChildeduEntity.getFatherName ().toString():null),"father_name", cjroneblChildeduEntity.getFatherName ())
            .eq(StringUtils.isNotBlank(cjroneblChildeduEntity.getFatherIdcard ()!=null && !"".equals(cjroneblChildeduEntity.getFatherIdcard ().toString())? cjroneblChildeduEntity.getFatherIdcard ().toString():null),"father_idcard", cjroneblChildeduEntity.getFatherIdcard ())
            .eq(StringUtils.isNotBlank(cjroneblChildeduEntity.getMotherName ()!=null && !"".equals(cjroneblChildeduEntity.getMotherName ().toString())? cjroneblChildeduEntity.getMotherName ().toString():null),"mother_name", cjroneblChildeduEntity.getMotherName ())
            .eq(StringUtils.isNotBlank(cjroneblChildeduEntity.getMotherIdcard ()!=null && !"".equals(cjroneblChildeduEntity.getMotherIdcard ().toString())? cjroneblChildeduEntity.getMotherIdcard ().toString():null),"mother_idcard", cjroneblChildeduEntity.getMotherIdcard ())
            .eq(StringUtils.isNotBlank(cjroneblChildeduEntity.getSubsidyMoney ()!=null && !"".equals(cjroneblChildeduEntity.getSubsidyMoney ().toString())? cjroneblChildeduEntity.getSubsidyMoney ().toString():null),"subsidy_money", cjroneblChildeduEntity.getSubsidyMoney ())
            .eq(StringUtils.isNotBlank(cjroneblChildeduEntity.getIsHuJiRation ()!=null && !"".equals(cjroneblChildeduEntity.getIsHuJiRation ().toString())? cjroneblChildeduEntity.getIsHuJiRation ().toString():null),"is_hu_ji_ration", cjroneblChildeduEntity.getIsHuJiRation ())
            .eq(StringUtils.isNotBlank(cjroneblChildeduEntity.getCreateId ()!=null && !"".equals(cjroneblChildeduEntity.getCreateId ().toString())? cjroneblChildeduEntity.getCreateId ().toString():null),"create_id", cjroneblChildeduEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(cjroneblChildeduEntity.getCreateTime ()!=null && !"".equals(cjroneblChildeduEntity.getCreateTime ().toString())? cjroneblChildeduEntity.getCreateTime ().toString():null),"create_time", cjroneblChildeduEntity.getCreateTime ())
            .eq(StringUtils.isNotBlank(cjroneblChildeduEntity.getCreateName ()!=null && !"".equals(cjroneblChildeduEntity.getCreateName ().toString())? cjroneblChildeduEntity.getCreateName ().toString():null),"create_name", cjroneblChildeduEntity.getCreateName ())
            .eq(StringUtils.isNotBlank(cjroneblChildeduEntity.getStatus ()!=null && !"".equals(cjroneblChildeduEntity.getStatus ().toString())? cjroneblChildeduEntity.getStatus ().toString():null),"status", cjroneblChildeduEntity.getStatus ())
            .eq(StringUtils.isNotBlank(cjroneblChildeduEntity.getStatusOptions ()!=null && !"".equals(cjroneblChildeduEntity.getStatusOptions ().toString())? cjroneblChildeduEntity.getStatusOptions ().toString():null),"status_options", cjroneblChildeduEntity.getStatusOptions ())
            .eq(StringUtils.isNotBlank(cjroneblChildeduEntity.getSignStatus ()!=null && !"".equals(cjroneblChildeduEntity.getSignStatus ().toString())? cjroneblChildeduEntity.getSignStatus ().toString():null),"sign_status", cjroneblChildeduEntity.getSignStatus ())
            .eq(StringUtils.isNotBlank(cjroneblChildeduEntity.getSignatureStatus ()!=null && !"".equals(cjroneblChildeduEntity.getSignatureStatus ().toString())? cjroneblChildeduEntity.getSignatureStatus ().toString():null),"signature_status", cjroneblChildeduEntity.getSignatureStatus ())
            .eq(StringUtils.isNotBlank(cjroneblChildeduEntity.getFatherDisid ()!=null && !"".equals(cjroneblChildeduEntity.getFatherDisid ().toString())? cjroneblChildeduEntity.getFatherDisid ().toString():null),"father_disid", cjroneblChildeduEntity.getFatherDisid ())
            .eq(StringUtils.isNotBlank(cjroneblChildeduEntity.getMotherDisid ()!=null && !"".equals(cjroneblChildeduEntity.getMotherDisid ().toString())? cjroneblChildeduEntity.getMotherDisid ().toString():null),"mother_disid", cjroneblChildeduEntity.getMotherDisid ())
        );

        // 开始为状态赋值
        page.getRecords().forEach( item -> {
            if ("1".equals(item.getStatus())){
                item.setStatus("申请人待手签");
            }else if ("2".equals(item.getStatus())){
                item.setStatus("街道待审核");
            }else if ("6".equals(item.getStatus())){
                item.setStatus("区残联经办人待审核");
            }else if ("7".equals(item.getStatus()) ){
                item.setStatus("区残联负责人待审核");
            } else if ("8".equals(item.getStatus())){
                item.setStatus("通过");
            }else if ("0".equals(item.getStatus())){
                item.setStatus("禁用");
            }else if("12".equals(item.getStatus())){
                item.setStatus("退回");  // 区残联退回至街道
            }

            if("1".equals(item.getSignStatus())){
                item.setSignStatus("申请人待手签");
            }else if("2".equals(item.getSignStatus())){
                item.setSignStatus("镇街道待手签");
            }else if("6".equals(item.getSignStatus())){
                item.setSignStatus("区残联经办人待手签");
            }else if("7".equals(item.getSignStatus())){
                item.setSignStatus("区残联负责人待手签");
            }else if("8".equals(item.getSignStatus())){
                item.setSignStatus("完成手签");
            }

            if("1".equals(item.getSignatureStatus())){
                item.setSignatureStatus("无");
            }else if("2".equals(item.getSignatureStatus())){
                item.setSignatureStatus("镇街道待电子签章");
            }else if("4".equals(item.getSignatureStatus())){
                item.setSignatureStatus("民政待电子签章");
            }else if("5".equals(item.getSignatureStatus())){
                item.setSignatureStatus("区残联待电子签章");
            }else if("6".equals(item.getSignatureStatus())){
                item.setSignatureStatus("完成电子签章");
            }



        });


        return new PageUtils(page);
    }
    @Override
    public List<CjroneblChildeduEntity> queryExportData(Map<String, Object> params) {
            return cjroneblChildeduDao.queryExportData(params);
    }

    @Override
    public void updateByIdSign(CjroneblChildeduEntity cjroneblChildeduEntity) {
        super.updateById(cjroneblChildeduEntity);
    }

    @Override
    public void updateAudioById(CjroneblChildeduEntity cjroneblChildeduEntity) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",cjroneblChildeduEntity.getId());
        map.put("matter_name","残疾人子女教育补贴");
        map.put("verify_time",new Date());
        map.put("status",cjroneblChildeduEntity.getStatus());
        map.put("statusOptions",cjroneblChildeduEntity.getStatusOptions());
        if (cjroneblChildeduEntity.getStatus() != null){
            cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        }
        super.updateById(cjroneblChildeduEntity);
    }

    @Override
    public boolean updateById(CjroneblChildeduEntity entity) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",entity.getId());
        map.put("matter_name","残疾人子女教育补贴");
        map.put("status",entity.getStatus());
        map.put("signatureStatus",entity.getSignatureStatus());  // 只需要更改电子印章的状态
        map.put("statusOptions",entity.getStatusOptions());

        cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        return super.updateById(entity);
    }

}