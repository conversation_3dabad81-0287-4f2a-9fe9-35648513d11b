package com.hmit.kernespring.modules.cjrone_bl.controller;

import java.io.File;
import java.io.IOException;
import java.util.*;

import java.io.OutputStream;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.modules.sys.controller.AbstractController;
import org.apache.poi.ss.usermodel.Workbook;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hmit.kernespring.modules.cjrone_bl.entity.LivingAllowanceSyncDataEntity;
import com.hmit.kernespring.modules.cjrone_bl.service.LivingAllowanceSyncDataService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 生活补助金比对数据
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-12-21 16:12:52
 */
@RestController
@RequestMapping("cjrone_bl/livingallowancesyncdata")
public class LivingAllowanceSyncDataController extends AbstractController {
    @Autowired
    private LivingAllowanceSyncDataService livingAllowanceSyncDataService;



    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("cjrone_bl:livingallowancesyncdata:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = livingAllowanceSyncDataService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("cjrone_bl:livingallowancesyncdata:info")
    public R info(@PathVariable("id") Integer id){
		LivingAllowanceSyncDataEntity livingAllowanceSyncData = livingAllowanceSyncDataService.getById(id);

        return R.ok().put("livingAllowanceSyncData", livingAllowanceSyncData);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("cjrone_bl:livingallowancesyncdata:save")
    public R save(@RequestBody LivingAllowanceSyncDataEntity livingAllowanceSyncData){

		livingAllowanceSyncDataService.save(livingAllowanceSyncData);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("cjrone_bl:livingallowancesyncdata:update")
    public R update(@RequestBody LivingAllowanceSyncDataEntity livingAllowanceSyncData){
		livingAllowanceSyncDataService.updateById(livingAllowanceSyncData);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("cjrone_bl:livingallowancesyncdata:delete")
    public R delete(@RequestBody Integer[] ids){
		livingAllowanceSyncDataService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    @RequiresPermissions("cjrone_bl:livingallowancesyncdata:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = ""; //bxProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        // 此处的headRows、titleRows 依据导入表格具体的数据行决定
        params_import.setHeadRows(2);
        params_import.setTitleRows(1);
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<LivingAllowanceSyncDataEntity> livingAllowanceSyncDataList = new ArrayList<>();
        System.out.println("当前导入数据生活补助金比对数据条数：" + list.size());
        list.forEach(item ->{
            if (null != item.get("身份证") && !"".equals(item.get("身份证").toString()) || (null != item.get("身份证号") && !"".equals(item.get("身份证号").toString()))) {
                item.put("id",item.get(""));
                item.put("name",item.get("残疾人姓名"));
                item.put("idCard",item.get("身份证号码"));
                item.put("createTime",item.get("创建时间"));
                item.put("familyEconoCondition",item.get("家庭经济情况"));
                item.put("isDead",item.get("是否死亡"));
                item.put("status",item.get("0 异常 1正常"));
                item.put("isFx",item.get("是否服刑"));
                item.put("isHkqy",item.get("是否户口迁移"));
                item.put("fxInfo",item.get("服刑信息"));
                item.put("hkqyInfo",item.get("户口迁移信息"));
                item.put("exceptionReason",item.get(""));
                item.put("isShbt",item.get("是否享受生活补贴"));
                item.put("isZgyl",item.get("是否职工基本养老保险"));
                item.put("ym",item.get("年和月"));
                item.put("isAge",item.get("年龄是否符合要求"));
                livingAllowanceSyncDataList.add(new Gson().fromJson(new Gson().toJson(item), LivingAllowanceSyncDataEntity.class));
            }
        });
        // 保存到数据库
        livingAllowanceSyncDataService.saveBatch(livingAllowanceSyncDataList);



        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    @RequiresPermissions("cjrone_bl:livingallowancesyncdata:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<LivingAllowanceSyncDataEntity> livingAllowanceSyncDataEntityList = livingAllowanceSyncDataService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("生活补助金比对数据", null, "生活补助金比对数据");
        Workbook workbook = ExcelExportUtil.exportExcel(params, LivingAllowanceSyncDataEntity.class, livingAllowanceSyncDataEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "生活补助金比对数据" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

}
