package com.hmit.kernespring.modules.cjrone_bl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblBusinessGrantEntity;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblRehabilitationSubsidyEntity;

import java.util.Map;

import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-22 19:54:10
 */
public interface CjroneblBusinessGrantService extends IService<CjroneblBusinessGrantEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneblBusinessGrantEntity> queryExportData(Map<String, Object> params);

    // 电子盖章专用
    boolean updateById(CjroneblBusinessGrantEntity cjroneblBusinessGrantEntity);

    void updateByIdSign(CjroneblBusinessGrantEntity cjroneblBusinessGrantEntity);

    void updateAudioById(CjroneblBusinessGrantEntity cjroneblBusinessGrantEntity);
}

