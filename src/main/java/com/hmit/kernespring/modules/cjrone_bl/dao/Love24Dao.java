package com.hmit.kernespring.modules.cjrone_bl.dao;

import com.hmit.kernespring.modules.cjrone_bl.entity.Love24Entity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 智慧爱心24小时
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-11-25 17:44:36
 */
@Mapper
public interface Love24Dao extends BaseMapper<Love24Entity> {
    List<Love24Entity> queryExportData(Map<String, Object> params);
	
}
