package com.hmit.kernespring.modules.cjrone_bl.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-22 19:54:10
 */
@Data
@TableName("cjronebl_business_grant")
public class CjroneblBusinessGrantEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
@TableId
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 姓名
	 */
@Excel(name = "姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 性别
	 */
@Excel(name = "性别", height = 20, width = 30, isImportField = "true_st")
private String sex;
	/**
	 * 年龄
	 */
@Excel(name = "年龄", height = 20, width = 30, isImportField = "true_st")
private Integer age;
	/**
	 * 残疾人证号
	 */
@Excel(name = "残疾人证号", height = 20, width = 30, isImportField = "true_st")
private String disableId;
	/**
	 * 电话
	 */
@Excel(name = "电话", height = 20, width = 30, isImportField = "true_st")
private String telephone;
	/**
	 * 家庭经济情况
	 */
@Excel(name = "家庭经济情况", height = 20, width = 30, isImportField = "true_st")
private String familyEconomy;
	/**
	 * 学历
	 */
@Excel(name = "学历", height = 20, width = 30, isImportField = "true_st")
private String educationDegree;
	/**
	 * 家庭详细地址
	 */
@Excel(name = "家庭详细地址", height = 20, width = 30, isImportField = "true_st")
private String liveAddress;
	/**
	 * 初次创业时间
	 */
@Excel(name = "初次创业时间", height = 20, width = 30, isImportField = "true_st")
private String startTime;
	/**
	 * 经营地址
	 */
@Excel(name = "经营地址", height = 20, width = 30, isImportField = "true_st")
private String manageAddress;
	/**
	 * 经营范围
	 */
@Excel(name = "经营范围", height = 20, width = 30, isImportField = "true_st")
private String manageRange;
	/**
	 * 是否处于大学生自主创业
	 */
@Excel(name = "是否处于大学生自主创业", height = 20, width = 30, isImportField = "true_st")
private String isCollege;
	/**
	 * 补助
	 */
@Excel(name = "补助", height = 20, width = 30, isImportField = "true_st")
private String subsidyMoney;
	/**
	 * 补助理由
	 */
@Excel(name = "补助理由", height = 20, width = 30, isImportField = "true_st")
private String subsidyReason;
	/**
	 * 是否就业
	 */
@Excel(name = "是否就业", height = 20, width = 30, isImportField = "true_st")
private String isEmployment;
	/**
	 * 是否初次创业
	 */
@Excel(name = "是否初次创业", height = 20, width = 30, isImportField = "true_st")
private String isFirsttime;
	/**
	 * 是否实质经营六个月
	 */
@Excel(name = "是否实质经营六个月", height = 20, width = 30, isImportField = "true_st")
private String isSixManage;
	/**
	 * 创建人编号
	 */
@Excel(name = "创建人编号", height = 20, width = 30, isImportField = "true_st")
private Integer createId;
	/**
	 * 创建时间
	 */
@Excel(name = "创建时间", height = 20, width = 30, isImportField = "true_st")
private String createTime;
	/**
	 * 创建人姓名
	 */
@Excel(name = "创建人姓名", height = 20, width = 30, isImportField = "true_st")
private String createName;
	/**
	 * 状态
	 */
@Excel(name = "状态", height = 20, width = 30, isImportField = "true_st")
private String status;
	/**
	 * 
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String statusOptions;
	/**
	 * 
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String signStatus;
	/**
	 * 
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String signatureStatus;

}
