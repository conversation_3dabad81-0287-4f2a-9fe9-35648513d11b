package com.hmit.kernespring.modules.cjrone_bl.dao;

import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblHospitalizationAllowanceEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-01-13 01:05:40
 */
@Mapper
public interface CjroneblHospitalizationAllowanceDao extends BaseMapper<CjroneblHospitalizationAllowanceEntity> {
    List<CjroneblHospitalizationAllowanceEntity> queryExportData(Map<String, Object> params);
	
}
