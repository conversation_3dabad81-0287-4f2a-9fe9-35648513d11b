package com.hmit.kernespring.modules.cjrone_bl.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblChildeduEntity;
import com.hmit.kernespring.modules.matter_application.dao.CjroneWelfareMatterApplicationDao;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.cjrone_bl.dao.Love24Dao;
import com.hmit.kernespring.modules.cjrone_bl.entity.Love24Entity;
import com.hmit.kernespring.modules.cjrone_bl.service.Love24Service;


@Service("love24Service")
public class Love24ServiceImpl extends ServiceImpl<Love24Dao, Love24Entity> implements Love24Service {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private Love24Dao love24Dao;
    @Autowired
    private CjroneWelfareMatterApplicationDao cjroneWelfareMatterApplicationDao;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        Love24Entity love24Entity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, Love24Entity.class);
        String startDay = love24Entity.getStartDay ()!=null && !"".equals(love24Entity.getStartDay ().toString())? love24Entity.getStartDay ().toString():null;
        String endDay = love24Entity.getEndDay ()!=null && !"".equals(love24Entity.getEndDay ().toString())? love24Entity.getEndDay ().toString():null;
        IPage<Love24Entity> page = this.page(
                new Query<Love24Entity>().getPage(params),
                new QueryWrapper<Love24Entity>()
            .eq(StringUtils.isNotBlank(love24Entity.getId ()!=null && !"".equals(love24Entity.getId ().toString())? love24Entity.getId ().toString():null),"id", love24Entity.getId ())
            .eq(StringUtils.isNotBlank(love24Entity.getName ()!=null && !"".equals(love24Entity.getName ().toString())? love24Entity.getName ().toString():null),"name", love24Entity.getName ())
            .eq(StringUtils.isNotBlank(love24Entity.getSex ()!=null && !"".equals(love24Entity.getSex ().toString())? love24Entity.getSex ().toString():null),"sex", love24Entity.getSex ())
            .eq(StringUtils.isNotBlank(love24Entity.getIdCard()!=null && !"".equals(love24Entity.getIdCard ().toString())? love24Entity.getIdCard ().toString():null),"id_card", love24Entity.getIdCard ())
            .eq(StringUtils.isNotBlank(love24Entity.getDisableId ()!=null && !"".equals(love24Entity.getDisableId ().toString())? love24Entity.getDisableId ().toString():null),"disable_id", love24Entity.getDisableId ())
            .eq(StringUtils.isNotBlank(love24Entity.getDisableType ()!=null && !"".equals(love24Entity.getDisableType ().toString())? love24Entity.getDisableType ().toString():null),"disable_type", love24Entity.getDisableType ())
            .eq(StringUtils.isNotBlank(love24Entity.getGuardian ()!=null && !"".equals(love24Entity.getGuardian ().toString())? love24Entity.getGuardian ().toString():null),"guardian", love24Entity.getGuardian ())
            .eq(StringUtils.isNotBlank(love24Entity.getLiveAddress ()!=null && !"".equals(love24Entity.getLiveAddress ().toString())? love24Entity.getLiveAddress ().toString():null),"live_address", love24Entity.getLiveAddress ())
            .eq(StringUtils.isNotBlank(love24Entity.getTelephone ()!=null && !"".equals(love24Entity.getTelephone ().toString())? love24Entity.getTelephone ().toString():null),"telephone", love24Entity.getTelephone ())
            .eq(StringUtils.isNotBlank(love24Entity.getRelation1 ()!=null && !"".equals(love24Entity.getRelation1 ().toString())? love24Entity.getRelation1 ().toString():null),"relation1", love24Entity.getRelation1 ())
            .eq(StringUtils.isNotBlank(love24Entity.getName1 ()!=null && !"".equals(love24Entity.getName1 ().toString())? love24Entity.getName1 ().toString():null),"name1", love24Entity.getName1 ())
            .eq(StringUtils.isNotBlank(love24Entity.getIdcard1 ()!=null && !"".equals(love24Entity.getIdcard1 ().toString())? love24Entity.getIdcard1 ().toString():null),"idcard1", love24Entity.getIdcard1 ())
            .eq(StringUtils.isNotBlank(love24Entity.getTel1 ()!=null && !"".equals(love24Entity.getTel1 ().toString())? love24Entity.getTel1 ().toString():null),"tel1", love24Entity.getTel1 ())
            .eq(StringUtils.isNotBlank(love24Entity.getRelation2 ()!=null && !"".equals(love24Entity.getRelation2 ().toString())? love24Entity.getRelation2 ().toString():null),"relation2", love24Entity.getRelation2 ())
            .eq(StringUtils.isNotBlank(love24Entity.getName2 ()!=null && !"".equals(love24Entity.getName2 ().toString())? love24Entity.getName2 ().toString():null),"name2", love24Entity.getName2 ())
            .eq(StringUtils.isNotBlank(love24Entity.getIdcard2 ()!=null && !"".equals(love24Entity.getIdcard2 ().toString())? love24Entity.getIdcard2 ().toString():null),"idcard2", love24Entity.getIdcard2 ())
            .eq(StringUtils.isNotBlank(love24Entity.getTel2 ()!=null && !"".equals(love24Entity.getTel2 ().toString())? love24Entity.getTel2 ().toString():null),"tel2", love24Entity.getTel2 ())
            .eq(StringUtils.isNotBlank(love24Entity.getRelation3 ()!=null && !"".equals(love24Entity.getRelation3 ().toString())? love24Entity.getRelation3 ().toString():null),"relation3", love24Entity.getRelation3 ())
            .eq(StringUtils.isNotBlank(love24Entity.getName3 ()!=null && !"".equals(love24Entity.getName3 ().toString())? love24Entity.getName3 ().toString():null),"name3", love24Entity.getName3 ())
            .eq(StringUtils.isNotBlank(love24Entity.getIdcard3 ()!=null && !"".equals(love24Entity.getIdcard3 ().toString())? love24Entity.getIdcard3 ().toString():null),"idcard3", love24Entity.getIdcard3 ())
            .eq(StringUtils.isNotBlank(love24Entity.getTel3 ()!=null && !"".equals(love24Entity.getTel3 ().toString())? love24Entity.getTel3 ().toString():null),"tel3", love24Entity.getTel3 ())
            .eq(StringUtils.isNotBlank(love24Entity.getRelation4 ()!=null && !"".equals(love24Entity.getRelation4 ().toString())? love24Entity.getRelation4 ().toString():null),"relation4", love24Entity.getRelation4 ())
            .eq(StringUtils.isNotBlank(love24Entity.getName4 ()!=null && !"".equals(love24Entity.getName4 ().toString())? love24Entity.getName4 ().toString():null),"name4", love24Entity.getName4 ())
            .eq(StringUtils.isNotBlank(love24Entity.getIdcard4 ()!=null && !"".equals(love24Entity.getIdcard4 ().toString())? love24Entity.getIdcard4 ().toString():null),"idcard4", love24Entity.getIdcard4 ())
            .eq(StringUtils.isNotBlank(love24Entity.getTel4 ()!=null && !"".equals(love24Entity.getTel4 ().toString())? love24Entity.getTel4 ().toString():null),"tel4", love24Entity.getTel4 ())
            .eq(StringUtils.isNotBlank(love24Entity.getType ()!=null && !"".equals(love24Entity.getType ().toString())? love24Entity.getType ().toString():null),"type", love24Entity.getType ())
            .eq(StringUtils.isNotBlank(love24Entity.getTypeTelephone ()!=null && !"".equals(love24Entity.getTypeTelephone ().toString())? love24Entity.getTypeTelephone ().toString():null),"type_telephone", love24Entity.getTypeTelephone ())
            .eq(StringUtils.isNotBlank(love24Entity.getRelation5 ()!=null && !"".equals(love24Entity.getRelation5 ().toString())? love24Entity.getRelation5 ().toString():null),"relation5", love24Entity.getRelation5 ())
            .eq(StringUtils.isNotBlank(love24Entity.getName5 ()!=null && !"".equals(love24Entity.getName5 ().toString())? love24Entity.getName5 ().toString():null),"name5", love24Entity.getName5 ())
            .eq(StringUtils.isNotBlank(love24Entity.getTel5 ()!=null && !"".equals(love24Entity.getTel5 ().toString())? love24Entity.getTel5 ().toString():null),"tel5", love24Entity.getTel5 ())
            .eq(StringUtils.isNotBlank(love24Entity.getRelation6 ()!=null && !"".equals(love24Entity.getRelation6 ().toString())? love24Entity.getRelation6 ().toString():null),"relation6", love24Entity.getRelation6 ())
            .eq(StringUtils.isNotBlank(love24Entity.getName6 ()!=null && !"".equals(love24Entity.getName6 ().toString())? love24Entity.getName6 ().toString():null),"name6", love24Entity.getName6 ())
            .eq(StringUtils.isNotBlank(love24Entity.getTel6 ()!=null && !"".equals(love24Entity.getTel6 ().toString())? love24Entity.getTel6 ().toString():null),"tel6", love24Entity.getTel6 ())
            .eq(StringUtils.isNotBlank(love24Entity.getRelation7 ()!=null && !"".equals(love24Entity.getRelation7 ().toString())? love24Entity.getRelation7 ().toString():null),"relation7", love24Entity.getRelation7 ())
            .eq(StringUtils.isNotBlank(love24Entity.getName7 ()!=null && !"".equals(love24Entity.getName7 ().toString())? love24Entity.getName7 ().toString():null),"name7", love24Entity.getName7 ())
            .eq(StringUtils.isNotBlank(love24Entity.getTel7 ()!=null && !"".equals(love24Entity.getTel7 ().toString())? love24Entity.getTel7 ().toString():null),"tel7", love24Entity.getTel7 ())
            .eq(StringUtils.isNotBlank(love24Entity.getRelation8 ()!=null && !"".equals(love24Entity.getRelation8 ().toString())? love24Entity.getRelation8 ().toString():null),"relation8", love24Entity.getRelation8 ())
            .eq(StringUtils.isNotBlank(love24Entity.getName8 ()!=null && !"".equals(love24Entity.getName8 ().toString())? love24Entity.getName8 ().toString():null),"name8", love24Entity.getName8 ())
            .eq(StringUtils.isNotBlank(love24Entity.getTel8 ()!=null && !"".equals(love24Entity.getTel8 ().toString())? love24Entity.getTel8 ().toString():null),"tel8", love24Entity.getTel8 ())
            .eq(StringUtils.isNotBlank(love24Entity.getCreateId ()!=null && !"".equals(love24Entity.getCreateId ().toString())? love24Entity.getCreateId ().toString():null),"create_id", love24Entity.getCreateId ())
            .eq(StringUtils.isNotBlank(love24Entity.getCreateTime ()!=null && !"".equals(love24Entity.getCreateTime ().toString())? love24Entity.getCreateTime ().toString():null),"create_time", love24Entity.getCreateTime ())
            .between(StringUtils.isNotBlank(startDay) && StringUtils.isNotBlank(endDay),"create_time",startDay,endDay)
            .eq(StringUtils.isNotBlank(love24Entity.getCreateName ()!=null && !"".equals(love24Entity.getCreateName ().toString())? love24Entity.getCreateName ().toString():null),"create_name", love24Entity.getCreateName ())
            .eq(StringUtils.isNotBlank(love24Entity.getStatus ()!=null && !"".equals(love24Entity.getStatus ().toString())? love24Entity.getStatus ().toString():null),"status", love24Entity.getStatus ())
            .eq(StringUtils.isNotBlank(love24Entity.getStatusOptions ()!=null && !"".equals(love24Entity.getStatusOptions ().toString())? love24Entity.getStatusOptions ().toString():null),"status_options", love24Entity.getStatusOptions ())
            .eq(StringUtils.isNotBlank(love24Entity.getSignStatus ()!=null && !"".equals(love24Entity.getSignStatus ().toString())? love24Entity.getSignStatus ().toString():null),"sign_status", love24Entity.getSignStatus ())
            .eq(StringUtils.isNotBlank(love24Entity.getSignatureStatus ()!=null && !"".equals(love24Entity.getSignatureStatus ().toString())? love24Entity.getSignatureStatus ().toString():null),"signature_status", love24Entity.getSignatureStatus ())
            .eq(StringUtils.isNotBlank(love24Entity.getReturnStatus ()!=null && !"".equals(love24Entity.getReturnStatus ().toString())? love24Entity.getReturnStatus ().toString():null),"return_status", love24Entity.getReturnStatus ())
            .eq(StringUtils.isNotBlank(love24Entity.getFamilyLove ()!=null && !"".equals(love24Entity.getFamilyLove ().toString())? love24Entity.getFamilyLove ().toString():null),"family_love", love24Entity.getFamilyLove ())
            .eq(StringUtils.isNotBlank(love24Entity.getApplicationType ()!=null && !"".equals(love24Entity.getApplicationType ().toString())? love24Entity.getApplicationType ().toString():null),"application_type", love24Entity.getApplicationType ())
            .eq(StringUtils.isNotBlank(love24Entity.getApplicationType ()!=null && !"".equals(love24Entity.getApplicationType ().toString())? love24Entity.getApplicationType ().toString():null),"application_type", love24Entity.getApplicationType ())
        );

        // 开始为状态赋值
        page.getRecords().forEach( item -> {
            if ("1".equals(item.getStatus())){
                item.setStatus("申请人待手签");
            }else if ("2".equals(item.getStatus())){
                item.setStatus("街道待审核");
            }else if ("6".equals(item.getStatus())){
                item.setStatus("区残联经办人待审核");
            }else if ("7".equals(item.getStatus()) ){
                item.setStatus("区残联负责人待审核");
            } else if ("8".equals(item.getStatus())){
                item.setStatus("通过");
            }else if ("0".equals(item.getStatus())){
                item.setStatus("禁用");
            }else if("12".equals(item.getStatus())){
                item.setStatus("退回");  // 区残联退回至街道
            }

            if("1".equals(item.getSignStatus())){
                item.setSignStatus("申请人待手签");
            }else if("2".equals(item.getSignStatus())){
                item.setSignStatus("镇街道待手签");
            }else if("6".equals(item.getSignStatus())){
                item.setSignStatus("区残联经办人待手签");
            }else if("7".equals(item.getSignStatus())){
                item.setSignStatus("区残联负责人待手签");
            }else if("8".equals(item.getSignStatus())){
                item.setSignStatus("完成手签");
            }

            if("1".equals(item.getSignatureStatus())){
                item.setSignatureStatus("无");
            }else if("2".equals(item.getSignatureStatus())){
                item.setSignatureStatus("镇街道待电子签章");
            }else if("4".equals(item.getSignatureStatus())){
                item.setSignatureStatus("民政待电子签章");
            }else if("5".equals(item.getSignatureStatus())){
                item.setSignatureStatus("区残联待电子签章");
            }else if("6".equals(item.getSignatureStatus())){
                item.setSignatureStatus("完成电子签章");
            }

        });

        return new PageUtils(page);
    }
    @Override
    public List<Love24Entity> queryExportData(Map<String, Object> params) {
        Love24Entity love24Entity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, Love24Entity.class);

        List<Love24Entity> entitys = list(new QueryWrapper<Love24Entity>()
                .eq(StringUtils.isNotBlank(love24Entity.getName ()!=null && !"".equals(love24Entity.getName ().toString())? love24Entity.getName ().toString():null),"name", love24Entity.getName ())
                .eq(StringUtils.isNotBlank(love24Entity.getDisableId ()!=null && !"".equals(love24Entity.getDisableId ().toString())? love24Entity.getDisableId ().toString():null),"disable_id", love24Entity.getDisableId ())
                .eq(StringUtils.isNotBlank(love24Entity.getIdCard()!=null && !"".equals(love24Entity.getIdCard ().toString())? love24Entity.getIdCard ().toString():null),"id_card", love24Entity.getIdCard ())
                .eq(StringUtils.isNotBlank(love24Entity.getApplicationType ()!=null && !"".equals(love24Entity.getApplicationType ().toString())? love24Entity.getApplicationType ().toString():null),"application_type", love24Entity.getApplicationType ())
        );
        return entitys;
    }


    @Override
    public boolean updateById(Love24Entity entity) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",entity.getId());
        map.put("matter_name","智慧爱心24小时");
        map.put("status",entity.getStatus());
        map.put("signatureStatus",entity.getSignatureStatus());  // 只需要更改电子印章的状态
        map.put("statusOptions",entity.getStatusOptions());

        cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        return super.updateById(entity);
    }

    @Override
    public void updateByIdSign(Love24Entity love24Entity) {
        super.updateById(love24Entity);
    }

    @Override
    public void updateAudioById(Love24Entity love24Entity) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",love24Entity.getId());
        map.put("matter_name","智慧爱心24小时");
        map.put("verify_time",new Date());
        map.put("status",love24Entity.getStatus());
        map.put("statusOptions",love24Entity.getStatusOptions());
        if (love24Entity.getStatus() != null){
            cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        }
        super.updateById(love24Entity);
    }

}
