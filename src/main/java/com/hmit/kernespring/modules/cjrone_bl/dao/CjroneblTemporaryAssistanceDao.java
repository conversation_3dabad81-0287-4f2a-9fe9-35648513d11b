package com.hmit.kernespring.modules.cjrone_bl.dao;

import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblTemporaryAssistanceEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 残疾人临时救助
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-13 13:51:37
 */
@Mapper
public interface CjroneblTemporaryAssistanceDao extends BaseMapper<CjroneblTemporaryAssistanceEntity> {
    List<CjroneblTemporaryAssistanceEntity> queryExportData(Map<String, Object> params);
	
}
