package com.hmit.kernespring.modules.cjrone_bl.dao;

import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblRehabilitationSubsidyEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 康复补助
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-13 16:03:17
 */
@Mapper
public interface CjroneblRehabilitationSubsidyDao extends BaseMapper<CjroneblRehabilitationSubsidyEntity> {
    List<CjroneblRehabilitationSubsidyEntity> queryExportData(Map<String, Object> params);
	
}
