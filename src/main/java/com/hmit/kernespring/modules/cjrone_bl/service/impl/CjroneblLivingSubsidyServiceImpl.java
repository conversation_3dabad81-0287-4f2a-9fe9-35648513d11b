package com.hmit.kernespring.modules.cjrone_bl.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.cjrone.entity.CjroneLivingAllowanceEntity;
import com.hmit.kernespring.modules.matter_application.dao.CjroneWelfareMatterApplicationDao;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.cjrone_bl.dao.CjroneblLivingSubsidyDao;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblLivingSubsidyEntity;
import com.hmit.kernespring.modules.cjrone_bl.service.CjroneblLivingSubsidyService;


@Service("cjroneblLivingSubsidyService")
public class CjroneblLivingSubsidyServiceImpl extends ServiceImpl<CjroneblLivingSubsidyDao, CjroneblLivingSubsidyEntity> implements CjroneblLivingSubsidyService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneblLivingSubsidyDao cjroneblLivingSubsidyDao;
    @Autowired
    private CjroneWelfareMatterApplicationDao cjroneWelfareMatterApplicationDao;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneblLivingSubsidyEntity cjroneblLivingSubsidyEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneblLivingSubsidyEntity.class);
        String usertype=params.get("usertype") !=null && !"".equals(params.get("usertype").toString()) ? params.get("usertype").toString() : null;  //用户类型
        IPage<CjroneblLivingSubsidyEntity> page = this.page(
                new Query<CjroneblLivingSubsidyEntity>().getPage(params),
                new QueryWrapper<CjroneblLivingSubsidyEntity>()
            .eq(StringUtils.isNotBlank(cjroneblLivingSubsidyEntity.getId ()!=null && !"".equals(cjroneblLivingSubsidyEntity.getId ().toString())? cjroneblLivingSubsidyEntity.getId ().toString():null),"id", cjroneblLivingSubsidyEntity.getId ())
            .eq(StringUtils.isNotBlank(cjroneblLivingSubsidyEntity.getName ()!=null && !"".equals(cjroneblLivingSubsidyEntity.getName ().toString())? cjroneblLivingSubsidyEntity.getName ().toString():null),"name", cjroneblLivingSubsidyEntity.getName ())
            .eq(StringUtils.isNotBlank(cjroneblLivingSubsidyEntity.getSex ()!=null && !"".equals(cjroneblLivingSubsidyEntity.getSex ().toString())? cjroneblLivingSubsidyEntity.getSex ().toString():null),"sex", cjroneblLivingSubsidyEntity.getSex ())
            .eq(StringUtils.isNotBlank(cjroneblLivingSubsidyEntity.getBirthday ()!=null && !"".equals(cjroneblLivingSubsidyEntity.getBirthday ().toString())? cjroneblLivingSubsidyEntity.getBirthday ().toString():null),"birthday", cjroneblLivingSubsidyEntity.getBirthday ())
            .eq(StringUtils.isNotBlank(cjroneblLivingSubsidyEntity.getDisabilityType ()!=null && !"".equals(cjroneblLivingSubsidyEntity.getDisabilityType ().toString())? cjroneblLivingSubsidyEntity.getDisabilityType ().toString():null),"disability_type", cjroneblLivingSubsidyEntity.getDisabilityType ())
            .eq(StringUtils.isNotBlank(cjroneblLivingSubsidyEntity.getDisabilityDegree ()!=null && !"".equals(cjroneblLivingSubsidyEntity.getDisabilityDegree ().toString())? cjroneblLivingSubsidyEntity.getDisabilityDegree ().toString():null),"disability_degree", cjroneblLivingSubsidyEntity.getDisabilityDegree ())
            .eq(StringUtils.isNotBlank(cjroneblLivingSubsidyEntity.getDisableId ()!=null && !"".equals(cjroneblLivingSubsidyEntity.getDisableId ().toString())? cjroneblLivingSubsidyEntity.getDisableId ().toString():null),"disable_id", cjroneblLivingSubsidyEntity.getDisableId ())
            .eq(StringUtils.isNotBlank(cjroneblLivingSubsidyEntity.getIdCard ()!=null && !"".equals(cjroneblLivingSubsidyEntity.getIdCard ().toString())? cjroneblLivingSubsidyEntity.getIdCard ().toString():null),"id_card", cjroneblLivingSubsidyEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(cjroneblLivingSubsidyEntity.getLiveAddress ()!=null && !"".equals(cjroneblLivingSubsidyEntity.getLiveAddress ().toString())? cjroneblLivingSubsidyEntity.getLiveAddress ().toString():null),"live_address", cjroneblLivingSubsidyEntity.getLiveAddress ())
            .eq(StringUtils.isNotBlank(cjroneblLivingSubsidyEntity.getTelephone ()!=null && !"".equals(cjroneblLivingSubsidyEntity.getTelephone ().toString())? cjroneblLivingSubsidyEntity.getTelephone ().toString():null),"telephone", cjroneblLivingSubsidyEntity.getTelephone ())
            .eq(StringUtils.isNotBlank(cjroneblLivingSubsidyEntity.getFamilyEconomy ()!=null && !"".equals(cjroneblLivingSubsidyEntity.getFamilyEconomy ().toString())? cjroneblLivingSubsidyEntity.getFamilyEconomy ().toString():null),"family_economy", cjroneblLivingSubsidyEntity.getFamilyEconomy ())
            .eq(StringUtils.isNotBlank(cjroneblLivingSubsidyEntity.getSubsidyMoney ()!=null && !"".equals(cjroneblLivingSubsidyEntity.getSubsidyMoney ().toString())? cjroneblLivingSubsidyEntity.getSubsidyMoney ().toString():null),"subsidy_money", cjroneblLivingSubsidyEntity.getSubsidyMoney ())
            .eq(StringUtils.isNotBlank(cjroneblLivingSubsidyEntity.getApplyType ()!=null && !"".equals(cjroneblLivingSubsidyEntity.getApplyType ().toString())? cjroneblLivingSubsidyEntity.getApplyType ().toString():null),"apply_type", cjroneblLivingSubsidyEntity.getApplyType ())
            .eq(StringUtils.isNotBlank(cjroneblLivingSubsidyEntity.getCreateId ()!=null && !"".equals(cjroneblLivingSubsidyEntity.getCreateId ().toString())? cjroneblLivingSubsidyEntity.getCreateId ().toString():null),"create_id", cjroneblLivingSubsidyEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(cjroneblLivingSubsidyEntity.getCreateTime ()!=null && !"".equals(cjroneblLivingSubsidyEntity.getCreateTime ().toString())? cjroneblLivingSubsidyEntity.getCreateTime ().toString():null),"create_time", cjroneblLivingSubsidyEntity.getCreateTime ())
            .eq(StringUtils.isNotBlank(cjroneblLivingSubsidyEntity.getCreateName ()!=null && !"".equals(cjroneblLivingSubsidyEntity.getCreateName ().toString())? cjroneblLivingSubsidyEntity.getCreateName ().toString():null),"create_name", cjroneblLivingSubsidyEntity.getCreateName ())
            .eq(StringUtils.isNotBlank(cjroneblLivingSubsidyEntity.getStatus ()!=null && !"".equals(cjroneblLivingSubsidyEntity.getStatus ().toString())? cjroneblLivingSubsidyEntity.getStatus ().toString():null),"status", cjroneblLivingSubsidyEntity.getStatus ())
            .eq(StringUtils.isNotBlank(cjroneblLivingSubsidyEntity.getStatusOptions ()!=null && !"".equals(cjroneblLivingSubsidyEntity.getStatusOptions ().toString())? cjroneblLivingSubsidyEntity.getStatusOptions ().toString():null),"status_options", cjroneblLivingSubsidyEntity.getStatusOptions ())
            .eq(StringUtils.isNotBlank(cjroneblLivingSubsidyEntity.getSignStatus ()!=null && !"".equals(cjroneblLivingSubsidyEntity.getSignStatus ().toString())? cjroneblLivingSubsidyEntity.getSignStatus ().toString():null),"sign_status", cjroneblLivingSubsidyEntity.getSignStatus ())
            .eq(StringUtils.isNotBlank(cjroneblLivingSubsidyEntity.getSignatureStatus ()!=null && !"".equals(cjroneblLivingSubsidyEntity.getSignatureStatus ().toString())? cjroneblLivingSubsidyEntity.getSignatureStatus ().toString():null),"signature_status", cjroneblLivingSubsidyEntity.getSignatureStatus ())
            .eq(StringUtils.isNotBlank(cjroneblLivingSubsidyEntity.getReturnStatus ()!=null && !"".equals(cjroneblLivingSubsidyEntity.getReturnStatus ().toString())? cjroneblLivingSubsidyEntity.getReturnStatus ().toString():null),"return_status", cjroneblLivingSubsidyEntity.getReturnStatus ())
                        .orderByDesc("create_time")
        );

        page.getRecords().forEach( item -> {
          //处理状态数据
            if ("4".equals(item.getStatus())){
                item.setStatus("民政经办人待审核");
            }

            if("4".equals(item.getSignStatus())){
                item.setSignStatus("民政经办人待手签");
            }

            if("4".equals(item.getSignatureStatus())){
                item.setSignatureStatus("民政负责人待电子盖章");
            }

            if("5".equals(usertype)){
                // 民政负责人

                if ("5".equals(item.getStatus())){
                    item.setStatus("民政负责人待审核");
                }

                if("5".equals(item.getSignStatus())){
                    item.setSignStatus("民政负责人待手签");
                }
                if("6".equals(item.getSignStatus())){
                    item.setSignStatus("民政负责人已手签");
                }

                if("5".equals(item.getSignatureStatus())){
                    item.setSignatureStatus("民政负责人已电子签章");
                }

            }

            if("6".equals(usertype)){
                //区残联经办人
                if ("6".equals(item.getStatus())){
                    item.setStatus("区残联经办人待审核");
                }

                if("6".equals(item.getSignStatus())){
                    item.setSignStatus("区残联经办人待手签");
                }

                if("5".equals(item.getSignatureStatus())){
                    item.setSignatureStatus("无");
                }
            }

            if("7".equals(usertype)){
                //区残联负责人
                if ("7".equals(item.getStatus())){
                    item.setStatus("区残联负责人待审核");
                }

                if("7".equals(item.getSignStatus())){
                    item.setSignStatus("区残联负责人待手签");
                }

                if("8".equals(item.getSignStatus())){
                    item.setSignStatus("区残联负责人已手签");
                }

                if("5".equals(item.getSignatureStatus())){
                    item.setSignatureStatus("区残联负责人待电子签章");
                }

                if("6".equals(item.getSignatureStatus())){
                    item.setSignatureStatus("区残联负责人已电子签章");
                }
            }

        });

        return new PageUtils(page);
    }



    @Override
    public List<CjroneblLivingSubsidyEntity> queryExportData(Map<String, Object> params) {
            return cjroneblLivingSubsidyDao.queryExportData(params);
    }

    @Override
    public boolean updateById(CjroneblLivingSubsidyEntity entity) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",entity.getId());
        map.put("matter_name","生活补贴");
        map.put("status",entity.getStatus());
        map.put("signatureStatus",entity.getSignatureStatus());  // 只需要更改电子印章的状态
        map.put("statusOptions",entity.getStatusOptions());

        cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        return super.updateById(entity);
    }

    @Override
    public boolean updateAudio(CjroneblLivingSubsidyEntity entity) {
        return super.updateById(entity);
    }

    @Override
    public void updateByIdSign(CjroneblLivingSubsidyEntity cjroneblLivingSubsidyEntity) {
        super.updateById(cjroneblLivingSubsidyEntity);
    }

    @Override
    public void updateAudioById(CjroneblLivingSubsidyEntity cjroneblLivingSubsidy) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",cjroneblLivingSubsidy.getId());
        map.put("matter_name","生活补贴");
        map.put("verify_time",new Date());
        map.put("status",cjroneblLivingSubsidy.getStatus());
        map.put("statusOptions",cjroneblLivingSubsidy.getStatusOptions());
        if (cjroneblLivingSubsidy.getStatus() != null){
            cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        }
        super.updateById(cjroneblLivingSubsidy);
    }

}