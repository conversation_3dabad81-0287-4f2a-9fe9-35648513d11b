package com.hmit.kernespring.modules.cjrone_bl.dao;

import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblChildeduEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-24 14:31:01
 */
@Mapper
public interface CjroneblChildeduDao extends BaseMapper<CjroneblChildeduEntity> {
    List<CjroneblChildeduEntity> queryExportData(Map<String, Object> params);
	
}
