package com.hmit.kernespring.modules.cjrone_bl.dao;

import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblLivingSubsidyEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-11-29 09:37:43
 */
@Mapper
public interface CjroneblLivingSubsidyDao extends BaseMapper<CjroneblLivingSubsidyEntity> {
    List<CjroneblLivingSubsidyEntity> queryExportData(Map<String, Object> params);
	
}
