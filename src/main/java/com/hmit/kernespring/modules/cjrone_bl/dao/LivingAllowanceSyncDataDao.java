package com.hmit.kernespring.modules.cjrone_bl.dao;

import com.hmit.kernespring.modules.cjrone_bl.entity.LivingAllowanceSyncDataEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 生活补助金比对数据
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-12-21 16:12:52
 */
@Mapper
public interface LivingAllowanceSyncDataDao extends BaseMapper<LivingAllowanceSyncDataEntity> {
    List<LivingAllowanceSyncDataEntity> queryExportData(Map<String, Object> params);
	
}
