package com.hmit.kernespring.modules.cjrone_bl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblLivingSubsidyEntity;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblNursingSubsidyEntity;

import java.util.Map;

import java.util.List;

/**
 * 护理补贴申请
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-11 09:17:47
 */
public interface CjroneblNursingSubsidyService extends IService<CjroneblNursingSubsidyEntity> {

    PageUtils queryPage(Map<String, Object> params);
    // 电子盖章专用
    boolean updateById(CjroneblNursingSubsidyEntity cjroneblNursingSubsidyEntity);
    List<CjroneblNursingSubsidyEntity> queryExportData(Map<String, Object> params);

    void updateAudioById(CjroneblNursingSubsidyEntity cjroneblNursingSubsidy);

    void updateByIdSign(CjroneblNursingSubsidyEntity cjroneblNursingSubsidyEntity);
}

