package com.hmit.kernespring.modules.cjrone_bl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblLivingAllowanceEntity;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblTemporaryAssistanceEntity;

import java.util.Map;

import java.util.List;

/**
 * 残疾人临时救助
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-13 13:51:37
 */
public interface CjroneblTemporaryAssistanceService extends IService<CjroneblTemporaryAssistanceEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneblTemporaryAssistanceEntity> queryExportData(Map<String, Object> params);

    // 电子盖章专用
    boolean updateById(CjroneblTemporaryAssistanceEntity cjroneblTemporaryAssistanceEntity);

    void updateByIdSign(CjroneblTemporaryAssistanceEntity cjroneblTemporaryAssistanceEntity);

    void updateAudioById(CjroneblTemporaryAssistanceEntity cjroneblTemporaryAssistanceEntity);
}

