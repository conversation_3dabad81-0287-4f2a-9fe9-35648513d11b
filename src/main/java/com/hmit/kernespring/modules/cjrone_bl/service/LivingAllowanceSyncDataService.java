package com.hmit.kernespring.modules.cjrone_bl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone_bl.entity.LivingAllowanceSyncDataEntity;

import java.util.Map;

import java.util.List;

/**
 * 生活补助金比对数据
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-12-21 16:12:52
 */
public interface LivingAllowanceSyncDataService extends IService<LivingAllowanceSyncDataEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<LivingAllowanceSyncDataEntity> queryExportData(Map<String, Object> params);
}

