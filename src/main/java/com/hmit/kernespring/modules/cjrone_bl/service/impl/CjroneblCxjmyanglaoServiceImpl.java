package com.hmit.kernespring.modules.cjrone_bl.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblZgjbyanglaoEntity;
import com.hmit.kernespring.modules.matter_application.dao.CjroneWelfareMatterApplicationDao;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.cjrone_bl.dao.CjroneblCxjmyanglaoDao;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblCxjmyanglaoEntity;
import com.hmit.kernespring.modules.cjrone_bl.service.CjroneblCxjmyanglaoService;


@Service("cjroneblCxjmyanglaoService")
public class CjroneblCxjmyanglaoServiceImpl extends ServiceImpl<CjroneblCxjmyanglaoDao, CjroneblCxjmyanglaoEntity> implements CjroneblCxjmyanglaoService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneblCxjmyanglaoDao cjroneblCxjmyanglaoDao;
    @Autowired
    private CjroneWelfareMatterApplicationDao cjroneWelfareMatterApplicationDao;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneblCxjmyanglaoEntity cjroneblCxjmyanglaoEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneblCxjmyanglaoEntity.class);
        IPage<CjroneblCxjmyanglaoEntity> page = this.page(
                new Query<CjroneblCxjmyanglaoEntity>().getPage(params),
                new QueryWrapper<CjroneblCxjmyanglaoEntity>()
            .eq(StringUtils.isNotBlank(cjroneblCxjmyanglaoEntity.getId ()!=null && !"".equals(cjroneblCxjmyanglaoEntity.getId ().toString())? cjroneblCxjmyanglaoEntity.getId ().toString():null),"id", cjroneblCxjmyanglaoEntity.getId ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyanglaoEntity.getName ()!=null && !"".equals(cjroneblCxjmyanglaoEntity.getName ().toString())? cjroneblCxjmyanglaoEntity.getName ().toString():null),"name", cjroneblCxjmyanglaoEntity.getName ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyanglaoEntity.getSex ()!=null && !"".equals(cjroneblCxjmyanglaoEntity.getSex ().toString())? cjroneblCxjmyanglaoEntity.getSex ().toString():null),"sex", cjroneblCxjmyanglaoEntity.getSex ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyanglaoEntity.getAge ()!=null && !"".equals(cjroneblCxjmyanglaoEntity.getAge ().toString())? cjroneblCxjmyanglaoEntity.getAge ().toString():null),"age", cjroneblCxjmyanglaoEntity.getAge ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyanglaoEntity.getDisableId ()!=null && !"".equals(cjroneblCxjmyanglaoEntity.getDisableId ().toString())? cjroneblCxjmyanglaoEntity.getDisableId ().toString():null),"disable_id", cjroneblCxjmyanglaoEntity.getDisableId ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyanglaoEntity.getTelephone ()!=null && !"".equals(cjroneblCxjmyanglaoEntity.getTelephone ().toString())? cjroneblCxjmyanglaoEntity.getTelephone ().toString():null),"telephone", cjroneblCxjmyanglaoEntity.getTelephone ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyanglaoEntity.getLiveAddress ()!=null && !"".equals(cjroneblCxjmyanglaoEntity.getLiveAddress ().toString())? cjroneblCxjmyanglaoEntity.getLiveAddress ().toString():null),"live_address", cjroneblCxjmyanglaoEntity.getLiveAddress ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyanglaoEntity.getInsuredStatus ()!=null && !"".equals(cjroneblCxjmyanglaoEntity.getInsuredStatus ().toString())? cjroneblCxjmyanglaoEntity.getInsuredStatus ().toString():null),"insured_status", cjroneblCxjmyanglaoEntity.getInsuredStatus ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyanglaoEntity.getOtherSubsidy ()!=null && !"".equals(cjroneblCxjmyanglaoEntity.getOtherSubsidy ().toString())? cjroneblCxjmyanglaoEntity.getOtherSubsidy ().toString():null),"other_subsidy", cjroneblCxjmyanglaoEntity.getOtherSubsidy ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyanglaoEntity.getSeTime ()!=null && !"".equals(cjroneblCxjmyanglaoEntity.getSeTime ().toString())? cjroneblCxjmyanglaoEntity.getSeTime ().toString():null),"se_time", cjroneblCxjmyanglaoEntity.getSeTime ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyanglaoEntity.getPayMoney ()!=null && !"".equals(cjroneblCxjmyanglaoEntity.getPayMoney ().toString())? cjroneblCxjmyanglaoEntity.getPayMoney ().toString():null),"pay_money", cjroneblCxjmyanglaoEntity.getPayMoney ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyanglaoEntity.getSubsidyMoney ()!=null && !"".equals(cjroneblCxjmyanglaoEntity.getSubsidyMoney ().toString())? cjroneblCxjmyanglaoEntity.getSubsidyMoney ().toString():null),"subsidy_money", cjroneblCxjmyanglaoEntity.getSubsidyMoney ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyanglaoEntity.getCreateId ()!=null && !"".equals(cjroneblCxjmyanglaoEntity.getCreateId ().toString())? cjroneblCxjmyanglaoEntity.getCreateId ().toString():null),"create_id", cjroneblCxjmyanglaoEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyanglaoEntity.getCreateTime ()!=null && !"".equals(cjroneblCxjmyanglaoEntity.getCreateTime ().toString())? cjroneblCxjmyanglaoEntity.getCreateTime ().toString():null),"create_time", cjroneblCxjmyanglaoEntity.getCreateTime ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyanglaoEntity.getCreateName ()!=null && !"".equals(cjroneblCxjmyanglaoEntity.getCreateName ().toString())? cjroneblCxjmyanglaoEntity.getCreateName ().toString():null),"create_name", cjroneblCxjmyanglaoEntity.getCreateName ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyanglaoEntity.getStatus ()!=null && !"".equals(cjroneblCxjmyanglaoEntity.getStatus ().toString())? cjroneblCxjmyanglaoEntity.getStatus ().toString():null),"status", cjroneblCxjmyanglaoEntity.getStatus ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyanglaoEntity.getStatusOptions ()!=null && !"".equals(cjroneblCxjmyanglaoEntity.getStatusOptions ().toString())? cjroneblCxjmyanglaoEntity.getStatusOptions ().toString():null),"status_options", cjroneblCxjmyanglaoEntity.getStatusOptions ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyanglaoEntity.getSignStatus ()!=null && !"".equals(cjroneblCxjmyanglaoEntity.getSignStatus ().toString())? cjroneblCxjmyanglaoEntity.getSignStatus ().toString():null),"sign_status", cjroneblCxjmyanglaoEntity.getSignStatus ())
            .eq(StringUtils.isNotBlank(cjroneblCxjmyanglaoEntity.getSignatureStatus ()!=null && !"".equals(cjroneblCxjmyanglaoEntity.getSignatureStatus ().toString())? cjroneblCxjmyanglaoEntity.getSignatureStatus ().toString():null),"signature_status", cjroneblCxjmyanglaoEntity.getSignatureStatus ())
        );

        // 开始为状态赋值
        page.getRecords().forEach( item -> {
            if ("1".equals(item.getStatus())){
                item.setStatus("申请人待手签");
            }else if ("2".equals(item.getStatus())){
                item.setStatus("街道待审核");
            }else if ("6".equals(item.getStatus())){
                item.setStatus("区残联经办人待审核");
            }else if ("7".equals(item.getStatus()) ){
                item.setStatus("区残联负责人待审核");
            } else if ("8".equals(item.getStatus())){
                item.setStatus("通过");
            }else if ("0".equals(item.getStatus())){
                item.setStatus("禁用");
            }else if("12".equals(item.getStatus())){
                item.setStatus("退回");  // 区残联退回至街道
            }

            if("1".equals(item.getSignStatus())){
                item.setSignStatus("申请人待手签");
            }else if("2".equals(item.getSignStatus())){
                item.setSignStatus("镇街道待手签");
            }else if("6".equals(item.getSignStatus())){
                item.setSignStatus("区残联经办人待手签");
            }else if("7".equals(item.getSignStatus())){
                item.setSignStatus("区残联负责人待手签");
            }else if("8".equals(item.getSignStatus())){
                item.setSignStatus("完成手签");
            }

            if("1".equals(item.getSignatureStatus())){
                item.setSignatureStatus("无");
            }else if("2".equals(item.getSignatureStatus())){
                item.setSignatureStatus("镇街道待电子签章");
            }else if("4".equals(item.getSignatureStatus())){
                item.setSignatureStatus("民政待电子签章");
            }else if("5".equals(item.getSignatureStatus())){
                item.setSignatureStatus("区残联待电子签章");
            }else if("6".equals(item.getSignatureStatus())){
                item.setSignatureStatus("完成电子签章");
            }

            //性别
            if(item.getSex()==0){
                item.setSexName("女");
            }else{
                item.setSexName("男");
            }

        });

        return new PageUtils(page);
    }
    @Override
    public List<CjroneblCxjmyanglaoEntity> queryExportData(Map<String, Object> params) {
            return cjroneblCxjmyanglaoDao.queryExportData(params);
    }

    @Override
    public void updateByIdSign(CjroneblCxjmyanglaoEntity cjroneblCxjmyanglaoEntity) {
        super.updateById(cjroneblCxjmyanglaoEntity);
    }

    @Override
    public void updateAudioById(CjroneblCxjmyanglaoEntity cjroneblCxjmyanglaoEntity) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",cjroneblCxjmyanglaoEntity.getId());
        map.put("matter_name","城乡居民养老保险补助");
        map.put("verify_time",new Date());
        map.put("status",cjroneblCxjmyanglaoEntity.getStatus());
        map.put("statusOptions",cjroneblCxjmyanglaoEntity.getStatusOptions());
        if (cjroneblCxjmyanglaoEntity.getStatus() != null){
            cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        }
        super.updateById(cjroneblCxjmyanglaoEntity);
    }

    @Override
    public boolean updateById(CjroneblCxjmyanglaoEntity entity) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",entity.getId());
        map.put("matter_name","城乡居民养老保险补助");
        map.put("status",entity.getStatus());
        map.put("signatureStatus",entity.getSignatureStatus());  // 只需要更改电子印章的状态
        map.put("statusOptions",entity.getStatusOptions());

        cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        return super.updateById(entity);
    }

}