package com.hmit.kernespring.modules.cjrone_bl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblBusinessGrantEntity;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblCollegeeduEntity;

import java.util.Map;

import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-22 21:13:55
 */
public interface CjroneblCollegeeduService extends IService<CjroneblCollegeeduEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneblCollegeeduEntity> queryExportData(Map<String, Object> params);

    // 电子盖章专用
    boolean updateById(CjroneblCollegeeduEntity cjroneblCollegeeduEntity);

    void updateByIdSign(CjroneblCollegeeduEntity cjroneblCollegeeduEntity);

    void updateAudioById(CjroneblCollegeeduEntity cjroneblCollegeeduEntity);
}

