package com.hmit.kernespring.modules.cjrone_bl.dao;

import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblNursingSubsidyEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 护理补贴申请
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-11 09:17:47
 */
@Mapper
public interface CjroneblNursingSubsidyDao extends BaseMapper<CjroneblNursingSubsidyEntity> {
    List<CjroneblNursingSubsidyEntity> queryExportData(Map<String, Object> params);
	
}
