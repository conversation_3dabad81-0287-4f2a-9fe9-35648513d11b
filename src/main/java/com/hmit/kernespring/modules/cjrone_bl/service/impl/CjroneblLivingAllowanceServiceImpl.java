package com.hmit.kernespring.modules.cjrone_bl.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblNursingSubsidyEntity;
import com.hmit.kernespring.modules.matter_application.dao.CjroneWelfareMatterApplicationDao;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.cjrone_bl.dao.CjroneblLivingAllowanceDao;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblLivingAllowanceEntity;
import com.hmit.kernespring.modules.cjrone_bl.service.CjroneblLivingAllowanceService;


@Service("cjroneblLivingAllowanceService")
public class CjroneblLivingAllowanceServiceImpl extends ServiceImpl<CjroneblLivingAllowanceDao, CjroneblLivingAllowanceEntity> implements CjroneblLivingAllowanceService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneblLivingAllowanceDao cjroneblLivingAllowanceDao;
    @Autowired
    private CjroneWelfareMatterApplicationDao cjroneWelfareMatterApplicationDao;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneblLivingAllowanceEntity cjroneblLivingAllowanceEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneblLivingAllowanceEntity.class);
        IPage<CjroneblLivingAllowanceEntity> page = this.page(
                new Query<CjroneblLivingAllowanceEntity>().getPage(params),
                new QueryWrapper<CjroneblLivingAllowanceEntity>()
            .eq(StringUtils.isNotBlank(cjroneblLivingAllowanceEntity.getId ()!=null && !"".equals(cjroneblLivingAllowanceEntity.getId ().toString())? cjroneblLivingAllowanceEntity.getId ().toString():null),"id", cjroneblLivingAllowanceEntity.getId ())
            .eq(StringUtils.isNotBlank(cjroneblLivingAllowanceEntity.getName ()!=null && !"".equals(cjroneblLivingAllowanceEntity.getName ().toString())? cjroneblLivingAllowanceEntity.getName ().toString():null),"name", cjroneblLivingAllowanceEntity.getName ())
            .eq(StringUtils.isNotBlank(cjroneblLivingAllowanceEntity.getSex ()!=null && !"".equals(cjroneblLivingAllowanceEntity.getSex ().toString())? cjroneblLivingAllowanceEntity.getSex ().toString():null),"sex", cjroneblLivingAllowanceEntity.getSex ())
            .eq(StringUtils.isNotBlank(cjroneblLivingAllowanceEntity.getBirthday ()!=null && !"".equals(cjroneblLivingAllowanceEntity.getBirthday ().toString())? cjroneblLivingAllowanceEntity.getBirthday ().toString():null),"birthday", cjroneblLivingAllowanceEntity.getBirthday ())
            .eq(StringUtils.isNotBlank(cjroneblLivingAllowanceEntity.getAge ()!=null && !"".equals(cjroneblLivingAllowanceEntity.getAge ().toString())? cjroneblLivingAllowanceEntity.getAge ().toString():null),"age", cjroneblLivingAllowanceEntity.getAge ())
            .eq(StringUtils.isNotBlank(cjroneblLivingAllowanceEntity.getDisabilityType ()!=null && !"".equals(cjroneblLivingAllowanceEntity.getDisabilityType ().toString())? cjroneblLivingAllowanceEntity.getDisabilityType ().toString():null),"disability_type", cjroneblLivingAllowanceEntity.getDisabilityType ())
            .eq(StringUtils.isNotBlank(cjroneblLivingAllowanceEntity.getDisabilityDegree ()!=null && !"".equals(cjroneblLivingAllowanceEntity.getDisabilityDegree ().toString())? cjroneblLivingAllowanceEntity.getDisabilityDegree ().toString():null),"disability_degree", cjroneblLivingAllowanceEntity.getDisabilityDegree ())
            .eq(StringUtils.isNotBlank(cjroneblLivingAllowanceEntity.getDisableId ()!=null && !"".equals(cjroneblLivingAllowanceEntity.getDisableId ().toString())? cjroneblLivingAllowanceEntity.getDisableId ().toString():null),"disable_id", cjroneblLivingAllowanceEntity.getDisableId ())
            .eq(StringUtils.isNotBlank(cjroneblLivingAllowanceEntity.getIdCard ()!=null && !"".equals(cjroneblLivingAllowanceEntity.getIdCard ().toString())? cjroneblLivingAllowanceEntity.getIdCard ().toString():null),"id_card", cjroneblLivingAllowanceEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(cjroneblLivingAllowanceEntity.getLiveAddress ()!=null && !"".equals(cjroneblLivingAllowanceEntity.getLiveAddress ().toString())? cjroneblLivingAllowanceEntity.getLiveAddress ().toString():null),"live_address", cjroneblLivingAllowanceEntity.getLiveAddress ())
            .eq(StringUtils.isNotBlank(cjroneblLivingAllowanceEntity.getTelephone ()!=null && !"".equals(cjroneblLivingAllowanceEntity.getTelephone ().toString())? cjroneblLivingAllowanceEntity.getTelephone ().toString():null),"telephone", cjroneblLivingAllowanceEntity.getTelephone ())
            .eq(StringUtils.isNotBlank(cjroneblLivingAllowanceEntity.getFamilyEconomy ()!=null && !"".equals(cjroneblLivingAllowanceEntity.getFamilyEconomy ().toString())? cjroneblLivingAllowanceEntity.getFamilyEconomy ().toString():null),"family_economy", cjroneblLivingAllowanceEntity.getFamilyEconomy ())
            .eq(StringUtils.isNotBlank(cjroneblLivingAllowanceEntity.getPension ()!=null && !"".equals(cjroneblLivingAllowanceEntity.getPension ().toString())? cjroneblLivingAllowanceEntity.getPension ().toString():null),"pension", cjroneblLivingAllowanceEntity.getPension ())
            .eq(StringUtils.isNotBlank(cjroneblLivingAllowanceEntity.getSubsidyMoney ()!=null && !"".equals(cjroneblLivingAllowanceEntity.getSubsidyMoney ().toString())? cjroneblLivingAllowanceEntity.getSubsidyMoney ().toString():null),"subsidy_money", cjroneblLivingAllowanceEntity.getSubsidyMoney ())
            .eq(StringUtils.isNotBlank(cjroneblLivingAllowanceEntity.getApplyType ()!=null && !"".equals(cjroneblLivingAllowanceEntity.getApplyType ().toString())? cjroneblLivingAllowanceEntity.getApplyType ().toString():null),"apply_type", cjroneblLivingAllowanceEntity.getApplyType ())
            .eq(StringUtils.isNotBlank(cjroneblLivingAllowanceEntity.getCreateId ()!=null && !"".equals(cjroneblLivingAllowanceEntity.getCreateId ().toString())? cjroneblLivingAllowanceEntity.getCreateId ().toString():null),"create_id", cjroneblLivingAllowanceEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(cjroneblLivingAllowanceEntity.getCreateTime ()!=null && !"".equals(cjroneblLivingAllowanceEntity.getCreateTime ().toString())? cjroneblLivingAllowanceEntity.getCreateTime ().toString():null),"create_time", cjroneblLivingAllowanceEntity.getCreateTime ())
            .eq(StringUtils.isNotBlank(cjroneblLivingAllowanceEntity.getCreateName ()!=null && !"".equals(cjroneblLivingAllowanceEntity.getCreateName ().toString())? cjroneblLivingAllowanceEntity.getCreateName ().toString():null),"create_name", cjroneblLivingAllowanceEntity.getCreateName ())
            .eq(StringUtils.isNotBlank(cjroneblLivingAllowanceEntity.getStatus ()!=null && !"".equals(cjroneblLivingAllowanceEntity.getStatus ().toString())? cjroneblLivingAllowanceEntity.getStatus ().toString():null),"status", cjroneblLivingAllowanceEntity.getStatus ())
            .eq(StringUtils.isNotBlank(cjroneblLivingAllowanceEntity.getStatusOptions ()!=null && !"".equals(cjroneblLivingAllowanceEntity.getStatusOptions ().toString())? cjroneblLivingAllowanceEntity.getStatusOptions ().toString():null),"status_options", cjroneblLivingAllowanceEntity.getStatusOptions ())
            .eq(StringUtils.isNotBlank(cjroneblLivingAllowanceEntity.getSignStatus ()!=null && !"".equals(cjroneblLivingAllowanceEntity.getSignStatus ().toString())? cjroneblLivingAllowanceEntity.getSignStatus ().toString():null),"sign_status", cjroneblLivingAllowanceEntity.getSignStatus ())
            .eq(StringUtils.isNotBlank(cjroneblLivingAllowanceEntity.getSignatureStatus ()!=null && !"".equals(cjroneblLivingAllowanceEntity.getSignatureStatus ().toString())? cjroneblLivingAllowanceEntity.getSignatureStatus ().toString():null),"signature_status", cjroneblLivingAllowanceEntity.getSignatureStatus ())
            .eq(StringUtils.isNotBlank(cjroneblLivingAllowanceEntity.getReturnStatus ()!=null && !"".equals(cjroneblLivingAllowanceEntity.getReturnStatus ().toString())? cjroneblLivingAllowanceEntity.getReturnStatus ().toString():null),"return_status", cjroneblLivingAllowanceEntity.getReturnStatus ())
        );

        // 开始为状态赋值
        page.getRecords().forEach( item -> {
            if ("1".equals(item.getStatus())){
                item.setStatus("申请人待手签");
            }else if ("2".equals(item.getStatus())){
                item.setStatus("街道待审核");
            }else if ("6".equals(item.getStatus())){
                item.setStatus("区残联经办人待审核");
            }else if ("7".equals(item.getStatus()) ){
                item.setStatus("区残联负责人待审核");
            } else if ("8".equals(item.getStatus())){
                item.setStatus("通过");
            }else if ("0".equals(item.getStatus())){
                item.setStatus("禁用");
            }else if("12".equals(item.getStatus())){
                item.setStatus("退回");  // 区残联退回至街道
            }

            if("1".equals(item.getSignStatus())){
                item.setSignStatus("申请人待手签");
            }else if("2".equals(item.getSignStatus())){
                item.setSignStatus("镇街道待手签");
            }else if("6".equals(item.getSignStatus())){
                item.setSignStatus("区残联经办人待手签");
            }else if("7".equals(item.getSignStatus())){
                item.setSignStatus("区残联负责人待手签");
            }else if("8".equals(item.getSignStatus())){
                item.setSignStatus("完成手签");
            }

            if("1".equals(item.getSignatureStatus())){
                item.setSignatureStatus("无");
            }else if("2".equals(item.getSignatureStatus())){
                item.setSignatureStatus("镇街道待电子签章");
            }else if("4".equals(item.getSignatureStatus())){
                item.setSignatureStatus("民政待电子签章");
            }else if("5".equals(item.getSignatureStatus())){
                item.setSignatureStatus("区残联待电子签章");
            }else if("6".equals(item.getSignatureStatus())){
                item.setSignatureStatus("完成电子签章");
            }

        });

        return new PageUtils(page);
    }
    @Override
    public List<CjroneblLivingAllowanceEntity> queryExportData(Map<String, Object> params) {
            return cjroneblLivingAllowanceDao.queryExportData(params);
    }

    @Override
    public void updateByIdSign(CjroneblLivingAllowanceEntity cjroneblLivingAllowanceEntity) {
        super.updateById(cjroneblLivingAllowanceEntity);
    }

    @Override
    public void updateAudioById(CjroneblLivingAllowanceEntity cjroneblLivingAllowance) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",cjroneblLivingAllowance.getId());
        map.put("matter_name","生活补助金");
        map.put("verify_time",new Date());
        map.put("status",cjroneblLivingAllowance.getStatus());
        map.put("statusOptions",cjroneblLivingAllowance.getStatusOptions());
        if (cjroneblLivingAllowance.getStatus() != null){
            cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        }
        super.updateById(cjroneblLivingAllowance);
    }

    @Override
    public boolean updateById(CjroneblLivingAllowanceEntity entity) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",entity.getId());
        map.put("matter_name","生活补助金");
        map.put("status",entity.getStatus());
        map.put("signatureStatus",entity.getSignatureStatus());  // 只需要更改电子印章的状态
        map.put("statusOptions",entity.getStatusOptions());

        cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        return super.updateById(entity);
    }

}