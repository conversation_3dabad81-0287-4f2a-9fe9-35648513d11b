package com.hmit.kernespring.modules.cjrone_bl.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 护理补贴申请
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-11 09:17:47
 */
@Data
@TableName("cjronebl_nursing_subsidy")
public class CjroneblNursingSubsidyEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
@TableId
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 残疾人姓名
	 */
@Excel(name = "残疾人姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 性别  1 男  2女
	 */
@Excel(name = "性别  1 男  2女", height = 20, width = 30, isImportField = "true_st")
private Integer sex;
	/**
	 * 出生日期
	 */
@Excel(name = "出生日期", height = 20, width = 30, isImportField = "true_st")
private String birthday;
	/**
	 * 民族
	 */
@Excel(name = "民族", height = 20, width = 30, isImportField = "true_st")
private String nationality;
	/**
	 * 身份证号码
	 */
@Excel(name = "身份证号码", height = 20, width = 30, isImportField = "true_st")
private String idCard;
	/**
	 * 手机号码
	 */
@Excel(name = "手机号码", height = 20, width = 30, isImportField = "true_st")
private String mobilePhone;
	/**
	 * 监护人姓名
	 */
@Excel(name = "监护人姓名", height = 20, width = 30, isImportField = "true_st")
private String guardianName;
	/**
	 * 监护人手机
	 */
@Excel(name = "监护人手机", height = 20, width = 30, isImportField = "true_st")
private String guardianPhone;
	/**
	 * 残疾类别
	 */
@Excel(name = "残疾类别", height = 20, width = 30, isImportField = "true_st")
private String disabilityCategory;
	/**
	 * 残疾等级
	 */
@Excel(name = "残疾等级", height = 20, width = 30, isImportField = "true_st")
private String disabilityDegree;
	/**
	 * 残疾证号
	 */
@Excel(name = "残疾证号", height = 20, width = 30, isImportField = "true_st")
private String disableId;
	/**
	 * 银行账户
	 */
@Excel(name = "银行账户", height = 20, width = 30, isImportField = "true_st")
private String bankAccount;
	/**
	 * 开户银行
	 */
@Excel(name = "开户银行", height = 20, width = 30, isImportField = "true_st")
private String bankName;
	/**
	 * 照料方式
	 */
@Excel(name = "照料方式", height = 20, width = 30, isImportField = "true_st")
private String careType;
	/**
	 * 申请日期
	 */
@Excel(name = "申请日期", height = 20, width = 30, isImportField = "true_st")
private String applicationDate;
	/**
	 * 审核人
	 */
@Excel(name = "审核人", height = 20, width = 30, isImportField = "true_st")
private String auditPerson;
	/**
	 * 审核时间
	 */
@Excel(name = "审核时间", height = 20, width = 30, isImportField = "true_st")
private Date auditDate;
	/**
	 * 状态
	 */
@Excel(name = "状态", height = 20, width = 30, isImportField = "true_st")
private String status;
	/**
	 * 创建人编号
	 */
@Excel(name = "创建人编号", height = 20, width = 30, isImportField = "true_st")
private Integer createId;
	/**
	 * 创建时间
	 */
@Excel(name = "创建时间", height = 20, width = 30, isImportField = "true_st")
private String createTime;
	/**
	 * 家庭经济情况
	 */
@Excel(name = "家庭经济情况", height = 20, width = 30, isImportField = "true_st")
private String familyEconoCondition;
	/**
	 * 生活自理状况
	 */
@Excel(name = "生活自理状况", height = 20, width = 30, isImportField = "true_st")
private String lifeStatus;
	/**
	 * 标准补贴
	 */
@Excel(name = "标准补贴", height = 20, width = 30, isImportField = "true_st")
private String standardSubsidy;
	/**
	 * 户籍所在地
	 */
@Excel(name = "户籍所在地", height = 20, width = 30, isImportField = "true_st")
private String nativeAddress;
	/**
	 * 实际补贴
	 */
@Excel(name = "实际补贴", height = 20, width = 30, isImportField = "true_st")
private String actuallySubsidy;
	/**
	 * 居住地
	 */
@Excel(name = "居住地", height = 20, width = 30, isImportField = "true_st")
private String liveAddress;
	/**
	 * 备注
	 */
@Excel(name = "备注", height = 20, width = 30, isImportField = "true_st")
private String remark;
	/**
	 * 
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String signStatus;
	/**
	 * 
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String signatureStatus;
	/**
	 * 被照护6个月以上
	 */
@Excel(name = "被照护6个月以上", height = 20, width = 30, isImportField = "true_st")
private String sixMonth;
	/**
	 * 照护月数
	 */
@Excel(name = "照护月数", height = 20, width = 30, isImportField = "true_st")
private Integer careMonths;

private String statusOptions;

}
