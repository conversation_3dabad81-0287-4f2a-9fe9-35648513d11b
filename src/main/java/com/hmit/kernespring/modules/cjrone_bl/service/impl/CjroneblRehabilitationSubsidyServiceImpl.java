package com.hmit.kernespring.modules.cjrone_bl.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblLivingAllowanceEntity;
import com.hmit.kernespring.modules.matter_application.dao.CjroneWelfareMatterApplicationDao;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.cjrone_bl.dao.CjroneblRehabilitationSubsidyDao;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblRehabilitationSubsidyEntity;
import com.hmit.kernespring.modules.cjrone_bl.service.CjroneblRehabilitationSubsidyService;


@Service("cjroneblRehabilitationSubsidyService")
public class CjroneblRehabilitationSubsidyServiceImpl extends ServiceImpl<CjroneblRehabilitationSubsidyDao, CjroneblRehabilitationSubsidyEntity> implements CjroneblRehabilitationSubsidyService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneblRehabilitationSubsidyDao cjroneblRehabilitationSubsidyDao;
    @Autowired
    private CjroneWelfareMatterApplicationDao cjroneWelfareMatterApplicationDao;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneblRehabilitationSubsidyEntity cjroneblRehabilitationSubsidyEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneblRehabilitationSubsidyEntity.class);
        IPage<CjroneblRehabilitationSubsidyEntity> page = this.page(
                new Query<CjroneblRehabilitationSubsidyEntity>().getPage(params),
                new QueryWrapper<CjroneblRehabilitationSubsidyEntity>()
            .eq(StringUtils.isNotBlank(cjroneblRehabilitationSubsidyEntity.getId ()!=null && !"".equals(cjroneblRehabilitationSubsidyEntity.getId ().toString())? cjroneblRehabilitationSubsidyEntity.getId ().toString():null),"id", cjroneblRehabilitationSubsidyEntity.getId ())
            .eq(StringUtils.isNotBlank(cjroneblRehabilitationSubsidyEntity.getName ()!=null && !"".equals(cjroneblRehabilitationSubsidyEntity.getName ().toString())? cjroneblRehabilitationSubsidyEntity.getName ().toString():null),"name", cjroneblRehabilitationSubsidyEntity.getName ())
            .eq(StringUtils.isNotBlank(cjroneblRehabilitationSubsidyEntity.getSex ()!=null && !"".equals(cjroneblRehabilitationSubsidyEntity.getSex ().toString())? cjroneblRehabilitationSubsidyEntity.getSex ().toString():null),"sex", cjroneblRehabilitationSubsidyEntity.getSex ())
            .eq(StringUtils.isNotBlank(cjroneblRehabilitationSubsidyEntity.getNationailty ()!=null && !"".equals(cjroneblRehabilitationSubsidyEntity.getNationailty ().toString())? cjroneblRehabilitationSubsidyEntity.getNationailty ().toString():null),"nationailty", cjroneblRehabilitationSubsidyEntity.getNationailty ())
            .eq(StringUtils.isNotBlank(cjroneblRehabilitationSubsidyEntity.getBirthday ()!=null && !"".equals(cjroneblRehabilitationSubsidyEntity.getBirthday ().toString())? cjroneblRehabilitationSubsidyEntity.getBirthday ().toString():null),"birthday", cjroneblRehabilitationSubsidyEntity.getBirthday ())
            .eq(StringUtils.isNotBlank(cjroneblRehabilitationSubsidyEntity.getIdCard ()!=null && !"".equals(cjroneblRehabilitationSubsidyEntity.getIdCard ().toString())? cjroneblRehabilitationSubsidyEntity.getIdCard ().toString():null),"id_card", cjroneblRehabilitationSubsidyEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(cjroneblRehabilitationSubsidyEntity.getDisabileId ()!=null && !"".equals(cjroneblRehabilitationSubsidyEntity.getDisabileId ().toString())? cjroneblRehabilitationSubsidyEntity.getDisabileId ().toString():null),"disabile_id", cjroneblRehabilitationSubsidyEntity.getDisabileId ())
            .eq(StringUtils.isNotBlank(cjroneblRehabilitationSubsidyEntity.getDisabilityType ()!=null && !"".equals(cjroneblRehabilitationSubsidyEntity.getDisabilityType ().toString())? cjroneblRehabilitationSubsidyEntity.getDisabilityType ().toString():null),"disability_type", cjroneblRehabilitationSubsidyEntity.getDisabilityType ())
            .eq(StringUtils.isNotBlank(cjroneblRehabilitationSubsidyEntity.getDisabilityDegree ()!=null && !"".equals(cjroneblRehabilitationSubsidyEntity.getDisabilityDegree ().toString())? cjroneblRehabilitationSubsidyEntity.getDisabilityDegree ().toString():null),"disability_degree", cjroneblRehabilitationSubsidyEntity.getDisabilityDegree ())
            .eq(StringUtils.isNotBlank(cjroneblRehabilitationSubsidyEntity.getLiveAddress ()!=null && !"".equals(cjroneblRehabilitationSubsidyEntity.getLiveAddress ().toString())? cjroneblRehabilitationSubsidyEntity.getLiveAddress ().toString():null),"live_address", cjroneblRehabilitationSubsidyEntity.getLiveAddress ())
            .eq(StringUtils.isNotBlank(cjroneblRehabilitationSubsidyEntity.getGuardianName ()!=null && !"".equals(cjroneblRehabilitationSubsidyEntity.getGuardianName ().toString())? cjroneblRehabilitationSubsidyEntity.getGuardianName ().toString():null),"guardian_name", cjroneblRehabilitationSubsidyEntity.getGuardianName ())
            .eq(StringUtils.isNotBlank(cjroneblRehabilitationSubsidyEntity.getGuardianTelephone ()!=null && !"".equals(cjroneblRehabilitationSubsidyEntity.getGuardianTelephone ().toString())? cjroneblRehabilitationSubsidyEntity.getGuardianTelephone ().toString():null),"guardian_telephone", cjroneblRehabilitationSubsidyEntity.getGuardianTelephone ())
            .eq(StringUtils.isNotBlank(cjroneblRehabilitationSubsidyEntity.getFamilyEconomy ()!=null && !"".equals(cjroneblRehabilitationSubsidyEntity.getFamilyEconomy ().toString())? cjroneblRehabilitationSubsidyEntity.getFamilyEconomy ().toString():null),"family_economy", cjroneblRehabilitationSubsidyEntity.getFamilyEconomy ())
            .eq(StringUtils.isNotBlank(cjroneblRehabilitationSubsidyEntity.getTelephone ()!=null && !"".equals(cjroneblRehabilitationSubsidyEntity.getTelephone ().toString())? cjroneblRehabilitationSubsidyEntity.getTelephone ().toString():null),"telephone", cjroneblRehabilitationSubsidyEntity.getTelephone ())
            .eq(StringUtils.isNotBlank(cjroneblRehabilitationSubsidyEntity.getYlbx ()!=null && !"".equals(cjroneblRehabilitationSubsidyEntity.getYlbx ().toString())? cjroneblRehabilitationSubsidyEntity.getYlbx ().toString():null),"ylbx", cjroneblRehabilitationSubsidyEntity.getYlbx ())
            .eq(StringUtils.isNotBlank(cjroneblRehabilitationSubsidyEntity.getCreateId ()!=null && !"".equals(cjroneblRehabilitationSubsidyEntity.getCreateId ().toString())? cjroneblRehabilitationSubsidyEntity.getCreateId ().toString():null),"create_id", cjroneblRehabilitationSubsidyEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(cjroneblRehabilitationSubsidyEntity.getCreateTime ()!=null && !"".equals(cjroneblRehabilitationSubsidyEntity.getCreateTime ().toString())? cjroneblRehabilitationSubsidyEntity.getCreateTime ().toString():null),"create_time", cjroneblRehabilitationSubsidyEntity.getCreateTime ())
            .eq(StringUtils.isNotBlank(cjroneblRehabilitationSubsidyEntity.getCreateName ()!=null && !"".equals(cjroneblRehabilitationSubsidyEntity.getCreateName ().toString())? cjroneblRehabilitationSubsidyEntity.getCreateName ().toString():null),"create_name", cjroneblRehabilitationSubsidyEntity.getCreateName ())
            .eq(StringUtils.isNotBlank(cjroneblRehabilitationSubsidyEntity.getStatus ()!=null && !"".equals(cjroneblRehabilitationSubsidyEntity.getStatus ().toString())? cjroneblRehabilitationSubsidyEntity.getStatus ().toString():null),"status", cjroneblRehabilitationSubsidyEntity.getStatus ())
            .eq(StringUtils.isNotBlank(cjroneblRehabilitationSubsidyEntity.getStatusOptions ()!=null && !"".equals(cjroneblRehabilitationSubsidyEntity.getStatusOptions ().toString())? cjroneblRehabilitationSubsidyEntity.getStatusOptions ().toString():null),"status_options", cjroneblRehabilitationSubsidyEntity.getStatusOptions ())
            .eq(StringUtils.isNotBlank(cjroneblRehabilitationSubsidyEntity.getSignStatus ()!=null && !"".equals(cjroneblRehabilitationSubsidyEntity.getSignStatus ().toString())? cjroneblRehabilitationSubsidyEntity.getSignStatus ().toString():null),"sign_status", cjroneblRehabilitationSubsidyEntity.getSignStatus ())
            .eq(StringUtils.isNotBlank(cjroneblRehabilitationSubsidyEntity.getSignatureStatus ()!=null && !"".equals(cjroneblRehabilitationSubsidyEntity.getSignatureStatus ().toString())? cjroneblRehabilitationSubsidyEntity.getSignatureStatus ().toString():null),"signature_status", cjroneblRehabilitationSubsidyEntity.getSignatureStatus ())
        );

        // 开始为状态赋值
        page.getRecords().forEach( item -> {
            if ("1".equals(item.getStatus())){
                item.setStatus("申请人待手签");
            }else if ("2".equals(item.getStatus())){
                item.setStatus("街道待审核");
            }else if ("6".equals(item.getStatus())){
                item.setStatus("区残联经办人待审核");
            }else if ("7".equals(item.getStatus()) ){
                item.setStatus("区残联负责人待审核");
            } else if ("8".equals(item.getStatus())){
                item.setStatus("通过");
            }else if ("0".equals(item.getStatus())){
                item.setStatus("禁用");
            }else if("12".equals(item.getStatus())){
                item.setStatus("退回");  // 区残联退回至街道
            }

            if("1".equals(item.getSignStatus())){
                item.setSignStatus("申请人待手签");
            }else if("2".equals(item.getSignStatus())){
                item.setSignStatus("镇街道待手签");
            }else if("6".equals(item.getSignStatus())){
                item.setSignStatus("区残联经办人待手签");
            }else if("7".equals(item.getSignStatus())){
                item.setSignStatus("区残联负责人待手签");
            }else if("8".equals(item.getSignStatus())){
                item.setSignStatus("完成手签");
            }

            if("1".equals(item.getSignatureStatus())){
                item.setSignatureStatus("无");
            }else if("2".equals(item.getSignatureStatus())){
                item.setSignatureStatus("镇街道待电子签章");
            }else if("4".equals(item.getSignatureStatus())){
                item.setSignatureStatus("民政待电子签章");
            }else if("5".equals(item.getSignatureStatus())){
                item.setSignatureStatus("区残联待电子签章");
            }else if("6".equals(item.getSignatureStatus())){
                item.setSignatureStatus("完成电子签章");
            }

            //性别
            if("0".equals(item.getSex())){
                item.setSex("女");
            }else{
                item.setSex("男");
            }

        });

        return new PageUtils(page);
    }
    @Override
    public List<CjroneblRehabilitationSubsidyEntity> queryExportData(Map<String, Object> params) {
            return cjroneblRehabilitationSubsidyDao.queryExportData(params);
    }

    @Override
    public void updateByIdSign(CjroneblRehabilitationSubsidyEntity cjroneblRehabilitationSubsidyEntity) {
        super.updateById(cjroneblRehabilitationSubsidyEntity);
    }

    @Override
    public void updateAudioById(CjroneblRehabilitationSubsidyEntity cjroneblRehabilitationSubsidyEntity) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",cjroneblRehabilitationSubsidyEntity.getId());
        map.put("matter_name","康复补助");
        map.put("verify_time",new Date());
        map.put("status",cjroneblRehabilitationSubsidyEntity.getStatus());
        map.put("statusOptions",cjroneblRehabilitationSubsidyEntity.getStatusOptions());
        if (cjroneblRehabilitationSubsidyEntity.getStatus() != null){
            cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        }
        super.updateById(cjroneblRehabilitationSubsidyEntity);
    }


    @Override
    public boolean updateById(CjroneblRehabilitationSubsidyEntity entity) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",entity.getId());
        map.put("matter_name","康复补助");
        map.put("status",entity.getStatus());
        map.put("signatureStatus",entity.getSignatureStatus());  // 只需要更改电子印章的状态
        map.put("statusOptions",entity.getStatusOptions());

        cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        return super.updateById(entity);
    }


}