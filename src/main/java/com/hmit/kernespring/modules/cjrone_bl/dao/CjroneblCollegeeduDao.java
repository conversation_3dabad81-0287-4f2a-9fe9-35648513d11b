package com.hmit.kernespring.modules.cjrone_bl.dao;

import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblCollegeeduEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-22 21:13:55
 */
@Mapper
public interface CjroneblCollegeeduDao extends BaseMapper<CjroneblCollegeeduEntity> {
    List<CjroneblCollegeeduEntity> queryExportData(Map<String, Object> params);
	
}
