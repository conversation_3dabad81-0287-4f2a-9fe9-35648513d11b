package com.hmit.kernespring.modules.cjrone_bl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblZgjbyanglaoEntity;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblZgjbyiliaoEntity;

import java.util.Map;

import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-12 16:24:08
 */
public interface CjroneblZgjbyiliaoService extends IService<CjroneblZgjbyiliaoEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneblZgjbyiliaoEntity> queryExportData(Map<String, Object> params);
    // 电子盖章专用
    boolean updateById(CjroneblZgjbyiliaoEntity cjroneblZgjbyiliaoEntity);
    void updateByIdSign(CjroneblZgjbyiliaoEntity cjroneblZgjbyiliaoEntity);

    void updateAudioById(CjroneblZgjbyiliaoEntity cjroneblZgjbyiliaoEntity);

}

