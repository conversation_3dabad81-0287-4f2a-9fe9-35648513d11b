package com.hmit.kernespring.modules.cjrone_bl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblChildeduEntity;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblHospitalizationAllowanceEntity;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblLivingAllowanceEntity;
import com.hmit.kernespring.modules.cjrone_bl.entity.Love24Entity;

import java.util.Map;

import java.util.List;

/**
 * 智慧爱心24小时
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-11-25 17:44:36
 */
public interface Love24Service extends IService<Love24Entity> {

    PageUtils queryPage(Map<String, Object> params);
    List<Love24Entity> queryExportData(Map<String, Object> params);

    // 电子盖章专用
    boolean updateById(Love24Entity love24Entity);

    void updateByIdSign(Love24Entity love24Entity);

    void updateAudioById(Love24Entity love24Entity);
}

