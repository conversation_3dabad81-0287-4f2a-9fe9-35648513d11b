package com.hmit.kernespring.modules.cjrone_bl.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblLivingAllowanceEntity;
import com.hmit.kernespring.modules.matter_application.dao.CjroneWelfareMatterApplicationDao;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.cjrone_bl.dao.CjroneblZgjbyanglaoDao;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblZgjbyanglaoEntity;
import com.hmit.kernespring.modules.cjrone_bl.service.CjroneblZgjbyanglaoService;


@Service("cjroneblZgjbyanglaoService")
public class CjroneblZgjbyanglaoServiceImpl extends ServiceImpl<CjroneblZgjbyanglaoDao, CjroneblZgjbyanglaoEntity> implements CjroneblZgjbyanglaoService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneblZgjbyanglaoDao cjroneblZgjbyanglaoDao;
    @Autowired
    private CjroneWelfareMatterApplicationDao cjroneWelfareMatterApplicationDao;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneblZgjbyanglaoEntity cjroneblZgjbyanglaoEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneblZgjbyanglaoEntity.class);
        IPage<CjroneblZgjbyanglaoEntity> page = this.page(
                new Query<CjroneblZgjbyanglaoEntity>().getPage(params),
                new QueryWrapper<CjroneblZgjbyanglaoEntity>()
            .eq(StringUtils.isNotBlank(cjroneblZgjbyanglaoEntity.getId ()!=null && !"".equals(cjroneblZgjbyanglaoEntity.getId ().toString())? cjroneblZgjbyanglaoEntity.getId ().toString():null),"id", cjroneblZgjbyanglaoEntity.getId ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyanglaoEntity.getName ()!=null && !"".equals(cjroneblZgjbyanglaoEntity.getName ().toString())? cjroneblZgjbyanglaoEntity.getName ().toString():null),"name", cjroneblZgjbyanglaoEntity.getName ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyanglaoEntity.getSex ()!=null && !"".equals(cjroneblZgjbyanglaoEntity.getSex ().toString())? cjroneblZgjbyanglaoEntity.getSex ().toString():null),"sex", cjroneblZgjbyanglaoEntity.getSex ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyanglaoEntity.getAge ()!=null && !"".equals(cjroneblZgjbyanglaoEntity.getAge ().toString())? cjroneblZgjbyanglaoEntity.getAge ().toString():null),"age", cjroneblZgjbyanglaoEntity.getAge ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyanglaoEntity.getDisableId ()!=null && !"".equals(cjroneblZgjbyanglaoEntity.getDisableId ().toString())? cjroneblZgjbyanglaoEntity.getDisableId ().toString():null),"disable_id", cjroneblZgjbyanglaoEntity.getDisableId ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyanglaoEntity.getTelephone ()!=null && !"".equals(cjroneblZgjbyanglaoEntity.getTelephone ().toString())? cjroneblZgjbyanglaoEntity.getTelephone ().toString():null),"telephone", cjroneblZgjbyanglaoEntity.getTelephone ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyanglaoEntity.getLiveAddress ()!=null && !"".equals(cjroneblZgjbyanglaoEntity.getLiveAddress ().toString())? cjroneblZgjbyanglaoEntity.getLiveAddress ().toString():null),"live_address", cjroneblZgjbyanglaoEntity.getLiveAddress ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyanglaoEntity.getInsuredStatus ()!=null && !"".equals(cjroneblZgjbyanglaoEntity.getInsuredStatus ().toString())? cjroneblZgjbyanglaoEntity.getInsuredStatus ().toString():null),"insured_status", cjroneblZgjbyanglaoEntity.getInsuredStatus ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyanglaoEntity.getOtherSubsidy ()!=null && !"".equals(cjroneblZgjbyanglaoEntity.getOtherSubsidy ().toString())? cjroneblZgjbyanglaoEntity.getOtherSubsidy ().toString():null),"other_subsidy", cjroneblZgjbyanglaoEntity.getOtherSubsidy ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyanglaoEntity.getSeTime ()!=null && !"".equals(cjroneblZgjbyanglaoEntity.getSeTime ().toString())? cjroneblZgjbyanglaoEntity.getSeTime ().toString():null),"se_time", cjroneblZgjbyanglaoEntity.getSeTime ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyanglaoEntity.getPayMoney ()!=null && !"".equals(cjroneblZgjbyanglaoEntity.getPayMoney ().toString())? cjroneblZgjbyanglaoEntity.getPayMoney ().toString():null),"pay_money", cjroneblZgjbyanglaoEntity.getPayMoney ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyanglaoEntity.getSubsidyMoney ()!=null && !"".equals(cjroneblZgjbyanglaoEntity.getSubsidyMoney ().toString())? cjroneblZgjbyanglaoEntity.getSubsidyMoney ().toString():null),"subsidy_money", cjroneblZgjbyanglaoEntity.getSubsidyMoney ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyanglaoEntity.getCreateId ()!=null && !"".equals(cjroneblZgjbyanglaoEntity.getCreateId ().toString())? cjroneblZgjbyanglaoEntity.getCreateId ().toString():null),"create_id", cjroneblZgjbyanglaoEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyanglaoEntity.getCreateTime ()!=null && !"".equals(cjroneblZgjbyanglaoEntity.getCreateTime ().toString())? cjroneblZgjbyanglaoEntity.getCreateTime ().toString():null),"create_time", cjroneblZgjbyanglaoEntity.getCreateTime ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyanglaoEntity.getCreateName ()!=null && !"".equals(cjroneblZgjbyanglaoEntity.getCreateName ().toString())? cjroneblZgjbyanglaoEntity.getCreateName ().toString():null),"create_name", cjroneblZgjbyanglaoEntity.getCreateName ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyanglaoEntity.getStatus ()!=null && !"".equals(cjroneblZgjbyanglaoEntity.getStatus ().toString())? cjroneblZgjbyanglaoEntity.getStatus ().toString():null),"status", cjroneblZgjbyanglaoEntity.getStatus ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyanglaoEntity.getStatusOptions ()!=null && !"".equals(cjroneblZgjbyanglaoEntity.getStatusOptions ().toString())? cjroneblZgjbyanglaoEntity.getStatusOptions ().toString():null),"status_options", cjroneblZgjbyanglaoEntity.getStatusOptions ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyanglaoEntity.getSignStatus ()!=null && !"".equals(cjroneblZgjbyanglaoEntity.getSignStatus ().toString())? cjroneblZgjbyanglaoEntity.getSignStatus ().toString():null),"sign_status", cjroneblZgjbyanglaoEntity.getSignStatus ())
            .eq(StringUtils.isNotBlank(cjroneblZgjbyanglaoEntity.getSignatureStatus ()!=null && !"".equals(cjroneblZgjbyanglaoEntity.getSignatureStatus ().toString())? cjroneblZgjbyanglaoEntity.getSignatureStatus ().toString():null),"signature_status", cjroneblZgjbyanglaoEntity.getSignatureStatus ())
        );

        // 开始为状态赋值
        page.getRecords().forEach( item -> {
            if ("1".equals(item.getStatus())){
                item.setStatus("申请人待手签");
            }else if ("2".equals(item.getStatus())){
                item.setStatus("街道待审核");
            }else if ("6".equals(item.getStatus())){
                item.setStatus("区残联经办人待审核");
            }else if ("7".equals(item.getStatus()) ){
                item.setStatus("区残联负责人待审核");
            } else if ("8".equals(item.getStatus())){
                item.setStatus("通过");
            }else if ("0".equals(item.getStatus())){
                item.setStatus("禁用");
            }else if("12".equals(item.getStatus())){
                item.setStatus("退回");  // 区残联退回至街道
            }

            if("1".equals(item.getSignStatus())){
                item.setSignStatus("申请人待手签");
            }else if("2".equals(item.getSignStatus())){
                item.setSignStatus("镇街道待手签");
            }else if("6".equals(item.getSignStatus())){
                item.setSignStatus("区残联经办人待手签");
            }else if("7".equals(item.getSignStatus())){
                item.setSignStatus("区残联负责人待手签");
            }else if("8".equals(item.getSignStatus())){
                item.setSignStatus("完成手签");
            }

            if("1".equals(item.getSignatureStatus())){
                item.setSignatureStatus("无");
            }else if("2".equals(item.getSignatureStatus())){
                item.setSignatureStatus("镇街道待电子签章");
            }else if("4".equals(item.getSignatureStatus())){
                item.setSignatureStatus("民政待电子签章");
            }else if("5".equals(item.getSignatureStatus())){
                item.setSignatureStatus("区残联待电子签章");
            }else if("6".equals(item.getSignatureStatus())){
                item.setSignatureStatus("完成电子签章");
            }

            //性别
            if(item.getSex()==0){
                item.setSexName("女");
            }else{
                item.setSexName("男");
            }

        });

        return new PageUtils(page);
    }
    @Override
    public List<CjroneblZgjbyanglaoEntity> queryExportData(Map<String, Object> params) {
            return cjroneblZgjbyanglaoDao.queryExportData(params);
    }

    @Override
    public void updateByIdSign(CjroneblZgjbyanglaoEntity cjroneblZgjbyanglaoEntity) {
        super.updateById(cjroneblZgjbyanglaoEntity);
    }

    @Override
    public void updateAudioById(CjroneblZgjbyanglaoEntity cjroneblZgjbyanglao) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",cjroneblZgjbyanglao.getId());
        map.put("matter_name","职工基本养老保险补助");
        map.put("verify_time",new Date());
        map.put("status",cjroneblZgjbyanglao.getStatus());
        map.put("statusOptions",cjroneblZgjbyanglao.getStatusOptions());
        if (cjroneblZgjbyanglao.getStatus() != null){
            cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        }
        super.updateById(cjroneblZgjbyanglao);
    }

    @Override
    public boolean updateById(CjroneblZgjbyanglaoEntity entity) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",entity.getId());
        map.put("matter_name","职工基本养老保险补助");
        map.put("status",entity.getStatus());
        map.put("signatureStatus",entity.getSignatureStatus());  // 只需要更改电子印章的状态
        map.put("statusOptions",entity.getStatusOptions());

        cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        return super.updateById(entity);
    }

}