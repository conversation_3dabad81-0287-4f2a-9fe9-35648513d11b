package com.hmit.kernespring.modules.cjrone_bl.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblTemporaryAssistanceEntity;
import com.hmit.kernespring.modules.matter_application.dao.CjroneWelfareMatterApplicationDao;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.cjrone_bl.dao.CjroneblMedicalSupportDao;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblMedicalSupportEntity;
import com.hmit.kernespring.modules.cjrone_bl.service.CjroneblMedicalSupportService;


@Service("cjroneblMedicalSupportService")
public class CjroneblMedicalSupportServiceImpl extends ServiceImpl<CjroneblMedicalSupportDao, CjroneblMedicalSupportEntity> implements CjroneblMedicalSupportService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneblMedicalSupportDao cjroneblMedicalSupportDao;
    @Autowired
    private CjroneWelfareMatterApplicationDao cjroneWelfareMatterApplicationDao;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneblMedicalSupportEntity cjroneblMedicalSupportEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneblMedicalSupportEntity.class);
        IPage<CjroneblMedicalSupportEntity> page = this.page(
                new Query<CjroneblMedicalSupportEntity>().getPage(params),
                new QueryWrapper<CjroneblMedicalSupportEntity>()
            .eq(StringUtils.isNotBlank(cjroneblMedicalSupportEntity.getId ()!=null && !"".equals(cjroneblMedicalSupportEntity.getId ().toString())? cjroneblMedicalSupportEntity.getId ().toString():null),"id", cjroneblMedicalSupportEntity.getId ())
            .eq(StringUtils.isNotBlank(cjroneblMedicalSupportEntity.getName ()!=null && !"".equals(cjroneblMedicalSupportEntity.getName ().toString())? cjroneblMedicalSupportEntity.getName ().toString():null),"name", cjroneblMedicalSupportEntity.getName ())
            .eq(StringUtils.isNotBlank(cjroneblMedicalSupportEntity.getSex ()!=null && !"".equals(cjroneblMedicalSupportEntity.getSex ().toString())? cjroneblMedicalSupportEntity.getSex ().toString():null),"sex", cjroneblMedicalSupportEntity.getSex ())
            .eq(StringUtils.isNotBlank(cjroneblMedicalSupportEntity.getBirthday ()!=null && !"".equals(cjroneblMedicalSupportEntity.getBirthday ().toString())? cjroneblMedicalSupportEntity.getBirthday ().toString():null),"birthday", cjroneblMedicalSupportEntity.getBirthday ())
            .eq(StringUtils.isNotBlank(cjroneblMedicalSupportEntity.getDisableId ()!=null && !"".equals(cjroneblMedicalSupportEntity.getDisableId ().toString())? cjroneblMedicalSupportEntity.getDisableId ().toString():null),"disable_id", cjroneblMedicalSupportEntity.getDisableId ())
            .eq(StringUtils.isNotBlank(cjroneblMedicalSupportEntity.getZyTime ()!=null && !"".equals(cjroneblMedicalSupportEntity.getZyTime ().toString())? cjroneblMedicalSupportEntity.getZyTime ().toString():null),"zy_time", cjroneblMedicalSupportEntity.getZyTime ())
            .eq(StringUtils.isNotBlank(cjroneblMedicalSupportEntity.getTelephone ()!=null && !"".equals(cjroneblMedicalSupportEntity.getTelephone ().toString())? cjroneblMedicalSupportEntity.getTelephone ().toString():null),"telephone", cjroneblMedicalSupportEntity.getTelephone ())
            .eq(StringUtils.isNotBlank(cjroneblMedicalSupportEntity.getGuardianName ()!=null && !"".equals(cjroneblMedicalSupportEntity.getGuardianName ().toString())? cjroneblMedicalSupportEntity.getGuardianName ().toString():null),"guardian_name", cjroneblMedicalSupportEntity.getGuardianName ())
            .eq(StringUtils.isNotBlank(cjroneblMedicalSupportEntity.getLiveAddress ()!=null && !"".equals(cjroneblMedicalSupportEntity.getLiveAddress ().toString())? cjroneblMedicalSupportEntity.getLiveAddress ().toString():null),"live_address", cjroneblMedicalSupportEntity.getLiveAddress ())
            .eq(StringUtils.isNotBlank(cjroneblMedicalSupportEntity.getCreateId ()!=null && !"".equals(cjroneblMedicalSupportEntity.getCreateId ().toString())? cjroneblMedicalSupportEntity.getCreateId ().toString():null),"create_id", cjroneblMedicalSupportEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(cjroneblMedicalSupportEntity.getCreateTime ()!=null && !"".equals(cjroneblMedicalSupportEntity.getCreateTime ().toString())? cjroneblMedicalSupportEntity.getCreateTime ().toString():null),"create_time", cjroneblMedicalSupportEntity.getCreateTime ())
            .eq(StringUtils.isNotBlank(cjroneblMedicalSupportEntity.getCreateName ()!=null && !"".equals(cjroneblMedicalSupportEntity.getCreateName ().toString())? cjroneblMedicalSupportEntity.getCreateName ().toString():null),"create_name", cjroneblMedicalSupportEntity.getCreateName ())
            .eq(StringUtils.isNotBlank(cjroneblMedicalSupportEntity.getStatus ()!=null && !"".equals(cjroneblMedicalSupportEntity.getStatus ().toString())? cjroneblMedicalSupportEntity.getStatus ().toString():null),"status", cjroneblMedicalSupportEntity.getStatus ())
            .eq(StringUtils.isNotBlank(cjroneblMedicalSupportEntity.getStatusOptions ()!=null && !"".equals(cjroneblMedicalSupportEntity.getStatusOptions ().toString())? cjroneblMedicalSupportEntity.getStatusOptions ().toString():null),"status_options", cjroneblMedicalSupportEntity.getStatusOptions ())
            .eq(StringUtils.isNotBlank(cjroneblMedicalSupportEntity.getSignStatus ()!=null && !"".equals(cjroneblMedicalSupportEntity.getSignStatus ().toString())? cjroneblMedicalSupportEntity.getSignStatus ().toString():null),"sign_status", cjroneblMedicalSupportEntity.getSignStatus ())
            .eq(StringUtils.isNotBlank(cjroneblMedicalSupportEntity.getSignatureStatus ()!=null && !"".equals(cjroneblMedicalSupportEntity.getSignatureStatus ().toString())? cjroneblMedicalSupportEntity.getSignatureStatus ().toString():null),"signature_status", cjroneblMedicalSupportEntity.getSignatureStatus ())
        );

        // 开始为状态赋值
        page.getRecords().forEach( item -> {
            if ("1".equals(item.getStatus())){
                item.setStatus("申请人待手签");
            }else if ("2".equals(item.getStatus())){
                item.setStatus("街道待审核");
            }else if ("6".equals(item.getStatus())){
                item.setStatus("区残联经办人待审核");
            }else if ("7".equals(item.getStatus()) ){
                item.setStatus("区残联负责人待审核");
            } else if ("8".equals(item.getStatus())){
                item.setStatus("通过");
            }else if ("0".equals(item.getStatus())){
                item.setStatus("禁用");
            }else if("12".equals(item.getStatus())){
                item.setStatus("退回");  // 区残联退回至街道
            }

            if("1".equals(item.getSignStatus())){
                item.setSignStatus("申请人待手签");
            }else if("2".equals(item.getSignStatus())){
                item.setSignStatus("镇街道待手签");
            }else if("6".equals(item.getSignStatus())){
                item.setSignStatus("区残联经办人待手签");
            }else if("7".equals(item.getSignStatus())){
                item.setSignStatus("区残联负责人待手签");
            }else if("8".equals(item.getSignStatus())){
                item.setSignStatus("完成手签");
            }

            if("1".equals(item.getSignatureStatus())){
                item.setSignatureStatus("无");
            }else if("2".equals(item.getSignatureStatus())){
                item.setSignatureStatus("镇街道待电子签章");
            }else if("4".equals(item.getSignatureStatus())){
                item.setSignatureStatus("民政待电子签章");
            }else if("5".equals(item.getSignatureStatus())){
                item.setSignatureStatus("区残联待电子签章");
            }else if("6".equals(item.getSignatureStatus())){
                item.setSignatureStatus("完成电子签章");
            }

            //性别
            if("0".equals(item.getSex())){
                item.setSex("女");
            }else{
                item.setSex("男");
            }

        });
        return new PageUtils(page);
    }
    @Override
    public List<CjroneblMedicalSupportEntity> queryExportData(Map<String, Object> params) {
            return cjroneblMedicalSupportDao.queryExportData(params);
    }

    @Override
    public void updateByIdSign(CjroneblMedicalSupportEntity cjroneblMedicalSupportEntity) {
        super.updateById(cjroneblMedicalSupportEntity);
    }

    @Override
    public void updateAudioById(CjroneblMedicalSupportEntity cjroneblMedicalSupportEntity) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",cjroneblMedicalSupportEntity.getId());
        map.put("matter_name","医疗救助");
        map.put("verify_time",new Date());
        map.put("status",cjroneblMedicalSupportEntity.getStatus());
        map.put("statusOptions",cjroneblMedicalSupportEntity.getStatusOptions());
        if (cjroneblMedicalSupportEntity.getStatus() != null){
            cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        }
        super.updateById(cjroneblMedicalSupportEntity);
    }

    @Override
    public boolean updateById(CjroneblMedicalSupportEntity entity) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",entity.getId());
        map.put("matter_name","医疗救助");
        map.put("status",entity.getStatus());
        map.put("signatureStatus",entity.getSignatureStatus());  // 只需要更改电子印章的状态
        map.put("statusOptions",entity.getStatusOptions());

        cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        return super.updateById(entity);
    }



}