package com.hmit.kernespring.modules.cjrone_bl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblMedicalSupportEntity;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblTemporaryAssistanceEntity;

import java.util.Map;

import java.util.List;

/**
 * 医疗救助
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-01-13 01:05:40
 */
public interface CjroneblMedicalSupportService extends IService<CjroneblMedicalSupportEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneblMedicalSupportEntity> queryExportData(Map<String, Object> params);

    // 电子盖章专用
    boolean updateById(CjroneblMedicalSupportEntity cjroneblMedicalSupportEntity);

    void updateByIdSign(CjroneblMedicalSupportEntity cjroneblMedicalSupportEntity);

    void updateAudioById(CjroneblMedicalSupportEntity cjroneblMedicalSupportEntity);
}

