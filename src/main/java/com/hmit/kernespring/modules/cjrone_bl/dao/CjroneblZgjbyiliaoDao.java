package com.hmit.kernespring.modules.cjrone_bl.dao;

import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblZgjbyiliaoEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-12 16:24:08
 */
@Mapper
public interface CjroneblZgjbyiliaoDao extends BaseMapper<CjroneblZgjbyiliaoEntity> {
    List<CjroneblZgjbyiliaoEntity> queryExportData(Map<String, Object> params);
	
}
