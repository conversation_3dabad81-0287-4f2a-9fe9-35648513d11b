package com.hmit.kernespring.modules.cjrone_bl.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 生活补助金比对数据
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-12-21 16:12:52
 */
@Data
@TableName("cjronebl_living_allowance_sync_data")
public class LivingAllowanceSyncDataEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 *
	 */

@TableId
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 残疾人姓名
	 */
@Excel(name = "残疾人姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 身份证号码
	 */
@Excel(name = "身份证号码", height = 20, width = 30, isImportField = "true_st")
private String idCard;
	/**
	 * 创建时间
	 */
@Excel(name = "创建时间", height = 20, width = 30, isImportField = "true_st")
private String createTime;
	/**
	 * 家庭经济情况
	 */
@Excel(name = "家庭经济情况", height = 20, width = 30, isImportField = "true_st")
private String familyEconoCondition;
	/**
	 * 是否死亡
	 */
@Excel(name = "是否死亡", height = 20, width = 30, isImportField = "true_st")
private Integer isDead;
	/**
	 * 0 异常 1正常
	 */
@Excel(name = "0 异常 1正常", height = 20, width = 30, isImportField = "true_st")
private Integer status;
	/**
	 * 是否服刑
	 */
@Excel(name = "是否服刑", height = 20, width = 30, isImportField = "true_st")
private Integer isFx;
	/**
	 * 是否户口迁移
	 */
@Excel(name = "是否户口迁移", height = 20, width = 30, isImportField = "true_st")
private Integer isHkqy;
	/**
	 * 服刑信息
	 */
@Excel(name = "服刑信息", height = 20, width = 30, isImportField = "true_st")
private String fxInfo;
	/**
	 * 户口迁移信息
	 */
@Excel(name = "户口迁移信息", height = 20, width = 30, isImportField = "true_st")
private String hkqyInfo;
	/**
	 *
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String exceptionReason;
	/**
	 * 是否享受生活补贴
	 */
@Excel(name = "是否享受生活补贴", height = 20, width = 30, isImportField = "true_st")
private Integer isShbt;
	/**
	 * 是否职工基本养老保险
	 */
@Excel(name = "是否职工基本养老保险", height = 20, width = 30, isImportField = "true_st")
private Integer isZgyl;
	/**
	 * 年和月
	 */
@Excel(name = "年和月", height = 20, width = 30, isImportField = "true_st")
private String ym;
	/**
	 * 年龄是否符合要求
	 */
@Excel(name = "年龄是否符合要求", height = 20, width = 30, isImportField = "true_st")
private Integer isAge;

// 是否持有残疾证
private Integer isDisable;

// 所属街道
private String nativeZhen;
}
