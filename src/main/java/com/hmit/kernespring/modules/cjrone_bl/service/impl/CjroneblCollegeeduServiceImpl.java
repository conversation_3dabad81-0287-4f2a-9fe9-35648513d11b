package com.hmit.kernespring.modules.cjrone_bl.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblBusinessGrantEntity;
import com.hmit.kernespring.modules.matter_application.dao.CjroneWelfareMatterApplicationDao;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.cjrone_bl.dao.CjroneblCollegeeduDao;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblCollegeeduEntity;
import com.hmit.kernespring.modules.cjrone_bl.service.CjroneblCollegeeduService;


@Service("cjroneblCollegeeduService")
public class CjroneblCollegeeduServiceImpl extends ServiceImpl<CjroneblCollegeeduDao, CjroneblCollegeeduEntity> implements CjroneblCollegeeduService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneblCollegeeduDao cjroneblCollegeeduDao;
    @Autowired
    private CjroneWelfareMatterApplicationDao cjroneWelfareMatterApplicationDao;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneblCollegeeduEntity cjroneblCollegeeduEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneblCollegeeduEntity.class);
        IPage<CjroneblCollegeeduEntity> page = this.page(
                new Query<CjroneblCollegeeduEntity>().getPage(params),
                new QueryWrapper<CjroneblCollegeeduEntity>()
            .eq(StringUtils.isNotBlank(cjroneblCollegeeduEntity.getId ()!=null && !"".equals(cjroneblCollegeeduEntity.getId ().toString())? cjroneblCollegeeduEntity.getId ().toString():null),"id", cjroneblCollegeeduEntity.getId ())
            .eq(StringUtils.isNotBlank(cjroneblCollegeeduEntity.getName ()!=null && !"".equals(cjroneblCollegeeduEntity.getName ().toString())? cjroneblCollegeeduEntity.getName ().toString():null),"name", cjroneblCollegeeduEntity.getName ())
            .eq(StringUtils.isNotBlank(cjroneblCollegeeduEntity.getSex ()!=null && !"".equals(cjroneblCollegeeduEntity.getSex ().toString())? cjroneblCollegeeduEntity.getSex ().toString():null),"sex", cjroneblCollegeeduEntity.getSex ())
            .eq(StringUtils.isNotBlank(cjroneblCollegeeduEntity.getBirthday ()!=null && !"".equals(cjroneblCollegeeduEntity.getBirthday ().toString())? cjroneblCollegeeduEntity.getBirthday ().toString():null),"birthday", cjroneblCollegeeduEntity.getBirthday ())
            .eq(StringUtils.isNotBlank(cjroneblCollegeeduEntity.getCollegeName ()!=null && !"".equals(cjroneblCollegeeduEntity.getCollegeName ().toString())? cjroneblCollegeeduEntity.getCollegeName ().toString():null),"college_name", cjroneblCollegeeduEntity.getCollegeName ())
            .eq(StringUtils.isNotBlank(cjroneblCollegeeduEntity.getMajorName ()!=null && !"".equals(cjroneblCollegeeduEntity.getMajorName ().toString())? cjroneblCollegeeduEntity.getMajorName ().toString():null),"major_name", cjroneblCollegeeduEntity.getMajorName ())
            .eq(StringUtils.isNotBlank(cjroneblCollegeeduEntity.getCollegeTime ()!=null && !"".equals(cjroneblCollegeeduEntity.getCollegeTime ().toString())? cjroneblCollegeeduEntity.getCollegeTime ().toString():null),"college_time", cjroneblCollegeeduEntity.getCollegeTime ())
            .eq(StringUtils.isNotBlank(cjroneblCollegeeduEntity.getDisableId ()!=null && !"".equals(cjroneblCollegeeduEntity.getDisableId ().toString())? cjroneblCollegeeduEntity.getDisableId ().toString():null),"disable_id", cjroneblCollegeeduEntity.getDisableId ())
            .eq(StringUtils.isNotBlank(cjroneblCollegeeduEntity.getTuition ()!=null && !"".equals(cjroneblCollegeeduEntity.getTuition ().toString())? cjroneblCollegeeduEntity.getTuition ().toString():null),"tuition", cjroneblCollegeeduEntity.getTuition ())
            .eq(StringUtils.isNotBlank(cjroneblCollegeeduEntity.getActuallyTuition ()!=null && !"".equals(cjroneblCollegeeduEntity.getActuallyTuition ().toString())? cjroneblCollegeeduEntity.getActuallyTuition ().toString():null),"actually_tuition", cjroneblCollegeeduEntity.getActuallyTuition ())
            .eq(StringUtils.isNotBlank(cjroneblCollegeeduEntity.getAccommodationFee ()!=null && !"".equals(cjroneblCollegeeduEntity.getAccommodationFee ().toString())? cjroneblCollegeeduEntity.getAccommodationFee ().toString():null),"accommodation_fee", cjroneblCollegeeduEntity.getAccommodationFee ())
            .eq(StringUtils.isNotBlank(cjroneblCollegeeduEntity.getActuallyAccommodationFee ()!=null && !"".equals(cjroneblCollegeeduEntity.getActuallyAccommodationFee ().toString())? cjroneblCollegeeduEntity.getActuallyAccommodationFee ().toString():null),"actually_accommodation_fee", cjroneblCollegeeduEntity.getActuallyAccommodationFee ())
            .eq(StringUtils.isNotBlank(cjroneblCollegeeduEntity.getHukouNature ()!=null && !"".equals(cjroneblCollegeeduEntity.getHukouNature ().toString())? cjroneblCollegeeduEntity.getHukouNature ().toString():null),"hukou_nature", cjroneblCollegeeduEntity.getHukouNature ())
            .eq(StringUtils.isNotBlank(cjroneblCollegeeduEntity.getFamilyCount ()!=null && !"".equals(cjroneblCollegeeduEntity.getFamilyCount ().toString())? cjroneblCollegeeduEntity.getFamilyCount ().toString():null),"family_count", cjroneblCollegeeduEntity.getFamilyCount ())
            .eq(StringUtils.isNotBlank(cjroneblCollegeeduEntity.getLiveAddress ()!=null && !"".equals(cjroneblCollegeeduEntity.getLiveAddress ().toString())? cjroneblCollegeeduEntity.getLiveAddress ().toString():null),"live_address", cjroneblCollegeeduEntity.getLiveAddress ())
            .eq(StringUtils.isNotBlank(cjroneblCollegeeduEntity.getTelephone ()!=null && !"".equals(cjroneblCollegeeduEntity.getTelephone ().toString())? cjroneblCollegeeduEntity.getTelephone ().toString():null),"telephone", cjroneblCollegeeduEntity.getTelephone ())
            .eq(StringUtils.isNotBlank(cjroneblCollegeeduEntity.getPostcode ()!=null && !"".equals(cjroneblCollegeeduEntity.getPostcode ().toString())? cjroneblCollegeeduEntity.getPostcode ().toString():null),"postcode", cjroneblCollegeeduEntity.getPostcode ())
            .eq(StringUtils.isNotBlank(cjroneblCollegeeduEntity.getFamilyFinances ()!=null && !"".equals(cjroneblCollegeeduEntity.getFamilyFinances ().toString())? cjroneblCollegeeduEntity.getFamilyFinances ().toString():null),"family_finances", cjroneblCollegeeduEntity.getFamilyFinances ())
            .eq(StringUtils.isNotBlank(cjroneblCollegeeduEntity.getFamilyIncome ()!=null && !"".equals(cjroneblCollegeeduEntity.getFamilyIncome ().toString())? cjroneblCollegeeduEntity.getFamilyIncome ().toString():null),"family_income", cjroneblCollegeeduEntity.getFamilyIncome ())
            .eq(StringUtils.isNotBlank(cjroneblCollegeeduEntity.getSubsidyReason ()!=null && !"".equals(cjroneblCollegeeduEntity.getSubsidyReason ().toString())? cjroneblCollegeeduEntity.getSubsidyReason ().toString():null),"subsidy_reason", cjroneblCollegeeduEntity.getSubsidyReason ())
            .eq(StringUtils.isNotBlank(cjroneblCollegeeduEntity.getCreateId ()!=null && !"".equals(cjroneblCollegeeduEntity.getCreateId ().toString())? cjroneblCollegeeduEntity.getCreateId ().toString():null),"create_id", cjroneblCollegeeduEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(cjroneblCollegeeduEntity.getCreateTime ()!=null && !"".equals(cjroneblCollegeeduEntity.getCreateTime ().toString())? cjroneblCollegeeduEntity.getCreateTime ().toString():null),"create_time", cjroneblCollegeeduEntity.getCreateTime ())
            .eq(StringUtils.isNotBlank(cjroneblCollegeeduEntity.getCreateName ()!=null && !"".equals(cjroneblCollegeeduEntity.getCreateName ().toString())? cjroneblCollegeeduEntity.getCreateName ().toString():null),"create_name", cjroneblCollegeeduEntity.getCreateName ())
            .eq(StringUtils.isNotBlank(cjroneblCollegeeduEntity.getStatus ()!=null && !"".equals(cjroneblCollegeeduEntity.getStatus ().toString())? cjroneblCollegeeduEntity.getStatus ().toString():null),"status", cjroneblCollegeeduEntity.getStatus ())
            .eq(StringUtils.isNotBlank(cjroneblCollegeeduEntity.getStatusOptions ()!=null && !"".equals(cjroneblCollegeeduEntity.getStatusOptions ().toString())? cjroneblCollegeeduEntity.getStatusOptions ().toString():null),"status_options", cjroneblCollegeeduEntity.getStatusOptions ())
            .eq(StringUtils.isNotBlank(cjroneblCollegeeduEntity.getSignStatus ()!=null && !"".equals(cjroneblCollegeeduEntity.getSignStatus ().toString())? cjroneblCollegeeduEntity.getSignStatus ().toString():null),"sign_status", cjroneblCollegeeduEntity.getSignStatus ())
            .eq(StringUtils.isNotBlank(cjroneblCollegeeduEntity.getSignatureStatus ()!=null && !"".equals(cjroneblCollegeeduEntity.getSignatureStatus ().toString())? cjroneblCollegeeduEntity.getSignatureStatus ().toString():null),"signature_status", cjroneblCollegeeduEntity.getSignatureStatus ())
        );

        // 开始为状态赋值
        page.getRecords().forEach( item -> {
            if ("1".equals(item.getStatus())) {
                item.setStatus("申请人待手签");
            } else if ("2".equals(item.getStatus())) {
                item.setStatus("街道待审核");
            } else if ("6".equals(item.getStatus())) {
                item.setStatus("区残联经办人待审核");
            } else if ("7".equals(item.getStatus())) {
                item.setStatus("区残联负责人待审核");
            } else if ("8".equals(item.getStatus())) {
                item.setStatus("通过");
            } else if ("0".equals(item.getStatus())) {
                item.setStatus("禁用");
            } else if ("12".equals(item.getStatus())) {
                item.setStatus("退回");  // 区残联退回至街道
            } else if ("98".equals(item.getStatus())) {
                item.setStatus("教育局待审核");
            } else if ("97".equals(item.getStatus())) {
                item.setStatus("财政局待审核");
            }

            if ("1".equals(item.getSignStatus())) {
                item.setSignStatus("申请人待手签");
            } else if ("2".equals(item.getSignStatus())) {
                item.setSignStatus("镇街道待手签");
            } else if ("6".equals(item.getSignStatus())) {
                item.setSignStatus("区残联经办人待手签");
            } else if ("7".equals(item.getSignStatus())) {
                item.setSignStatus("区残联负责人待手签");
            } else if ("8".equals(item.getSignStatus())) {
                item.setSignStatus("完成手签");
            }

            if ("1".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("无");
            } else if ("2".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("镇街道待电子签章");
            } else if ("4".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("民政待电子签章");
            } else if ("5".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("区残联待电子签章");
            } else if ("6".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("完成电子签章");
            } else if ("7".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("教育局待电子签章");
            } else if ("8".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("财政局待电子签章");
            }


            //性别
            if ("0".equals(item.getSex())) {
                item.setSex("女");
            } else {
                item.setSex("男");
            }
        });

        return new PageUtils(page);
    }
    @Override
    public List<CjroneblCollegeeduEntity> queryExportData(Map<String, Object> params) {
            return cjroneblCollegeeduDao.queryExportData(params);
    }



    @Override
    public void updateByIdSign(CjroneblCollegeeduEntity cjroneblCollegeeduEntity) {
        super.updateById(cjroneblCollegeeduEntity);
    }

    @Override
    public void updateAudioById(CjroneblCollegeeduEntity cjroneblCollegeeduEntity) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",cjroneblCollegeeduEntity.getId());
        map.put("matter_name","大学生补助");
        map.put("verify_time",new Date());
        map.put("status",cjroneblCollegeeduEntity.getStatus());
        map.put("statusOptions",cjroneblCollegeeduEntity.getStatusOptions());
        if (cjroneblCollegeeduEntity.getStatus() != null){
            cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        }
        super.updateById(cjroneblCollegeeduEntity);
    }

    @Override
    public boolean updateById(CjroneblCollegeeduEntity entity) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",entity.getId());
        map.put("matter_name","大学生补助");
        map.put("status",entity.getStatus());
        map.put("signatureStatus",entity.getSignatureStatus());  // 只需要更改电子印章的状态
        map.put("statusOptions",entity.getStatusOptions());

        cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        return super.updateById(entity);
    }

}