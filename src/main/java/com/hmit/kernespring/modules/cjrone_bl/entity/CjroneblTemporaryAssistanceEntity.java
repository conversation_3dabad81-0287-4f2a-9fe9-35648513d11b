package com.hmit.kernespring.modules.cjrone_bl.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 残疾人临时救助
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-13 13:51:37
 */
@Data
@TableName("cjronebl_temporary_assistance")
public class CjroneblTemporaryAssistanceEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
@TableId
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 姓名
	 */
@Excel(name = "姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 性别
	 */
@Excel(name = "性别", height = 20, width = 30, isImportField = "true_st")
private String sex;
	/**
	 * 出生日期
	 */
@Excel(name = "出生日期", height = 20, width = 30, isImportField = "true_st")
private String birthday;
	/**
	 * 残疾类别
	 */
@Excel(name = "残疾类别", height = 20, width = 30, isImportField = "true_st")
private String disabilityType;
	/**
	 * 残疾等级
	 */
@Excel(name = "残疾等级", height = 20, width = 30, isImportField = "true_st")
private String disabilityDegree;
	/**
	 * 残疾证号码
	 */
@Excel(name = "残疾证号码", height = 20, width = 30, isImportField = "true_st")
private String disableId;
	/**
	 * 身份证号码
	 */
@Excel(name = "身份证号码", height = 20, width = 30, isImportField = "true_st")
private String idCard;
	/**
	 * 家庭详细地址
	 */
@Excel(name = "家庭详细地址", height = 20, width = 30, isImportField = "true_st")
private String liveAddress;
	/**
	 * 联系电话
	 */
@Excel(name = "联系电话", height = 20, width = 30, isImportField = "true_st")
private String telephone;
	/**
	 * 家庭经济情况
	 */
@Excel(name = "家庭经济情况", height = 20, width = 30, isImportField = "true_st")
private String familyEconomy;
	/**
	 * 是否享受民政临时补助
	 */
@Excel(name = "是否享受民政临时补助", height = 20, width = 30, isImportField = "true_st")
private String mingzhenSubsidy;
	/**
	 * 经济损失或直接费用
	 */
@Excel(name = "经济损失或直接费用", height = 20, width = 30, isImportField = "true_st")
private String payMoney;
	/**
	 * 补贴金额
	 */
@Excel(name = "补贴金额", height = 20, width = 30, isImportField = "true_st")
private String subsidyMoney;
	/**
	 * 申请理由
	 */
@Excel(name = "申请理由", height = 20, width = 30, isImportField = "true_st")
private String applyReason;
	/**
	 * 创建人编号
	 */
@Excel(name = "创建人编号", height = 20, width = 30, isImportField = "true_st")
private Integer createId;
	/**
	 * 创建时间
	 */
@Excel(name = "创建时间", height = 20, width = 30, isImportField = "true_st")
private String createTime;
	/**
	 * 创建人姓名
	 */
@Excel(name = "创建人姓名", height = 20, width = 30, isImportField = "true_st")
private String createName;
	/**
	 * 状态
	 */
@Excel(name = "状态", height = 20, width = 30, isImportField = "true_st")
private String status;
	/**
	 * 
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String statusOptions;
	/**
	 * 
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String signStatus;
	/**
	 * 
	 */
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String signatureStatus;

}
