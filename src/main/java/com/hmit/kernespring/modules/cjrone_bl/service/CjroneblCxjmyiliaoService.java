package com.hmit.kernespring.modules.cjrone_bl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblCxjmyiliaoEntity;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblZgjbyiliaoEntity;

import java.util.Map;

import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-13 09:59:33
 */
public interface CjroneblCxjmyiliaoService extends IService<CjroneblCxjmyiliaoEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneblCxjmyiliaoEntity> queryExportData(Map<String, Object> params);

    // 电子盖章专用
    boolean updateById(CjroneblCxjmyiliaoEntity cjroneblCxjmyiliaoEntity);
    void updateByIdSign(CjroneblCxjmyiliaoEntity cjroneblCxjmyiliaoEntity);

    void updateAudioById(CjroneblCxjmyiliaoEntity cjroneblCxjmyiliaoEntity);
}

