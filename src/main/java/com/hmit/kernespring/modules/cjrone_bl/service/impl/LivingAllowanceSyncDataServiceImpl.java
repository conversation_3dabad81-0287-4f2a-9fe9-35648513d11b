package com.hmit.kernespring.modules.cjrone_bl.service.impl;


import com.google.gson.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.cjrone_bl.dao.LivingAllowanceSyncDataDao;
import com.hmit.kernespring.modules.cjrone_bl.entity.LivingAllowanceSyncDataEntity;
import com.hmit.kernespring.modules.cjrone_bl.service.LivingAllowanceSyncDataService;


@Service("livingAllowanceSyncDataService")
public class LivingAllowanceSyncDataServiceImpl extends ServiceImpl<LivingAllowanceSyncDataDao, LivingAllowanceSyncDataEntity> implements LivingAllowanceSyncDataService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private LivingAllowanceSyncDataDao livingAllowanceSyncDataDao;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        LivingAllowanceSyncDataEntity livingAllowanceSyncDataEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, LivingAllowanceSyncDataEntity.class);
        IPage<LivingAllowanceSyncDataEntity> page = this.page(
                new Query<LivingAllowanceSyncDataEntity>().getPage(params),
                new QueryWrapper<LivingAllowanceSyncDataEntity>()
            .eq(StringUtils.isNotBlank(livingAllowanceSyncDataEntity.getId ()!=null && !"".equals(livingAllowanceSyncDataEntity.getId ().toString())? livingAllowanceSyncDataEntity.getId ().toString():null),"id", livingAllowanceSyncDataEntity.getId ())
            .eq(StringUtils.isNotBlank(livingAllowanceSyncDataEntity.getName ()!=null && !"".equals(livingAllowanceSyncDataEntity.getName ().toString())? livingAllowanceSyncDataEntity.getName ().toString():null),"name", livingAllowanceSyncDataEntity.getName ())
            .eq(StringUtils.isNotBlank(livingAllowanceSyncDataEntity.getIdCard ()!=null && !"".equals(livingAllowanceSyncDataEntity.getIdCard ().toString())? livingAllowanceSyncDataEntity.getIdCard ().toString():null),"id_card", livingAllowanceSyncDataEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(livingAllowanceSyncDataEntity.getCreateTime ()!=null && !"".equals(livingAllowanceSyncDataEntity.getCreateTime ().toString())? livingAllowanceSyncDataEntity.getCreateTime ().toString():null),"create_time", livingAllowanceSyncDataEntity.getCreateTime ())
            .eq(StringUtils.isNotBlank(livingAllowanceSyncDataEntity.getFamilyEconoCondition ()!=null && !"".equals(livingAllowanceSyncDataEntity.getFamilyEconoCondition ().toString())? livingAllowanceSyncDataEntity.getFamilyEconoCondition ().toString():null),"family_econo_condition", livingAllowanceSyncDataEntity.getFamilyEconoCondition ())
            .eq(StringUtils.isNotBlank(livingAllowanceSyncDataEntity.getIsDead ()!=null && !"".equals(livingAllowanceSyncDataEntity.getIsDead ().toString())? livingAllowanceSyncDataEntity.getIsDead ().toString():null),"is_dead", livingAllowanceSyncDataEntity.getIsDead ())
            .eq(StringUtils.isNotBlank(livingAllowanceSyncDataEntity.getStatus ()!=null && !"".equals(livingAllowanceSyncDataEntity.getStatus ().toString())? livingAllowanceSyncDataEntity.getStatus ().toString():null),"status", livingAllowanceSyncDataEntity.getStatus ())
            .eq(StringUtils.isNotBlank(livingAllowanceSyncDataEntity.getIsFx ()!=null && !"".equals(livingAllowanceSyncDataEntity.getIsFx ().toString())? livingAllowanceSyncDataEntity.getIsFx ().toString():null),"is_fx", livingAllowanceSyncDataEntity.getIsFx ())
            .eq(StringUtils.isNotBlank(livingAllowanceSyncDataEntity.getIsHkqy ()!=null && !"".equals(livingAllowanceSyncDataEntity.getIsHkqy ().toString())? livingAllowanceSyncDataEntity.getIsHkqy ().toString():null),"is_hkqy", livingAllowanceSyncDataEntity.getIsHkqy ())
            .eq(StringUtils.isNotBlank(livingAllowanceSyncDataEntity.getFxInfo ()!=null && !"".equals(livingAllowanceSyncDataEntity.getFxInfo ().toString())? livingAllowanceSyncDataEntity.getFxInfo ().toString():null),"fx_info", livingAllowanceSyncDataEntity.getFxInfo ())
            .eq(StringUtils.isNotBlank(livingAllowanceSyncDataEntity.getHkqyInfo ()!=null && !"".equals(livingAllowanceSyncDataEntity.getHkqyInfo ().toString())? livingAllowanceSyncDataEntity.getHkqyInfo ().toString():null),"hkqy_info", livingAllowanceSyncDataEntity.getHkqyInfo ())
            .eq(StringUtils.isNotBlank(livingAllowanceSyncDataEntity.getExceptionReason ()!=null && !"".equals(livingAllowanceSyncDataEntity.getExceptionReason ().toString())? livingAllowanceSyncDataEntity.getExceptionReason ().toString():null),"exception_reason", livingAllowanceSyncDataEntity.getExceptionReason ())
            .eq(StringUtils.isNotBlank(livingAllowanceSyncDataEntity.getIsShbt ()!=null && !"".equals(livingAllowanceSyncDataEntity.getIsShbt ().toString())? livingAllowanceSyncDataEntity.getIsShbt ().toString():null),"is_shbt", livingAllowanceSyncDataEntity.getIsShbt ())
            .eq(StringUtils.isNotBlank(livingAllowanceSyncDataEntity.getIsZgyl ()!=null && !"".equals(livingAllowanceSyncDataEntity.getIsZgyl ().toString())? livingAllowanceSyncDataEntity.getIsZgyl ().toString():null),"is_zgyl", livingAllowanceSyncDataEntity.getIsZgyl ())
            .eq(StringUtils.isNotBlank(livingAllowanceSyncDataEntity.getYm ()!=null && !"".equals(livingAllowanceSyncDataEntity.getYm ().toString())? livingAllowanceSyncDataEntity.getYm ().toString():null),"ym", livingAllowanceSyncDataEntity.getYm ())
            .eq(StringUtils.isNotBlank(livingAllowanceSyncDataEntity.getIsAge ()!=null && !"".equals(livingAllowanceSyncDataEntity.getIsAge ().toString())? livingAllowanceSyncDataEntity.getIsAge ().toString():null),"is_age", livingAllowanceSyncDataEntity.getIsAge ())
            .eq(StringUtils.isNotBlank(livingAllowanceSyncDataEntity.getNativeZhen ()!=null && !"".equals(livingAllowanceSyncDataEntity.getNativeZhen ().toString())? livingAllowanceSyncDataEntity.getNativeZhen ().toString():null),"native_zhen", livingAllowanceSyncDataEntity.getNativeZhen ())
        );
        return new PageUtils(page);
    }
    @Override
    public List<LivingAllowanceSyncDataEntity> queryExportData(Map<String, Object> params) {
            return livingAllowanceSyncDataDao.queryExportData(params);
    }

}