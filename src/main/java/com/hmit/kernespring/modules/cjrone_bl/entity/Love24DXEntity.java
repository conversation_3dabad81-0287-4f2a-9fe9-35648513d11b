package com.hmit.kernespring.modules.cjrone_bl.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 智慧爱心24小时(导出专用--电信）
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-11-25 17:44:36
 */
@Data
public class Love24DXEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 申请人姓名
	 */
	//@ApiModelProperty(value = "申请人姓名")
@Excel(name = "申请人姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 性别
	 */
	//@ApiModelProperty(value = "性别")
@Excel(name = "性别", height = 20, width = 30, isImportField = "true_st")
private String sexName;

private String sex;
	/**
	 * 残疾证号
	 */
	//ApiModelProperty(value = "残疾证号")
@Excel(name = "残疾证号", height = 20, width = 30, isImportField = "true_st")
private String disableId;

	// 身份证号码
	@Excel(name = "身份证号", height = 20, width = 30, isImportField = "true_st")
	private String idCard;

	/**
	 * 残疾类别
	 */
	//@ApiModelProperty(value = "残疾类别")
@Excel(name = "残疾类别", height = 20, width = 30, isImportField = "true_st")
private String disableType;
	/**
	 * 监护人
	 */
	//@ApiModelProperty(value = "监护人")
@Excel(name = "监护人", height = 20, width = 30, isImportField = "true_st")
private String guardian;
	/**
	 * 监护人
	 */
	//@ApiModelProperty(value = "监护人")
@Excel(name = "监护人", height = 20, width = 30, isImportField = "true_st")
private String liveAddress;
	/**
	 * 联系电话
	 */
	//@ApiModelProperty(value = "联系电话")
@Excel(name = "联系电话", height = 20, width = 30, isImportField = "true_st")
private String telephone;
	/**
	 * 与申请人关系1
	 */
	//@ApiModelProperty(value = "与申请人关系1")
@Excel(name = "与申请人关系", height = 20, width = 30, isImportField = "true_st")
private String relation1;
	/**
	 * 姓名1
	 */
	//@ApiModelProperty(value = "姓名1")
@Excel(name = "姓名", height = 20, width = 30, isImportField = "true_st")
private String name1;
	/**
	 * 身份证号或残疾证号1
	 */
	//@ApiModelProperty(value = "身份证号或残疾证号1")
@Excel(name = "身份证号或残疾证号", height = 20, width = 30, isImportField = "true_st")
private String idcard1;
	/**
	 * 联系电话1
	 */
	//@ApiModelProperty(value = "联系电话")
@Excel(name = "联系电话1", height = 20, width = 30, isImportField = "true_st")
private String tel1;
	/**
	 * 电信：
	 0 固定电话+定制手机；
	 1 定制CDMA手机一部；
	 2 固定电话；
	 3 号码定位；
	 4 单手机用户
	 移动：
	 10 智能机手机定位；
	 11 购机优惠；
	 12 套餐优惠；
	 13 宽带电视；
	 */
@Excel(name = "申请项目类型", height = 20, width = 30, isImportField = "true_st")
private String typeName;
private String type;
	/**
	 * 申请电话号码
	 */
	//@ApiModelProperty(value = "申请电话号码")
@Excel(name = "手机号码", height = 20, width = 30, isImportField = "true_st")
//电信手机号码
private String typeTelephone;

//电信固定电话
@Excel(name = "固定电话", height = 20, width = 30, isImportField = "true_st")
private String dianxinTelephone;
	/**
	 * 创建时间
	 */
	//@ApiModelProperty(value = "创建时间")
@Excel(name = "创建时间", height = 20, width = 30, isImportField = "true_st")
private String createTime;
	/**
	 * 创建人姓名
	 */
	//@ApiModelProperty(value = "创建人姓名")
@Excel(name = "创建人姓名", height = 20, width = 30, isImportField = "true_st")
private String createName;
	/**
	 * 是否禁用
	 * 异常数据（户籍迁出，死亡人员） -- 禁用之后状态设置成1，正常为0
	 */
	//@ApiModelProperty(value = "退回状态")
private Integer isDisable;

@Excel(name = "是否禁用", height = 20, width = 30, isImportField = "true_st")
private String isDisableName;

	/**
	 * 号码定位（电信）
	 */
@Excel(name = "号码定位", height = 20, width = 30, isImportField = "true_st")
private String familyLove;

@Excel(name = "运营商", height = 20, width = 30, isImportField = "true_st")
private String applicationType;


}
