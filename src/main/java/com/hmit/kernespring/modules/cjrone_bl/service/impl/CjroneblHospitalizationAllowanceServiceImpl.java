package com.hmit.kernespring.modules.cjrone_bl.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblMedicalSupportEntity;
import com.hmit.kernespring.modules.matter_application.dao.CjroneWelfareMatterApplicationDao;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.cjrone_bl.dao.CjroneblHospitalizationAllowanceDao;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblHospitalizationAllowanceEntity;
import com.hmit.kernespring.modules.cjrone_bl.service.CjroneblHospitalizationAllowanceService;


@Service("cjroneblHospitalizationAllowanceService")
public class CjroneblHospitalizationAllowanceServiceImpl extends ServiceImpl<CjroneblHospitalizationAllowanceDao, CjroneblHospitalizationAllowanceEntity> implements CjroneblHospitalizationAllowanceService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneblHospitalizationAllowanceDao cjroneblHospitalizationAllowanceDao;
    @Autowired
    private CjroneWelfareMatterApplicationDao cjroneWelfareMatterApplicationDao;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneblHospitalizationAllowanceEntity cjroneblHospitalizationAllowanceEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneblHospitalizationAllowanceEntity.class);
        IPage<CjroneblHospitalizationAllowanceEntity> page = this.page(
                new Query<CjroneblHospitalizationAllowanceEntity>().getPage(params),
                new QueryWrapper<CjroneblHospitalizationAllowanceEntity>()
            .eq(StringUtils.isNotBlank(cjroneblHospitalizationAllowanceEntity.getId ()!=null && !"".equals(cjroneblHospitalizationAllowanceEntity.getId ().toString())? cjroneblHospitalizationAllowanceEntity.getId ().toString():null),"id", cjroneblHospitalizationAllowanceEntity.getId ())
            .eq(StringUtils.isNotBlank(cjroneblHospitalizationAllowanceEntity.getName ()!=null && !"".equals(cjroneblHospitalizationAllowanceEntity.getName ().toString())? cjroneblHospitalizationAllowanceEntity.getName ().toString():null),"name", cjroneblHospitalizationAllowanceEntity.getName ())
            .eq(StringUtils.isNotBlank(cjroneblHospitalizationAllowanceEntity.getSex ()!=null && !"".equals(cjroneblHospitalizationAllowanceEntity.getSex ().toString())? cjroneblHospitalizationAllowanceEntity.getSex ().toString():null),"sex", cjroneblHospitalizationAllowanceEntity.getSex ())
            .eq(StringUtils.isNotBlank(cjroneblHospitalizationAllowanceEntity.getBirthday ()!=null && !"".equals(cjroneblHospitalizationAllowanceEntity.getBirthday ().toString())? cjroneblHospitalizationAllowanceEntity.getBirthday ().toString():null),"birthday", cjroneblHospitalizationAllowanceEntity.getBirthday ())
            .eq(StringUtils.isNotBlank(cjroneblHospitalizationAllowanceEntity.getDisableId ()!=null && !"".equals(cjroneblHospitalizationAllowanceEntity.getDisableId ().toString())? cjroneblHospitalizationAllowanceEntity.getDisableId ().toString():null),"disable_id", cjroneblHospitalizationAllowanceEntity.getDisableId ())
            .eq(StringUtils.isNotBlank(cjroneblHospitalizationAllowanceEntity.getZyTime ()!=null && !"".equals(cjroneblHospitalizationAllowanceEntity.getZyTime ().toString())? cjroneblHospitalizationAllowanceEntity.getZyTime ().toString():null),"zy_time", cjroneblHospitalizationAllowanceEntity.getZyTime ())
            .eq(StringUtils.isNotBlank(cjroneblHospitalizationAllowanceEntity.getTelephone ()!=null && !"".equals(cjroneblHospitalizationAllowanceEntity.getTelephone ().toString())? cjroneblHospitalizationAllowanceEntity.getTelephone ().toString():null),"telephone", cjroneblHospitalizationAllowanceEntity.getTelephone ())
            .eq(StringUtils.isNotBlank(cjroneblHospitalizationAllowanceEntity.getGuardianName ()!=null && !"".equals(cjroneblHospitalizationAllowanceEntity.getGuardianName ().toString())? cjroneblHospitalizationAllowanceEntity.getGuardianName ().toString():null),"guardian_name", cjroneblHospitalizationAllowanceEntity.getGuardianName ())
            .eq(StringUtils.isNotBlank(cjroneblHospitalizationAllowanceEntity.getLiveAddress ()!=null && !"".equals(cjroneblHospitalizationAllowanceEntity.getLiveAddress ().toString())? cjroneblHospitalizationAllowanceEntity.getLiveAddress ().toString():null),"live_address", cjroneblHospitalizationAllowanceEntity.getLiveAddress ())
            .eq(StringUtils.isNotBlank(cjroneblHospitalizationAllowanceEntity.getCreateId ()!=null && !"".equals(cjroneblHospitalizationAllowanceEntity.getCreateId ().toString())? cjroneblHospitalizationAllowanceEntity.getCreateId ().toString():null),"create_id", cjroneblHospitalizationAllowanceEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(cjroneblHospitalizationAllowanceEntity.getCreateTime ()!=null && !"".equals(cjroneblHospitalizationAllowanceEntity.getCreateTime ().toString())? cjroneblHospitalizationAllowanceEntity.getCreateTime ().toString():null),"create_time", cjroneblHospitalizationAllowanceEntity.getCreateTime ())
            .eq(StringUtils.isNotBlank(cjroneblHospitalizationAllowanceEntity.getCreateName ()!=null && !"".equals(cjroneblHospitalizationAllowanceEntity.getCreateName ().toString())? cjroneblHospitalizationAllowanceEntity.getCreateName ().toString():null),"create_name", cjroneblHospitalizationAllowanceEntity.getCreateName ())
            .eq(StringUtils.isNotBlank(cjroneblHospitalizationAllowanceEntity.getStatus ()!=null && !"".equals(cjroneblHospitalizationAllowanceEntity.getStatus ().toString())? cjroneblHospitalizationAllowanceEntity.getStatus ().toString():null),"status", cjroneblHospitalizationAllowanceEntity.getStatus ())
            .eq(StringUtils.isNotBlank(cjroneblHospitalizationAllowanceEntity.getStatusOptions ()!=null && !"".equals(cjroneblHospitalizationAllowanceEntity.getStatusOptions ().toString())? cjroneblHospitalizationAllowanceEntity.getStatusOptions ().toString():null),"status_options", cjroneblHospitalizationAllowanceEntity.getStatusOptions ())
            .eq(StringUtils.isNotBlank(cjroneblHospitalizationAllowanceEntity.getSignStatus ()!=null && !"".equals(cjroneblHospitalizationAllowanceEntity.getSignStatus ().toString())? cjroneblHospitalizationAllowanceEntity.getSignStatus ().toString():null),"sign_status", cjroneblHospitalizationAllowanceEntity.getSignStatus ())
            .eq(StringUtils.isNotBlank(cjroneblHospitalizationAllowanceEntity.getSignatureStatus ()!=null && !"".equals(cjroneblHospitalizationAllowanceEntity.getSignatureStatus ().toString())? cjroneblHospitalizationAllowanceEntity.getSignatureStatus ().toString():null),"signature_status", cjroneblHospitalizationAllowanceEntity.getSignatureStatus ())
        );

        // 开始为状态赋值
        page.getRecords().forEach( item -> {
            if ("1".equals(item.getStatus())){
                item.setStatus("申请人待手签");
            }else if ("2".equals(item.getStatus())){
                item.setStatus("街道待审核");
            }else if ("6".equals(item.getStatus())){
                item.setStatus("区残联经办人待审核");
            }else if ("7".equals(item.getStatus()) ){
                item.setStatus("区残联负责人待审核");
            } else if ("8".equals(item.getStatus())){
                item.setStatus("通过");
            }else if ("0".equals(item.getStatus())){
                item.setStatus("禁用");
            }else if("12".equals(item.getStatus())){
                item.setStatus("退回");  // 区残联退回至街道
            }

            if("1".equals(item.getSignStatus())){
                item.setSignStatus("申请人待手签");
            }else if("2".equals(item.getSignStatus())){
                item.setSignStatus("镇街道待手签");
            }else if("6".equals(item.getSignStatus())){
                item.setSignStatus("区残联经办人待手签");
            }else if("7".equals(item.getSignStatus())){
                item.setSignStatus("区残联负责人待手签");
            }else if("8".equals(item.getSignStatus())){
                item.setSignStatus("完成手签");
            }

            if("1".equals(item.getSignatureStatus())){
                item.setSignatureStatus("无");
            }else if("2".equals(item.getSignatureStatus())){
                item.setSignatureStatus("镇街道待电子签章");
            }else if("4".equals(item.getSignatureStatus())){
                item.setSignatureStatus("民政待电子签章");
            }else if("5".equals(item.getSignatureStatus())){
                item.setSignatureStatus("区残联待电子签章");
            }else if("6".equals(item.getSignatureStatus())){
                item.setSignatureStatus("完成电子签章");
            }

            //性别
            if("0".equals(item.getSex())){
                item.setSex("女");
            }else{
                item.setSex("男");
            }

        });

        return new PageUtils(page);
    }
    @Override
    public List<CjroneblHospitalizationAllowanceEntity> queryExportData(Map<String, Object> params) {
            return cjroneblHospitalizationAllowanceDao.queryExportData(params);
    }

    @Override
    public void updateByIdSign(CjroneblHospitalizationAllowanceEntity cjroneblHospitalizationAllowanceEntity) {
        super.updateById(cjroneblHospitalizationAllowanceEntity);
    }

    @Override
    public void updateAudioById(CjroneblHospitalizationAllowanceEntity cjroneblHospitalizationAllowanceEntity) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",cjroneblHospitalizationAllowanceEntity.getId());
        map.put("matter_name","住院补助");
        map.put("verify_time",new Date());
        map.put("status",cjroneblHospitalizationAllowanceEntity.getStatus());
        map.put("statusOptions",cjroneblHospitalizationAllowanceEntity.getStatusOptions());
        if (cjroneblHospitalizationAllowanceEntity.getStatus() != null){
            cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        }
        super.updateById(cjroneblHospitalizationAllowanceEntity);
    }

    @Override
    public boolean updateById(CjroneblHospitalizationAllowanceEntity entity) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",entity.getId());
        map.put("matter_name","住院补助");
        map.put("status",entity.getStatus());
        map.put("signatureStatus",entity.getSignatureStatus());  // 只需要更改电子印章的状态
        map.put("statusOptions",entity.getStatusOptions());

        cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        return super.updateById(entity);
    }

}