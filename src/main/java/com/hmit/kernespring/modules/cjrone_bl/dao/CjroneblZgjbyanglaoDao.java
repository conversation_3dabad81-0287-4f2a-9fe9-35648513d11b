package com.hmit.kernespring.modules.cjrone_bl.dao;

import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblZgjbyanglaoEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-12 11:25:34
 */
@Mapper
public interface CjroneblZgjbyanglaoDao extends BaseMapper<CjroneblZgjbyanglaoEntity> {
    List<CjroneblZgjbyanglaoEntity> queryExportData(Map<String, Object> params);
	
}
