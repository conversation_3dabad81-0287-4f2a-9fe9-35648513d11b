package com.hmit.kernespring.modules.cjrone_bl.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblChildeduEntity;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblRehabilitationSubsidyEntity;

import java.util.Map;

import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-24 14:31:01
 */
public interface CjroneblChildeduService extends IService<CjroneblChildeduEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneblChildeduEntity> queryExportData(Map<String, Object> params);

    // 电子盖章专用
    boolean updateById(CjroneblChildeduEntity cjroneblChildeduEntity);

    void updateByIdSign(CjroneblChildeduEntity cjroneblChildeduEntity);

    void updateAudioById(CjroneblChildeduEntity cjroneblChildeduEntity);
}

