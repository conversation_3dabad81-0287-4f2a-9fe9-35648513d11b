package com.hmit.kernespring.modules.data_management.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 服刑数据信息

 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-12-18 16:37:13
 */
@Data
@TableName("data_fxinfo")
public class FxinfoEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 *
	 */

@TableId
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 姓名
	 */
@Excel(name = "姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 性别
	 */
@Excel(name = "性别", height = 20, width = 30, isImportField = "true_st")
private String sex;
	/**
	 * 年龄
	 */
@Excel(name = "年龄", height = 20, width = 30, isImportField = "true_st")
private String age;
	/**
	 * 籍贯
	 */
@Excel(name = "籍贯", height = 20, width = 30, isImportField = "true_st")
private String birthplace;
	/**
	 * 身份证号
	 */
@Excel(name = "身份证号", height = 20, width = 30, isImportField = "true_st")
private String idCard;
	/**
	 * 数据来源接口
	 */
@Excel(name = "数据来源接口", height = 20, width = 30, isImportField = "true_st")
private String fromInfo;
	/**
	 * 接口返回的json数据
	 */
@Excel(name = "接口返回的json数据", height = 20, width = 30, isImportField = "true_st")
private String resultInfo;

}
