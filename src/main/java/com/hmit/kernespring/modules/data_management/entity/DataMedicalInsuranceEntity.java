package com.hmit.kernespring.modules.data_management.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 医保参保导入表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-27 14:00:03
 */
@Data
@TableName("data_medical_insurance")
public class DataMedicalInsuranceEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
@TableId
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 残疾人姓名
	 */
	@Excel(name = "残疾人姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 身份证号码
	 */
	@Excel(name = "身份证号码", height = 20, width = 30, isImportField = "true_st")
private String idCard;
	/**
	 * 医保参保情况
	 */
	@Excel(name = "医保参保情况", height = 20, width = 30, isImportField = "true_st")
private String insuredSituation;
	/**
	 * 参保时间
	 */
	@Excel(name = "参保时间", height = 20, width = 30, isImportField = "true_st")
private String insuredTime;

}
