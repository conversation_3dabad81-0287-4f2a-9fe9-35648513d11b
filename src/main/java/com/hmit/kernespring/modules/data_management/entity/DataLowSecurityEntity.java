package com.hmit.kernespring.modules.data_management.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 家庭经济情况-低保信息
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-19 09:01:24
 */
@Data
@TableName("data_low_security")
public class DataLowSecurityEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
@TableId
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 行政划分
	 */
	@Excel(name = "行政划分", height = 20, width = 30, isImportField = "true_st")
private String administrativeDivision;
	/**
	 * 申请人
	 */
	@Excel(name = "申请人", height = 20, width = 30, isImportField = "true_st")
private String applicant;
	/**
	 * 申请身份证
	 */
	@Excel(name = "申请身份证", height = 20, width = 30, isImportField = "true_st")
private String applyIdCard;
	/**
	 * 人员姓名
	 */
	@Excel(name = "人员姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 与户主关系
	 */
	@Excel(name = "与户主关系", height = 20, width = 30, isImportField = "true_st")
private String relationshipWithHousehold;
	/**
	 * 身份证号
	 */
	@Excel(name = "身份证号", height = 20, width = 30, isImportField = "true_st")
private String idCard;
	/**
	 *  人员信息类别
	 */
	@Excel(name = " 人员信息类别", height = 20, width = 30, isImportField = "true_st")
private String personnelInformationCategory;
	/**
	 * 性别
	 */
	@Excel(name = "性别", height = 20, width = 30, isImportField = "true_st")
private String sex;
	/**
	 * 出生日期
	 */
	@Excel(name = "出生日期", height = 20, width = 30, isImportField = "true_st")
private Date birthday;
	/**
	 * 年龄
	 */
	@Excel(name = "年龄", height = 20, width = 30, isImportField = "true_st")
private String age;
	/**
	 *  申请类别
	 */
	@Excel(name = " 申请类别", height = 20, width = 30, isImportField = "true_st")
private String applicationCategory;
	/**
	 * 救助证编号 
	 */
	@Excel(name = "救助证编号 ", height = 20, width = 30, isImportField = "true_st")
private String rescueCertificateNumber;
	/**
	 * 开户银行
	 */
	@Excel(name = "开户银行", height = 20, width = 30, isImportField = "true_st")
private String bankName;
	/**
	 * 人员银行账号 
	 */
	@Excel(name = "人员银行账号 ", height = 20, width = 30, isImportField = "true_st")
private String personBankAccount;
	/**
	 * 文化程度
	 */
	@Excel(name = "文化程度", height = 20, width = 30, isImportField = "true_st")
private String educationalLevel;
	/**
	 *  政治面貌 
	 */
	@Excel(name = " 政治面貌 ", height = 20, width = 30, isImportField = "true_st")
private String politicalStatus;
	/**
	 *  婚姻状况
	 */
	@Excel(name = " 婚姻状况", height = 20, width = 30, isImportField = "true_st")
private String maritalStatus;
	/**
	 * 民族 
	 */
	@Excel(name = "民族 ", height = 20, width = 30, isImportField = "true_st")
private String nationality;
	/**
	 * 月人均收入
	 */
	@Excel(name = "月人均收入", height = 20, width = 30, isImportField = "true_st")
private String monthlyPerCapitaIncome;
	/**
	 * 保障总人口 
	 */
	@Excel(name = "保障总人口 ", height = 20, width = 30, isImportField = "true_st")
private String guaranteeTheTotalPopulation;
	/**
	 * 户保障金
	 */
	@Excel(name = "户保障金", height = 20, width = 30, isImportField = "true_st")
private String householdSecurity;
	/**
	 *  其他补助金
	 */
	@Excel(name = " 其他补助金", height = 20, width = 30, isImportField = "true_st")
private String otherSubsidies;
	/**
	 * 保障总金额
	 */
	@Excel(name = "保障总金额", height = 20, width = 30, isImportField = "true_st")
private String totalAmountOfProtection;
	/**
	 *  户口性质 
	 */
	@Excel(name = " 户口性质 ", height = 20, width = 30, isImportField = "true_st")
private String accountNature;
	/**
	 * 人员类别
	 */
	@Excel(name = "人员类别", height = 20, width = 30, isImportField = "true_st")
private String personnelCategory;
	/**
	 * 健康状况
	 */
	@Excel(name = "健康状况", height = 20, width = 30, isImportField = "true_st")
private String healthStatus;
	/**
	 *  职业状况
	 */
	@Excel(name = " 职业状况", height = 20, width = 30, isImportField = "true_st")
private String careerStatus;
	/**
	 *  残疾类别
	 */
	@Excel(name = " 残疾类别", height = 20, width = 30, isImportField = "true_st")
private String disabilityCategory;
	/**
	 *  残疾等级
	 */
	@Excel(name = " 残疾等级", height = 20, width = 30, isImportField = "true_st")
private String disabilityLevel;
	/**
	 * 残疾证号
	 */
	@Excel(name = "残疾证号", height = 20, width = 30, isImportField = "true_st")
private String disableId;
	/**
	 *  救助日期
	 */
	@Excel(name = " 救助日期", height = 20, width = 30, isImportField = "true_st")
private Date dateOfSalvage;
	/**
	 * 低保类别
	 */
	@Excel(name = "低保类别", height = 20, width = 30, isImportField = "true_st")
private String subsidiaryCategory;
	/**
	 * 联系电话 
	 */
	@Excel(name = "联系电话 ", height = 20, width = 30, isImportField = "true_st")
private String telephone;
	/**
	 * 手机号
	 */
	@Excel(name = "手机号", height = 20, width = 30, isImportField = "true_st")
private String mobilePhone;
	/**
	 * 银行账户
	 */
	@Excel(name = "银行账户", height = 20, width = 30, isImportField = "true_st")
private String bankAccount;
	/**
	 * 人均保障金
	 */
	@Excel(name = "人均保障金", height = 20, width = 30, isImportField = "true_st")
private String perCapitaSecurity;
	/**
	 * 
	 */
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Long createId;
	/**
	 * 创建时间
	 */
	@Excel(name = "创建时间", height = 20, width = 30, isImportField = "true_st")
private Date createTime;

}
