package com.hmit.kernespring.modules.data_management.entity;

public class ApiZhiAnEntity {

    /**
     * MZ : 汉族
     * RYZT : 正常
     * CSRQ : 19880913
     * QYSJ : 20080320134608
     * XM : 史光建
     * QWGJ : null
     * XB : 男
     * QCYY : null
     * SFZH : 330225198809131971
     * HJPCS : 象山县公安局丹西派出所
     * HJDXZ : 浙江省象山县丹西街道九顷村冷水潭１１０号
     * QCRQ : null
     * QWXXDZ : null
     * QWSX : null
     */

    private String MZ;
    private String RYZT;
    private String CSRQ;
    private String QYSJ;
    private String XM;
    private Object QWGJ;
    private String XB;
    private Object QCYY;
    private String SFZH;
    private String HJPCS;
    private String HJDXZ;
    private Object QCRQ;
    private Object QWXXDZ;
    private Object QWSX;

    public String getMZ() {
        return MZ;
    }

    public void setMZ(String MZ) {
        this.MZ = MZ;
    }

    public String getRYZT() {
        return RYZT;
    }

    public void setRYZT(String RYZT) {
        this.RYZT = RYZT;
    }

    public String getCSRQ() {
        return CSRQ;
    }

    public void setCSRQ(String CSRQ) {
        this.CSRQ = CSRQ;
    }

    public String getQYSJ() {
        return QYSJ;
    }

    public void setQYSJ(String QYSJ) {
        this.QYSJ = QYSJ;
    }

    public String getXM() {
        return XM;
    }

    public void setXM(String XM) {
        this.XM = XM;
    }

    public Object getQWGJ() {
        return QWGJ;
    }

    public void setQWGJ(Object QWGJ) {
        this.QWGJ = QWGJ;
    }

    public String getXB() {
        return XB;
    }

    public void setXB(String XB) {
        this.XB = XB;
    }

    public Object getQCYY() {
        return QCYY;
    }

    public void setQCYY(Object QCYY) {
        this.QCYY = QCYY;
    }

    public String getSFZH() {
        return SFZH;
    }

    public void setSFZH(String SFZH) {
        this.SFZH = SFZH;
    }

    public String getHJPCS() {
        return HJPCS;
    }

    public void setHJPCS(String HJPCS) {
        this.HJPCS = HJPCS;
    }

    public String getHJDXZ() {
        return HJDXZ;
    }

    public void setHJDXZ(String HJDXZ) {
        this.HJDXZ = HJDXZ;
    }

    public Object getQCRQ() {
        return QCRQ;
    }

    public void setQCRQ(Object QCRQ) {
        this.QCRQ = QCRQ;
    }

    public Object getQWXXDZ() {
        return QWXXDZ;
    }

    public void setQWXXDZ(Object QWXXDZ) {
        this.QWXXDZ = QWXXDZ;
    }

    public Object getQWSX() {
        return QWSX;
    }

    public void setQWSX(Object QWSX) {
        this.QWSX = QWSX;
    }
}
