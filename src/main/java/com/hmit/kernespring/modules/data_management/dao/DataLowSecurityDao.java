package com.hmit.kernespring.modules.data_management.dao;

import com.hmit.kernespring.modules.data_management.entity.DataLowSecurityEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 家庭经济情况-低保信息
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-19 09:01:24
 */
@Mapper
public interface DataLowSecurityDao extends BaseMapper<DataLowSecurityEntity> {
    List<DataLowSecurityEntity> queryExportData(Map<String, Object> params);
    void deleteAllData(Map<String, Object> params);
	
}
