package com.hmit.kernespring.modules.data_management.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;
import com.hmit.kernespring.modules.data_management.dao.DisabilityCertificateSyncDataDao;
import com.hmit.kernespring.modules.data_management.entity.DisabilityCertificateSyncDataEntity;
import com.hmit.kernespring.modules.data_management.service.DisabilityCertificateSyncDataService;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service("disabilityCertificateSyncDataService")
public class DisabilityCertificateSyncDataServiceImpl extends ServiceImpl<DisabilityCertificateSyncDataDao, DisabilityCertificateSyncDataEntity> implements DisabilityCertificateSyncDataService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private DisabilityCertificateSyncDataDao disabilityCertificateSyncDataDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        DisabilityCertificateSyncDataEntity disabilityCertificateSyncDataEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, DisabilityCertificateSyncDataEntity.class);
        IPage<DisabilityCertificateSyncDataEntity> page = this.page(
                new Query<DisabilityCertificateSyncDataEntity>().getPage(params),
                new QueryWrapper<DisabilityCertificateSyncDataEntity>()
            .eq(StringUtils.isNotBlank(disabilityCertificateSyncDataEntity.getId ()!=null && !"".equals(disabilityCertificateSyncDataEntity.getId ().toString())? disabilityCertificateSyncDataEntity.getId ().toString():null),"id", disabilityCertificateSyncDataEntity.getId ())
            .eq(StringUtils.isNotBlank(disabilityCertificateSyncDataEntity.getName ()!=null && !"".equals(disabilityCertificateSyncDataEntity.getName ().toString())? disabilityCertificateSyncDataEntity.getName ().toString():null),"name", disabilityCertificateSyncDataEntity.getName ())
            .eq(StringUtils.isNotBlank(disabilityCertificateSyncDataEntity.getIdCard ()!=null && !"".equals(disabilityCertificateSyncDataEntity.getIdCard ().toString())? disabilityCertificateSyncDataEntity.getIdCard ().toString():null),"id_card", disabilityCertificateSyncDataEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(disabilityCertificateSyncDataEntity.getCreateTime ()!=null && !"".equals(disabilityCertificateSyncDataEntity.getCreateTime ().toString())? disabilityCertificateSyncDataEntity.getCreateTime ().toString():null),"create_time", disabilityCertificateSyncDataEntity.getCreateTime ())
            .eq(StringUtils.isNotBlank(disabilityCertificateSyncDataEntity.getFamilyEconoCondition ()!=null && !"".equals(disabilityCertificateSyncDataEntity.getFamilyEconoCondition ().toString())? disabilityCertificateSyncDataEntity.getFamilyEconoCondition ().toString():null),"family_econo_condition", disabilityCertificateSyncDataEntity.getFamilyEconoCondition ())
            .eq(StringUtils.isNotBlank(disabilityCertificateSyncDataEntity.getMedicalInsurance ()!=null && !"".equals(disabilityCertificateSyncDataEntity.getMedicalInsurance ().toString())? disabilityCertificateSyncDataEntity.getMedicalInsurance ().toString():null),"medical_insurance", disabilityCertificateSyncDataEntity.getMedicalInsurance ())
            .eq(StringUtils.isNotBlank(disabilityCertificateSyncDataEntity.getIsDead ()!=null && !"".equals(disabilityCertificateSyncDataEntity.getIsDead ().toString())? disabilityCertificateSyncDataEntity.getIsDead ().toString():null),"is_dead", disabilityCertificateSyncDataEntity.getIsDead ())
            .eq(StringUtils.isNotBlank(disabilityCertificateSyncDataEntity.getIsTk ()!=null && !"".equals(disabilityCertificateSyncDataEntity.getIsTk ().toString())? disabilityCertificateSyncDataEntity.getIsTk ().toString():null),"is_tk", disabilityCertificateSyncDataEntity.getIsTk ())
            .eq(StringUtils.isNotBlank(disabilityCertificateSyncDataEntity.getIsSixty ()!=null && !"".equals(disabilityCertificateSyncDataEntity.getIsSixty ().toString())? disabilityCertificateSyncDataEntity.getIsSixty ().toString():null),"is_sixty", disabilityCertificateSyncDataEntity.getIsSixty ())
            .eq(StringUtils.isNotBlank(disabilityCertificateSyncDataEntity.getIsWorkinjury ()!=null && !"".equals(disabilityCertificateSyncDataEntity.getIsWorkinjury ().toString())? disabilityCertificateSyncDataEntity.getIsWorkinjury ().toString():null),"is_workinjury", disabilityCertificateSyncDataEntity.getIsWorkinjury ())
            .eq(StringUtils.isNotBlank(disabilityCertificateSyncDataEntity.getIsDischild ()!=null && !"".equals(disabilityCertificateSyncDataEntity.getIsDischild ().toString())? disabilityCertificateSyncDataEntity.getIsDischild ().toString():null),"is_dischild", disabilityCertificateSyncDataEntity.getIsDischild ())
            .eq(StringUtils.isNotBlank(disabilityCertificateSyncDataEntity.getIsResident ()!=null && !"".equals(disabilityCertificateSyncDataEntity.getIsResident ().toString())? disabilityCertificateSyncDataEntity.getIsResident ().toString():null),"is_resident", disabilityCertificateSyncDataEntity.getIsResident ())
            .eq(StringUtils.isNotBlank(disabilityCertificateSyncDataEntity.getIsClork ()!=null && !"".equals(disabilityCertificateSyncDataEntity.getIsClork ().toString())? disabilityCertificateSyncDataEntity.getIsClork ().toString():null),"is_clork", disabilityCertificateSyncDataEntity.getIsClork ())
            .eq(StringUtils.isNotBlank(disabilityCertificateSyncDataEntity.getIsApplyWelfareMatter ()!=null && !"".equals(disabilityCertificateSyncDataEntity.getIsApplyWelfareMatter ().toString())? disabilityCertificateSyncDataEntity.getIsApplyWelfareMatter ().toString():null),"is_apply_welfare_matter", disabilityCertificateSyncDataEntity.getIsApplyWelfareMatter ())
            .eq(StringUtils.isNotBlank(disabilityCertificateSyncDataEntity.getStatus ()!=null && !"".equals(disabilityCertificateSyncDataEntity.getStatus ().toString())? disabilityCertificateSyncDataEntity.getStatus ().toString():null),"status", disabilityCertificateSyncDataEntity.getStatus ())
            .eq(StringUtils.isNotBlank(disabilityCertificateSyncDataEntity.getIsFx ()!=null && !"".equals(disabilityCertificateSyncDataEntity.getIsFx ().toString())? disabilityCertificateSyncDataEntity.getIsFx ().toString():null),"is_fx", disabilityCertificateSyncDataEntity.getIsFx ())
            .eq(StringUtils.isNotBlank(disabilityCertificateSyncDataEntity.getIsHkqy ()!=null && !"".equals(disabilityCertificateSyncDataEntity.getIsHkqy ().toString())? disabilityCertificateSyncDataEntity.getIsHkqy ().toString():null),"is_hkqy", disabilityCertificateSyncDataEntity.getIsHkqy ())
        );
        Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key","sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        page.getRecords().forEach( item -> {

        });
        return new PageUtils(page);
    }

    @Override
    public PageUtils queryNursingYcDataByMap(Map<String, Object> params) {
        System.out.println("params----->"+params);
        Integer offect = Integer.parseInt(params.get("page").toString());
        params.put("page",offect-1);
        params.put("limit",Integer.parseInt(params.get("limit").toString()));
        int count = disabilityCertificateSyncDataDao.queryNursingYcDataTotalByMap(params);
        List<DisabilityCertificateSyncDataEntity> list = disabilityCertificateSyncDataDao.queryNursingYcDataByMap(params);
        IPage<DisabilityCertificateSyncDataEntity> page = new IPage<DisabilityCertificateSyncDataEntity>() {
            @Override
            public List<DisabilityCertificateSyncDataEntity> getRecords() {
                return null;
            }

            @Override
            public IPage<DisabilityCertificateSyncDataEntity> setRecords(List<DisabilityCertificateSyncDataEntity> records) {
                return null;
            }

            @Override
            public long getTotal() {
                return 0;
            }

            @Override
            public IPage<DisabilityCertificateSyncDataEntity> setTotal(long total) {
                return null;
            }

            @Override
            public long getSize() {
                return 0;
            }

            @Override
            public IPage<DisabilityCertificateSyncDataEntity> setSize(long size) {
                return null;
            }

            @Override
            public long getCurrent() {
                return 0;
            }

            @Override
            public IPage<DisabilityCertificateSyncDataEntity> setCurrent(long current) {
                return null;
            }
        };
        page.setTotal(count);
        page.setRecords(list);
        return new PageUtils(page);
    }

    @Override
    public PageUtils queryLivingYcDataByMap(Map<String, Object> params) {
        System.out.println("params----->"+params);
        Integer offect = Integer.parseInt(params.get("page").toString());
        params.put("page",offect-1);
        params.put("limit",Integer.parseInt(params.get("limit").toString()));
        int count = disabilityCertificateSyncDataDao.queryNursingYcDataTotalByMap(params);
        List<DisabilityCertificateSyncDataEntity> list = disabilityCertificateSyncDataDao.queryLivingYcDataByMap(params);
        IPage<DisabilityCertificateSyncDataEntity> page = new IPage<DisabilityCertificateSyncDataEntity>() {
            @Override
            public List<DisabilityCertificateSyncDataEntity> getRecords() {
                return null;
            }

            @Override
            public IPage<DisabilityCertificateSyncDataEntity> setRecords(List<DisabilityCertificateSyncDataEntity> records) {
                return null;
            }

            @Override
            public long getTotal() {
                return 0;
            }

            @Override
            public IPage<DisabilityCertificateSyncDataEntity> setTotal(long total) {
                return null;
            }

            @Override
            public long getSize() {
                return 0;
            }

            @Override
            public IPage<DisabilityCertificateSyncDataEntity> setSize(long size) {
                return null;
            }

            @Override
            public long getCurrent() {
                return 0;
            }

            @Override
            public IPage<DisabilityCertificateSyncDataEntity> setCurrent(long current) {
                return null;
            }
        };
        page.setTotal(count);
        page.setRecords(list);
        return new PageUtils(page);
    }

    @Override
    public List<DisabilityCertificateSyncDataEntity> queryExportData(Map<String, Object> params) {
            return disabilityCertificateSyncDataDao.queryExportData(params);
    }

    @Override
    public DisabilityCertificateSyncDataEntity queryStaticsData(Map<String, Object> params) {
        return disabilityCertificateSyncDataDao.queryStaticsData(params);
    }

    @Override
    public int queryStaticsLivingData(Map<String, Object> params) {
        return disabilityCertificateSyncDataDao.queryStaticsLivingData(params);
    }

    @Override
    public int queryStaticsNursingData(Map<String, Object> params) {
        return disabilityCertificateSyncDataDao.queryStaticsNursingData(params);
    }

}