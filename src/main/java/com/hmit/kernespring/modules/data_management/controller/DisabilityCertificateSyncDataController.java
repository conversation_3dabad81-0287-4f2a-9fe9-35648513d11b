package com.hmit.kernespring.modules.data_management.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.cjrone.service.CjroneLivingAllowanceService;
import com.hmit.kernespring.modules.cjrone.service.CjroneNursingSubsidyService;
import com.hmit.kernespring.modules.data_management.entity.DisabilityCertificateSyncDataEntity;
import com.hmit.kernespring.modules.data_management.service.DisabilityCertificateSyncDataService;
import com.hmit.kernespring.modules.matter_application.entity.CjroneWelfareMatterApplicationEntity;
import com.hmit.kernespring.modules.matter_application.service.CjroneWelfareMatterApplicationService;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;

/**
 * 残疾人对比数据
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-11 13:41:25
 */
@RestController
@RequestMapping("data_management/disabilitycertificatesyncdata")
public class DisabilityCertificateSyncDataController {
    @Autowired
    private DisabilityCertificateSyncDataService disabilityCertificateSyncDataService;
    @Autowired
    private CjroneWelfareMatterApplicationService cjroneWelfareMatterApplicationService;
    @Autowired
    private CjroneLivingAllowanceService cjroneLivingAllowanceService;
    @Autowired
    private CjroneNursingSubsidyService cjroneNursingSubsidyService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = disabilityCertificateSyncDataService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 护理补贴异常数据列表
     */
    @RequestMapping("/nursingList")
    public R nursingList(@RequestParam Map<String, Object> params){
        PageUtils page = disabilityCertificateSyncDataService.queryNursingYcDataByMap(params);

        return R.ok().put("page", page);
    }


    /**
     * 生活补贴异常数据列表
     */
    @RequestMapping("/livingList")
    public R livingList(@RequestParam Map<String, Object> params){
        PageUtils page = disabilityCertificateSyncDataService.queryLivingYcDataByMap(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    public R info(@PathVariable("id") Integer id){
		DisabilityCertificateSyncDataEntity disabilityCertificateSyncData = disabilityCertificateSyncDataService.getById(id);

        return R.ok().put("disabilityCertificateSyncData", disabilityCertificateSyncData);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    public R save(@RequestBody DisabilityCertificateSyncDataEntity disabilityCertificateSyncData){
		disabilityCertificateSyncDataService.save(disabilityCertificateSyncData);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    public R update(@RequestBody DisabilityCertificateSyncDataEntity disabilityCertificateSyncData){
		disabilityCertificateSyncDataService.updateById(disabilityCertificateSyncData);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    public R delete(@RequestBody Integer[] ids){
		disabilityCertificateSyncDataService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
     * 禁用
     */
    @RequestMapping("/enable")
    public R enable(@RequestBody Integer[] ids){
        for (Integer id : ids) {
            List<DisabilityCertificateSyncDataEntity> list = new ArrayList<>();

            DisabilityCertificateSyncDataEntity certificateSyncDataEntity = disabilityCertificateSyncDataService.getById(id);
            System.out.println(new Gson().toJson(certificateSyncDataEntity));

            Map<String, Object> params = new HashMap<>();
            params.put("id_card",certificateSyncDataEntity.getIdCard());
            List<CjroneWelfareMatterApplicationEntity> welfareMatterApplicationEntities = (List<CjroneWelfareMatterApplicationEntity>) cjroneWelfareMatterApplicationService.listByMap(params);
            List<CjroneWelfareMatterApplicationEntity> updateWelfareMatters = new ArrayList<>();
            welfareMatterApplicationEntities.forEach(item -> {
                item.setStatus("0");
                if ("生活补贴".equals(item.getMatterName())){
                    updateWelfareMatters.add(item);
                }else if ("护理补贴".equals(item.getMatterName())){
                    updateWelfareMatters.add(item);
                }
            });
            if (updateWelfareMatters.size()>0){
                cjroneWelfareMatterApplicationService.updateBatchById(updateWelfareMatters);
            }
        }

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<DisabilityCertificateSyncDataEntity> disabilityCertificateSyncDataList = new ArrayList<>();
        list.forEach(item ->{
                    item.put("id",item.get(""));
                    item.put("name",item.get("残疾人姓名"));
                    item.put("idCard",item.get("身份证号码"));
                    item.put("createTime",item.get("创建时间"));
                    item.put("familyEconoCondition",item.get("家庭经济情况"));
                    item.put("medicalInsurance",item.get("医疗保险情况"));
                    item.put("isDead",item.get("是否死亡"));
                    item.put("isTk",item.get("是否特困"));
                    item.put("isSixty",item.get("是否60岁"));
                    item.put("isWorkinjury",item.get("是否工伤保险"));
                    item.put("isDischild",item.get("是否困境儿童"));
                    item.put("isResident",item.get("是否居民养老保险"));
                    item.put("isClork",item.get("是否职工参保"));
                    disabilityCertificateSyncDataList.add(new Gson().fromJson(new Gson().toJson(item), DisabilityCertificateSyncDataEntity.class));
        });
        // 保存到数据库
        disabilityCertificateSyncDataService.saveBatch(disabilityCertificateSyncDataList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<DisabilityCertificateSyncDataEntity> disabilityCertificateSyncDataEntityList = disabilityCertificateSyncDataService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("残疾人对比数据", null, "残疾人对比数据");
        Workbook workbook = ExcelExportUtil.exportExcel(params, DisabilityCertificateSyncDataEntity.class, disabilityCertificateSyncDataEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "残疾人对比数据" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }



}
