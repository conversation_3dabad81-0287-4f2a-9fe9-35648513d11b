package com.hmit.kernespring.modules.data_management.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.data_management.entity.DataEmployeeSocialSecurityInsuredEntity;

import java.util.Map;

import java.util.List;

/**
 * 职工社保参保人员导入
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-27 14:00:04
 */
public interface DataEmployeeSocialSecurityInsuredService extends IService<DataEmployeeSocialSecurityInsuredEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<DataEmployeeSocialSecurityInsuredEntity> queryExportData(Map<String, Object> params);
}

