package com.hmit.kernespring.modules.data_management.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 残疾人对比数据
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-11 13:41:25
 */
@Data
@TableName("disability_certificate_sync_data")
public class DisabilityCertificateSyncDataEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 *
	 */

@TableId
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 残疾人姓名
	 */
	@Excel(name = "残疾人姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 身份证号码
	 */
	@Excel(name = "身份证号码", height = 20, width = 30, isImportField = "true_st")
private String idCard;
	/**
	 * 家庭经济情况
	 */
	@Excel(name = "家庭经济情况", height = 20, width = 30, isImportField = "true_st")
private String familyEconoCondition;
	/**
	 * 医疗保险情况
	 */
	@Excel(name = "医疗保险情况", height = 20, width = 30, isImportField = "true_st")
private String medicalInsurance;
	/**
	 * 是否死亡
	 */
	@Excel(name = "是否死亡", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
private String isDeadName;
private Integer isDead;
	/**
	 * 是否特困
	 */
	@Excel(name = "是否特困", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String isTkName;
private Integer isTk;
	/**
	 * 是否60岁
	 */
	@Excel(name = "是否60岁", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String isSixtyName;
private Integer isSixty;
	/**
	 * 是否工伤保险
	 */
	@Excel(name = "是否工伤保险", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String isWorkinjuryName;
private Integer isWorkinjury;
	/**
	 * 是否困境儿童
	 */
	@Excel(name = "是否困境儿童", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String isDischildName;
private Integer isDischild;
	/**
	 * 是否居民养老保险
	 */
	@Excel(name = "是否居民养老保险", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String isResidentName;
private Integer isResident;
	/**
	 * 是否职工参保
	 */
	@Excel(name = "是否职工参保", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String isClorkName;
private Integer isClork;
	/**
	 * 低保
	 */
	@TableField(exist = false)
	private Integer dataLowSecurity;
	/**
	 * 低保边缘
	 */
	@TableField(exist = false)
	private Integer dataLowSecurityMargin;
	/**
	 * 状态 0 异常 1正常
	 */
	@Excel(name = "状态", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String statusName;
	private Integer status;
	/**
	 * 创建时间
	 */
	@Excel(name = "异常时间", height = 20, width = 30, isImportField = "true_st")
	private Date createTime;

	/**
	 * 是否申请过福利事项
	 */
	@Excel(name = "是否申请过福利事项", height = 20, width = 30, isImportField = "true_st")
	private String isApplyWelfareMatter;

/**
	 * 是否服刑
	 */
	@Excel(name = "是否服刑", height = 20, width = 30, isImportField = "true_st")
	private Integer isFx;

/**
	 * 是否户口迁移
	 */
	@Excel(name = "是否户口迁移", height = 20, width = 30, isImportField = "true_st")
	private Integer isHkqy;

/**
	 * 服刑信息
	 */
	@Excel(name = "服刑信息", height = 20, width = 30, isImportField = "true_st")
	private String fxInfo;

/**
	 * 户口迁移信息
	 */
	@Excel(name = "户口迁移信息", height = 20, width = 30, isImportField = "true_st")
	private String hkqyInfo;

	// 是否享受生活补贴
	private Integer isShbt;

	// 是否职工养老保险
	private Integer isZgyl;

}
