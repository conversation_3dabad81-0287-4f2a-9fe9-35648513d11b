package com.hmit.kernespring.modules.data_management.dao;

import com.hmit.kernespring.modules.data_management.entity.DataPensionSubsidyEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 60周岁养老补贴导入表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-27 13:58:44
 */
@Mapper
public interface DataPensionSubsidyDao extends BaseMapper<DataPensionSubsidyEntity> {
    List<DataPensionSubsidyEntity> queryExportData(Map<String, Object> params);
    void deleteAllData(Map<String, Object> params);
}
