package com.hmit.kernespring.modules.data_management.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.data_management.entity.DisabilityCertificateSyncDataEntity;

import java.util.List;
import java.util.Map;

/**
 * 残疾人对比数据
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-11 13:41:25
 */
public interface DisabilityCertificateSyncDataService extends IService<DisabilityCertificateSyncDataEntity> {

    PageUtils queryPage(Map<String, Object> params);
    PageUtils queryNursingYcDataByMap(Map<String, Object> params);
    PageUtils queryLivingYcDataByMap(Map<String, Object> params);
    List<DisabilityCertificateSyncDataEntity> queryExportData(Map<String, Object> params);
    DisabilityCertificateSyncDataEntity queryStaticsData(Map<String, Object> params);
    int queryStaticsLivingData(Map<String, Object> params);
    int queryStaticsNursingData(Map<String, Object> params);
}

