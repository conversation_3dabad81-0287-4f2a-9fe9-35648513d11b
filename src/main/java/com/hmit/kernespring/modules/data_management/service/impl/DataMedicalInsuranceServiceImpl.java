package com.hmit.kernespring.modules.data_management.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.data_management.dao.DataMedicalInsuranceDao;
import com.hmit.kernespring.modules.data_management.entity.DataMedicalInsuranceEntity;
import com.hmit.kernespring.modules.data_management.service.DataMedicalInsuranceService;


@Service("dataMedicalInsuranceService")
public class DataMedicalInsuranceServiceImpl extends ServiceImpl<DataMedicalInsuranceDao, DataMedicalInsuranceEntity> implements DataMedicalInsuranceService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private DataMedicalInsuranceDao dataMedicalInsuranceDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        DataMedicalInsuranceEntity dataMedicalInsuranceEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, DataMedicalInsuranceEntity.class);
        IPage<DataMedicalInsuranceEntity> page = this.page(
                new Query<DataMedicalInsuranceEntity>().getPage(params),
                new QueryWrapper<DataMedicalInsuranceEntity>()
            .eq(StringUtils.isNotBlank(dataMedicalInsuranceEntity.getId ()!=null && !"".equals(dataMedicalInsuranceEntity.getId ().toString())? dataMedicalInsuranceEntity.getId ().toString():null),"id", dataMedicalInsuranceEntity.getId ())
            .eq(StringUtils.isNotBlank(dataMedicalInsuranceEntity.getName ()!=null && !"".equals(dataMedicalInsuranceEntity.getName ().toString())? dataMedicalInsuranceEntity.getName ().toString():null),"name", dataMedicalInsuranceEntity.getName ())
            .eq(StringUtils.isNotBlank(dataMedicalInsuranceEntity.getIdCard ()!=null && !"".equals(dataMedicalInsuranceEntity.getIdCard ().toString())? dataMedicalInsuranceEntity.getIdCard ().toString():null),"id_card", dataMedicalInsuranceEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(dataMedicalInsuranceEntity.getInsuredSituation ()!=null && !"".equals(dataMedicalInsuranceEntity.getInsuredSituation ().toString())? dataMedicalInsuranceEntity.getInsuredSituation ().toString():null),"insured_situation", dataMedicalInsuranceEntity.getInsuredSituation ())
            .eq(StringUtils.isNotBlank(dataMedicalInsuranceEntity.getInsuredTime ()!=null && !"".equals(dataMedicalInsuranceEntity.getInsuredTime ().toString())? dataMedicalInsuranceEntity.getInsuredTime ().toString():null),"insured_time", dataMedicalInsuranceEntity.getInsuredTime ())
        );
        Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key","sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        page.getRecords().forEach( item -> {
        });
        return new PageUtils(page);
    }
    @Override
    public List<DataMedicalInsuranceEntity> queryExportData(Map<String, Object> params) {
            return dataMedicalInsuranceDao.queryExportData(params);
    }

}