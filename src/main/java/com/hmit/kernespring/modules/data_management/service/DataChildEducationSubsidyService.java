package com.hmit.kernespring.modules.data_management.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.data_management.entity.DataChildEducationSubsidyEntity;

import java.util.Map;

import java.util.List;

/**
 * 残疾人子女教育补贴导入表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-27 13:58:44
 */
public interface DataChildEducationSubsidyService extends IService<DataChildEducationSubsidyEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<DataChildEducationSubsidyEntity> queryExportData(Map<String, Object> params);
}

