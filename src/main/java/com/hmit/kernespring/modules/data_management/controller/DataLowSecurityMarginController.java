package com.hmit.kernespring.modules.data_management.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.data_management.entity.DataLowSecurityMarginEntity;
import com.hmit.kernespring.modules.data_management.service.DataLowSecurityMarginService;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import com.hmit.kernespring.modules.sys.controller.AbstractController;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;

/**
 * 家庭经济情况-低保边缘信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-19 10:49:28
 */
@RestController
@RequestMapping("data_management/datalowsecuritymargin")
public class DataLowSecurityMarginController extends AbstractController {
    @Autowired
    private DataLowSecurityMarginService dataLowSecurityMarginService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("data_management:datalowsecuritymargin:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = dataLowSecurityMarginService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("data_management:datalowsecuritymargin:info")
    public R info(@PathVariable("id") Integer id){
		DataLowSecurityMarginEntity dataLowSecurityMargin = dataLowSecurityMarginService.getById(id);

        return R.ok().put("dataLowSecurityMargin", dataLowSecurityMargin);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("data_management:datalowsecuritymargin:save")
    public R save(@RequestBody DataLowSecurityMarginEntity dataLowSecurityMargin){
        dataLowSecurityMargin.setCreateTime(new Date());
        dataLowSecurityMargin.setCreateId(getUserId());
		dataLowSecurityMarginService.save(dataLowSecurityMargin);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("data_management:datalowsecuritymargin:update")
    public R update(@RequestBody DataLowSecurityMarginEntity dataLowSecurityMargin){
		dataLowSecurityMarginService.updateById(dataLowSecurityMargin);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("data_management:datalowsecuritymargin:delete")
    public R delete(@RequestBody Integer[] ids){
		dataLowSecurityMarginService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    // @RequiresPermissions("data_management:datalowsecuritymargin:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        params_import.setHeadRows(1);
        params_import.setTitleRows(0);
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<DataLowSecurityMarginEntity> dataLowSecurityMarginList = new ArrayList<>();
        System.out.println("当前导入数据家庭经济情况-低保边缘信息条数：" + list.size());
        list.forEach(item ->{
            if (null != item.get("身份证") && !"".equals(item.get("身份证").toString()) || (null != item.get("身份证号") && !"".equals(item.get("身份证号").toString()))) {
                item.put("id",item.get(""));
                item.put("administrativeDivision",item.get("行政划分"));
                item.put("applicant",item.get("申请人"));
                item.put("applyIdCard",item.get("申请身份证"));
                item.put("applicantAccountNature",item.get("申请人户口性质 "));
                item.put("telephone",item.get("联系电话 "));
                item.put("mobilePhone",item.get("手机号"));
                item.put("causeOfPoverty",item.get("致贫原因 "));
                item.put("residenceAddress",item.get("户籍地址"));
                item.put("lowSideCategory",item.get(" 低边类别 "));
                item.put("residentialAddress",item.get(" 居住地址"));
                item.put("reasonForApplying",item.get(" 申请救助原因 "));
                item.put("applicationCategory",item.get(" 申请类别"));
                item.put("bankName",item.get("开户银行"));
                item.put("accountHolder",item.get("开户人 "));
                item.put("accountHolderIdCard",item.get("开户人身份证 "));
                item.put("bankAccount",item.get("银行账户"));
                item.put("cumulativeMonth",item.get("累计月份"));
                item.put("cumulativeIncome",item.get("累计收入"));
                item.put("cumulativeExpenditure",item.get("累计支出"));
                item.put("monthlyPerCapitaIncome",item.get("月人均收入 "));
                item.put("inclusionOfRescueStandards",item.get("纳入救助标准 "));
                item.put("totalFamilyPopulation",item.get("家庭总人口 "));
                item.put("guaranteeTotalPopulation",item.get(" 保障总人口"));
                item.put("generalNumberOfPeople",item.get("一般残人数 "));
                item.put("livingAllowance",item.get("生活补助金 "));
                item.put("otherSubsidies",item.get("其他补助金 "));
                item.put("totalAmountOfProtection",item.get("保障总金额"));
                item.put("dateOfSalvage",item.get(" 救助日期"));
                item.put("relationshipWithHousehold",item.get("与户主关系"));
                item.put("name",item.get("人员姓名"));
                item.put("idCard",item.get("身份证号"));
                item.put("sex",item.get("性别"));
                item.put("birthday",item.get("出生日期"));
                item.put("age",item.get("年龄"));
                item.put("rescueCertificateNumber",item.get("救助证编号 "));
                item.put("whetherToEnjoy",item.get("是否享受"));
                item.put("memberAccountNature",item.get("成员户口性质"));
                item.put("personnelCategory",item.get("人员类别 "));
                item.put("healthStatus",item.get("健康状况"));
                item.put("maritalStatus",item.get(" 婚姻状况"));
                item.put("nationality",item.get("民族 "));
                item.put("careerStatus",item.get(" 职业状况"));
                item.put("politicalStatus",item.get(" 政治面貌 "));
                item.put("specificGuaranteeObject",item.get(" 特定是否保障对象 "));
                item.put("disabilityCategory",item.get("残疾类别"));
                item.put("disabilityLevel",item.get(" 残疾等级 "));
                item.put("disableId",item.get("残疾证号"));
                item.put("personBankAccount",item.get("人员银行账号 "));
                item.put("bankNameTwo",item.get("开户银行"));
                item.put("monthlyGuaranteeAmount",item.get("月保障金额 "));
                item.put("educationalLevel",item.get("文化程度"));
                item.put("laborAbility",item.get("劳动能力"));
                item.put("workUnit",item.get("工作学习单位 "));
                item.put("severeDisabilitiesNum",item.get("重残保障人数"));
                item.put("createId",item.get(""));
                item.put("createTime",item.get("创建时间"));
                dataLowSecurityMarginList.add(new Gson().fromJson(new Gson().toJson(item), DataLowSecurityMarginEntity.class));
            }
        });
        // 保存到数据库
        // dataLowSecurityMarginService.saveBatch(dataLowSecurityMarginList);
        ((DataLowSecurityMarginService) AopContext.currentProxy()).saveBatch(dataLowSecurityMarginList);
        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    //@RequiresPermissions("data_management:datalowsecuritymargin:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<DataLowSecurityMarginEntity> dataLowSecurityMarginEntityList = dataLowSecurityMarginService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("家庭经济情况-低保边缘信息", null, "家庭经济情况-低保边缘信息");
        Workbook workbook = ExcelExportUtil.exportExcel(params, DataLowSecurityMarginEntity.class, dataLowSecurityMarginEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "家庭经济情况-低保边缘信息" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

}
