package com.hmit.kernespring.modules.data_management.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.chinamobile.openmas.client.Sms;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.cjrone.entity.CjroneMessageHistoryEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneMessageHistoryService;
import com.hmit.kernespring.modules.data_management.entity.DataSmsTemplateEntity;
import com.hmit.kernespring.modules.data_management.service.DataSmsTemplateService;
import com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity;
import com.hmit.kernespring.modules.matter_application.service.DisabilityCertificateApplicationService;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.rmi.RemoteException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-27 15:39:33
 */
@RestController
@RequestMapping("data_management/datasmstemplate")
public class DataSmsTemplateController {
    @Autowired
    private DataSmsTemplateService dataSmsTemplateService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;
    @Autowired
    private DisabilityCertificateApplicationService disabilityCertificateApplicationService;
    @Autowired
    private CjroneMessageHistoryService cjroneMessageHistoryService;


    /**
     * 列表
     */
    @RequestMapping("/list")
    // @RequiresPermissions("data_management:datasmstemplate:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = dataSmsTemplateService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("data_management:datasmstemplate:info")
    public R info(@PathVariable("id") Integer id){
		DataSmsTemplateEntity dataSmsTemplate = dataSmsTemplateService.getById(id);

        return R.ok().put("dataSmsTemplate", dataSmsTemplate);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("data_management:datasmstemplate:save")
    public R save(@RequestBody DataSmsTemplateEntity dataSmsTemplate){
		dataSmsTemplateService.save(dataSmsTemplate);

        return R.ok();
    }

    /**
     * 发送短信
     */
    @RequestMapping("/sendMessage")
    // @RequiresPermissions("data_management:datasmstemplate:sendMessage")
    public R sendMessage(@RequestBody DataSmsTemplateEntity dataSmsTemplate) throws RemoteException {
        String[] destinationAddresses = null ;
        String[] sendType=null;
        System.out.println(new Gson().toJson(dataSmsTemplate));
        // DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity=disabilityCertificateApplicationService.getByIDCard(dataSmsTemplate.getIdCard());
        // DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity=disabilityCertificateApplicationService.getByIDCardN(dataSmsTemplate.getIdCard());
        DisabilityCertificateApplicationEntity entity = disabilityCertificateApplicationService.getById(dataSmsTemplate.getId());
        if (dataSmsTemplate.getMobile() != null && !"".equals(dataSmsTemplate.getMobile())) {
            String mobilephone = dataSmsTemplate.getMobile();
            destinationAddresses = new String[]{mobilephone};
            sendType = new String[]{"指定号码"};
        } else {
        /*DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity=disabilityCertificateApplicationService.getByIDCard(dataSmsTemplate.getIdCard());
        //判断发送本人还是监护人
        if(dataSmsTemplate.getIsMyself()==1){
            mobilephone=disabilityCertificateApplicationEntity.getMobilePhone();
        }
        else{
            mobilephone=disabilityCertificateApplicationEntity.getGuardianPhone();
        }*/
            //本人和监护人都发送一遍
            //String mobilephonePerson = "18368404744";  //本人
            //String mobilephoneGuardian = "18067444337"; //监护人
            String mobilephonePerson=entity.getMobilePhone();  //本人
            String mobilephoneGuardian=entity.getGuardianPhone(); //监护人
            destinationAddresses = new String[]{mobilephonePerson, mobilephoneGuardian};
            sendType = new String[]{"本人", "监护人"};
        }

        // 短信
        Sms sms = new Sms("http://nb005.openmas.net:9080/OpenMasService");
        //String[] destinationAddresses = new String[]{mobilephone};   //"18067444337"
        String message = dataSmsTemplate.getSmsContent().toString();
        String extendCode = "4"; //自定义扩展代码（模块）
        String ApplicationID = "8023003";
        String Password = "rh4bRti2faPd";

        //发送短信
        String GateWayid = sms.SendMessage(destinationAddresses, message, extendCode, ApplicationID, Password);
        System.out.println(GateWayid);

        //得到当前的时间
        long l = System.currentTimeMillis();
        Date date = new Date(l);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM- dd HH:mm:ss");
        String sendTime = dateFormat.format(date);

        //保存历史记录
        for (int i = 0; i < destinationAddresses.length; i++) {
            CjroneMessageHistoryEntity cjroneMessageHistoryEntity = new CjroneMessageHistoryEntity();
            cjroneMessageHistoryEntity.setReceiveName(entity.getName());
            cjroneMessageHistoryEntity.setReceiveIdCard(entity.getIdCard());
            cjroneMessageHistoryEntity.setMessageContent(message);
            cjroneMessageHistoryEntity.setMobilePhone(destinationAddresses[i]);
            cjroneMessageHistoryEntity.setSendType(sendType[i]);
            cjroneMessageHistoryEntity.setSendTime(sendTime);
            cjroneMessageHistoryEntity.setApplyId(entity.getId());
            cjroneMessageHistoryService.save(cjroneMessageHistoryEntity);
        }
        return R.ok();
    }

    /**
     * 发送短信
     */
    @RequestMapping("/sendMessagePinCan")
    // @RequiresPermissions("data_management:datasmstemplate:sendMessage")
    public R sendMessagePinCan(@RequestBody DataSmsTemplateEntity dataSmsTemplate) throws RemoteException {
        String[] destinationAddresses = null ;
        String[] sendType=null;
        System.out.println(new Gson().toJson(dataSmsTemplate));
        // DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity=disabilityCertificateApplicationService.getByIDCard(dataSmsTemplate.getIdCard());
        // DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity=disabilityCertificateApplicationService.getByIDCardN(dataSmsTemplate.getIdCard());
        DisabilityCertificateApplicationEntity entity = disabilityCertificateApplicationService.getById(dataSmsTemplate.getId());
        if (dataSmsTemplate.getMobile() != null && !"".equals(dataSmsTemplate.getMobile())) {
            String mobilephone = dataSmsTemplate.getMobile();
            destinationAddresses = new String[]{mobilephone};
            sendType = new String[]{"指定号码"};
        } else {
        /*DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity=disabilityCertificateApplicationService.getByIDCard(dataSmsTemplate.getIdCard());
        //判断发送本人还是监护人
        if(dataSmsTemplate.getIsMyself()==1){
            mobilephone=disabilityCertificateApplicationEntity.getMobilePhone();
        }
        else{
            mobilephone=disabilityCertificateApplicationEntity.getGuardianPhone();
        }*/
            //本人和监护人都发送一遍
            //String mobilephonePerson = "18368404744";  //本人
            //String mobilephoneGuardian = "18067444337"; //监护人
            String mobilephonePerson=entity.getMobilePhone();  //本人
            String mobilephoneGuardian=entity.getGuardianPhone(); //监护人
            destinationAddresses = new String[]{mobilephonePerson, mobilephoneGuardian};
            sendType = new String[]{"本人", "监护人"};
        }

        // 短信
        Sms sms = new Sms("http://nb005.openmas.net:9080/OpenMasService");
        //String[] destinationAddresses = new String[]{mobilephone};   //"18067444337"
        String message = dataSmsTemplate.getSmsContent().toString();
        String extendCode = "4"; //自定义扩展代码（模块）
        String ApplicationID = "8023003";
        String Password = "rh4bRti2faPd";

        //发送短信
        String GateWayid = sms.SendMessage(destinationAddresses, message, extendCode, ApplicationID, Password);
        System.out.println(GateWayid);

        //得到当前的时间
        long l = System.currentTimeMillis();
        Date date = new Date(l);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM- dd HH:mm:ss");
        String sendTime = dateFormat.format(date);

        //保存历史记录
        for (int i = 0; i < destinationAddresses.length; i++) {
            CjroneMessageHistoryEntity cjroneMessageHistoryEntity = new CjroneMessageHistoryEntity();
            cjroneMessageHistoryEntity.setReceiveName(entity.getName());
            cjroneMessageHistoryEntity.setReceiveIdCard(entity.getIdCard());
            cjroneMessageHistoryEntity.setMessageContent(message);
            cjroneMessageHistoryEntity.setMobilePhone(destinationAddresses[i]);
            cjroneMessageHistoryEntity.setSendType(sendType[i]);
            cjroneMessageHistoryEntity.setSendTime(sendTime);
            cjroneMessageHistoryEntity.setApplyId(entity.getId());
            cjroneMessageHistoryService.save(cjroneMessageHistoryEntity);
        }
        return R.ok();
    }
    /**
     * 评残完毕 发送短信 并本地系统生成残疾证号
     */
    @RequestMapping("/sendMessageFaZheng")
    // @RequiresPermissions("data_management:datasmstemplate:sendMessage")
    public R sendMessageFaZheng(@RequestBody DataSmsTemplateEntity dataSmsTemplate) throws RemoteException {
        // 发送短信
        String[] destinationAddresses = null ;
        String[] sendType=null;
        System.out.println(new Gson().toJson(dataSmsTemplate));
        // DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity=disabilityCertificateApplicationService.getByIDCard(dataSmsTemplate.getIdCard());
        // DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity=disabilityCertificateApplicationService.getByIDCardN(dataSmsTemplate.getIdCard());
        DisabilityCertificateApplicationEntity entity = disabilityCertificateApplicationService.getById(dataSmsTemplate.getId());
        // Map<String, Object> aaa_tmp = new HashMap<>();
        // aaa_tmp.put("id_card", dataSmsTemplate.getIdCard());
        // List<DisabilityCertificateApplicationEntity> is_apply_list = (List<DisabilityCertificateApplicationEntity>) disabilityCertificateApplicationService.listByMap(aaa_tmp);
        // is_apply_list = is_apply_list.stream().sorted((h1,h2) -> h2.getCreateTime().compareTo(h1.getCreateTime())).collect(Collectors.toList());
        if (dataSmsTemplate.getMobile() != null && !"".equals(dataSmsTemplate.getMobile())) {
            String mobilephone = dataSmsTemplate.getMobile();
            destinationAddresses = new String[]{mobilephone};
            sendType = new String[]{"指定号码"};
        } else {
        /*DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity=disabilityCertificateApplicationService.getByIDCard(dataSmsTemplate.getIdCard());
        //判断发送本人还是监护人
        if(dataSmsTemplate.getIsMyself()==1){
            mobilephone=disabilityCertificateApplicationEntity.getMobilePhone();
        }
        else{
            mobilephone=disabilityCertificateApplicationEntity.getGuardianPhone();
        }*/
            //本人和监护人都发送一遍
            // String mobilephonePerson = "18368404744";  //本人
            // String mobilephoneGuardian = "18067444337"; //监护人
            String mobilephonePerson = entity.getMobilePhone();  //本人
            String mobilephoneGuardian = entity.getGuardianPhone(); //监护人
            destinationAddresses = new String[]{mobilephonePerson, mobilephoneGuardian};
            sendType = new String[]{"本人", "监护人"};
        }

        // 短信
        Sms sms = new Sms("http://nb005.openmas.net:9080/OpenMasService");
        //String[] destinationAddresses = new String[]{mobilephone};   //"18067444337"
        String message = dataSmsTemplate.getSmsContent().toString();
        String extendCode = "4"; //自定义扩展代码（模块）
        String ApplicationID = "8023003";
        String Password = "rh4bRti2faPd";


        //发送短信
        String GateWayid = sms.SendMessage(destinationAddresses, message, extendCode, ApplicationID, Password);
        System.out.println(GateWayid);

        //得到当前的时间
        long l = System.currentTimeMillis();
        Date date = new Date(l);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM- dd HH:mm:ss");
        String sendTime = dateFormat.format(date);

        //保存历史记录
        for (int i = 0; i < destinationAddresses.length; i++) {
            CjroneMessageHistoryEntity cjroneMessageHistoryEntity = new CjroneMessageHistoryEntity();
            cjroneMessageHistoryEntity.setReceiveName(entity.getName());
            cjroneMessageHistoryEntity.setReceiveIdCard(entity.getIdCard());
            cjroneMessageHistoryEntity.setMessageContent(message);
            cjroneMessageHistoryEntity.setMobilePhone(destinationAddresses[i]);
            cjroneMessageHistoryEntity.setSendType(sendType[i]);
            cjroneMessageHistoryEntity.setSendTime(sendTime);
            cjroneMessageHistoryEntity.setApplyId(entity.getId());
            cjroneMessageHistoryService.save(cjroneMessageHistoryEntity);
        }
        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("data_management:datasmstemplate:update")
    public R update(@RequestBody DataSmsTemplateEntity dataSmsTemplate){
		dataSmsTemplateService.updateById(dataSmsTemplate);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("data_management:datasmstemplate:delete")
    public R delete(@RequestBody Integer[] ids){
		dataSmsTemplateService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    // @RequiresPermissions("data_management:datasmstemplate:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<DataSmsTemplateEntity> dataSmsTemplateList = new ArrayList<>();
        list.forEach(item ->{
                    item.put("id",item.get(""));
                    item.put("type",item.get("模板类型 1 视力评残   2听力语言评残 3 肢体评残 4智力评残 5精神评残 6 领证模板"));
                    item.put("time",item.get("时间"));
                    item.put("address",item.get("地点"));
                    item.put("material",item.get("所携带材料"));
                    item.put("precautions",item.get("注意事项"));
                    item.put("status",item.get(""));
                    dataSmsTemplateList.add(new Gson().fromJson(new Gson().toJson(item), DataSmsTemplateEntity.class));
        });
        // 保存到数据库
        dataSmsTemplateService.saveBatch(dataSmsTemplateList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    // @RequiresPermissions("data_management:datasmstemplate:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<DataSmsTemplateEntity> dataSmsTemplateEntityList = dataSmsTemplateService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("", null, "");
        Workbook workbook = ExcelExportUtil.exportExcel(params, DataSmsTemplateEntity.class, dataSmsTemplateEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

}
