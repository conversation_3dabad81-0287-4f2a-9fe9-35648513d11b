package com.hmit.kernespring.modules.data_management.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.data_management.entity.DataMedicalInsuranceEntity;

import java.util.Map;

import java.util.List;

/**
 * 医保参保导入表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-27 14:00:03
 */
public interface DataMedicalInsuranceService extends IService<DataMedicalInsuranceEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<DataMedicalInsuranceEntity> queryExportData(Map<String, Object> params);
}

