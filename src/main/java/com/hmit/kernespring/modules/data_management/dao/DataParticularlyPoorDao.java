package com.hmit.kernespring.modules.data_management.dao;

import com.hmit.kernespring.modules.data_management.entity.DataParticularlyPoorEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 特困人员名单表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-19 09:01:24
 */
@Mapper
public interface DataParticularlyPoorDao extends BaseMapper<DataParticularlyPoorEntity> {
    List<DataParticularlyPoorEntity> queryExportData(Map<String, Object> params);
    void deleteAllData(Map<String, Object> params);
	
}
