package com.hmit.kernespring.modules.data_management.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.data_management.entity.DataResidentPensionInsuranceEntity;
import com.hmit.kernespring.modules.data_management.service.DataResidentPensionInsuranceService;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;

/**
 * 城乡居民养老保险补贴汇总表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-30 10:44:01
 */
@RestController
@RequestMapping("data_management/dataresidentpensioninsurance")
public class DataResidentPensionInsuranceController {
    @Autowired
    private DataResidentPensionInsuranceService dataResidentPensionInsuranceService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("data_management:dataresidentpensioninsurance:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = dataResidentPensionInsuranceService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("data_management:dataresidentpensioninsurance:info")
    public R info(@PathVariable("id") Integer id){
		DataResidentPensionInsuranceEntity dataResidentPensionInsurance = dataResidentPensionInsuranceService.getById(id);

        return R.ok().put("dataResidentPensionInsurance", dataResidentPensionInsurance);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("data_management:dataresidentpensioninsurance:save")
    public R save(@RequestBody DataResidentPensionInsuranceEntity dataResidentPensionInsurance){
		dataResidentPensionInsuranceService.save(dataResidentPensionInsurance);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("data_management:dataresidentpensioninsurance:update")
    public R update(@RequestBody DataResidentPensionInsuranceEntity dataResidentPensionInsurance){
		dataResidentPensionInsuranceService.updateById(dataResidentPensionInsurance);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("data_management:dataresidentpensioninsurance:delete")
    public R delete(@RequestBody Integer[] ids){
		dataResidentPensionInsuranceService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    // @RequiresPermissions("data_management:dataresidentpensioninsurance:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<DataResidentPensionInsuranceEntity> dataResidentPensionInsuranceList = new ArrayList<>();
        list.forEach(item ->{
                    item.put("id",item.get(""));
                    item.put("name",item.get("姓名"));
                    item.put("idCard",item.get("身份证号"));
                    item.put("projectName",item.get("项目名称"));
                    item.put("subsidy",item.get("补贴金额"));
                    item.put("mobilePhone",item.get("联系电话"));
                    item.put("address",item.get("地址"));
                    item.put("status",item.get("状态"));
                    item.put("createId",item.get("创建人编号"));
                    item.put("createTime",item.get("创建时间"));
                    dataResidentPensionInsuranceList.add(new Gson().fromJson(new Gson().toJson(item), DataResidentPensionInsuranceEntity.class));
        });
        // 保存到数据库
        dataResidentPensionInsuranceService.saveBatch(dataResidentPensionInsuranceList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    // @RequiresPermissions("data_management:dataresidentpensioninsurance:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<DataResidentPensionInsuranceEntity> dataResidentPensionInsuranceEntityList = dataResidentPensionInsuranceService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("城乡居民养老保险补贴汇总表", null, "城乡居民养老保险补贴汇总表");
        Workbook workbook = ExcelExportUtil.exportExcel(params, DataResidentPensionInsuranceEntity.class, dataResidentPensionInsuranceEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "城乡居民养老保险补贴汇总表" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

}
