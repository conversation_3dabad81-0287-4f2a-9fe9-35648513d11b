package com.hmit.kernespring.modules.data_management.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.cjrone.entity.TempDiffEntity;
import com.hmit.kernespring.modules.cjrone.service.TempDiffService;
import com.hmit.kernespring.modules.cjrone_bl.entity.Love24Entity;
import com.hmit.kernespring.modules.cjrone_bl.service.Love24Service;
import com.hmit.kernespring.modules.data_management.entity.ApiCardIdEntity;
import com.hmit.kernespring.modules.data_management.entity.DataDeadinfoEntity;
import com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity;
import com.hmit.kernespring.modules.data_management.entity.DeadInfoEntity;
import com.hmit.kernespring.modules.data_management.service.ApiCardIdService;
import com.hmit.kernespring.modules.data_management.service.DataDeadinfoService;
import com.hmit.kernespring.modules.data_management.service.DataDisabilityCertificateService;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;

/**
 * 省公安身份证信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-13 15:39:30
 */
@RestController
@RequestMapping("data_management/apicardid")
public class ApiCardIdController {
    @Autowired
    private ApiCardIdService apiCardIdService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;
    @Autowired
    private Love24Service love24Service;

    @Autowired
    private DataDisabilityCertificateService dataDisabilityCertificateService;
    @Autowired
    private TempDiffService tempDiffService;

    @Autowired
    private DataDeadinfoService dataDeadinfoService;


    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("data_management:apicardid:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = apiCardIdService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("data_management:apicardid:info")
    public R info(@PathVariable("id") Integer id){
		ApiCardIdEntity apiCardId = apiCardIdService.getById(id);

        return R.ok().put("apiCardId", apiCardId);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("data_management:apicardid:save")
    public R save(@RequestBody ApiCardIdEntity apiCardId){
		apiCardIdService.save(apiCardId);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("data_management:apicardid:update")
    public R update(@RequestBody ApiCardIdEntity apiCardId){
		apiCardIdService.updateById(apiCardId);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("data_management:apicardid:delete")
    public R delete(@RequestBody Integer[] ids){
		apiCardIdService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    // @RequiresPermissions("data_management:apicardid:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<ApiCardIdEntity> apiCardIdList = new ArrayList<>();
        list.forEach(item ->{
                    item.put("id",item.get(""));
                    item.put("name",item.get("残疾人姓名"));
                    item.put("sex",item.get("性别  1 男  2女"));
                    item.put("nationality",item.get("民族"));
                    item.put("birthday",item.get("出生日期"));
                    item.put("nativePlace",item.get("籍贯"));
                    item.put("idCard",item.get("身份证号码"));
                    item.put("nativeAddress",item.get("户籍地址"));
                    item.put("photo",item.get("二寸照"));
                    item.put("qfjg",item.get("发证机构"));
                    item.put("jgbh",item.get("机构编号"));
                    item.put("maritalStatus",item.get("婚姻情况  0未婚  1已婚"));
                    item.put("educationDegree",item.get("文化程度"));
                    item.put("nativeZhen",item.get("户籍地址  镇"));
                    item.put("nativeCun",item.get("户籍地址 村"));
                    item.put("presentZhen",item.get("现住址 镇"));
                    item.put("presentCun",item.get("现地址  村"));
                    item.put("presentAddress",item.get("现地址"));
                    item.put("postcode",item.get("邮编"));
                    item.put("mobilePhone",item.get("联系电话"));
                    item.put("guardianName",item.get("监护人姓名"));
                    item.put("guardianPhone",item.get("监护人手机"));
                    item.put("guardianIdcard",item.get("监护人身份证号"));
                    item.put("guardianRelation",item.get("与申请人关系"));
                    item.put("bankAccount",item.get("银行账户"));
                    item.put("bankName",item.get("开户银行"));
                    item.put("disabilityType",item.get("残疾类别"));
                    apiCardIdList.add(new Gson().fromJson(new Gson().toJson(item), ApiCardIdEntity.class));
        });
        // 保存到数据库
        apiCardIdService.saveBatch(apiCardIdList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    // @RequiresPermissions("data_management:apicardid:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<ApiCardIdEntity> apiCardIdEntityList = apiCardIdService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("省公安身份证信息", null, "省公安身份证信息");
        Workbook workbook = ExcelExportUtil.exportExcel(params, ApiCardIdEntity.class, apiCardIdEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "省公安身份证信息" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

    /**
     * 首次爱心24小时和残疾证数据比对
     */
    @RequestMapping("/diffData")
    public void testdiffData()  {
        List<Love24Entity> love24Entities = love24Service.list();
        List<Love24Entity> errorInfos = new ArrayList<>();
        List<TempDiffEntity> tempDiffEntityList = new ArrayList<>();
        List<DataDeadinfoEntity> deadInfoEntities = dataDeadinfoService.list();



        love24Entities.forEach(item->{
            TempDiffEntity diffEntity = new TempDiffEntity();
            diffEntity.setIdCard(item.getIdCard());
            diffEntity.setName(item.getName());
            diffEntity.setDisableId(item.getDisableId());
            String error ="";


            List<DataDisabilityCertificateEntity> infolits = dataDisabilityCertificateService.list(new QueryWrapper<DataDisabilityCertificateEntity>()
                    .eq("name",item.getName()).eq("id_card",item.getIdCard()).eq("disable_id",item.getDisableId()).notLike("native_place","大榭"));
            if(infolits.size()>0){
                List<DataDeadinfoEntity> deadinfo = dataDeadinfoService.list(new QueryWrapper<DataDeadinfoEntity>()
                        .eq("id_card",item.getIdCard()));
                if(deadinfo.size()>0) {
                    error += "此人已死亡；";
                    item.setError(error);
                    diffEntity.setError(error);
                    tempDiffEntityList.add(diffEntity);
                    errorInfos.add(item);
                }
                 return;
            }

            List<DataDisabilityCertificateEntity> nameList = dataDisabilityCertificateService.list(new QueryWrapper<DataDisabilityCertificateEntity>()
                    .eq("name",item.getName()));

            List<DataDisabilityCertificateEntity> idList = dataDisabilityCertificateService.list(new QueryWrapper<DataDisabilityCertificateEntity>()
                    .eq("id_card",item.getIdCard()));

            List<DataDisabilityCertificateEntity> disableList = dataDisabilityCertificateService.list(new QueryWrapper<DataDisabilityCertificateEntity>()
                    .eq("disable_id",item.getDisableId()));

            List<DataDeadinfoEntity> nameDeadinfo = dataDeadinfoService.list(new QueryWrapper<DataDeadinfoEntity>()
                    .eq("name",item.getName()));

            List<DataDeadinfoEntity> idCardDeadInfo = dataDeadinfoService.list(new QueryWrapper<DataDeadinfoEntity>()
                    .eq("id_card",item.getIdCard()));


            if(nameList.size()==0 && idList.size()==0 && disableList.size() ==0){

                error += " 残疾证中没有和此人匹配的信息；";
                item.setError(error);
                diffEntity.setError(error);
                tempDiffEntityList.add(diffEntity);
                errorInfos.add(item);

                List<DataDeadinfoEntity> deadinfo = dataDeadinfoService.list(new QueryWrapper<DataDeadinfoEntity>()
                        .eq("id_card",item.getIdCard()).eq("name",item.getName()));
                if(deadinfo.size()>0) {
                    error += " 此人已死亡；";
                    item.setError(error);
                    diffEntity.setError(error);
                    tempDiffEntityList.add(diffEntity);
                    errorInfos.add(item);
                    return;
                }
                List<DataDeadinfoEntity> nameinfo = dataDeadinfoService.list(new QueryWrapper<DataDeadinfoEntity>()
                        .eq("name",item.getName()));
                if(nameinfo.size()>0) {
                    error += " 根据 姓名查询 ，身份证号为："+nameinfo.get(0).getIdCard()+" 已死亡；";
                    item.setError(error);
                    diffEntity.setError(error);
                    tempDiffEntityList.add(diffEntity);
                    errorInfos.add(item);
                }
                List<DataDeadinfoEntity> idCardinfo = dataDeadinfoService.list(new QueryWrapper<DataDeadinfoEntity>()
                        .eq("id_card",item.getIdCard()));
                if(idCardinfo.size()>0) {
                    error += " 根据 身份证号查询 姓名为："+idCardinfo.get(0).getName()+" 已死亡；";
                    item.setError(error);
                    diffEntity.setError(error);
                    tempDiffEntityList.add(diffEntity);
                    errorInfos.add(item);
                }

                return;
            }

            if(nameDeadinfo.size()>0){
                error += " ;根据 姓名 查询,身份证号为："+nameDeadinfo.get(0).getIdCard()+" 已死亡；";
                item.setError(error);
                diffEntity.setError(error);
                tempDiffEntityList.add(diffEntity);
                errorInfos.add(item);

            }

            if(idCardDeadInfo.size()>0){
                error += " ;根据 身份证号 查询姓名为："+idCardDeadInfo.get(0).getName()+" 已死亡；";
                item.setError(error);
                diffEntity.setError(error);
                tempDiffEntityList.add(diffEntity);
                errorInfos.add(item);

            }

             if (nameList.size()>0){
                error += "根据 姓名 查询 ";
                for (DataDisabilityCertificateEntity entity : nameList) {
                    if(entity.getNativePlace().contains("大榭")){
                        error += ";属于大榭开发区;";
                        item.setError(error);
                        errorInfos.add(item);
                        diffEntity.setError(error);
                        tempDiffEntityList.add(diffEntity);
                        continue;
                    }

                    if (!item.getIdCard().equals(entity.getIdCard()) ){
                        error += " ;身份证号码不一致，残疾证 身份证号码 为："+entity.getIdCard();
                        item.setError(error);
                        errorInfos.add(item);
                        diffEntity.setError(error);
                        tempDiffEntityList.add(diffEntity);

                    }
                    if (!item.getDisableId().equals(entity.getDisableId())){
                        error += " ;残疾证号不一致 ，残疾证 残疾号码 为："+entity.getDisableId();
                        item.setError(error);
                        errorInfos.add(item);
                        diffEntity.setError(error);
                        tempDiffEntityList.add(diffEntity);

                    }
                }
            }else{
                error += " ;残疾证中未查到此名字; ";
                item.setError(error);
                errorInfos.add(item);
                diffEntity.setError(error);
                tempDiffEntityList.add(diffEntity);

            }

            if (idList.size()>0){
                error += " ;根据 身份证号 查询 ";
                for (DataDisabilityCertificateEntity entity : idList) {
                    if(entity.getNativePlace().contains("大榭")){
                        error += " ;属于大榭开发区; ";
                        item.setError(error);
                        errorInfos.add(item);
                        diffEntity.setError(error);
                        tempDiffEntityList.add(diffEntity);
                        continue;
                    }

                    if (!item.getName().equals(entity.getName()) ){
                        error += " ;姓名不一致,残疾人证姓名为："+entity.getName();
                        item.setError(error);
                        errorInfos.add(item);
                        diffEntity.setError(error);
                        tempDiffEntityList.add(diffEntity);

                    }
                    if (!item.getDisableId().equals(entity.getDisableId())){
                        error += " ;残疾证号不一致,残疾人证上的残疾证号为："+entity.getDisableId();
                        item.setError(error);
                        errorInfos.add(item);
                        diffEntity.setError(error);
                        tempDiffEntityList.add(diffEntity);

                    }
                }
            }else{
                error += " ;残疾证中未查到身份证号;";
                item.setError(error);
                errorInfos.add(item);
                diffEntity.setError(error);
                tempDiffEntityList.add(diffEntity);

            }

            if (disableList.size()>0){
                error += " ;根据 残疾证号 查询 ";
                for (DataDisabilityCertificateEntity entity : disableList) {
                    if(entity.getNativePlace().contains("大榭")){
                        error += " ;属于大榭开发区;";
                        item.setError(error);
                        errorInfos.add(item);
                        diffEntity.setError(error);
                        tempDiffEntityList.add(diffEntity);
                        continue;
                    }

                    if (!item.getName().equals(entity.getName()) ){
                        error += " ;姓名不一致,残疾人证姓名为："+entity.getName();
                        item.setError(error);
                        errorInfos.add(item);
                        diffEntity.setError(error);
                        tempDiffEntityList.add(diffEntity);

                    }
                    if (!item.getIdCard().equals(entity.getIdCard())){
                        error += " ;身份证号号不一致,残疾人证上的身份证号为："+entity.getIdCard()+";";
                        item.setError(error);
                        errorInfos.add(item);
                        diffEntity.setError(error);
                        tempDiffEntityList.add(diffEntity);

                    }
                }
            }else{
                error += " ;残疾证中未查到对应残疾证号;";
                item.setError(error);
                errorInfos.add(item);
                diffEntity.setError(error);
                tempDiffEntityList.add(diffEntity);

            }
        });
        tempDiffService.saveBatch(tempDiffEntityList);
        errorInfos.stream().forEach(System.out::println);

    }

}
