package com.hmit.kernespring.modules.data_management.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.data_management.dao.DataDeadinfoDao;
import com.hmit.kernespring.modules.data_management.entity.DataDeadinfoEntity;
import com.hmit.kernespring.modules.data_management.service.DataDeadinfoService;


@Service("dataDeadinfoService")
public class DataDeadinfoServiceImpl extends ServiceImpl<DataDeadinfoDao, DataDeadinfoEntity> implements DataDeadinfoService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private DataDeadinfoDao dataDeadinfoDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        DataDeadinfoEntity dataDeadinfoEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, DataDeadinfoEntity.class);
        IPage<DataDeadinfoEntity> page = this.page(
                new Query<DataDeadinfoEntity>().getPage(params),
                new QueryWrapper<DataDeadinfoEntity>()
            .eq(StringUtils.isNotBlank(dataDeadinfoEntity.getId ()!=null && !"".equals(dataDeadinfoEntity.getId ().toString())? dataDeadinfoEntity.getId ().toString():null),"id", dataDeadinfoEntity.getId ())
            .eq(StringUtils.isNotBlank(dataDeadinfoEntity.getBusinessNumber ()!=null && !"".equals(dataDeadinfoEntity.getBusinessNumber ().toString())? dataDeadinfoEntity.getBusinessNumber ().toString():null),"business_number", dataDeadinfoEntity.getBusinessNumber ())
            .eq(StringUtils.isNotBlank(dataDeadinfoEntity.getName ()!=null && !"".equals(dataDeadinfoEntity.getName ().toString())? dataDeadinfoEntity.getName ().toString():null),"name", dataDeadinfoEntity.getName ())
            .eq(StringUtils.isNotBlank(dataDeadinfoEntity.getSex ()!=null && !"".equals(dataDeadinfoEntity.getSex ().toString())? dataDeadinfoEntity.getSex ().toString():null),"sex", dataDeadinfoEntity.getSex ())
            .eq(StringUtils.isNotBlank(dataDeadinfoEntity.getAge ()!=null && !"".equals(dataDeadinfoEntity.getAge ().toString())? dataDeadinfoEntity.getAge ().toString():null),"age", dataDeadinfoEntity.getAge ())
            .eq(StringUtils.isNotBlank(dataDeadinfoEntity.getBirthplace ()!=null && !"".equals(dataDeadinfoEntity.getBirthplace ().toString())? dataDeadinfoEntity.getBirthplace ().toString():null),"birthplace", dataDeadinfoEntity.getBirthplace ())
            .eq(StringUtils.isNotBlank(dataDeadinfoEntity.getIdCard ()!=null && !"".equals(dataDeadinfoEntity.getIdCard ().toString())? dataDeadinfoEntity.getIdCard ().toString():null),"id_card", dataDeadinfoEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(dataDeadinfoEntity.getResidenceAddress ()!=null && !"".equals(dataDeadinfoEntity.getResidenceAddress ().toString())? dataDeadinfoEntity.getResidenceAddress ().toString():null),"residence_address", dataDeadinfoEntity.getResidenceAddress ())
            .eq(StringUtils.isNotBlank(dataDeadinfoEntity.getDeadDate ()!=null && !"".equals(dataDeadinfoEntity.getDeadDate ().toString())? dataDeadinfoEntity.getDeadDate ().toString():null),"dead_date", dataDeadinfoEntity.getDeadDate ())
            .eq(StringUtils.isNotBlank(dataDeadinfoEntity.getCremationDate ()!=null && !"".equals(dataDeadinfoEntity.getCremationDate ().toString())? dataDeadinfoEntity.getCremationDate ().toString():null),"cremation_date", dataDeadinfoEntity.getCremationDate ())
            .eq(StringUtils.isNotBlank(dataDeadinfoEntity.getGetDate ()!=null && !"".equals(dataDeadinfoEntity.getGetDate ().toString())? dataDeadinfoEntity.getGetDate ().toString():null),"get_date", dataDeadinfoEntity.getGetDate ())
            .eq(StringUtils.isNotBlank(dataDeadinfoEntity.getGetAddress ()!=null && !"".equals(dataDeadinfoEntity.getGetAddress ().toString())? dataDeadinfoEntity.getGetAddress ().toString():null),"get_address", dataDeadinfoEntity.getGetAddress ())
            .eq(StringUtils.isNotBlank(dataDeadinfoEntity.getFamilyName ()!=null && !"".equals(dataDeadinfoEntity.getFamilyName ().toString())? dataDeadinfoEntity.getFamilyName ().toString():null),"family_name", dataDeadinfoEntity.getFamilyName ())
            .eq(StringUtils.isNotBlank(dataDeadinfoEntity.getRelation ()!=null && !"".equals(dataDeadinfoEntity.getRelation ().toString())? dataDeadinfoEntity.getRelation ().toString():null),"relation", dataDeadinfoEntity.getRelation ())
            .eq(StringUtils.isNotBlank(dataDeadinfoEntity.getPhone ()!=null && !"".equals(dataDeadinfoEntity.getPhone ().toString())? dataDeadinfoEntity.getPhone ().toString():null),"phone", dataDeadinfoEntity.getPhone ())
        );
        Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key","sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        page.getRecords().forEach( item -> {
        });
        return new PageUtils(page);
    }
    @Override
    public List<DataDeadinfoEntity> queryExportData(Map<String, Object> params) {
            return dataDeadinfoDao.queryExportData(params);
    }

}