package com.hmit.kernespring.modules.data_management.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.data_management.entity.DataDistressedChildEntity;

import java.util.Map;

import java.util.List;

/**
 * 困境儿童
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-19 09:08:58
 */
public interface DataDistressedChildService extends IService<DataDistressedChildEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<DataDistressedChildEntity> queryExportData(Map<String, Object> params);
}

