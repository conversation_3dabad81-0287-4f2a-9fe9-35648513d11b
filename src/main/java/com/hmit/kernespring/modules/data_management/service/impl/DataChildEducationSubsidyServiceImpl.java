package com.hmit.kernespring.modules.data_management.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;
import com.hmit.kernespring.modules.data_management.dao.DataChildEducationSubsidyDao;
import com.hmit.kernespring.modules.data_management.entity.DataChildEducationSubsidyEntity;
import com.hmit.kernespring.modules.data_management.entity.DataChildEducationSubsidyMoneyEntity;
import com.hmit.kernespring.modules.data_management.service.DataChildEducationSubsidyMoneyService;
import com.hmit.kernespring.modules.data_management.service.DataChildEducationSubsidyService;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service("dataChildEducationSubsidyService")
public class DataChildEducationSubsidyServiceImpl extends ServiceImpl<DataChildEducationSubsidyDao, DataChildEducationSubsidyEntity> implements DataChildEducationSubsidyService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private DataChildEducationSubsidyDao dataChildEducationSubsidyDao;
    @Autowired
    private SysDictService sysDictService;
    @Autowired
    private DataChildEducationSubsidyMoneyService dataChildEducationSubsidyMoneyService;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        DataChildEducationSubsidyEntity dataChildEducationSubsidyEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, DataChildEducationSubsidyEntity.class);
        IPage<DataChildEducationSubsidyEntity> page = this.page(
                new Query<DataChildEducationSubsidyEntity>().getPage(params),
                new QueryWrapper<DataChildEducationSubsidyEntity>()
            .eq(StringUtils.isNotBlank(dataChildEducationSubsidyEntity.getId ()!=null && !"".equals(dataChildEducationSubsidyEntity.getId ().toString())? dataChildEducationSubsidyEntity.getId ().toString():null),"id", dataChildEducationSubsidyEntity.getId ())
            .eq(StringUtils.isNotBlank(dataChildEducationSubsidyEntity.getName ()!=null && !"".equals(dataChildEducationSubsidyEntity.getName ().toString())? dataChildEducationSubsidyEntity.getName ().toString():null),"name", dataChildEducationSubsidyEntity.getName ())
            .eq(StringUtils.isNotBlank(dataChildEducationSubsidyEntity.getIdCard ()!=null && !"".equals(dataChildEducationSubsidyEntity.getIdCard ().toString())? dataChildEducationSubsidyEntity.getIdCard ().toString():null),"id_card", dataChildEducationSubsidyEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(dataChildEducationSubsidyEntity.getSex ()!=null && !"".equals(dataChildEducationSubsidyEntity.getSex ().toString())? dataChildEducationSubsidyEntity.getSex ().toString():null),"sex", dataChildEducationSubsidyEntity.getSex ())
            .eq(StringUtils.isNotBlank(dataChildEducationSubsidyEntity.getMobilePhone ()!=null && !"".equals(dataChildEducationSubsidyEntity.getMobilePhone ().toString())? dataChildEducationSubsidyEntity.getMobilePhone ().toString():null),"mobile_phone", dataChildEducationSubsidyEntity.getMobilePhone ())
            .eq(StringUtils.isNotBlank(dataChildEducationSubsidyEntity.getPresentAddress ()!=null && !"".equals(dataChildEducationSubsidyEntity.getPresentAddress ().toString())? dataChildEducationSubsidyEntity.getPresentAddress ().toString():null),"present_address", dataChildEducationSubsidyEntity.getPresentAddress ())
            .eq(StringUtils.isNotBlank(dataChildEducationSubsidyEntity.getDisableId ()!=null && !"".equals(dataChildEducationSubsidyEntity.getDisableId ().toString())? dataChildEducationSubsidyEntity.getDisableId ().toString():null),"disable_id", dataChildEducationSubsidyEntity.getDisableId ())
            .eq(StringUtils.isNotBlank(dataChildEducationSubsidyEntity.getFamilyEconoCondition ()!=null && !"".equals(dataChildEducationSubsidyEntity.getFamilyEconoCondition ().toString())? dataChildEducationSubsidyEntity.getFamilyEconoCondition ().toString():null),"family_econo_condition", dataChildEducationSubsidyEntity.getFamilyEconoCondition ())
            .eq(StringUtils.isNotBlank(dataChildEducationSubsidyEntity.getCurrentSchool ()!=null && !"".equals(dataChildEducationSubsidyEntity.getCurrentSchool ().toString())? dataChildEducationSubsidyEntity.getCurrentSchool ().toString():null),"current_school", dataChildEducationSubsidyEntity.getCurrentSchool ())
            .eq(StringUtils.isNotBlank(dataChildEducationSubsidyEntity.getGrade ()!=null && !"".equals(dataChildEducationSubsidyEntity.getGrade ().toString())? dataChildEducationSubsidyEntity.getGrade ().toString():null),"grade", dataChildEducationSubsidyEntity.getGrade ())
            .eq(StringUtils.isNotBlank(dataChildEducationSubsidyEntity.getAdmissionTime ()!=null && !"".equals(dataChildEducationSubsidyEntity.getAdmissionTime ().toString())? dataChildEducationSubsidyEntity.getAdmissionTime ().toString():null),"admission_time", dataChildEducationSubsidyEntity.getAdmissionTime ())
            .eq(StringUtils.isNotBlank(dataChildEducationSubsidyEntity.getApplicantType ()!=null && !"".equals(dataChildEducationSubsidyEntity.getApplicantType ().toString())? dataChildEducationSubsidyEntity.getApplicantType ().toString():null),"applicant_type", dataChildEducationSubsidyEntity.getApplicantType ())
            .eq(StringUtils.isNotBlank(dataChildEducationSubsidyEntity.getApplicationTime ()!=null && !"".equals(dataChildEducationSubsidyEntity.getApplicationTime ().toString())? dataChildEducationSubsidyEntity.getApplicationTime ().toString():null),"application_time", dataChildEducationSubsidyEntity.getApplicationTime ())
            .eq(StringUtils.isNotBlank(dataChildEducationSubsidyEntity.getFatherName ()!=null && !"".equals(dataChildEducationSubsidyEntity.getFatherName ().toString())? dataChildEducationSubsidyEntity.getFatherName ().toString():null),"father_name", dataChildEducationSubsidyEntity.getFatherName ())
            .eq(StringUtils.isNotBlank(dataChildEducationSubsidyEntity.getFatherIdcard ()!=null && !"".equals(dataChildEducationSubsidyEntity.getFatherIdcard ().toString())? dataChildEducationSubsidyEntity.getFatherIdcard ().toString():null),"father_idcard", dataChildEducationSubsidyEntity.getFatherIdcard ())
            .eq(StringUtils.isNotBlank(dataChildEducationSubsidyEntity.getMotherName ()!=null && !"".equals(dataChildEducationSubsidyEntity.getMotherName ().toString())? dataChildEducationSubsidyEntity.getMotherName ().toString():null),"mother_name", dataChildEducationSubsidyEntity.getMotherName ())
            .eq(StringUtils.isNotBlank(dataChildEducationSubsidyEntity.getMotherIdcard ()!=null && !"".equals(dataChildEducationSubsidyEntity.getMotherIdcard ().toString())? dataChildEducationSubsidyEntity.getMotherIdcard ().toString():null),"mother_idcard", dataChildEducationSubsidyEntity.getMotherIdcard ())
            .eq(StringUtils.isNotBlank(dataChildEducationSubsidyEntity.getEducation()!=null&&!"".equals(dataChildEducationSubsidyEntity.getEducation().toString())?dataChildEducationSubsidyEntity.getEducation().toString():null),"education",dataChildEducationSubsidyEntity.getEducation())
            .eq(StringUtils.isNotBlank(dataChildEducationSubsidyEntity.getSignStatus()!=null&&!"".equals(dataChildEducationSubsidyEntity.getSignStatus().toString())?dataChildEducationSubsidyEntity.getSignStatus().toString():null),"sign_status",dataChildEducationSubsidyEntity.getSignStatus())
            .eq(StringUtils.isNotBlank(dataChildEducationSubsidyEntity.getStatus()!=null&&!"".equals(dataChildEducationSubsidyEntity.getStatus().toString())?dataChildEducationSubsidyEntity.getStatus().toString():null),"status",dataChildEducationSubsidyEntity.getStatus())
            .eq(StringUtils.isNotBlank(dataChildEducationSubsidyEntity.getSubsidyMoney()!=null&&!"".equals(dataChildEducationSubsidyEntity.getSubsidyMoney().toString())?dataChildEducationSubsidyEntity.getSubsidyMoney().toString():null),"subsidy_money",dataChildEducationSubsidyEntity.getSubsidyMoney())

        );
        Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key","sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        List<DataChildEducationSubsidyMoneyEntity> child_edu_subsidy_money=dataChildEducationSubsidyMoneyService.list();
        page.getRecords().forEach( item -> {
            if (item.getStatus()!=null && "8".equals(item.getStatus())){
                item.setStatus("申请已提交");
            } if (item.getStatus()!=null && "10".equals(item.getStatus())){
                item.setStatus("申请已归档");
            }
            if (item.getSignStatus()!=null && "1".equals(item.getSignStatus())){
                item.setSignStatus("申请人待手签");
            }else if (item.getSignStatus()!=null && "2".equals(item.getSignStatus())){
                item.setSignStatus("申请人已手签");
            }

           /* SysDictEntity familyEconoCondition_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("jtjjqk_0000") && iii.getValue().equals(
                        item.getFamilyEconoCondition ())).findAny().orElse(null);
            if (familyEconoCondition_sysDictEntity != null){
                item.setFamilyEconoCondition (familyEconoCondition_sysDictEntity.getLabel());
            }else{
                item.setFamilyEconoCondition (null);
            }
            SysDictEntity applicantType_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("sqrlx_0000") && iii.getValue().equals(
                        item.getApplicantType ())).findAny().orElse(null);
            if (applicantType_sysDictEntity != null){
                item.setApplicantType (applicantType_sysDictEntity.getLabel());
            }else{
                item.setApplicantType (null);
            }
            // 开始关联补助金额
            DataChildEducationSubsidyMoneyEntity dsme=child_edu_subsidy_money.stream().filter(
                    iii->iii.getEducation().equals(item.getEducation())
                            &&iii.getFamilyEconomicSituation().equals(item.getFamilyEconoCondition())
            ).findAny().orElse(null);
            if(dsme!=null){
                item.setSubsidyMoney(dsme.getSubsidyMoney().toString());
            }else{
                item.setSubsidyMoney(null);
            }
*/
        });
        return new PageUtils(page);
    }
    @Override
    public List<DataChildEducationSubsidyEntity> queryExportData(Map<String, Object> params) {
            return dataChildEducationSubsidyDao.queryExportData(params);
    }

}