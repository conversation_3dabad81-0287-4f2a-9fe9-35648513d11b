package com.hmit.kernespring.modules.data_management.entity;

import com.google.gson.annotations.Expose;

import java.sql.Date;

public class ApiZCszmEntity {

    /**
     * msfz : 330501198804052428
     * fsfz : 330522198908252119
     * fxm : 吴振杰
     * sr : 2015-12-25
     * mxm : 金莉
     * xb : 女
     * csdzmc : 浙江省湖州市吴兴区
     * xm : 金鑫荧
     * tong_time : 2018-04-12 20:28:24.0
     */

    private String msfz;
    private String fsfz;
    private String fxm;
    private String sr;
    private String mxm;
    private String xb;
    private String csdzmc;
    private String xm;
    private String tong_time;
    public String getMsfz() {
        return msfz;
    }

    public void setMsfz(String msfz) {
        this.msfz = msfz;
    }

    public String getFsfz() {
        return fsfz;
    }

    public void setFsfz(String fsfz) {
        this.fsfz = fsfz;
    }

    public String getFxm() {
        return fxm;
    }

    public void setFxm(String fxm) {
        this.fxm = fxm;
    }

    public String getSr() {
        return sr;
    }

    public void setSr(String sr) {
        this.sr = sr;
    }

    public String getMxm() {
        return mxm;
    }

    public void setMxm(String mxm) {
        this.mxm = mxm;
    }

    public String getXb() {
        return xb;
    }

    public void setXb(String xb) {
        this.xb = xb;
    }

    public String getCsdzmc() {
        return csdzmc;
    }

    public void setCsdzmc(String csdzmc) {
        this.csdzmc = csdzmc;
    }

    public String getXm() {
        return xm;
    }

    public void setXm(String xm) {
        this.xm = xm;
    }

    public String getTong_time() {
        return tong_time;
    }

    public void setTong_time(String tong_time) {
        this.tong_time = tong_time;
    }
}
