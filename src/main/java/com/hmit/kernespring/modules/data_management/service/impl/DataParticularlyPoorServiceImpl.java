package com.hmit.kernespring.modules.data_management.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;
import com.hmit.kernespring.modules.data_management.dao.DataParticularlyPoorDao;
import com.hmit.kernespring.modules.data_management.entity.DataParticularlyPoorEntity;
import com.hmit.kernespring.modules.data_management.service.DataParticularlyPoorService;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Type;
import java.util.Collection;
import java.util.List;
import java.util.Map;


@Service("dataParticularlyPoorService")
public class DataParticularlyPoorServiceImpl extends ServiceImpl<DataParticularlyPoorDao, DataParticularlyPoorEntity> implements DataParticularlyPoorService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private DataParticularlyPoorDao dataParticularlyPoorDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        DataParticularlyPoorEntity dataParticularlyPoorEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, DataParticularlyPoorEntity.class);
        IPage<DataParticularlyPoorEntity> page = this.page(
                new Query<DataParticularlyPoorEntity>().getPage(params),
                new QueryWrapper<DataParticularlyPoorEntity>()
            .eq(StringUtils.isNotBlank(dataParticularlyPoorEntity.getId ()!=null && !"".equals(dataParticularlyPoorEntity.getId ().toString())? dataParticularlyPoorEntity.getId ().toString():null),"id", dataParticularlyPoorEntity.getId ())
            .eq(StringUtils.isNotBlank(dataParticularlyPoorEntity.getAdministrativeDivision ()!=null && !"".equals(dataParticularlyPoorEntity.getAdministrativeDivision ().toString())? dataParticularlyPoorEntity.getAdministrativeDivision ().toString():null),"administrative_division", dataParticularlyPoorEntity.getAdministrativeDivision ())
            .eq(StringUtils.isNotBlank(dataParticularlyPoorEntity.getName ()!=null && !"".equals(dataParticularlyPoorEntity.getName ().toString())? dataParticularlyPoorEntity.getName ().toString():null),"name", dataParticularlyPoorEntity.getName ())
            .eq(StringUtils.isNotBlank(dataParticularlyPoorEntity.getRelationshipWithHousehold ()!=null && !"".equals(dataParticularlyPoorEntity.getRelationshipWithHousehold ().toString())? dataParticularlyPoorEntity.getRelationshipWithHousehold ().toString():null),"relationship_with_household", dataParticularlyPoorEntity.getRelationshipWithHousehold ())
            .eq(StringUtils.isNotBlank(dataParticularlyPoorEntity.getIdCard ()!=null && !"".equals(dataParticularlyPoorEntity.getIdCard ().toString())? dataParticularlyPoorEntity.getIdCard ().toString():null),"id_card", dataParticularlyPoorEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(dataParticularlyPoorEntity.getMobilePhone ()!=null && !"".equals(dataParticularlyPoorEntity.getMobilePhone ().toString())? dataParticularlyPoorEntity.getMobilePhone ().toString():null),"mobile_phone", dataParticularlyPoorEntity.getMobilePhone ())
            .eq(StringUtils.isNotBlank(dataParticularlyPoorEntity.getTelephone ()!=null && !"".equals(dataParticularlyPoorEntity.getTelephone ().toString())? dataParticularlyPoorEntity.getTelephone ().toString():null),"telephone", dataParticularlyPoorEntity.getTelephone ())
            .eq(StringUtils.isNotBlank(dataParticularlyPoorEntity.getBankAccount ()!=null && !"".equals(dataParticularlyPoorEntity.getBankAccount ().toString())? dataParticularlyPoorEntity.getBankAccount ().toString():null),"bank_account", dataParticularlyPoorEntity.getBankAccount ())
            .eq(StringUtils.isNotBlank(dataParticularlyPoorEntity.getBankName ()!=null && !"".equals(dataParticularlyPoorEntity.getBankName ().toString())? dataParticularlyPoorEntity.getBankName ().toString():null),"bank_name", dataParticularlyPoorEntity.getBankName ())
            .eq(StringUtils.isNotBlank(dataParticularlyPoorEntity.getPersonBankAccount ()!=null && !"".equals(dataParticularlyPoorEntity.getPersonBankAccount ().toString())? dataParticularlyPoorEntity.getPersonBankAccount ().toString():null),"person_bank_account", dataParticularlyPoorEntity.getPersonBankAccount ())
            .eq(StringUtils.isNotBlank(dataParticularlyPoorEntity.getApplicationCategory ()!=null && !"".equals(dataParticularlyPoorEntity.getApplicationCategory ().toString())? dataParticularlyPoorEntity.getApplicationCategory ().toString():null),"application_category", dataParticularlyPoorEntity.getApplicationCategory ())
            .eq(StringUtils.isNotBlank(dataParticularlyPoorEntity.getNumberOfSupport ()!=null && !"".equals(dataParticularlyPoorEntity.getNumberOfSupport ().toString())? dataParticularlyPoorEntity.getNumberOfSupport ().toString():null),"number_of_support", dataParticularlyPoorEntity.getNumberOfSupport ())
            .eq(StringUtils.isNotBlank(dataParticularlyPoorEntity.getAmountOfSupport ()!=null && !"".equals(dataParticularlyPoorEntity.getAmountOfSupport ().toString())? dataParticularlyPoorEntity.getAmountOfSupport ().toString():null),"amount_of_support", dataParticularlyPoorEntity.getAmountOfSupport ())
            .eq(StringUtils.isNotBlank(dataParticularlyPoorEntity.getHealthStatus ()!=null && !"".equals(dataParticularlyPoorEntity.getHealthStatus ().toString())? dataParticularlyPoorEntity.getHealthStatus ().toString():null),"health_status", dataParticularlyPoorEntity.getHealthStatus ())
            .eq(StringUtils.isNotBlank(dataParticularlyPoorEntity.getNursingLevel ()!=null && !"".equals(dataParticularlyPoorEntity.getNursingLevel ().toString())? dataParticularlyPoorEntity.getNursingLevel ().toString():null),"nursing_level", dataParticularlyPoorEntity.getNursingLevel ())
            .eq(StringUtils.isNotBlank(dataParticularlyPoorEntity.getNursingStandard ()!=null && !"".equals(dataParticularlyPoorEntity.getNursingStandard ().toString())? dataParticularlyPoorEntity.getNursingStandard ().toString():null),"nursing_standard", dataParticularlyPoorEntity.getNursingStandard ())
            .eq(StringUtils.isNotBlank(dataParticularlyPoorEntity.getRescueCertificateNumber ()!=null && !"".equals(dataParticularlyPoorEntity.getRescueCertificateNumber ().toString())? dataParticularlyPoorEntity.getRescueCertificateNumber ().toString():null),"rescue_certificate_number", dataParticularlyPoorEntity.getRescueCertificateNumber ())
            .eq(StringUtils.isNotBlank(dataParticularlyPoorEntity.getAgedInstitution ()!=null && !"".equals(dataParticularlyPoorEntity.getAgedInstitution ().toString())? dataParticularlyPoorEntity.getAgedInstitution ().toString():null),"aged_institution", dataParticularlyPoorEntity.getAgedInstitution ())
            .eq(StringUtils.isNotBlank(dataParticularlyPoorEntity.getSex ()!=null && !"".equals(dataParticularlyPoorEntity.getSex ().toString())? dataParticularlyPoorEntity.getSex ().toString():null),"sex", dataParticularlyPoorEntity.getSex ())
            .eq(StringUtils.isNotBlank(dataParticularlyPoorEntity.getBirthday ()!=null && !"".equals(dataParticularlyPoorEntity.getBirthday ().toString())? dataParticularlyPoorEntity.getBirthday ().toString():null),"birthday", dataParticularlyPoorEntity.getBirthday ())
            .eq(StringUtils.isNotBlank(dataParticularlyPoorEntity.getReasonForApplying ()!=null && !"".equals(dataParticularlyPoorEntity.getReasonForApplying ().toString())? dataParticularlyPoorEntity.getReasonForApplying ().toString():null),"reason_for_applying", dataParticularlyPoorEntity.getReasonForApplying ())
            .eq(StringUtils.isNotBlank(dataParticularlyPoorEntity.getCreateId ()!=null && !"".equals(dataParticularlyPoorEntity.getCreateId ().toString())? dataParticularlyPoorEntity.getCreateId ().toString():null),"create_id", dataParticularlyPoorEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(dataParticularlyPoorEntity.getCreateTime ()!=null && !"".equals(dataParticularlyPoorEntity.getCreateTime ().toString())? dataParticularlyPoorEntity.getCreateTime ().toString():null),"create_time", dataParticularlyPoorEntity.getCreateTime ())
        );
        return new PageUtils(page);
    }
    @Override
    public List<DataParticularlyPoorEntity> queryExportData(Map<String, Object> params) {
            return dataParticularlyPoorDao.queryExportData(params);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatch(Collection<DataParticularlyPoorEntity> entityList) {

        //删除
        dataParticularlyPoorDao.deleteAllData(null);

        return super.saveBatch(entityList);
    }



    /**
     * 事务保存，先删除，后保存
     * @param entityList
     * @param batchSize
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatch(Collection<DataParticularlyPoorEntity> entityList, int batchSize) {

        //删除
        dataParticularlyPoorDao.deleteAllData(null);

        return super.saveBatch(entityList, batchSize);
    }
}