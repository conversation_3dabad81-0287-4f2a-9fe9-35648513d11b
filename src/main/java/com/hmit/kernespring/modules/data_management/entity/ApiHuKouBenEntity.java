package com.hmit.kernespring.modules.data_management.entity;

public class ApiHuKouBenEntity {

    /**
     * "czrkmz": "汉族",
     * 			"czrkxb": "男",
     * 			"czrkzjxy": "",
     * 			"czrkhdqlssx": "",
     * 			"czrkwhcd": "初中毕业",
     * 			"zjzxrq": "",
     * 			"sspcsjgdm": "330213007",
     * 			"czrkgmsfhm": "330224197611275231",
     * 			"czrkfwcs": "",
     * 			"czrkhdqlgj": "",
     * 			"czrksg": "",
     * 			"swzxlb": "",
     * 			"czrkhyzk": "已婚",
     * 			"czrkhzxm": "江显彪",
     * 			"czrkbyzk": "未服兵役",
     * 			"czrkhsqlbs": "",-
     * 			"czrkjgssx": "浙江省奉化市",-
     * 			"czrkcsdssx": "浙江省奉化市",-
     * 			"czrkhzqlgj": "",-
     * 			"czrkhsqlbz": "",-
     * 			"czrkcsdgj": "", -
     * 			"czrkxx": "", -
     * 			"czrkhh": "007020069", -
     * 			"czrkzz": "浙江省宁波市奉化区尚田街道印家坑村１组２号", -
     * 			"czrkhyqlbz": "", -
     * 			"czrkhzqlssx": "", -
     * 			"czrkyhzgx": "子", -
     * 			"czrkzy": "", -
     * 			"czrkhlx": "1", -
     * 			"czrkhyqlbs": "", -
     * 			"czrkhzqlxz": "", -
     * 			"czrkcym": "", -
     * 			"czrkcsrq": "19761127", -
     * 			"qczxlb": "", -
     * 			"czrkxm": "江信立", -
     * 			"elcLicenceCode": "", -
     * 			"czrkpcsmc": "奉化分局尚田派出所", -
     * 			"czrkjggj": "", -
     * 			"czrkhdqlxz": ""
     */

    private String czrkmz;  // 民族
    private String czrkxb;  // 性别
    private String czrkzjxy;
    private String czrkhdqlssx;
    private String czrkwhcd;
    private String zjzxrq;
    private String sspcsjgdm;
    private String czrkgmsfhm;  // 身份证号码
    private String czrkfwcs;
    private String czrkhdqlgj;
    private String czrksg;
    private String swzxlb;
    private String czrkhyzk;  // 婚姻状况
    private String czrkhzxm;
    private String czrkbyzk;
    private String czrkhsqlbs;
    private String czrkjgssx;  // 籍贯省市县（区）
    private String czrkcsdssx;  // 出生地省市县（区）
    private String czrkhzqlgj;
    private  String czrkhsqlbz;
    private String czrkcsdgj;
    private String czrkxx;
    private String czrkhh;
    private String czrkzz;  // 居住地
    private String czrkhyqlbz;
    private String czrkhzqlssx;
    private String czrkyhzgx;
    private String czrkzy;
    private String czrkhlx;
    private String czrkhyqlbs;
    private String czrkhzqlxz;
    private String czrkcym;
    private String czrkcsrq;  // 出生日期
    private String qczxlb;
    private String czrkxm;  // 姓名
    private String elcLicenceCode;
    private String czrkpcsmc;  // 所属派出所名称
    private String czrkjggj;
    private String czrkhdqlxz;

    public String getCzrkmz() {
        return czrkmz;
    }

    public void setCzrkmz(String czrkmz) {
        this.czrkmz = czrkmz;
    }

    public String getCzrkxb() {
        return czrkxb;
    }

    public void setCzrkxb(String czrkxb) {
        this.czrkxb = czrkxb;
    }

    public String getCzrkzjxy() {
        return czrkzjxy;
    }

    public void setCzrkzjxy(String czrkzjxy) {
        this.czrkzjxy = czrkzjxy;
    }

    public String getCzrkhdqlssx() {
        return czrkhdqlssx;
    }

    public void setCzrkhdqlssx(String czrkhdqlssx) {
        this.czrkhdqlssx = czrkhdqlssx;
    }

    public String getCzrkwhcd() {
        return czrkwhcd;
    }

    public void setCzrkwhcd(String czrkwhcd) {
        this.czrkwhcd = czrkwhcd;
    }

    public String getZjzxrq() {
        return zjzxrq;
    }

    public void setZjzxrq(String zjzxrq) {
        this.zjzxrq = zjzxrq;
    }

    public String getSspcsjgdm() {
        return sspcsjgdm;
    }

    public void setSspcsjgdm(String sspcsjgdm) {
        this.sspcsjgdm = sspcsjgdm;
    }

    public String getCzrkgmsfhm() {
        return czrkgmsfhm;
    }

    public void setCzrkgmsfhm(String czrkgmsfhm) {
        this.czrkgmsfhm = czrkgmsfhm;
    }

    public String getCzrkfwcs() {
        return czrkfwcs;
    }

    public void setCzrkfwcs(String czrkfwcs) {
        this.czrkfwcs = czrkfwcs;
    }

    public String getCzrkhdqlgj() {
        return czrkhdqlgj;
    }

    public void setCzrkhdqlgj(String czrkhdqlgj) {
        this.czrkhdqlgj = czrkhdqlgj;
    }

    public String getCzrksg() {
        return czrksg;
    }

    public void setCzrksg(String czrksg) {
        this.czrksg = czrksg;
    }

    public String getSwzxlb() {
        return swzxlb;
    }

    public void setSwzxlb(String swzxlb) {
        this.swzxlb = swzxlb;
    }

    public String getCzrkhyzk() {
        return czrkhyzk;
    }

    public void setCzrkhyzk(String czrkhyzk) {
        this.czrkhyzk = czrkhyzk;
    }

    public String getCzrkhzxm() {
        return czrkhzxm;
    }

    public void setCzrkhzxm(String czrkhzxm) {
        this.czrkhzxm = czrkhzxm;
    }

    public String getCzrkbyzk() {
        return czrkbyzk;
    }

    public void setCzrkbyzk(String czrkbyzk) {
        this.czrkbyzk = czrkbyzk;
    }

    public String getCzrkhsqlbs() {
        return czrkhsqlbs;
    }

    public void setCzrkhsqlbs(String czrkhsqlbs) {
        this.czrkhsqlbs = czrkhsqlbs;
    }

    public String getCzrkjgssx() {
        return czrkjgssx;
    }

    public void setCzrkjgssx(String czrkjgssx) {
        this.czrkjgssx = czrkjgssx;
    }

    public String getCzrkcsdssx() {
        return czrkcsdssx;
    }

    public void setCzrkcsdssx(String czrkcsdssx) {
        this.czrkcsdssx = czrkcsdssx;
    }

    public String getCzrkhzqlgj() {
        return czrkhzqlgj;
    }

    public void setCzrkhzqlgj(String czrkhzqlgj) {
        this.czrkhzqlgj = czrkhzqlgj;
    }

    public String getCzrkhsqlbz() {
        return czrkhsqlbz;
    }

    public void setCzrkhsqlbz(String czrkhsqlbz) {
        this.czrkhsqlbz = czrkhsqlbz;
    }

    public String getCzrkcsdgj() {
        return czrkcsdgj;
    }

    public void setCzrkcsdgj(String czrkcsdgj) {
        this.czrkcsdgj = czrkcsdgj;
    }

    public String getCzrkxx() {
        return czrkxx;
    }

    public void setCzrkxx(String czrkxx) {
        this.czrkxx = czrkxx;
    }

    public String getCzrkhh() {
        return czrkhh;
    }

    public void setCzrkhh(String czrkhh) {
        this.czrkhh = czrkhh;
    }

    public String getCzrkzz() {
        return czrkzz;
    }

    public void setCzrkzz(String czrkzz) {
        this.czrkzz = czrkzz;
    }

    public String getCzrkhyqlbz() {
        return czrkhyqlbz;
    }

    public void setCzrkhyqlbz(String czrkhyqlbz) {
        this.czrkhyqlbz = czrkhyqlbz;
    }

    public String getCzrkhzqlssx() {
        return czrkhzqlssx;
    }

    public void setCzrkhzqlssx(String czrkhzqlssx) {
        this.czrkhzqlssx = czrkhzqlssx;
    }

    public String getCzrkyhzgx() {
        return czrkyhzgx;
    }

    public void setCzrkyhzgx(String czrkyhzgx) {
        this.czrkyhzgx = czrkyhzgx;
    }

    public String getCzrkzy() {
        return czrkzy;
    }

    public void setCzrkzy(String czrkzy) {
        this.czrkzy = czrkzy;
    }

    public String getCzrkhlx() {
        return czrkhlx;
    }

    public void setCzrkhlx(String czrkhlx) {
        this.czrkhlx = czrkhlx;
    }

    public String getCzrkhyqlbs() {
        return czrkhyqlbs;
    }

    public void setCzrkhyqlbs(String czrkhyqlbs) {
        this.czrkhyqlbs = czrkhyqlbs;
    }

    public String getCzrkhzqlxz() {
        return czrkhzqlxz;
    }

    public void setCzrkhzqlxz(String czrkhzqlxz) {
        this.czrkhzqlxz = czrkhzqlxz;
    }

    public String getCzrkcym() {
        return czrkcym;
    }

    public void setCzrkcym(String czrkcym) {
        this.czrkcym = czrkcym;
    }

    public String getCzrkcsrq() {
        return czrkcsrq;
    }

    public void setCzrkcsrq(String czrkcsrq) {
        this.czrkcsrq = czrkcsrq;
    }

    public String getQczxlb() {
        return qczxlb;
    }

    public void setQczxlb(String qczxlb) {
        this.qczxlb = qczxlb;
    }

    public String getCzrkxm() {
        return czrkxm;
    }

    public void setCzrkxm(String czrkxm) {
        this.czrkxm = czrkxm;
    }

    public String getElcLicenceCode() {
        return elcLicenceCode;
    }

    public void setElcLicenceCode(String elcLicenceCode) {
        this.elcLicenceCode = elcLicenceCode;
    }

    public String getCzrkpcsmc() {
        return czrkpcsmc;
    }

    public void setCzrkpcsmc(String czrkpcsmc) {
        this.czrkpcsmc = czrkpcsmc;
    }

    public String getCzrkjggj() {
        return czrkjggj;
    }

    public void setCzrkjggj(String czrkjggj) {
        this.czrkjggj = czrkjggj;
    }

    public String getCzrkhdqlxz() {
        return czrkhdqlxz;
    }

    public void setCzrkhdqlxz(String czrkhdqlxz) {
        this.czrkhdqlxz = czrkhdqlxz;
    }
}

