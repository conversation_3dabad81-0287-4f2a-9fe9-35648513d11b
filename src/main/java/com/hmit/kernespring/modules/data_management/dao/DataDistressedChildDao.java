package com.hmit.kernespring.modules.data_management.dao;

import com.hmit.kernespring.modules.data_management.entity.DataDistressedChildEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 困境儿童
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-19 09:08:58
 */
@Mapper
public interface DataDistressedChildDao extends BaseMapper<DataDistressedChildEntity> {
    List<DataDistressedChildEntity> queryExportData(Map<String, Object> params);
	
}
