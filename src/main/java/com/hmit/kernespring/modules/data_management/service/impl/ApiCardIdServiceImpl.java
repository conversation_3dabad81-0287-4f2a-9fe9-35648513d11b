package com.hmit.kernespring.modules.data_management.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;
import com.hmit.kernespring.modules.app.entity.UserEntity;
import com.hmit.kernespring.modules.data_management.dao.ApiCardIdDao;
import com.hmit.kernespring.modules.data_management.entity.ApiCardIdEntity;
import com.hmit.kernespring.modules.data_management.service.ApiCardIdService;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service("apiCardIdService")
public class ApiCardIdServiceImpl extends ServiceImpl<ApiCardIdDao, ApiCardIdEntity> implements ApiCardIdService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private ApiCardIdDao apiCardIdDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        ApiCardIdEntity apiCardIdEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, ApiCardIdEntity.class);
        IPage<ApiCardIdEntity> page = this.page(
                new Query<ApiCardIdEntity>().getPage(params),
                new QueryWrapper<ApiCardIdEntity>()
            .eq(StringUtils.isNotBlank(apiCardIdEntity.getId ()!=null && !"".equals(apiCardIdEntity.getId ().toString())? apiCardIdEntity.getId ().toString():null),"id", apiCardIdEntity.getId ())
            .eq(StringUtils.isNotBlank(apiCardIdEntity.getName ()!=null && !"".equals(apiCardIdEntity.getName ().toString())? apiCardIdEntity.getName ().toString():null),"name", apiCardIdEntity.getName ())
            .eq(StringUtils.isNotBlank(apiCardIdEntity.getSex ()!=null && !"".equals(apiCardIdEntity.getSex ().toString())? apiCardIdEntity.getSex ().toString():null),"sex", apiCardIdEntity.getSex ())
            .eq(StringUtils.isNotBlank(apiCardIdEntity.getNationality ()!=null && !"".equals(apiCardIdEntity.getNationality ().toString())? apiCardIdEntity.getNationality ().toString():null),"nationality", apiCardIdEntity.getNationality ())
            .eq(StringUtils.isNotBlank(apiCardIdEntity.getBirthday ()!=null && !"".equals(apiCardIdEntity.getBirthday ().toString())? apiCardIdEntity.getBirthday ().toString():null),"birthday", apiCardIdEntity.getBirthday ())
            .eq(StringUtils.isNotBlank(apiCardIdEntity.getNativePlace ()!=null && !"".equals(apiCardIdEntity.getNativePlace ().toString())? apiCardIdEntity.getNativePlace ().toString():null),"native_place", apiCardIdEntity.getNativePlace ())
            .eq(StringUtils.isNotBlank(apiCardIdEntity.getIdCard ()!=null && !"".equals(apiCardIdEntity.getIdCard ().toString())? apiCardIdEntity.getIdCard ().toString():null),"id_card", apiCardIdEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(apiCardIdEntity.getNativeAddress ()!=null && !"".equals(apiCardIdEntity.getNativeAddress ().toString())? apiCardIdEntity.getNativeAddress ().toString():null),"native_address", apiCardIdEntity.getNativeAddress ())
            .eq(StringUtils.isNotBlank(apiCardIdEntity.getPhoto ()!=null && !"".equals(apiCardIdEntity.getPhoto ().toString())? apiCardIdEntity.getPhoto ().toString():null),"photo", apiCardIdEntity.getPhoto ())
            .eq(StringUtils.isNotBlank(apiCardIdEntity.getQfjg ()!=null && !"".equals(apiCardIdEntity.getQfjg ().toString())? apiCardIdEntity.getQfjg ().toString():null),"qfjg", apiCardIdEntity.getQfjg ())
            .eq(StringUtils.isNotBlank(apiCardIdEntity.getJgbh ()!=null && !"".equals(apiCardIdEntity.getJgbh ().toString())? apiCardIdEntity.getJgbh ().toString():null),"jgbh", apiCardIdEntity.getJgbh ())
            .eq(StringUtils.isNotBlank(apiCardIdEntity.getMaritalStatus ()!=null && !"".equals(apiCardIdEntity.getMaritalStatus ().toString())? apiCardIdEntity.getMaritalStatus ().toString():null),"marital_status", apiCardIdEntity.getMaritalStatus ())
            .eq(StringUtils.isNotBlank(apiCardIdEntity.getEducationDegree ()!=null && !"".equals(apiCardIdEntity.getEducationDegree ().toString())? apiCardIdEntity.getEducationDegree ().toString():null),"education_degree", apiCardIdEntity.getEducationDegree ())
            .eq(StringUtils.isNotBlank(apiCardIdEntity.getNativeZhen ()!=null && !"".equals(apiCardIdEntity.getNativeZhen ().toString())? apiCardIdEntity.getNativeZhen ().toString():null),"native_zhen", apiCardIdEntity.getNativeZhen ())
            .eq(StringUtils.isNotBlank(apiCardIdEntity.getNativeCun ()!=null && !"".equals(apiCardIdEntity.getNativeCun ().toString())? apiCardIdEntity.getNativeCun ().toString():null),"native_cun", apiCardIdEntity.getNativeCun ())
            .eq(StringUtils.isNotBlank(apiCardIdEntity.getPresentZhen ()!=null && !"".equals(apiCardIdEntity.getPresentZhen ().toString())? apiCardIdEntity.getPresentZhen ().toString():null),"present_zhen", apiCardIdEntity.getPresentZhen ())
            .eq(StringUtils.isNotBlank(apiCardIdEntity.getPresentCun ()!=null && !"".equals(apiCardIdEntity.getPresentCun ().toString())? apiCardIdEntity.getPresentCun ().toString():null),"present_cun", apiCardIdEntity.getPresentCun ())
            .eq(StringUtils.isNotBlank(apiCardIdEntity.getPresentAddress ()!=null && !"".equals(apiCardIdEntity.getPresentAddress ().toString())? apiCardIdEntity.getPresentAddress ().toString():null),"present_address", apiCardIdEntity.getPresentAddress ())
            .eq(StringUtils.isNotBlank(apiCardIdEntity.getPostcode ()!=null && !"".equals(apiCardIdEntity.getPostcode ().toString())? apiCardIdEntity.getPostcode ().toString():null),"postcode", apiCardIdEntity.getPostcode ())
            .eq(StringUtils.isNotBlank(apiCardIdEntity.getMobilePhone ()!=null && !"".equals(apiCardIdEntity.getMobilePhone ().toString())? apiCardIdEntity.getMobilePhone ().toString():null),"mobile_phone", apiCardIdEntity.getMobilePhone ())
            .eq(StringUtils.isNotBlank(apiCardIdEntity.getGuardianName ()!=null && !"".equals(apiCardIdEntity.getGuardianName ().toString())? apiCardIdEntity.getGuardianName ().toString():null),"guardian_name", apiCardIdEntity.getGuardianName ())
            .eq(StringUtils.isNotBlank(apiCardIdEntity.getGuardianPhone ()!=null && !"".equals(apiCardIdEntity.getGuardianPhone ().toString())? apiCardIdEntity.getGuardianPhone ().toString():null),"guardian_phone", apiCardIdEntity.getGuardianPhone ())
            .eq(StringUtils.isNotBlank(apiCardIdEntity.getGuardianIdcard ()!=null && !"".equals(apiCardIdEntity.getGuardianIdcard ().toString())? apiCardIdEntity.getGuardianIdcard ().toString():null),"guardian_idcard", apiCardIdEntity.getGuardianIdcard ())
            .eq(StringUtils.isNotBlank(apiCardIdEntity.getGuardianRelation ()!=null && !"".equals(apiCardIdEntity.getGuardianRelation ().toString())? apiCardIdEntity.getGuardianRelation ().toString():null),"guardian_relation", apiCardIdEntity.getGuardianRelation ())
            .eq(StringUtils.isNotBlank(apiCardIdEntity.getBankAccount ()!=null && !"".equals(apiCardIdEntity.getBankAccount ().toString())? apiCardIdEntity.getBankAccount ().toString():null),"bank_account", apiCardIdEntity.getBankAccount ())
            .eq(StringUtils.isNotBlank(apiCardIdEntity.getBankName ()!=null && !"".equals(apiCardIdEntity.getBankName ().toString())? apiCardIdEntity.getBankName ().toString():null),"bank_name", apiCardIdEntity.getBankName ())
            .eq(StringUtils.isNotBlank(apiCardIdEntity.getDisabilityType ()!=null && !"".equals(apiCardIdEntity.getDisabilityType ().toString())? apiCardIdEntity.getDisabilityType ().toString():null),"disability_type", apiCardIdEntity.getDisabilityType ())
        );
        Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key","sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        page.getRecords().forEach( item -> {
            SysDictEntity disabilityType_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("cjlb_0000") && iii.getValue().equals(
                        item.getDisabilityType ())).findAny().orElse(null);
            if (disabilityType_sysDictEntity != null){
                item.setDisabilityType (Integer.parseInt(disabilityType_sysDictEntity.getLabel()));
            }else{
                item.setDisabilityType (null);
            }
        });
        return new PageUtils(page);
    }
    @Override
    public List<ApiCardIdEntity> queryExportData(Map<String, Object> params) {
            return apiCardIdDao.queryExportData(params);
    }

    @Override
    public ApiCardIdEntity queryByIdnum(String idnum) {
        return baseMapper.selectOne(new QueryWrapper<ApiCardIdEntity>().eq("id_card", idnum));
    }

}
