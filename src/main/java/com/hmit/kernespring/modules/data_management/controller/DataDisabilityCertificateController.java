package com.hmit.kernespring.modules.data_management.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.common.utils.FileUtils;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.app.service.APIService;
import com.hmit.kernespring.modules.cjrone.entity.CjroneDocumentEntity;
import com.hmit.kernespring.modules.cjrone.entity.CjroneSignatureEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneDocumentService;
import com.hmit.kernespring.modules.cjrone.service.CjroneSignatureService;
import com.hmit.kernespring.modules.data_management.entity.ApiCardIdEntity;
import com.hmit.kernespring.modules.data_management.entity.CardIdInfoEntity;
import com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity;
import com.hmit.kernespring.modules.data_management.entity.DeadInfoEntity;
import com.hmit.kernespring.modules.data_management.service.ApiCardIdService;
import com.hmit.kernespring.modules.data_management.service.DataDisabilityCertificateService;
import com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity;
import com.hmit.kernespring.modules.matter_application.service.CjroneWelfareMatterApplicationService;
import com.hmit.kernespring.modules.matter_application.service.DisabilityCertificateApplicationService;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import com.hmit.kernespring.modules.sys.controller.AbstractController;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;

/**
 * 残疾人汇总表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-19 10:54:28
 */
@RestController
@RequestMapping("data_management/datadisabilitycertificate")
public class DataDisabilityCertificateController extends AbstractController {
    @Autowired
    private APIService apiService;
    @Autowired
    private CjroneSignatureService cjroneSignatureService;
    @Autowired
    private ApiCardIdService apiCardIdService;
    @Autowired
    private SysDictService sysDictService;
    @Autowired
    private CjroneDocumentService cjroneDocumentService;
    @Autowired
    private CjroneWelfareMatterApplicationService cjroneWelfareMatterApplicationService;
    @Autowired
    private DataDisabilityCertificateService dataDisabilityCertificateService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;
    @Autowired
    private DisabilityCertificateApplicationService disabilityCertificateApplicationService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("data_management:datadisabilitycertificate:list")
    public R list(@RequestParam Map<String, Object> params) {
        PageUtils page = dataDisabilityCertificateService.queryPage(params);

        return R.ok().put("page", page);
    }

    /**
     * 信息
     */
    @RequestMapping("/infoDetails/{id}")
    @RequiresPermissions("data_management:datadisabilitycertificate:info")
    public R info(@PathVariable("id") Integer id){
		DataDisabilityCertificateEntity dataDisabilityCertificate = dataDisabilityCertificateService.getById(id);

        return R.ok().put("dataDisabilityCertificate", dataDisabilityCertificate);
    }

    /**
     * 残疾人汇总详情
     */
  /*  @RequestMapping("/infoDetails/{id}")
    public R infoDetails(@PathVariable("id") Integer id){
		DataDisabilityCertificateEntity dataDisabilityCertificate = dataDisabilityCertificateService.getById(id);
        DisabilityCertificateApplicationEntity disabilityCertificateApplication = disabilityCertificateApplicationService.getByIDCardN(dataDisabilityCertificate.getIdCard());
        System.out.println();
        if (dataDisabilityCertificate.getDisabilityCategoryName()!=null && dataDisabilityCertificate.getDisabilityCategoryName().indexOf("多重") != -1){
            // 多重
            List<CjroneDocumentEntity> documentEntities = new ArrayList<>();
            String disabilityCategoryName = dataDisabilityCertificate.getDisabilityCategoryName();
            List<String> result_list = Arrays.asList(disabilityCategoryName.substring(disabilityCategoryName.indexOf("(") +1,disabilityCategoryName.indexOf(")")).split("、"));
            result_list.forEach(item -> {
                // 遍历获取申请记录里面 各个类别的附件
                Map<String,Object> map = new HashMap<>();
                map.put("disabilityCategoryName",item);
                map.put("idCard",dataDisabilityCertificate.getIdCard());
                List<CjroneDocumentEntity> documentEntities_tmp = cjroneDocumentService.queryDocumentDataByMapN(map);
                documentEntities.addAll(documentEntities_tmp);
            });
            System.out.println(documentEntities);
            if (documentEntities != null && documentEntities.size()>0){
                disabilityCertificateApplication.setDocumentEntityList(documentEntities);
            }else {
                disabilityCertificateApplication.setDocumentEntityList(documentEntities);
            }
            disabilityCertificateApplication.setDisabilityTypeName(disabilityCategoryName.substring(disabilityCategoryName.indexOf("(") +1,disabilityCategoryName.indexOf(")")));
        }else if (dataDisabilityCertificate.getDisabilityCategoryName()!=null && dataDisabilityCertificate.getDisabilityCategoryName().indexOf("多重") == -1){
            // 单重
            List<CjroneDocumentEntity> documentEntities = new ArrayList<>();
            Map<String,Object> map = new HashMap<>();
            map.put("disabilityCategoryName",dataDisabilityCertificate.getDisabilityCategoryName());
            map.put("idCard",dataDisabilityCertificate.getIdCard());
            List<CjroneDocumentEntity> documentEntities_tmp = cjroneDocumentService.queryDocumentDataByMapN(map);
            documentEntities.addAll(documentEntities_tmp);
            if (documentEntities != null && documentEntities.size()>0){
                disabilityCertificateApplication.setDocumentEntityList(documentEntities);
            }else {
                disabilityCertificateApplication.setDocumentEntityList(documentEntities);
            }
        }else {
            // 没有残疾类别
        }
        Map<String, Object> params = new HashMap<>();
        params.put("idCard",disabilityCertificateApplication.getIdCard());
        Map<String,Object> result_map = cjroneWelfareMatterApplicationService.queryMattersByMap(params);
        disabilityCertificateApplication.setFamilyEconoCondition(result_map.get("familyEcho") != null ? result_map.get("familyEcho").toString(): "无");
        disabilityCertificateApplication.setHealthCareCondition(result_map.get("medicalInsurance") != null ? result_map.get("medicalInsurance").toString(): "无");

        // 残疾证信息

        Map<String,Object> nmap = new HashMap<>();
        nmap.put("id_card",disabilityCertificateApplication.getIdCard());
        nmap.put("status","4");
        List<CjroneDisabilityHospitalEntity> hospitalEntities = (List<CjroneDisabilityHospitalEntity>) cjroneDisabilityHospitalService.listByMap(nmap);
        List<Map<String,Object>> dcdisable_list = new ArrayList<>();
        if (hospitalEntities.size() >0){
            for (CjroneDisabilityHospitalEntity hospitalEntity : hospitalEntities){
                Map<String, Object> params_map = new HashMap<>();
                params_map.put("redis_key", "sys_dict:all");
                List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
                SysDictEntity disabilityCategory_sysDictEntity = sys_dict_all_list.stream().filter(
                        iii -> iii.getCode().equals("cjlb_0000") && iii.getValue().equals(
                                hospitalEntity.getDisabilityCategory())).findAny().orElse(null);
                if (disabilityCategory_sysDictEntity != null) {
                    disabilityCertificateApplication.setDisabilityCategoryName(disabilityCategory_sysDictEntity.getLabel());
                } else {
                    disabilityCertificateApplication.setDisabilityCategoryName(null);
                }
                SysDictEntity disabilityDegree_sysDictEntity = sys_dict_all_list.stream().filter(
                        iii -> iii.getCode().equals("cjdj_0000") && iii.getValue().toString().equals(
                                hospitalEntity.getDisabilityDegree())).findAny().orElse(null);
                if (disabilityDegree_sysDictEntity != null) {
                    disabilityCertificateApplication.setDisabilityDegreeName(disabilityDegree_sysDictEntity.getLabel());
                } else {
                    disabilityCertificateApplication.setDisabilityDegreeName(null);
                }
                // disabilityCertificateApplication.setDisableId(disabilityCertificateApplication.getDisableId());
                disabilityCertificateApplication.setDisabilityCategory(hospitalEntity.getDisabilityCategory());
                disabilityCertificateApplication.setDisabilityDegree(hospitalEntity.getDisabilityDegree());

                Map<String, Object> params_tmpp = new HashMap<>();
                params_tmpp.put("disableId",dataDisabilityCertificate.getDisableId());
                params_tmpp.put("disabilityCategoryName",disabilityCertificateApplication.getDisabilityCategoryName());
                params_tmpp.put("disabilityDegreeName",disabilityCertificateApplication.getDisabilityDegreeName());
                dcdisable_list.add(params_tmpp);
            }
        }
        return R.ok().put("disabilityCertificateApplication", disabilityCertificateApplication).put("dcList",dcdisable_list);
    }
*/
    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("data_management:datadisabilitycertificate:save")
    public R save(@RequestBody DataDisabilityCertificateEntity dataDisabilityCertificate){
        // 验证必填字段：残疾证号 OR 身份证号至少一个不为空
        if ((dataDisabilityCertificate.getDisableId() == null || "".equals(dataDisabilityCertificate.getDisableId())) 
            && (dataDisabilityCertificate.getIdCard() == null || "".equals(dataDisabilityCertificate.getIdCard()))) {
            return R.error("残疾证号和身份证号至少填写一个");
        }
        
        // 性别字段转换：男 -> 1, 其他 -> 0
        if ("男".equals(dataDisabilityCertificate.getSex())) {
            dataDisabilityCertificate.setSex("1");
        } else {
            dataDisabilityCertificate.setSex("0");
        }
        
        // 残疾等级字段转换：四级 -> 4, 其他 -> 0
        if ("四级".equals(dataDisabilityCertificate.getDisabilityDegree())) {
            dataDisabilityCertificate.setDisabilityDegree("4");
        } else {
            dataDisabilityCertificate.setDisabilityDegree("0");
        }
        
        // 设置默认状态
        dataDisabilityCertificate.setStatus(1);
        
        // 设置创建时间
        dataDisabilityCertificate.setCreateTime(new Date());
        //dataDisabilityCertificate.setCreateId(getUserId());
		dataDisabilityCertificateService.save(dataDisabilityCertificate);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("data_management:datadisabilitycertificate:update")
    public R update(@RequestBody DataDisabilityCertificateEntity dataDisabilityCertificate){
		dataDisabilityCertificateService.updateById(dataDisabilityCertificate);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("data_management:datadisabilitycertificate:delete")
    public R delete(@RequestBody Integer[] ids){
		dataDisabilityCertificateService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }

    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    // @RequiresPermissions("data_management:datadisabilitycertificate:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        params_import.setHeadRows(1);
        params_import.setTitleRows(0);
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<DataDisabilityCertificateEntity> dataDisabilityCertificateList = new ArrayList<>();
        System.out.println("当前导入数据残疾人汇总表条数：" + list.size());
        list.forEach(item ->{
            if (null != item.get("残疾证号") && !"".equals(item.get("残疾证号").toString()) || (null != item.get("身份证号") && !"".equals(item.get("身份证号").toString()))) {
                item.put("id",item.get(""));
                item.put("name",item.get("姓名"));
                item.put("idCard",item.get("残疾证号"));
                item.put("disableId",item.get("残疾证号"));
                item.put("sex","男".equals(item.get("性别"))?1:0);
                item.put("disabilityCategory",item.get("残疾类别"));
                item.put("disabilityDegree","四级".equals(item.get("残疾等级"))? 4:0);
                item.put("guardianName",item.get("监护人姓名"));
                item.put("guardianPhone",item.get("监护人手机"));
                item.put("familyEconoCondition",item.get("家庭经济情况"));
                item.put("medicalInsurance",item.get("医疗保险情况"));
                item.put("rehabilitationSubsidy",item.get("康复补助"));
                item.put("busCard",item.get("公交卡"));
                item.put("nursingSubsidy",item.get("护理补贴"));
                item.put("livingAllowance",item.get("生活补贴"));
                item.put("childEducationSubsidy",item.get("子女教育补贴"));
                item.put("residentPensionInsurance",item.get("城乡居民养老保险"));
                item.put("individualPensionInsurance",item.get("个体工商户养老保险"));
                item.put("status",'1');
                dataDisabilityCertificateList.add(new Gson().fromJson(new Gson().toJson(item), DataDisabilityCertificateEntity.class));
            }
        });
        // 保存到数据库
        // dataDisabilityCertificateService.saveBatch(dataDisabilityCertificateList);
        dataDisabilityCertificateList.forEach(item->{
            List<DataDisabilityCertificateEntity> is_exits= dataDisabilityCertificateService.queryListByMEntity(item);
            if (is_exits.size() != 0){
                item.setId(is_exits.get(0).getId());
            }
            //item.setCreateId(getUserId());
            item.setCreateTime(new Date());
            dataDisabilityCertificateService.saveOrUpdate(item);
        });
        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    // @RequiresPermissions("data_management:datadisabilitycertificate:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        Gson gson=new Gson();
        System.out.println("====1========="+mapArgs);
        Map<String,Object> mapparams = gson.fromJson(mapArgs.get("key")!=null ? mapArgs.get("key").toString():null, Map.class);
        System.out.println("====2========="+mapparams);
        List<DisabilityCertificateApplicationEntity> disabilityCertificateApplicationEntityList = disabilityCertificateApplicationService.queryExportData1(mapparams);
        disabilityCertificateApplicationEntityList.forEach(item ->{
            System.out.println("item-------------------->"+new Gson().toJson(item)+disabilityCertificateApplicationEntityList.size());
            if ("1".equals(item.getSex())){
                item.setSex("男");
            }else {
                item.setSex("女");
            }
            if ("1".equals(item.getMaritalStatus())){
                item.setMaritalStatus("已婚");
            }else {
                item.setMaritalStatus("未婚");
            }
            if ("1".equals(item.getApplicationType())){
                item.setApplicationType("新申请");
            } else if("2".equals(item.getApplicationType())){
                item.setApplicationType("换领申请");
            } else {
                item.setApplicationType("补办申请");
            }
            if (item.getStatus().equals('1')) {
                item.setStatus("'镇街道待审核'");
            } else if (item.getStatus().equals('2')) {
                item.setStatus("''区残联待审核''");
            } else if (item.getStatus().equals('3')) {
                item.setStatus("''医院评残''");
            } else if (item.getStatus().equals('4')) {
                item.setStatus("''退回''");
            } else if (item.getStatus().equals('5')) {
                item.setStatus("''待生成残疾证''");
            } else if (item.getStatus().equals('6')) {
                item.setStatus("''通过''");
            }

           // File file = new File(cjroneProperties.getDownloadPath()+item.getIdCard()+".jpg");
            //if(file.exists()){
                //设置电子照片
            if("2".equals(item.getDatastatus())){
                try{

                    item.setPhoto(cjroneProperties.getDownloadPath()+item.getIdCard()+".jpg");
                }
                catch (Exception ex){
                }
            }

           // }
        });
        ExportParams params = new ExportParams("残疾证申请表", null, "残疾证申请表");
        Workbook workbook = ExcelExportUtil.exportExcel(params, DisabilityCertificateApplicationEntity.class, disabilityCertificateApplicationEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "残疾证申请表" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }


    /**
     * 归档资料
     */
    @RequestMapping("/fileDatas")
    public R fileDatas(@RequestBody DataDisabilityCertificateEntity dataDisabilityCertificate){
        Map<String,Object> map = new HashMap<>();
        map.put("disability_certificate_application_id",dataDisabilityCertificate.getId());
        DisabilityCertificateApplicationEntity entity = disabilityCertificateApplicationService.getById(dataDisabilityCertificate.getId());
        List<CjroneDocumentEntity> documentEntities = cjroneDocumentService.queryDocumentDataByMap(map);

        List<String> doc_list = new ArrayList<>();
        documentEntities.forEach(item ->{
            doc_list.add(item.getFilePathAct());
        });
       // 获取残疾证申请表 医院评定表 通过id
        map.put("applyId",dataDisabilityCertificate.getId());
        List<CjroneSignatureEntity> signatureEntityList = cjroneSignatureService.queryDataByMap(map);
        signatureEntityList.forEach(item ->{
            doc_list.add(item.getFileActUrl());
        });
        String filePath = cjroneProperties.getDownloadZipPath()+"fileDatas/" + entity.getName() + "_" + entity.getId();
        FileUtils.Pdf(doc_list, filePath);//filePaths为存储位置
        return R.ok().put("fileUrl",filePath);
    }



}
