package com.hmit.kernespring.modules.data_management.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 省公安身份证信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-13 15:39:30
 */
@Data
@TableName("api_card_id")
public class ApiCardIdEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 *
	 */

	@TableId
	@ApiModelProperty(value="")
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
	private Integer id;
	/**
	 * 残疾人姓名
	 */
	@ApiModelProperty(value="残疾人姓名")
	@Excel(name = "残疾人姓名", height = 20, width = 30, isImportField = "true_st")
	private String name;
	/**
	 * 性别  1 男  2女
	 */
	@ApiModelProperty(value="性别  1 男  2女")
	@Excel(name = "性别  1 男  2女", height = 20, width = 30, isImportField = "true_st")
	private String sex;
	/**
	 * 民族
	 */
	@ApiModelProperty(value="民族")
	@Excel(name = "民族", height = 20, width = 30, isImportField = "true_st")
	private String nationality;
	/**
	 * 出生日期
	 */
	@ApiModelProperty(value="出生日期")
	@Excel(name = "出生日期", height = 20, width = 30, isImportField = "true_st")
	private String birthday;
	/**
	 * 籍贯
	 */
	@ApiModelProperty(value="籍贯")
	@Excel(name = "籍贯", height = 20, width = 30, isImportField = "true_st")
	private String nativePlace;
	/**
	 * 身份证号码
	 */
	@ApiModelProperty(value="身份证号码")
	@Excel(name = "身份证号码", height = 20, width = 30, isImportField = "true_st")
	private String idCard;
	/**
	 * 户籍地址
	 */
	@ApiModelProperty(value="户籍地址")
	@Excel(name = "户籍地址", height = 20, width = 30, isImportField = "true_st")
	private String nativeAddress;
	/**
	 * 二寸照
	 */
	@ApiModelProperty(value="二寸照")
	@Excel(name = "二寸照", height = 20, width = 30, isImportField = "true_st")
	private String photo;
	/**
	 * 发证机构
	 */
	@ApiModelProperty(value="发证机构")
	@Excel(name = "发证机构", height = 20, width = 30, isImportField = "true_st")
	private String qfjg;
	/**
	 * 机构编号
	 */
	@ApiModelProperty(value="机构编号")
	@Excel(name = "机构编号", height = 20, width = 30, isImportField = "true_st")
	private String jgbh;
	/**
	 * 婚姻情况  0未婚  1已婚
	 */
	@ApiModelProperty(value="婚姻情况  0未婚  1已婚")
	@Excel(name = "婚姻情况  0未婚  1已婚", height = 20, width = 30, isImportField = "true_st")
	private String maritalStatus;
	/**
	 * 文化程度
	 */
	@ApiModelProperty(value="文化程度")
	@Excel(name = "文化程度", height = 20, width = 30, isImportField = "true_st")
	private String educationDegree;
	/**
	 * 户籍地址  镇
	 */
	@ApiModelProperty(value="户籍地址  镇")
	@Excel(name = "户籍地址  镇", height = 20, width = 30, isImportField = "true_st")
	private String nativeZhen;
	/**
	 * 户籍地址 村
	 */
	@ApiModelProperty(value="户籍地址 村")
	@Excel(name = "户籍地址 村", height = 20, width = 30, isImportField = "true_st")
	private String nativeCun;
	/**
	 * 现住址 镇
	 */
	@ApiModelProperty(value="现住址 镇")
	@Excel(name = "现住址 镇", height = 20, width = 30, isImportField = "true_st")
	private String presentZhen;
	/**
	 * 现地址  村
	 */
	@ApiModelProperty(value="现地址  村")
	@Excel(name = "现地址  村", height = 20, width = 30, isImportField = "true_st")
	private String presentCun;
	/**
	 * 现地址
	 */
	@ApiModelProperty(value="现地址")
	@Excel(name = "现地址", height = 20, width = 30, isImportField = "true_st")
	private String presentAddress;
	/**
	 * 邮编
	 */
	@ApiModelProperty(value="邮编")
	@Excel(name = "邮编", height = 20, width = 30, isImportField = "true_st")
	private String postcode;
	/**
	 * 联系电话
	 */
	@ApiModelProperty(value="联系电话")
	@Excel(name = "联系电话", height = 20, width = 30, isImportField = "true_st")
	private String mobilePhone;
	/**
	 * 监护人姓名
	 */
	@ApiModelProperty(value="监护人姓名")
	@Excel(name = "监护人姓名", height = 20, width = 30, isImportField = "true_st")
	private String guardianName;
	/**
	 * 监护人手机
	 */
	@ApiModelProperty(value="监护人手机")
	@Excel(name = "监护人手机", height = 20, width = 30, isImportField = "true_st")
	private String guardianPhone;
	/**
	 * 监护人身份证号
	 */
	@ApiModelProperty(value="监护人身份证号")
	@Excel(name = "监护人身份证号", height = 20, width = 30, isImportField = "true_st")
	private String guardianIdcard;
	/**
	 * 与申请人关系
	 */
	@ApiModelProperty(value="与申请人关系")
	@Excel(name = "与申请人关系", height = 20, width = 30, isImportField = "true_st")
	private String guardianRelation;
	/**
	 * 银行账户
	 */
	@ApiModelProperty(value="银行账户")
	@Excel(name = "银行账户", height = 20, width = 30, isImportField = "true_st")
	private String bankAccount;
	/**
	 * 开户银行
	 */
	@ApiModelProperty(value="开户银行")
	@Excel(name = "开户银行", height = 20, width = 30, isImportField = "true_st")
	private String bankName;
	/**
	 * 残疾类别
	 */
	@ApiModelProperty(value="残疾类别")
	@Excel(name = "残疾类别", height = 20, width = 30, isImportField = "true_st")
	private Integer disabilityType;

	@TableField(exist = false)
	private String shengName;
	@TableField(exist = false)
	private String shiName;
	@TableField(exist = false)
	private String quName;
	@TableField(exist = false)
	private String zhenName;
	@TableField(exist = false)
	private String cunName;

}
