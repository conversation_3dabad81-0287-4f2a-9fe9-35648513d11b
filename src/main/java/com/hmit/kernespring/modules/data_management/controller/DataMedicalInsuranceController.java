package com.hmit.kernespring.modules.data_management.controller;

import java.io.File;
import java.io.IOException;
import java.util.*;

import java.io.OutputStream;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import org.apache.poi.ss.usermodel.Workbook;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hmit.kernespring.modules.data_management.entity.DataMedicalInsuranceEntity;
import com.hmit.kernespring.modules.data_management.service.DataMedicalInsuranceService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 医保参保导入表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-27 14:00:03
 */
@RestController
@RequestMapping("data_management/datamedicalinsurance")
public class DataMedicalInsuranceController {
    @Autowired
    private DataMedicalInsuranceService dataMedicalInsuranceService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("data_management:datamedicalinsurance:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = dataMedicalInsuranceService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("data_management:datamedicalinsurance:info")
    public R info(@PathVariable("id") Integer id){
		DataMedicalInsuranceEntity dataMedicalInsurance = dataMedicalInsuranceService.getById(id);

        return R.ok().put("dataMedicalInsurance", dataMedicalInsurance);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("data_management:datamedicalinsurance:save")
    public R save(@RequestBody DataMedicalInsuranceEntity dataMedicalInsurance){
		dataMedicalInsuranceService.save(dataMedicalInsurance);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("data_management:datamedicalinsurance:update")
    public R update(@RequestBody DataMedicalInsuranceEntity dataMedicalInsurance){
		dataMedicalInsuranceService.updateById(dataMedicalInsurance);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("data_management:datamedicalinsurance:delete")
    public R delete(@RequestBody Integer[] ids){
		dataMedicalInsuranceService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    // @RequiresPermissions("data_management:datamedicalinsurance:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<DataMedicalInsuranceEntity> dataMedicalInsuranceList = new ArrayList<>();
        list.forEach(item ->{
                    item.put("id",item.get(""));
                    item.put("name",item.get("姓名"));
                    item.put("idCard",item.get("身份证号"));
                    item.put("insuredSituation",item.get("医保参保情况"));
                    item.put("insuredTime",item.get("参保时间"));
                    dataMedicalInsuranceList.add(new Gson().fromJson(new Gson().toJson(item), DataMedicalInsuranceEntity.class));
        });
        // 保存到数据库
        dataMedicalInsuranceService.saveBatch(dataMedicalInsuranceList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    // @RequiresPermissions("data_management:datamedicalinsurance:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<DataMedicalInsuranceEntity> dataMedicalInsuranceEntityList = dataMedicalInsuranceService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("医保参保导入表", null, "医保参保导入表");
        Workbook workbook = ExcelExportUtil.exportExcel(params, DataMedicalInsuranceEntity.class, dataMedicalInsuranceEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "医保参保导入表" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

}
