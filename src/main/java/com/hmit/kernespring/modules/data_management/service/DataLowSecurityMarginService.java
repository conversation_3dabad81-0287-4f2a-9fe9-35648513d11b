package com.hmit.kernespring.modules.data_management.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.data_management.entity.DataLowSecurityMarginEntity;

import java.util.Collection;
import java.util.Map;

import java.util.List;

/**
 * 家庭经济情况-低保边缘信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-19 09:01:24
 */
public interface DataLowSecurityMarginService extends IService<DataLowSecurityMarginEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<DataLowSecurityMarginEntity> queryExportData(Map<String, Object> params);

    @Override
     boolean saveBatch(Collection<DataLowSecurityMarginEntity> entityList) ;
}

