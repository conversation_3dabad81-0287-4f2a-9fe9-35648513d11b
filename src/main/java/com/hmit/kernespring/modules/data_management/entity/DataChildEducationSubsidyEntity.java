package com.hmit.kernespring.modules.data_management.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 残疾人子女教育补贴导入表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-27 13:58:44
 */
@Data
@TableName("data_child_education_subsidy")
public class DataChildEducationSubsidyEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
@TableId
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 残疾人姓名
	 */
	@Excel(name = "学生姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 身份证号码
	 */
	@Excel(name = "身份证号码", height = 20, width = 30, isImportField = "true_st")
private String idCard;
	/**
	 * 性别
	 */
	@Excel(name = "性别", height = 20, width = 30, isImportField = "true_st")
private String sex;
	/**
	 * 联系电话
	 */
	@Excel(name = "联系电话", height = 20, width = 30, isImportField = "true_st")
private String mobilePhone;
	/**
	 * 家庭住址
	 */
	@Excel(name = "家庭住址", height = 20, width = 30, isImportField = "true_st")
private String presentAddress;
	/**
	 * 残疾证号
	 */
	@Excel(name = "残疾证号", height = 20, width = 30, isImportField = "true_st")
private String disableId;
	/**
	 * 家庭经济情况
	 */
	@Excel(name = "家庭经济情况", height = 20, width = 30, isImportField = "true_st")
private String familyEconoCondition;
	/**
	 * 就读学校
	 */
	@Excel(name = "就读学校", height = 20, width = 30, isImportField = "true_st")
private String currentSchool;
	/**
	 * 年级
	 */
	@Excel(name = "年级", height = 20, width = 30, isImportField = "true_st")
private String grade;
	/**
	 * 入学时间
	 */
	@Excel(name = "入学时间", height = 20, width = 30, isImportField = "true_st")
private String admissionTime;
	/**
	 * 申请人类型
	 */
	@Excel(name = "申请人类型", height = 20, width = 30, isImportField = "true_st")
private String applicantType;
	/**
	 * 申请时间
	 */
	@Excel(name = "申请时间", height = 20, width = 30, isImportField = "true_st")
private String applicationTime;
	/**
	 * 父亲姓名
	 */
	@Excel(name = "父亲姓名", height = 20, width = 30, isImportField = "true_st")
private String fatherName;
	/**
	 * 父亲身份证
	 */
	@Excel(name = "父亲身份证", height = 20, width = 30, isImportField = "true_st")
private String fatherIdcard;
	/**
	 * 母亲姓名
	 */
	@Excel(name = "母亲姓名", height = 20, width = 30, isImportField = "true_st")
private String motherName;
	/**
	 * 母亲身份证
	 */
	@Excel(name = "母亲身份证", height = 20, width = 30, isImportField = "true_st")
private String motherIdcard;

/**
	 * 补贴金额
	 */
	@Excel(name = "补贴金额", height = 20, width = 30, isImportField = "true_st")
private String subsidyMoney;

	/*
	* * 学历
	 */
	@Excel(name = "学历", height = 20, width = 30, isImportField = "true_st")
	private String education;


	//@Excel(name = "户籍验证结果", height = 20, width = 30, isImportField = "true_st")
	private String isHuJiRation;
	private String status;
	private String signStatus;


}
