package com.hmit.kernespring.modules.data_management.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 困境儿童
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-19 09:08:58
 */
@Data
@TableName("data_distressed_child")
public class DataDistressedChildEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
@TableId
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 登记日期
	 */
	@Excel(name = "登记日期", height = 20, width = 30, isImportField = "true_st")
private Date registrationDate;
	/**
	 * 人员姓名
	 */
	@Excel(name = "人员姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 身份证号
	 */
	@Excel(name = "身份证号", height = 20, width = 30, isImportField = "true_st")
private String idCard;
	/**
	 * 性别
	 */
	@Excel(name = "性别", height = 20, width = 30, isImportField = "true_st")
private String sex;
	/**
	 * 出生日期
	 */
	@Excel(name = "出生日期", height = 20, width = 30, isImportField = "true_st")
private Date birthday;
	/**
	 * 困境儿童类别
	 */
	@Excel(name = "困境儿童类别", height = 20, width = 30, isImportField = "true_st")
private String distressedChildrenCategory;
	/**
	 * 户籍地
	 */
	@Excel(name = "户籍地", height = 20, width = 30, isImportField = "true_st")
private String householdRegistration;
	/**
	 * 儿童现住址：镇（街道）、村（社区）
	 */
	@Excel(name = "儿童现住址：镇（街道）、村（社区）", height = 20, width = 30, isImportField = "true_st")
private String currentAddress;
	/**
	 * 家庭情况
	 */
	@Excel(name = "家庭情况", height = 20, width = 30, isImportField = "true_st")
private String familySituation;
	/**
	 * 儿童本人奉化农商银行帐号
	 */
	@Excel(name = "儿童本人奉化农商银行帐号", height = 20, width = 30, isImportField = "true_st")
private String bankAccount;
	/**
	 * 监护人姓名
	 */
	@Excel(name = "监护人姓名", height = 20, width = 30, isImportField = "true_st")
private String guardianName;
	/**
	 * 监护人身份证
	 */
	@Excel(name = "监护人身份证", height = 20, width = 30, isImportField = "true_st")
private String guardianIdCard;
	/**
	 * 监护人联系电话
	 */
	@Excel(name = "监护人联系电话", height = 20, width = 30, isImportField = "true_st")
private String guardianPhone;
	/**
	 * 监护人关系
	 */
	@Excel(name = "监护人关系", height = 20, width = 30, isImportField = "true_st")
private String guardianRelationship;
	/**
	 * 是否同住
	 */
	@Excel(name = "是否同住", height = 20, width = 30, isImportField = "true_st")
private String livingTogether;
	/**
	 * 备注
	 */
	@Excel(name = "备注", height = 20, width = 30, isImportField = "true_st")
private String remark;
	/**
	 * 
	 */
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Long createId;
	/**
	 * 创建时间
	 */
	@Excel(name = "创建时间", height = 20, width = 30, isImportField = "true_st")
private Date createTime;

}
