package com.hmit.kernespring.modules.data_management.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity;

import java.util.Map;

import java.util.List;

/**
 * 残疾人汇总表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-19 10:54:28
 */
public interface DataDisabilityCertificateService extends IService<DataDisabilityCertificateEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<DataDisabilityCertificateEntity> queryExportData(Map<String, Object> params);
    void updateOthersDisableIdByMap(Map<String, Object> params);
    List<DataDisabilityCertificateEntity> queryListByMEntity(DataDisabilityCertificateEntity dataDisabilityCertificateEntity);
    DataDisabilityCertificateEntity getByIDCard(String idCard);


}

