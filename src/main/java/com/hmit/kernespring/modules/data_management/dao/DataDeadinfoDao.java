package com.hmit.kernespring.modules.data_management.dao;

import com.hmit.kernespring.modules.data_management.entity.DataDeadinfoEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 死亡名单导入信息表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-07 10:23:14
 */
@Mapper
public interface DataDeadinfoDao extends BaseMapper<DataDeadinfoEntity> {
    List<DataDeadinfoEntity> queryExportData(Map<String, Object> params);
	
}
