package com.hmit.kernespring.modules.data_management.dao;

import com.hmit.kernespring.modules.data_management.entity.DataChildEducationSubsidyEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 残疾人子女教育补贴导入表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-27 13:58:44
 */
@Mapper
public interface DataChildEducationSubsidyDao extends BaseMapper<DataChildEducationSubsidyEntity> {
    List<DataChildEducationSubsidyEntity> queryExportData(Map<String, Object> params);
	
}
