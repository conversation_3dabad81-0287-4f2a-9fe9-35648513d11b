package com.hmit.kernespring.modules.data_management.service.impl;


import com.google.gson.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.data_management.dao.YanglaobxDao;
import com.hmit.kernespring.modules.data_management.entity.YanglaobxEntity;
import com.hmit.kernespring.modules.data_management.service.YanglaobxService;


@Service("yanglaobxService")
public class YanglaobxServiceImpl extends ServiceImpl<YanglaobxDao, YanglaobxEntity> implements YanglaobxService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private YanglaobxDao yanglaobxDao;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        YanglaobxEntity yanglaobxEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, YanglaobxEntity.class);
        IPage<YanglaobxEntity> page = this.page(
                new Query<YanglaobxEntity>().getPage(params),
                new QueryWrapper<YanglaobxEntity>()
            .eq(StringUtils.isNotBlank(yanglaobxEntity.getId ()!=null && !"".equals(yanglaobxEntity.getId ().toString())? yanglaobxEntity.getId ().toString():null),"id", yanglaobxEntity.getId ())
            .eq(StringUtils.isNotBlank(yanglaobxEntity.getName ()!=null && !"".equals(yanglaobxEntity.getName ().toString())? yanglaobxEntity.getName ().toString():null),"name", yanglaobxEntity.getName ())
            .eq(StringUtils.isNotBlank(yanglaobxEntity.getIdCard ()!=null && !"".equals(yanglaobxEntity.getIdCard ().toString())? yanglaobxEntity.getIdCard ().toString():null),"id_card", yanglaobxEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(yanglaobxEntity.getType ()!=null && !"".equals(yanglaobxEntity.getType ().toString())? yanglaobxEntity.getType ().toString():null),"type", yanglaobxEntity.getType ())
            .eq(StringUtils.isNotBlank(yanglaobxEntity.getRemark ()!=null && !"".equals(yanglaobxEntity.getRemark ().toString())? yanglaobxEntity.getRemark ().toString():null),"remark", yanglaobxEntity.getRemark ())
        );
        return new PageUtils(page);
    }
    @Override
    public List<YanglaobxEntity> queryExportData(Map<String, Object> params) {
            return yanglaobxDao.queryExportData(params);
    }

}