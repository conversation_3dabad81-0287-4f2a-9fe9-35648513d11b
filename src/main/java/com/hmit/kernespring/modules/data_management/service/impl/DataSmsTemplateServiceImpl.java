package com.hmit.kernespring.modules.data_management.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;
import com.hmit.kernespring.modules.data_management.dao.DataSmsTemplateDao;
import com.hmit.kernespring.modules.data_management.entity.DataSmsTemplateEntity;
import com.hmit.kernespring.modules.data_management.service.DataSmsTemplateService;
import com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity;
import com.hmit.kernespring.modules.matter_application.service.DisabilityCertificateApplicationService;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service("dataSmsTemplateService")
public class DataSmsTemplateServiceImpl extends ServiceImpl<DataSmsTemplateDao, DataSmsTemplateEntity> implements DataSmsTemplateService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private DisabilityCertificateApplicationService disabilityCertificateApplicationService;
    @Autowired
    private DataSmsTemplateDao dataSmsTemplateDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        DataSmsTemplateEntity dataSmsTemplateEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, DataSmsTemplateEntity.class);
        IPage<DataSmsTemplateEntity> page = this.page(
                new Query<DataSmsTemplateEntity>().getPage(params),
                new QueryWrapper<DataSmsTemplateEntity>()
            .eq(StringUtils.isNotBlank(dataSmsTemplateEntity.getId ()!=null && !"".equals(dataSmsTemplateEntity.getId ().toString())? dataSmsTemplateEntity.getId ().toString():null),"id", dataSmsTemplateEntity.getId ())
            .eq(StringUtils.isNotBlank(dataSmsTemplateEntity.getType ()!=null && !"".equals(dataSmsTemplateEntity.getType ().toString())? dataSmsTemplateEntity.getType ().toString():null),"type", dataSmsTemplateEntity.getType ())
            .eq(StringUtils.isNotBlank(dataSmsTemplateEntity.getTime ()!=null && !"".equals(dataSmsTemplateEntity.getTime ().toString())? dataSmsTemplateEntity.getTime ().toString():null),"time", dataSmsTemplateEntity.getTime ())
            .eq(StringUtils.isNotBlank(dataSmsTemplateEntity.getAddress ()!=null && !"".equals(dataSmsTemplateEntity.getAddress ().toString())? dataSmsTemplateEntity.getAddress ().toString():null),"address", dataSmsTemplateEntity.getAddress ())
            .eq(StringUtils.isNotBlank(dataSmsTemplateEntity.getMaterial ()!=null && !"".equals(dataSmsTemplateEntity.getMaterial ().toString())? dataSmsTemplateEntity.getMaterial ().toString():null),"material", dataSmsTemplateEntity.getMaterial ())
            .eq(StringUtils.isNotBlank(dataSmsTemplateEntity.getPrecautions ()!=null && !"".equals(dataSmsTemplateEntity.getPrecautions ().toString())? dataSmsTemplateEntity.getPrecautions ().toString():null),"precautions", dataSmsTemplateEntity.getPrecautions ())
            .eq(StringUtils.isNotBlank(dataSmsTemplateEntity.getStatus ()!=null && !"".equals(dataSmsTemplateEntity.getStatus ().toString())? dataSmsTemplateEntity.getStatus ().toString():null),"status", dataSmsTemplateEntity.getStatus ())
        );

        if(params.get("id")!=null){
            DisabilityCertificateApplicationEntity entity = disabilityCertificateApplicationService.getById(params.get("id").toString());
            if(entity!=null){
                // 获得医院评残的数据从而获得评残时间
                Map<String, Object> map = new HashMap<>();
                map.put("applyId",entity.getId());

                page.getRecords().forEach(item->{
                    if(item.getType()==10){
                        //替换短信内容 -- 领取残疾证 -- 替换镇街道
                        item.setAddress(item.getAddress().replace("**",entity.getNativeZhenName()));
                    }
                    else{

                    }
                });
            }
        }

        return new PageUtils(page);
    }
    @Override
    public List<DataSmsTemplateEntity> queryExportData(Map<String, Object> params) {
            return dataSmsTemplateDao.queryExportData(params);
    }

}