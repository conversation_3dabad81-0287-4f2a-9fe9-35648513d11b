package com.hmit.kernespring.modules.data_management.entity;

public class CardIdInfoEntity {


    /**
     * ELC_LICENCE_NAME : 中华人民共和国居民身份证
     * ELC_LICENCE_DEPT : 省公安厅
     * ELC_LICENCE_CODE : ddceb6c779ce428bb1902143303f57dc
     * ELC_LICENCE_FILE : {"URL":"","SIGN_CERT":"","SIGN_VALUE":"","TSA":""}
     * ELC_LICENCE_STRUCT : {"SIGN_CERT":"04776F1E9EDF7AD603D7BCA1D5391A62304D590A984657BC47D94D2A5DDC3F5ABA39CFC55E645C76FB1F16BC9CE356AD5825894D753B807BD91C35294A4C1D25B5","SIGN_VALUE":"kciWAo3LaWAgPsv1x4SBAYYeDNS/Zi0DsMBYLE+vQAi71gP5eQY7e/En06UmT/PsSoyKzeq3NEmbWuT3Y8exXA==","TSA":"","DATA":{"CZRKYXQXJZRQ":"20350721","CZRKCSRQ":"19820814","CZRKQFJG":"奉化市公安局","CZRKXM":"蒋仲侠","CZRKGMSFHM":"330206198208141710","CZRKMZ":"汉族","CZRKYXQXQSRQ":"20150721","CZRKXB":"男","CZRKZZ":"浙江省奉化市岳林街道龙津尚都１１幢６０３室"}}
     */

    private String ELC_LICENCE_NAME;
    private String ELC_LICENCE_DEPT;
    private String ELC_LICENCE_CODE;
    private ELCLICENCEFILEBean ELC_LICENCE_FILE;
    private ELCLICENCESTRUCTBean ELC_LICENCE_STRUCT;

    public String getELC_LICENCE_NAME() {
        return ELC_LICENCE_NAME;
    }

    public void setELC_LICENCE_NAME(String ELC_LICENCE_NAME) {
        this.ELC_LICENCE_NAME = ELC_LICENCE_NAME;
    }

    public String getELC_LICENCE_DEPT() {
        return ELC_LICENCE_DEPT;
    }

    public void setELC_LICENCE_DEPT(String ELC_LICENCE_DEPT) {
        this.ELC_LICENCE_DEPT = ELC_LICENCE_DEPT;
    }

    public String getELC_LICENCE_CODE() {
        return ELC_LICENCE_CODE;
    }

    public void setELC_LICENCE_CODE(String ELC_LICENCE_CODE) {
        this.ELC_LICENCE_CODE = ELC_LICENCE_CODE;
    }

    public ELCLICENCEFILEBean getELC_LICENCE_FILE() {
        return ELC_LICENCE_FILE;
    }

    public void setELC_LICENCE_FILE(ELCLICENCEFILEBean ELC_LICENCE_FILE) {
        this.ELC_LICENCE_FILE = ELC_LICENCE_FILE;
    }

    public ELCLICENCESTRUCTBean getELC_LICENCE_STRUCT() {
        return ELC_LICENCE_STRUCT;
    }

    public void setELC_LICENCE_STRUCT(ELCLICENCESTRUCTBean ELC_LICENCE_STRUCT) {
        this.ELC_LICENCE_STRUCT = ELC_LICENCE_STRUCT;
    }

    public static class ELCLICENCEFILEBean {
        /**
         * URL :
         * SIGN_CERT :
         * SIGN_VALUE :
         * TSA :
         */

        private String URL;
        private String SIGN_CERT;
        private String SIGN_VALUE;
        private String TSA;

        public String getURL() {
            return URL;
        }

        public void setURL(String URL) {
            this.URL = URL;
        }

        public String getSIGN_CERT() {
            return SIGN_CERT;
        }

        public void setSIGN_CERT(String SIGN_CERT) {
            this.SIGN_CERT = SIGN_CERT;
        }

        public String getSIGN_VALUE() {
            return SIGN_VALUE;
        }

        public void setSIGN_VALUE(String SIGN_VALUE) {
            this.SIGN_VALUE = SIGN_VALUE;
        }

        public String getTSA() {
            return TSA;
        }

        public void setTSA(String TSA) {
            this.TSA = TSA;
        }
    }

    public static class ELCLICENCESTRUCTBean {
        /**
         * SIGN_CERT : 04776F1E9EDF7AD603D7BCA1D5391A62304D590A984657BC47D94D2A5DDC3F5ABA39CFC55E645C76FB1F16BC9CE356AD5825894D753B807BD91C35294A4C1D25B5
         * SIGN_VALUE : kciWAo3LaWAgPsv1x4SBAYYeDNS/Zi0DsMBYLE+vQAi71gP5eQY7e/En06UmT/PsSoyKzeq3NEmbWuT3Y8exXA==
         * TSA :
         * DATA : {"CZRKYXQXJZRQ":"20350721","CZRKCSRQ":"19820814","CZRKQFJG":"奉化市公安局","CZRKXM":"蒋仲侠","CZRKGMSFHM":"330206198208141710","CZRKMZ":"汉族","CZRKYXQXQSRQ":"20150721","CZRKXB":"男","CZRKZZ":"浙江省奉化市岳林街道龙津尚都１１幢６０３室"}
         */

        private String SIGN_CERT;
        private String SIGN_VALUE;
        private String TSA;
        private DATABean DATA;

        public String getSIGN_CERT() {
            return SIGN_CERT;
        }

        public void setSIGN_CERT(String SIGN_CERT) {
            this.SIGN_CERT = SIGN_CERT;
        }

        public String getSIGN_VALUE() {
            return SIGN_VALUE;
        }

        public void setSIGN_VALUE(String SIGN_VALUE) {
            this.SIGN_VALUE = SIGN_VALUE;
        }

        public String getTSA() {
            return TSA;
        }

        public void setTSA(String TSA) {
            this.TSA = TSA;
        }

        public DATABean getDATA() {
            return DATA;
        }

        public void setDATA(DATABean DATA) {
            this.DATA = DATA;
        }

        public static class DATABean {
            /**
             * CZRKYXQXJZRQ : 20350721
             * CZRKCSRQ : 19820814
             * CZRKQFJG : 奉化市公安局
             * CZRKXM : 蒋仲侠
             * CZRKGMSFHM : 330206198208141710
             * CZRKMZ : 汉族
             * CZRKYXQXQSRQ : 20150721
             * CZRKXB : 男
             * CZRKZZ : 浙江省奉化市岳林街道龙津尚都１１幢６０３室
             */

            private String CZRKYXQXJZRQ;
            private String CZRKCSRQ;
            private String CZRKQFJG;
            private String CZRKXM;
            private String CZRKGMSFHM;
            private String CZRKMZ;
            private String CZRKYXQXQSRQ;
            private String CZRKXB;
            private String CZRKZZ;

            public String getCZRKYXQXJZRQ() {
                return CZRKYXQXJZRQ;
            }

            public void setCZRKYXQXJZRQ(String CZRKYXQXJZRQ) {
                this.CZRKYXQXJZRQ = CZRKYXQXJZRQ;
            }

            public String getCZRKCSRQ() {
                return CZRKCSRQ;
            }

            public void setCZRKCSRQ(String CZRKCSRQ) {
                this.CZRKCSRQ = CZRKCSRQ;
            }

            public String getCZRKQFJG() {
                return CZRKQFJG;
            }

            public void setCZRKQFJG(String CZRKQFJG) {
                this.CZRKQFJG = CZRKQFJG;
            }

            public String getCZRKXM() {
                return CZRKXM;
            }

            public void setCZRKXM(String CZRKXM) {
                this.CZRKXM = CZRKXM;
            }

            public String getCZRKGMSFHM() {
                return CZRKGMSFHM;
            }

            public void setCZRKGMSFHM(String CZRKGMSFHM) {
                this.CZRKGMSFHM = CZRKGMSFHM;
            }

            public String getCZRKMZ() {
                return CZRKMZ;
            }

            public void setCZRKMZ(String CZRKMZ) {
                this.CZRKMZ = CZRKMZ;
            }

            public String getCZRKYXQXQSRQ() {
                return CZRKYXQXQSRQ;
            }

            public void setCZRKYXQXQSRQ(String CZRKYXQXQSRQ) {
                this.CZRKYXQXQSRQ = CZRKYXQXQSRQ;
            }

            public String getCZRKXB() {
                return CZRKXB;
            }

            public void setCZRKXB(String CZRKXB) {
                this.CZRKXB = CZRKXB;
            }

            public String getCZRKZZ() {
                return CZRKZZ;
            }

            public void setCZRKZZ(String CZRKZZ) {
                this.CZRKZZ = CZRKZZ;
            }
        }
    }
}
