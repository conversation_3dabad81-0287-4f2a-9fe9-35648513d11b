package com.hmit.kernespring.modules.data_management.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.data_management.entity.DataSmsTemplateEntity;

import java.util.Map;

import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-27 15:39:33
 */
public interface DataSmsTemplateService extends IService<DataSmsTemplateEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<DataSmsTemplateEntity> queryExportData(Map<String, Object> params);
}

