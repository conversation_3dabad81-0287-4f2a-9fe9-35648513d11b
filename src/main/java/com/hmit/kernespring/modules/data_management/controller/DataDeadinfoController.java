package com.hmit.kernespring.modules.data_management.controller;

import java.io.File;
import java.io.IOException;
import java.util.*;

import java.io.OutputStream;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import org.apache.poi.ss.usermodel.Workbook;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hmit.kernespring.modules.data_management.entity.DataDeadinfoEntity;
import com.hmit.kernespring.modules.data_management.service.DataDeadinfoService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 死亡名单导入信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-07 10:23:14
 */
@RestController
@RequestMapping("data_management/datadeadinfo")
public class DataDeadinfoController {
    @Autowired
    private DataDeadinfoService dataDeadinfoService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("data_management:datadeadinfo:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = dataDeadinfoService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("data_management:datadeadinfo:info")
    public R info(@PathVariable("id") Integer id){
		DataDeadinfoEntity dataDeadinfo = dataDeadinfoService.getById(id);

        return R.ok().put("dataDeadinfo", dataDeadinfo);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("data_management:datadeadinfo:save")
    public R save(@RequestBody DataDeadinfoEntity dataDeadinfo){
		dataDeadinfoService.save(dataDeadinfo);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("data_management:datadeadinfo:update")
    public R update(@RequestBody DataDeadinfoEntity dataDeadinfo){
		dataDeadinfoService.updateById(dataDeadinfo);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("data_management:datadeadinfo:delete")
    public R delete(@RequestBody Integer[] ids){
		dataDeadinfoService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    // @RequiresPermissions("data_management:datadeadinfo:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<DataDeadinfoEntity> dataDeadinfoList = new ArrayList<>();
        list.forEach(item ->{
                    item.put("id",item.get(""));
                    item.put("businessNumber",item.get("业务编号"));
                    item.put("name",item.get("死者姓名"));
                    item.put("sex",item.get("性别"));
                    item.put("age",item.get("年龄"));
                    item.put("birthplace",item.get("籍贯"));
                    item.put("idCard",item.get("身份证号"));
                    item.put("residenceAddress",item.get("户籍地址"));
                    item.put("deadDate",item.get("死亡日期"));
                    item.put("cremationDate",item.get("火化日期"));
                    item.put("getDate",item.get("接尸日期"));
                    item.put("getAddress",item.get("接尸地址"));
                    item.put("familyName",item.get("家属姓名"));
                    item.put("relation",item.get("与死者关系"));
                    item.put("phone",item.get("联系电话"));
                    dataDeadinfoList.add(new Gson().fromJson(new Gson().toJson(item), DataDeadinfoEntity.class));
        });
        // 保存到数据库
        dataDeadinfoService.saveBatch(dataDeadinfoList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    // @RequiresPermissions("data_management:datadeadinfo:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<DataDeadinfoEntity> dataDeadinfoEntityList = dataDeadinfoService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("死亡名单导入信息表", null, "死亡名单导入信息表");
        Workbook workbook = ExcelExportUtil.exportExcel(params, DataDeadinfoEntity.class, dataDeadinfoEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "死亡名单导入信息表" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

}
