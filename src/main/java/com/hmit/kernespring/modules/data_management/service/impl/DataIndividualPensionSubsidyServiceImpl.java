package com.hmit.kernespring.modules.data_management.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.data_management.dao.DataIndividualPensionSubsidyDao;
import com.hmit.kernespring.modules.data_management.entity.DataIndividualPensionSubsidyEntity;
import com.hmit.kernespring.modules.data_management.service.DataIndividualPensionSubsidyService;


@Service("dataIndividualPensionSubsidyService")
public class DataIndividualPensionSubsidyServiceImpl extends ServiceImpl<DataIndividualPensionSubsidyDao, DataIndividualPensionSubsidyEntity> implements DataIndividualPensionSubsidyService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private DataIndividualPensionSubsidyDao dataIndividualPensionSubsidyDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        DataIndividualPensionSubsidyEntity dataIndividualPensionSubsidyEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, DataIndividualPensionSubsidyEntity.class);
        IPage<DataIndividualPensionSubsidyEntity> page = this.page(
                new Query<DataIndividualPensionSubsidyEntity>().getPage(params),
                new QueryWrapper<DataIndividualPensionSubsidyEntity>()
            .eq(StringUtils.isNotBlank(dataIndividualPensionSubsidyEntity.getId ()!=null && !"".equals(dataIndividualPensionSubsidyEntity.getId ().toString())? dataIndividualPensionSubsidyEntity.getId ().toString():null),"id", dataIndividualPensionSubsidyEntity.getId ())
            .eq(StringUtils.isNotBlank(dataIndividualPensionSubsidyEntity.getName ()!=null && !"".equals(dataIndividualPensionSubsidyEntity.getName ().toString())? dataIndividualPensionSubsidyEntity.getName ().toString():null),"name", dataIndividualPensionSubsidyEntity.getName ())
            .eq(StringUtils.isNotBlank(dataIndividualPensionSubsidyEntity.getIdCard ()!=null && !"".equals(dataIndividualPensionSubsidyEntity.getIdCard ().toString())? dataIndividualPensionSubsidyEntity.getIdCard ().toString():null),"id_card", dataIndividualPensionSubsidyEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(dataIndividualPensionSubsidyEntity.getIsInsurance ()!=null && !"".equals(dataIndividualPensionSubsidyEntity.getIsInsurance ().toString())? dataIndividualPensionSubsidyEntity.getIsInsurance ().toString():null),"is_insurance", dataIndividualPensionSubsidyEntity.getIsInsurance ())
            .eq(StringUtils.isNotBlank(dataIndividualPensionSubsidyEntity.getInsuranceMonth ()!=null && !"".equals(dataIndividualPensionSubsidyEntity.getInsuranceMonth ().toString())? dataIndividualPensionSubsidyEntity.getInsuranceMonth ().toString():null),"insurance_month", dataIndividualPensionSubsidyEntity.getInsuranceMonth ())
        );
        Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key","sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        page.getRecords().forEach( item -> {
        });
        return new PageUtils(page);
    }
    @Override
    public List<DataIndividualPensionSubsidyEntity> queryExportData(Map<String, Object> params) {
            return dataIndividualPensionSubsidyDao.queryExportData(params);
    }

}