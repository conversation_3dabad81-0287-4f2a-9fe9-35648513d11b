package com.hmit.kernespring.modules.data_management.dao;

import com.hmit.kernespring.modules.data_management.entity.DataChildEducationSubsidyMoneyEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-14 17:09:45
 */
@Mapper
public interface DataChildEducationSubsidyMoneyDao extends BaseMapper<DataChildEducationSubsidyMoneyEntity> {
    List<DataChildEducationSubsidyMoneyEntity> queryExportData(Map<String, Object> params);

    List<DataChildEducationSubsidyMoneyEntity> queryByMap(Map<String, Object> params);
}
