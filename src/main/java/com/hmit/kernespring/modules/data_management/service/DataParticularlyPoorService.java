package com.hmit.kernespring.modules.data_management.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.data_management.entity.DataParticularlyPoorEntity;

import java.util.Collection;
import java.util.Map;

import java.util.List;

/**
 * 特困人员名单表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-19 09:01:24
 */
public interface DataParticularlyPoorService extends IService<DataParticularlyPoorEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<DataParticularlyPoorEntity> queryExportData(Map<String, Object> params);

    @Override
     boolean saveBatch(Collection<DataParticularlyPoorEntity> entityList);

}

