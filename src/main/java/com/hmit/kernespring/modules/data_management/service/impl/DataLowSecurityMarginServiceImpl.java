package com.hmit.kernespring.modules.data_management.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;
import com.hmit.kernespring.modules.data_management.dao.DataLowSecurityMarginDao;
import com.hmit.kernespring.modules.data_management.entity.DataLowSecurityMarginEntity;
import com.hmit.kernespring.modules.data_management.service.DataLowSecurityMarginService;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Type;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service("dataLowSecurityMarginService")
public class DataLowSecurityMarginServiceImpl extends ServiceImpl<DataLowSecurityMarginDao, DataLowSecurityMarginEntity> implements DataLowSecurityMarginService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private DataLowSecurityMarginDao dataLowSecurityMarginDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        DataLowSecurityMarginEntity dataLowSecurityMarginEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, DataLowSecurityMarginEntity.class);
        IPage<DataLowSecurityMarginEntity> page = this.page(
                new Query<DataLowSecurityMarginEntity>().getPage(params),
                new QueryWrapper<DataLowSecurityMarginEntity>()
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getId ()!=null && !"".equals(dataLowSecurityMarginEntity.getId ().toString())? dataLowSecurityMarginEntity.getId ().toString():null),"id", dataLowSecurityMarginEntity.getId ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getAdministrativeDivision ()!=null && !"".equals(dataLowSecurityMarginEntity.getAdministrativeDivision ().toString())? dataLowSecurityMarginEntity.getAdministrativeDivision ().toString():null),"administrative_division", dataLowSecurityMarginEntity.getAdministrativeDivision ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getApplicant ()!=null && !"".equals(dataLowSecurityMarginEntity.getApplicant ().toString())? dataLowSecurityMarginEntity.getApplicant ().toString():null),"applicant", dataLowSecurityMarginEntity.getApplicant ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getApplyIdCard ()!=null && !"".equals(dataLowSecurityMarginEntity.getApplyIdCard ().toString())? dataLowSecurityMarginEntity.getApplyIdCard ().toString():null),"apply_id_card", dataLowSecurityMarginEntity.getApplyIdCard ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getApplicantAccountNature ()!=null && !"".equals(dataLowSecurityMarginEntity.getApplicantAccountNature ().toString())? dataLowSecurityMarginEntity.getApplicantAccountNature ().toString():null),"applicant_account_nature", dataLowSecurityMarginEntity.getApplicantAccountNature ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getTelephone ()!=null && !"".equals(dataLowSecurityMarginEntity.getTelephone ().toString())? dataLowSecurityMarginEntity.getTelephone ().toString():null),"telephone", dataLowSecurityMarginEntity.getTelephone ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getMobilePhone ()!=null && !"".equals(dataLowSecurityMarginEntity.getMobilePhone ().toString())? dataLowSecurityMarginEntity.getMobilePhone ().toString():null),"mobile_phone", dataLowSecurityMarginEntity.getMobilePhone ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getCauseOfPoverty ()!=null && !"".equals(dataLowSecurityMarginEntity.getCauseOfPoverty ().toString())? dataLowSecurityMarginEntity.getCauseOfPoverty ().toString():null),"cause_of_poverty", dataLowSecurityMarginEntity.getCauseOfPoverty ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getResidenceAddress ()!=null && !"".equals(dataLowSecurityMarginEntity.getResidenceAddress ().toString())? dataLowSecurityMarginEntity.getResidenceAddress ().toString():null),"residence_address", dataLowSecurityMarginEntity.getResidenceAddress ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getLowSideCategory ()!=null && !"".equals(dataLowSecurityMarginEntity.getLowSideCategory ().toString())? dataLowSecurityMarginEntity.getLowSideCategory ().toString():null),"low_side_category", dataLowSecurityMarginEntity.getLowSideCategory ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getResidentialAddress ()!=null && !"".equals(dataLowSecurityMarginEntity.getResidentialAddress ().toString())? dataLowSecurityMarginEntity.getResidentialAddress ().toString():null),"residential_address", dataLowSecurityMarginEntity.getResidentialAddress ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getReasonForApplying ()!=null && !"".equals(dataLowSecurityMarginEntity.getReasonForApplying ().toString())? dataLowSecurityMarginEntity.getReasonForApplying ().toString():null),"reason_for_applying", dataLowSecurityMarginEntity.getReasonForApplying ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getApplicationCategory ()!=null && !"".equals(dataLowSecurityMarginEntity.getApplicationCategory ().toString())? dataLowSecurityMarginEntity.getApplicationCategory ().toString():null),"application_category", dataLowSecurityMarginEntity.getApplicationCategory ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getBankName ()!=null && !"".equals(dataLowSecurityMarginEntity.getBankName ().toString())? dataLowSecurityMarginEntity.getBankName ().toString():null),"bank_name", dataLowSecurityMarginEntity.getBankName ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getAccountHolder ()!=null && !"".equals(dataLowSecurityMarginEntity.getAccountHolder ().toString())? dataLowSecurityMarginEntity.getAccountHolder ().toString():null),"account_holder", dataLowSecurityMarginEntity.getAccountHolder ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getAccountHolderIdCard ()!=null && !"".equals(dataLowSecurityMarginEntity.getAccountHolderIdCard ().toString())? dataLowSecurityMarginEntity.getAccountHolderIdCard ().toString():null),"account_holder_id_card", dataLowSecurityMarginEntity.getAccountHolderIdCard ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getBankAccount ()!=null && !"".equals(dataLowSecurityMarginEntity.getBankAccount ().toString())? dataLowSecurityMarginEntity.getBankAccount ().toString():null),"bank_account", dataLowSecurityMarginEntity.getBankAccount ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getCumulativeMonth ()!=null && !"".equals(dataLowSecurityMarginEntity.getCumulativeMonth ().toString())? dataLowSecurityMarginEntity.getCumulativeMonth ().toString():null),"cumulative_month", dataLowSecurityMarginEntity.getCumulativeMonth ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getCumulativeIncome ()!=null && !"".equals(dataLowSecurityMarginEntity.getCumulativeIncome ().toString())? dataLowSecurityMarginEntity.getCumulativeIncome ().toString():null),"cumulative_income", dataLowSecurityMarginEntity.getCumulativeIncome ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getCumulativeExpenditure ()!=null && !"".equals(dataLowSecurityMarginEntity.getCumulativeExpenditure ().toString())? dataLowSecurityMarginEntity.getCumulativeExpenditure ().toString():null),"cumulative_expenditure", dataLowSecurityMarginEntity.getCumulativeExpenditure ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getMonthlyPerCapitaIncome ()!=null && !"".equals(dataLowSecurityMarginEntity.getMonthlyPerCapitaIncome ().toString())? dataLowSecurityMarginEntity.getMonthlyPerCapitaIncome ().toString():null),"monthly_per_capita_income", dataLowSecurityMarginEntity.getMonthlyPerCapitaIncome ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getInclusionOfRescueStandards ()!=null && !"".equals(dataLowSecurityMarginEntity.getInclusionOfRescueStandards ().toString())? dataLowSecurityMarginEntity.getInclusionOfRescueStandards ().toString():null),"inclusion_of_rescue_standards", dataLowSecurityMarginEntity.getInclusionOfRescueStandards ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getTotalFamilyPopulation ()!=null && !"".equals(dataLowSecurityMarginEntity.getTotalFamilyPopulation ().toString())? dataLowSecurityMarginEntity.getTotalFamilyPopulation ().toString():null),"total_family_population", dataLowSecurityMarginEntity.getTotalFamilyPopulation ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getGuaranteeTotalPopulation ()!=null && !"".equals(dataLowSecurityMarginEntity.getGuaranteeTotalPopulation ().toString())? dataLowSecurityMarginEntity.getGuaranteeTotalPopulation ().toString():null),"guarantee_total_population", dataLowSecurityMarginEntity.getGuaranteeTotalPopulation ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getGeneralNumberOfPeople ()!=null && !"".equals(dataLowSecurityMarginEntity.getGeneralNumberOfPeople ().toString())? dataLowSecurityMarginEntity.getGeneralNumberOfPeople ().toString():null),"general_number_of_people", dataLowSecurityMarginEntity.getGeneralNumberOfPeople ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getLivingAllowance ()!=null && !"".equals(dataLowSecurityMarginEntity.getLivingAllowance ().toString())? dataLowSecurityMarginEntity.getLivingAllowance ().toString():null),"living_allowance", dataLowSecurityMarginEntity.getLivingAllowance ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getOtherSubsidies ()!=null && !"".equals(dataLowSecurityMarginEntity.getOtherSubsidies ().toString())? dataLowSecurityMarginEntity.getOtherSubsidies ().toString():null),"other_subsidies", dataLowSecurityMarginEntity.getOtherSubsidies ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getTotalAmountOfProtection ()!=null && !"".equals(dataLowSecurityMarginEntity.getTotalAmountOfProtection ().toString())? dataLowSecurityMarginEntity.getTotalAmountOfProtection ().toString():null),"total_amount_of_protection", dataLowSecurityMarginEntity.getTotalAmountOfProtection ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getDateOfSalvage ()!=null && !"".equals(dataLowSecurityMarginEntity.getDateOfSalvage ().toString())? dataLowSecurityMarginEntity.getDateOfSalvage ().toString():null),"date_of_salvage", dataLowSecurityMarginEntity.getDateOfSalvage ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getRelationshipWithHousehold ()!=null && !"".equals(dataLowSecurityMarginEntity.getRelationshipWithHousehold ().toString())? dataLowSecurityMarginEntity.getRelationshipWithHousehold ().toString():null),"relationship_with_household", dataLowSecurityMarginEntity.getRelationshipWithHousehold ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getName ()!=null && !"".equals(dataLowSecurityMarginEntity.getName ().toString())? dataLowSecurityMarginEntity.getName ().toString():null),"name", dataLowSecurityMarginEntity.getName ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getIdCard ()!=null && !"".equals(dataLowSecurityMarginEntity.getIdCard ().toString())? dataLowSecurityMarginEntity.getIdCard ().toString():null),"id_card", dataLowSecurityMarginEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getSex ()!=null && !"".equals(dataLowSecurityMarginEntity.getSex ().toString())? dataLowSecurityMarginEntity.getSex ().toString():null),"sex", dataLowSecurityMarginEntity.getSex ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getBirthday ()!=null && !"".equals(dataLowSecurityMarginEntity.getBirthday ().toString())? dataLowSecurityMarginEntity.getBirthday ().toString():null),"birthday", dataLowSecurityMarginEntity.getBirthday ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getAge ()!=null && !"".equals(dataLowSecurityMarginEntity.getAge ().toString())? dataLowSecurityMarginEntity.getAge ().toString():null),"age", dataLowSecurityMarginEntity.getAge ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getRescueCertificateNumber ()!=null && !"".equals(dataLowSecurityMarginEntity.getRescueCertificateNumber ().toString())? dataLowSecurityMarginEntity.getRescueCertificateNumber ().toString():null),"rescue_certificate_number", dataLowSecurityMarginEntity.getRescueCertificateNumber ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getWhetherToEnjoy ()!=null && !"".equals(dataLowSecurityMarginEntity.getWhetherToEnjoy ().toString())? dataLowSecurityMarginEntity.getWhetherToEnjoy ().toString():null),"whether_to_enjoy", dataLowSecurityMarginEntity.getWhetherToEnjoy ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getMemberAccountNature ()!=null && !"".equals(dataLowSecurityMarginEntity.getMemberAccountNature ().toString())? dataLowSecurityMarginEntity.getMemberAccountNature ().toString():null),"member_account_nature", dataLowSecurityMarginEntity.getMemberAccountNature ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getPersonnelCategory ()!=null && !"".equals(dataLowSecurityMarginEntity.getPersonnelCategory ().toString())? dataLowSecurityMarginEntity.getPersonnelCategory ().toString():null),"personnel_category", dataLowSecurityMarginEntity.getPersonnelCategory ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getHealthStatus ()!=null && !"".equals(dataLowSecurityMarginEntity.getHealthStatus ().toString())? dataLowSecurityMarginEntity.getHealthStatus ().toString():null),"health_status", dataLowSecurityMarginEntity.getHealthStatus ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getMaritalStatus ()!=null && !"".equals(dataLowSecurityMarginEntity.getMaritalStatus ().toString())? dataLowSecurityMarginEntity.getMaritalStatus ().toString():null),"marital_status", dataLowSecurityMarginEntity.getMaritalStatus ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getNationality ()!=null && !"".equals(dataLowSecurityMarginEntity.getNationality ().toString())? dataLowSecurityMarginEntity.getNationality ().toString():null),"nationality", dataLowSecurityMarginEntity.getNationality ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getCareerStatus ()!=null && !"".equals(dataLowSecurityMarginEntity.getCareerStatus ().toString())? dataLowSecurityMarginEntity.getCareerStatus ().toString():null),"career_status", dataLowSecurityMarginEntity.getCareerStatus ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getPoliticalStatus ()!=null && !"".equals(dataLowSecurityMarginEntity.getPoliticalStatus ().toString())? dataLowSecurityMarginEntity.getPoliticalStatus ().toString():null),"political_status", dataLowSecurityMarginEntity.getPoliticalStatus ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getSpecificGuaranteeObject ()!=null && !"".equals(dataLowSecurityMarginEntity.getSpecificGuaranteeObject ().toString())? dataLowSecurityMarginEntity.getSpecificGuaranteeObject ().toString():null),"specific_guarantee_object", dataLowSecurityMarginEntity.getSpecificGuaranteeObject ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getDisabilityCategory ()!=null && !"".equals(dataLowSecurityMarginEntity.getDisabilityCategory ().toString())? dataLowSecurityMarginEntity.getDisabilityCategory ().toString():null),"disability_category", dataLowSecurityMarginEntity.getDisabilityCategory ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getDisabilityLevel ()!=null && !"".equals(dataLowSecurityMarginEntity.getDisabilityLevel ().toString())? dataLowSecurityMarginEntity.getDisabilityLevel ().toString():null),"disability_level", dataLowSecurityMarginEntity.getDisabilityLevel ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getDisableId ()!=null && !"".equals(dataLowSecurityMarginEntity.getDisableId ().toString())? dataLowSecurityMarginEntity.getDisableId ().toString():null),"disable_id", dataLowSecurityMarginEntity.getDisableId ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getPersonBankAccount ()!=null && !"".equals(dataLowSecurityMarginEntity.getPersonBankAccount ().toString())? dataLowSecurityMarginEntity.getPersonBankAccount ().toString():null),"person_bank_account", dataLowSecurityMarginEntity.getPersonBankAccount ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getBankNameTwo ()!=null && !"".equals(dataLowSecurityMarginEntity.getBankNameTwo ().toString())? dataLowSecurityMarginEntity.getBankNameTwo ().toString():null),"bank_name_two", dataLowSecurityMarginEntity.getBankNameTwo ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getMonthlyGuaranteeAmount ()!=null && !"".equals(dataLowSecurityMarginEntity.getMonthlyGuaranteeAmount ().toString())? dataLowSecurityMarginEntity.getMonthlyGuaranteeAmount ().toString():null),"monthly_guarantee_amount", dataLowSecurityMarginEntity.getMonthlyGuaranteeAmount ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getEducationalLevel ()!=null && !"".equals(dataLowSecurityMarginEntity.getEducationalLevel ().toString())? dataLowSecurityMarginEntity.getEducationalLevel ().toString():null),"educational_level", dataLowSecurityMarginEntity.getEducationalLevel ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getLaborAbility ()!=null && !"".equals(dataLowSecurityMarginEntity.getLaborAbility ().toString())? dataLowSecurityMarginEntity.getLaborAbility ().toString():null),"labor_ability", dataLowSecurityMarginEntity.getLaborAbility ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getWorkUnit ()!=null && !"".equals(dataLowSecurityMarginEntity.getWorkUnit ().toString())? dataLowSecurityMarginEntity.getWorkUnit ().toString():null),"work_unit", dataLowSecurityMarginEntity.getWorkUnit ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getSevereDisabilitiesNum ()!=null && !"".equals(dataLowSecurityMarginEntity.getSevereDisabilitiesNum ().toString())? dataLowSecurityMarginEntity.getSevereDisabilitiesNum ().toString():null),"severe_disabilities_num", dataLowSecurityMarginEntity.getSevereDisabilitiesNum ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getCreateId ()!=null && !"".equals(dataLowSecurityMarginEntity.getCreateId ().toString())? dataLowSecurityMarginEntity.getCreateId ().toString():null),"create_id", dataLowSecurityMarginEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(dataLowSecurityMarginEntity.getCreateTime ()!=null && !"".equals(dataLowSecurityMarginEntity.getCreateTime ().toString())? dataLowSecurityMarginEntity.getCreateTime ().toString():null),"create_time", dataLowSecurityMarginEntity.getCreateTime ())
        );
        Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key","sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        page.getRecords().forEach( item -> {
            SysDictEntity lowSideCategory_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("dblb_0000") && iii.getValue().equals(
                        item.getLowSideCategory ())).findAny().orElse(null);
            if (lowSideCategory_sysDictEntity != null){
                item.setLowSideCategory (lowSideCategory_sysDictEntity.getLabel());
            }else{
                item.setLowSideCategory (null);
            }
            SysDictEntity applicationCategory_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("sqlb_0000") && iii.getValue().equals(
                        item.getApplicationCategory ())).findAny().orElse(null);
            if (applicationCategory_sysDictEntity != null){
                item.setApplicationCategory (applicationCategory_sysDictEntity.getLabel());
            }else{
                item.setApplicationCategory (null);
            }
            SysDictEntity personnelCategory_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("rylb_0000") && iii.getValue().equals(
                        item.getPersonnelCategory ())).findAny().orElse(null);
            if (personnelCategory_sysDictEntity != null){
                item.setPersonnelCategory (personnelCategory_sysDictEntity.getLabel());
            }else{
                item.setPersonnelCategory (null);
            }
            SysDictEntity disabilityCategory_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("cjlb_0000") && iii.getValue().equals(
                        item.getDisabilityCategory ())).findAny().orElse(null);
            if (disabilityCategory_sysDictEntity != null){
                item.setDisabilityCategory (disabilityCategory_sysDictEntity.getLabel());
            }else{
                item.setDisabilityCategory (null);
            }
            SysDictEntity disabilityLevel_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("cjdj_0000") && iii.getValue().equals(
                        item.getDisabilityLevel ())).findAny().orElse(null);
            if (disabilityLevel_sysDictEntity != null){
                item.setDisabilityLevel (disabilityLevel_sysDictEntity.getLabel());
            }else{
                item.setDisabilityLevel (null);
            }
        });
        return new PageUtils(page);
    }
    @Override
    public List<DataLowSecurityMarginEntity> queryExportData(Map<String, Object> params) {
            return dataLowSecurityMarginDao.queryExportData(params);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatch(Collection<DataLowSecurityMarginEntity> entityList) {

        dataLowSecurityMarginDao.deleteAllData(null);
        return super.saveBatch(entityList);
    }

}