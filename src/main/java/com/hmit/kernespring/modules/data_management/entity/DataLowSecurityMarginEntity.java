package com.hmit.kernespring.modules.data_management.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 家庭经济情况-低保边缘信息
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-19 09:01:24
 */
@Data
@TableName("data_low_security_margin")
public class DataLowSecurityMarginEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
@TableId
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 行政划分
	 */
	@Excel(name = "行政划分", height = 20, width = 30, isImportField = "true_st")
private String administrativeDivision;
	/**
	 * 申请人
	 */
	@Excel(name = "申请人", height = 20, width = 30, isImportField = "true_st")
private String applicant;
	/**
	 * 申请身份证
	 */
	@Excel(name = "申请身份证", height = 20, width = 30, isImportField = "true_st")
private String applyIdCard;
	/**
	 * 申请人户口性质 
	 */
	@Excel(name = "申请人户口性质 ", height = 20, width = 30, isImportField = "true_st")
private String applicantAccountNature;
	/**
	 * 联系电话 
	 */
	@Excel(name = "联系电话 ", height = 20, width = 30, isImportField = "true_st")
private String telephone;
	/**
	 * 手机号
	 */
	@Excel(name = "手机号", height = 20, width = 30, isImportField = "true_st")
private String mobilePhone;
	/**
	 * 致贫原因 
	 */
	@Excel(name = "致贫原因 ", height = 20, width = 30, isImportField = "true_st")
private String causeOfPoverty;
	/**
	 * 户籍地址
	 */
	@Excel(name = "户籍地址", height = 20, width = 30, isImportField = "true_st")
private String residenceAddress;
	/**
	 *  低边类别 
	 */
	@Excel(name = " 低边类别 ", height = 20, width = 30, isImportField = "true_st")
private String lowSideCategory;
	/**
	 *  居住地址
	 */
	@Excel(name = " 居住地址", height = 20, width = 30, isImportField = "true_st")
private String residentialAddress;
	/**
	 *  申请救助原因 
	 */
	@Excel(name = " 申请救助原因 ", height = 20, width = 30, isImportField = "true_st")
private String reasonForApplying;
	/**
	 *  申请类别
	 */
	@Excel(name = " 申请类别", height = 20, width = 30, isImportField = "true_st")
private String applicationCategory;
	/**
	 * 开户银行
	 */
	@Excel(name = "开户银行", height = 20, width = 30, isImportField = "true_st")
private String bankName;
	/**
	 * 开户人 
	 */
	@Excel(name = "开户人 ", height = 20, width = 30, isImportField = "true_st")
private String accountHolder;
	/**
	 * 开户人身份证 
	 */
	@Excel(name = "开户人身份证 ", height = 20, width = 30, isImportField = "true_st")
private String accountHolderIdCard;
	/**
	 * 银行账户
	 */
	@Excel(name = "银行账户", height = 20, width = 30, isImportField = "true_st")
private String bankAccount;
	/**
	 * 累计月份
	 */
	@Excel(name = "累计月份", height = 20, width = 30, isImportField = "true_st")
private String cumulativeMonth;
	/**
	 * 累计收入
	 */
	@Excel(name = "累计收入", height = 20, width = 30, isImportField = "true_st")
private String cumulativeIncome;
	/**
	 * 累计支出
	 */
	@Excel(name = "累计支出", height = 20, width = 30, isImportField = "true_st")
private String cumulativeExpenditure;
	/**
	 * 月人均收入 
	 */
	@Excel(name = "月人均收入 ", height = 20, width = 30, isImportField = "true_st")
private String monthlyPerCapitaIncome;
	/**
	 * 纳入救助标准 
	 */
	@Excel(name = "纳入救助标准 ", height = 20, width = 30, isImportField = "true_st")
private String inclusionOfRescueStandards;
	/**
	 * 家庭总人口 
	 */
	@Excel(name = "家庭总人口 ", height = 20, width = 30, isImportField = "true_st")
private String totalFamilyPopulation;
	/**
	 *  保障总人口
	 */
	@Excel(name = " 保障总人口", height = 20, width = 30, isImportField = "true_st")
private String guaranteeTotalPopulation;
	/**
	 * 一般残人数 
	 */
	@Excel(name = "一般残人数 ", height = 20, width = 30, isImportField = "true_st")
private String generalNumberOfPeople;
	/**
	 * 生活补助金 
	 */
	@Excel(name = "生活补助金 ", height = 20, width = 30, isImportField = "true_st")
private BigDecimal livingAllowance;
	/**
	 * 其他补助金 
	 */
	@Excel(name = "其他补助金 ", height = 20, width = 30, isImportField = "true_st")
private BigDecimal otherSubsidies;
	/**
	 * 保障总金额
	 */
	@Excel(name = "保障总金额", height = 20, width = 30, isImportField = "true_st")
private String totalAmountOfProtection;
	/**
	 *  救助日期
	 */
	@Excel(name = " 救助日期", height = 20, width = 30, isImportField = "true_st")
private Date dateOfSalvage;
	/**
	 * 与户主关系
	 */
	@Excel(name = "与户主关系", height = 20, width = 30, isImportField = "true_st")
private String relationshipWithHousehold;
	/**
	 * 人员姓名
	 */
	@Excel(name = "人员姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 身份证号
	 */
	@Excel(name = "身份证号", height = 20, width = 30, isImportField = "true_st")
private String idCard;
	/**
	 * 性别
	 */
	@Excel(name = "性别", height = 20, width = 30, isImportField = "true_st")
private Integer sex;
	/**
	 * 出生日期
	 */
	@Excel(name = "出生日期", height = 20, width = 30, isImportField = "true_st")
private Date birthday;
	/**
	 * 年龄
	 */
	@Excel(name = "年龄", height = 20, width = 30, isImportField = "true_st")
private Integer age;
	/**
	 * 救助证编号 
	 */
	@Excel(name = "救助证编号 ", height = 20, width = 30, isImportField = "true_st")
private String rescueCertificateNumber;
	/**
	 * 是否享受
	 */
	@Excel(name = "是否享受", height = 20, width = 30, isImportField = "true_st")
private String whetherToEnjoy;
	/**
	 * 成员户口性质
	 */
	@Excel(name = "成员户口性质", height = 20, width = 30, isImportField = "true_st")
private String memberAccountNature;
	/**
	 * 人员类别 
	 */
	@Excel(name = "人员类别 ", height = 20, width = 30, isImportField = "true_st")
private String personnelCategory;
	/**
	 * 健康状况
	 */
	@Excel(name = "健康状况", height = 20, width = 30, isImportField = "true_st")
private String healthStatus;
	/**
	 *  婚姻状况
	 */
	@Excel(name = " 婚姻状况", height = 20, width = 30, isImportField = "true_st")
private String maritalStatus;
	/**
	 * 民族 
	 */
	@Excel(name = "民族 ", height = 20, width = 30, isImportField = "true_st")
private String nationality;
	/**
	 *  职业状况
	 */
	@Excel(name = " 职业状况", height = 20, width = 30, isImportField = "true_st")
private String careerStatus;
	/**
	 *  政治面貌 
	 */
	@Excel(name = " 政治面貌 ", height = 20, width = 30, isImportField = "true_st")
private String politicalStatus;
	/**
	 *  特定是否保障对象 
	 */
	@Excel(name = " 特定是否保障对象 ", height = 20, width = 30, isImportField = "true_st")
private String specificGuaranteeObject;
	/**
	 * 残疾类别
	 */
	@Excel(name = "残疾类别", height = 20, width = 30, isImportField = "true_st")
private String disabilityCategory;
	/**
	 *  残疾等级 
	 */
	@Excel(name = " 残疾等级 ", height = 20, width = 30, isImportField = "true_st")
private String disabilityLevel;
	/**
	 * 残疾证号
	 */
	@Excel(name = "残疾证号", height = 20, width = 30, isImportField = "true_st")
private String disableId;
	/**
	 * 人员银行账号 
	 */
	@Excel(name = "人员银行账号 ", height = 20, width = 30, isImportField = "true_st")
private String personBankAccount;
	/**
	 * 开户银行
	 */
	@Excel(name = "开户银行", height = 20, width = 30, isImportField = "true_st")
private String bankNameTwo;
	/**
	 * 月保障金额 
	 */
	@Excel(name = "月保障金额 ", height = 20, width = 30, isImportField = "true_st")
private BigDecimal monthlyGuaranteeAmount;
	/**
	 * 文化程度
	 */
	@Excel(name = "文化程度", height = 20, width = 30, isImportField = "true_st")
private String educationalLevel;
	/**
	 * 劳动能力
	 */
	@Excel(name = "劳动能力", height = 20, width = 30, isImportField = "true_st")
private String laborAbility;
	/**
	 * 工作学习单位 
	 */
	@Excel(name = "工作学习单位 ", height = 20, width = 30, isImportField = "true_st")
private String workUnit;
	/**
	 * 重残保障人数
	 */
	@Excel(name = "重残保障人数", height = 20, width = 30, isImportField = "true_st")
private Integer severeDisabilitiesNum;
	/**
	 * 
	 */
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Long createId;
	/**
	 * 创建时间
	 */
	@Excel(name = "创建时间", height = 20, width = 30, isImportField = "true_st")
private Date createTime;

}
