package com.hmit.kernespring.modules.data_management.controller;

import java.io.File;
import java.io.IOException;
import java.util.*;

import java.io.OutputStream;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.modules.sys.controller.AbstractController;
import org.apache.poi.ss.usermodel.Workbook;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hmit.kernespring.modules.data_management.entity.FxinfoEntity;
import com.hmit.kernespring.modules.data_management.service.FxinfoService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 服刑数据信息

 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-12-18 16:37:13
 */
@RestController
@RequestMapping("data_management/fxinfo")
public class FxinfoController extends AbstractController {
    @Autowired
    private FxinfoService fxinfoService;


    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("data_management:fxinfo:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = fxinfoService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("data_management:fxinfo:info")
    public R info(@PathVariable("id") Integer id){
		FxinfoEntity fxinfo = fxinfoService.getById(id);

        return R.ok().put("fxinfo", fxinfo);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("data_management:fxinfo:save")
    public R save(@RequestBody FxinfoEntity fxinfo){

		fxinfoService.save(fxinfo);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("data_management:fxinfo:update")
    public R update(@RequestBody FxinfoEntity fxinfo){
		fxinfoService.updateById(fxinfo);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("data_management:fxinfo:delete")
    public R delete(@RequestBody Integer[] ids){
		fxinfoService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    @RequiresPermissions("data_management:fxinfo:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path =""; //bxProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        // 此处的headRows、titleRows 依据导入表格具体的数据行决定
        params_import.setHeadRows(2);
        params_import.setTitleRows(1);
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<FxinfoEntity> fxinfoList = new ArrayList<>();
        System.out.println("当前导入数据服刑数据信息 条数：" + list.size());
        list.forEach(item ->{
            if (null != item.get("身份证") && !"".equals(item.get("身份证").toString()) || (null != item.get("身份证号") && !"".equals(item.get("身份证号").toString()))) {
                item.put("id",item.get(""));
                item.put("name",item.get("姓名"));
                item.put("sex",item.get("性别"));
                item.put("age",item.get("年龄"));
                item.put("birthplace",item.get("籍贯"));
                item.put("idCard",item.get("身份证号"));
                item.put("fromInfo",item.get("数据来源接口"));
                item.put("resultInfo",item.get("接口返回的json数据"));
                fxinfoList.add(new Gson().fromJson(new Gson().toJson(item), FxinfoEntity.class));
            }
        });
        // 保存到数据库
        fxinfoService.saveBatch(fxinfoList);



        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    @RequiresPermissions("data_management:fxinfo:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<FxinfoEntity> fxinfoEntityList = fxinfoService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("服刑数据信息 ", null, "服刑数据信息 ");
        Workbook workbook = ExcelExportUtil.exportExcel(params, FxinfoEntity.class, fxinfoEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "服刑数据信息 " ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

}
