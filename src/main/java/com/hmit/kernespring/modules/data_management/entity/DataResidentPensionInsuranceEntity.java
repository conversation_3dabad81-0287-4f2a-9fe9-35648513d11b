package com.hmit.kernespring.modules.data_management.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 城乡居民养老保险补贴汇总表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-30 10:44:01
 */
@Data
@TableName("data_resident_pension_insurance")
public class DataResidentPensionInsuranceEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
@TableId
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 姓名
	 */
	@Excel(name = "姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 身份证号
	 */
	@Excel(name = "身份证号", height = 20, width = 30, isImportField = "true_st")
private String idCard;
	/**
	 * 项目名称
	 */
	@Excel(name = "项目名称", height = 20, width = 30, isImportField = "true_st")
private String projectName;
	/**
	 * 补贴金额
	 */
	@Excel(name = "补贴金额", height = 20, width = 30, isImportField = "true_st")
private Double subsidy;
	/**
	 * 联系电话
	 */
	@Excel(name = "联系电话", height = 20, width = 30, isImportField = "true_st")
private String mobilePhone;
	/**
	 * 地址
	 */
	@Excel(name = "地址", height = 20, width = 30, isImportField = "true_st")
private String address;
	/**
	 * 状态
	 */
	@Excel(name = "状态", height = 20, width = 30, isImportField = "true_st")
private String status;
	/**
	 * 创建人编号
	 */
	@Excel(name = "创建人编号", height = 20, width = 30, isImportField = "true_st")
private Integer createId;
	/**
	 * 创建时间
	 */
	@Excel(name = "创建时间", height = 20, width = 30, isImportField = "true_st")
private Date createTime;

}
