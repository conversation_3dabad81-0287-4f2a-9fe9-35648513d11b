package com.hmit.kernespring.modules.data_management.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 *
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-09-15 11:13:17
 */
@Data
@TableName("data_yanglaobx")
public class YanglaobxEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 *
	 */

@TableId
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 姓名
	 */
@Excel(name = "姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 身份证
	 */
@Excel(name = "身份证", height = 20, width = 30, isImportField = "true_st")
private String idCard;
	/**
	 * 类型
	 */
@Excel(name = "类型", height = 20, width = 30, isImportField = "true_st")
private String type;
	/**
	 * 备注
	 */
@Excel(name = "备注", height = 20, width = 30, isImportField = "true_st")
private String remark;

}
