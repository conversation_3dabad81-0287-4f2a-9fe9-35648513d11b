package com.hmit.kernespring.modules.data_management.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hmit.kernespring.modules.data_management.entity.DisabilityCertificateSyncDataEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 残疾人对比数据
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-11 13:41:25
 */
@Mapper
public interface DisabilityCertificateSyncDataDao extends BaseMapper<DisabilityCertificateSyncDataEntity> {
    List<DisabilityCertificateSyncDataEntity> queryExportData(Map<String, Object> params);
    DisabilityCertificateSyncDataEntity queryStaticsData(Map<String, Object> params);
    int queryNursingYcDataTotalByMap(Map<String, Object> params);
    List<DisabilityCertificateSyncDataEntity> queryNursingYcDataByMap(Map<String, Object> params);
    int queryLivingYcDataTotalByMap(Map<String, Object> params);
    List<DisabilityCertificateSyncDataEntity> queryLivingYcDataByMap(Map<String, Object> params);
    int queryStaticsLivingData(Map<String, Object> params);
    int queryStaticsNursingData(Map<String, Object> params);
}
