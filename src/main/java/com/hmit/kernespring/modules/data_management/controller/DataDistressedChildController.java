package com.hmit.kernespring.modules.data_management.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.data_management.entity.DataDistressedChildEntity;
import com.hmit.kernespring.modules.data_management.service.DataDistressedChildService;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import com.hmit.kernespring.modules.sys.controller.AbstractController;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;

/**
 * 困境儿童
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-19 09:08:58
 */
@RestController
@RequestMapping("data_management/datadistressedchild")
public class DataDistressedChildController extends AbstractController {
    @Autowired
    private DataDistressedChildService dataDistressedChildService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("data_management:datadistressedchild:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = dataDistressedChildService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("data_management:datadistressedchild:info")
    public R info(@PathVariable("id") Integer id){
		DataDistressedChildEntity dataDistressedChild = dataDistressedChildService.getById(id);

        return R.ok().put("dataDistressedChild", dataDistressedChild);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("data_management:datadistressedchild:save")
    public R save(@RequestBody DataDistressedChildEntity dataDistressedChild){
        dataDistressedChild.setCreateTime(new Date());
        dataDistressedChild.setCreateId(getUserId());
		dataDistressedChildService.save(dataDistressedChild);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("data_management:datadistressedchild:update")
    public R update(@RequestBody DataDistressedChildEntity dataDistressedChild){
		dataDistressedChildService.updateById(dataDistressedChild);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("data_management:datadistressedchild:delete")
    public R delete(@RequestBody Integer[] ids){
		dataDistressedChildService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    // @RequiresPermissions("data_management:datadistressedchild:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        params_import.setHeadRows(2);
        params_import.setTitleRows(1);
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<DataDistressedChildEntity> dataDistressedChildList = new ArrayList<>();
        System.out.println("当前导入数据困境儿童条数：" + list.size());
        list.forEach(item ->{
            System.out.println(item);
            if (null != item.get("身份证") && !"".equals(item.get("身份证").toString()) || (null != item.get("身份证号") && !"".equals(item.get("身份证号").toString()))) {
                item.put("id", item.get(""));
                item.put("registrationDate", item.get("登记日期"));
                item.put("name", item.get("姓名"));
                item.put("idCard", item.get("身份证号"));
                item.put("sex", "男".equals(item.get("性别").toString()) ? 1:0);
                item.put("birthday", item.get("出生日期"));
                item.put("distressedChildrenCategory", item.get("困境儿童类别"));
                item.put("householdRegistration", item.get("户籍地：镇（街道）、村（社区）"));
                item.put("currentAddress", item.get("儿童现住址：镇（街道）、村（社区）"));
                item.put("familySituation", item.get("家庭情况"));
                item.put("bankAccount", item.get("儿童本人奉化农商银行帐号"));
                item.put("guardianName", item.get("监护人姓名"));
                item.put("guardianIdCard", item.get("身份证号码"));
                item.put("guardianPhone", item.get("联系电话"));
                item.put("guardianRelationship", item.get("关系"));
                item.put("livingTogether", item.get("是否同住"));
                item.put("remark", item.get("备注"));
                item.put("createId", getUser().getUserId());
                item.put("createTime", new Date());
                dataDistressedChildList.add(new Gson().fromJson(new Gson().toJson(item), DataDistressedChildEntity.class));
            }
        });
        // 保存到数据库
        dataDistressedChildService.saveBatch(dataDistressedChildList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    //@RequiresPermissions("data_management:datadistressedchild:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<DataDistressedChildEntity> dataDistressedChildEntityList = dataDistressedChildService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("困境儿童", null, "困境儿童");
        Workbook workbook = ExcelExportUtil.exportExcel(params, DataDistressedChildEntity.class, dataDistressedChildEntityList);

        // response.setContentType("application/vnd.ms-excel");
        // response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setContentType("application/x-msdownload");
        String fileName = "困境儿童" ;
        String downFileName = "aaa.xlsx" ;
        response.setHeader("Content-Disposition", "attachment"
                + ";filename=\"" + downFileName + "\"");
        System.out.println(downFileName);
        // response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xlsx");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

}
