package com.hmit.kernespring.modules.data_management.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 60周岁养老补贴导入表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-27 13:58:44
 */
@Data
@TableName("data_pension_subsidy")
public class DataPensionSubsidyEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
@TableId
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 镇（街道）
	 */
	@Excel(name = "镇（街道）", height = 20, width = 30, isImportField = "true_st")
private String twon;
	/**
	 * 村
	 */
	@Excel(name = "村", height = 20, width = 30, isImportField = "true_st")
private String village;
	/**
	 * 姓名
	 */
	@Excel(name = "姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 性别
	 */
	@Excel(name = "性别", height = 20, width = 30, isImportField = "true_st")
private String sex;
	/**
	 * 身份证号
	 */
	@Excel(name = "身份证号", height = 20, width = 30, isImportField = "true_st")
private String idCard;
	/**
	 * 失智失能程度
	 */
	@Excel(name = "失智失能程度", height = 20, width = 30, isImportField = "true_st")
private String dementiaDegree;
	/**
	 * 补助金额
	 */
	@Excel(name = "补助金额", height = 20, width = 30, isImportField = "true_st")
private Double subsidyAmount;
	/**
	 * 补助月数
	 */
	@Excel(name = "补助月数", height = 20, width = 30, isImportField = "true_st")
private String subsidyMonth;
	/**
	 * 合计
	 */
	@Excel(name = "合计", height = 20, width = 30, isImportField = "true_st")
private Double total;
	/**
	 * 备注
	 */
	@Excel(name = "备注", height = 20, width = 30, isImportField = "true_st")
private String remark;

}
