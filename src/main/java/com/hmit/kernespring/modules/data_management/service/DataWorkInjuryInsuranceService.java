package com.hmit.kernespring.modules.data_management.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.data_management.entity.DataWorkInjuryInsuranceEntity;

import java.util.Map;

import java.util.List;

/**
 * 工伤保险生活护理费
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-27 14:00:04
 */
public interface DataWorkInjuryInsuranceService extends IService<DataWorkInjuryInsuranceEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<DataWorkInjuryInsuranceEntity> queryExportData(Map<String, Object> params);
}

