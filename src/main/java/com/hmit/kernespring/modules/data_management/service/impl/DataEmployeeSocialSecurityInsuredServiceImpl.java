package com.hmit.kernespring.modules.data_management.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.data_management.dao.DataEmployeeSocialSecurityInsuredDao;
import com.hmit.kernespring.modules.data_management.entity.DataEmployeeSocialSecurityInsuredEntity;
import com.hmit.kernespring.modules.data_management.service.DataEmployeeSocialSecurityInsuredService;


@Service("dataEmployeeSocialSecurityInsuredService")
public class DataEmployeeSocialSecurityInsuredServiceImpl extends ServiceImpl<DataEmployeeSocialSecurityInsuredDao, DataEmployeeSocialSecurityInsuredEntity> implements DataEmployeeSocialSecurityInsuredService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private DataEmployeeSocialSecurityInsuredDao dataEmployeeSocialSecurityInsuredDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        DataEmployeeSocialSecurityInsuredEntity dataEmployeeSocialSecurityInsuredEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, DataEmployeeSocialSecurityInsuredEntity.class);
        IPage<DataEmployeeSocialSecurityInsuredEntity> page = this.page(
                new Query<DataEmployeeSocialSecurityInsuredEntity>().getPage(params),
                new QueryWrapper<DataEmployeeSocialSecurityInsuredEntity>()
            .eq(StringUtils.isNotBlank(dataEmployeeSocialSecurityInsuredEntity.getId ()!=null && !"".equals(dataEmployeeSocialSecurityInsuredEntity.getId ().toString())? dataEmployeeSocialSecurityInsuredEntity.getId ().toString():null),"id", dataEmployeeSocialSecurityInsuredEntity.getId ())
            .eq(StringUtils.isNotBlank(dataEmployeeSocialSecurityInsuredEntity.getName ()!=null && !"".equals(dataEmployeeSocialSecurityInsuredEntity.getName ().toString())? dataEmployeeSocialSecurityInsuredEntity.getName ().toString():null),"name", dataEmployeeSocialSecurityInsuredEntity.getName ())
            .eq(StringUtils.isNotBlank(dataEmployeeSocialSecurityInsuredEntity.getIdCard ()!=null && !"".equals(dataEmployeeSocialSecurityInsuredEntity.getIdCard ().toString())? dataEmployeeSocialSecurityInsuredEntity.getIdCard ().toString():null),"id_card", dataEmployeeSocialSecurityInsuredEntity.getIdCard ())
        );
        Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key","sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        page.getRecords().forEach( item -> {
        });
        return new PageUtils(page);
    }
    @Override
    public List<DataEmployeeSocialSecurityInsuredEntity> queryExportData(Map<String, Object> params) {
            return dataEmployeeSocialSecurityInsuredDao.queryExportData(params);
    }

}