package com.hmit.kernespring.modules.data_management.entity;

public class CardInfoPhoEntity {

    /**
     * isExist : 1
     * tong_time : 2018-03-23 11:48:23.0
     * uRL : http://59.202.58.229/gat-photo/330283/8520516_330283_68538669_330206198208141710.jpg?Expires=33057777079&OSSAccessKeyId=cdvgHXmEI54qbhDf&Signature=ZPxaemoxn4Z80O925MSxohpNntE%3D
     */

    private String isExist;
    private String tong_time;
    private String uRL;

    public String getIsExist() {
        return isExist;
    }

    public void setIsExist(String isExist) {
        this.isExist = isExist;
    }

    public String getTong_time() {
        return tong_time;
    }

    public void setTong_time(String tong_time) {
        this.tong_time = tong_time;
    }

    public String getURL() {
        return uRL;
    }

    public void setURL(String uRL) {
        this.uRL = uRL;
    }


}
