package com.hmit.kernespring.modules.data_management.dao;

import com.hmit.kernespring.modules.data_management.entity.DataEmployeeSocialSecurityInsuredEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 职工社保参保人员导入
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-27 14:00:04
 */
@Mapper
public interface DataEmployeeSocialSecurityInsuredDao extends BaseMapper<DataEmployeeSocialSecurityInsuredEntity> {
    List<DataEmployeeSocialSecurityInsuredEntity> queryExportData(Map<String, Object> params);
	
}
