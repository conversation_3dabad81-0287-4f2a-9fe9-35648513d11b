package com.hmit.kernespring.modules.data_management.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.data_management.entity.DataIndividualPensionSubsidyEntity;

import java.util.Map;

import java.util.List;

/**
 * 个体工商户养老补贴
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-27 14:00:04
 */
public interface DataIndividualPensionSubsidyService extends IService<DataIndividualPensionSubsidyEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<DataIndividualPensionSubsidyEntity> queryExportData(Map<String, Object> params);
}

