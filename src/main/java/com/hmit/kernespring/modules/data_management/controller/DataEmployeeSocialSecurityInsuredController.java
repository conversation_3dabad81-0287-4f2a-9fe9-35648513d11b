package com.hmit.kernespring.modules.data_management.controller;

import java.io.File;
import java.io.IOException;
import java.util.*;

import java.io.OutputStream;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import org.apache.poi.ss.usermodel.Workbook;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hmit.kernespring.modules.data_management.entity.DataEmployeeSocialSecurityInsuredEntity;
import com.hmit.kernespring.modules.data_management.service.DataEmployeeSocialSecurityInsuredService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 职工社保参保人员导入
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-27 14:00:04
 */
@RestController
@RequestMapping("data_management/dataemployeesocialsecurityinsured")
public class DataEmployeeSocialSecurityInsuredController {
    @Autowired
    private DataEmployeeSocialSecurityInsuredService dataEmployeeSocialSecurityInsuredService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("data_management:dataemployeesocialsecurityinsured:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = dataEmployeeSocialSecurityInsuredService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("data_management:dataemployeesocialsecurityinsured:info")
    public R info(@PathVariable("id") Integer id){
		DataEmployeeSocialSecurityInsuredEntity dataEmployeeSocialSecurityInsured = dataEmployeeSocialSecurityInsuredService.getById(id);

        return R.ok().put("dataEmployeeSocialSecurityInsured", dataEmployeeSocialSecurityInsured);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("data_management:dataemployeesocialsecurityinsured:save")
    public R save(@RequestBody DataEmployeeSocialSecurityInsuredEntity dataEmployeeSocialSecurityInsured){
		dataEmployeeSocialSecurityInsuredService.save(dataEmployeeSocialSecurityInsured);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("data_management:dataemployeesocialsecurityinsured:update")
    public R update(@RequestBody DataEmployeeSocialSecurityInsuredEntity dataEmployeeSocialSecurityInsured){
		dataEmployeeSocialSecurityInsuredService.updateById(dataEmployeeSocialSecurityInsured);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("data_management:dataemployeesocialsecurityinsured:delete")
    public R delete(@RequestBody Integer[] ids){
		dataEmployeeSocialSecurityInsuredService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    // @RequiresPermissions("data_management:dataemployeesocialsecurityinsured:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<DataEmployeeSocialSecurityInsuredEntity> dataEmployeeSocialSecurityInsuredList = new ArrayList<>();
        list.forEach(item ->{
                    item.put("id",item.get(""));
                    item.put("name",item.get("残疾人姓名"));
                    item.put("idCard",item.get("身份证号码"));
                    dataEmployeeSocialSecurityInsuredList.add(new Gson().fromJson(new Gson().toJson(item), DataEmployeeSocialSecurityInsuredEntity.class));
        });
        // 保存到数据库
        dataEmployeeSocialSecurityInsuredService.saveBatch(dataEmployeeSocialSecurityInsuredList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    // @RequiresPermissions("data_management:dataemployeesocialsecurityinsured:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<DataEmployeeSocialSecurityInsuredEntity> dataEmployeeSocialSecurityInsuredEntityList = dataEmployeeSocialSecurityInsuredService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("职工社保参保人员导入", null, "职工社保参保人员导入");
        Workbook workbook = ExcelExportUtil.exportExcel(params, DataEmployeeSocialSecurityInsuredEntity.class, dataEmployeeSocialSecurityInsuredEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "职工社保参保人员导入" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

}
