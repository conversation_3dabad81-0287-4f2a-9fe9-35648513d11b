package com.hmit.kernespring.modules.data_management.dao;

import com.hmit.kernespring.modules.data_management.entity.DataWorkInjuryInsuranceEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 工伤保险生活护理费
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-27 14:00:04
 */
@Mapper
public interface DataWorkInjuryInsuranceDao extends BaseMapper<DataWorkInjuryInsuranceEntity> {
    List<DataWorkInjuryInsuranceEntity> queryExportData(Map<String, Object> params);
	
}
