package com.hmit.kernespring.modules.data_management.dao;

import com.hmit.kernespring.modules.data_management.entity.ApiCardIdEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 省公安身份证信息
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-13 15:39:30
 */
@Mapper
public interface ApiCardIdDao extends BaseMapper<ApiCardIdEntity> {
    List<ApiCardIdEntity> queryExportData(Map<String, Object> params);
	
}
