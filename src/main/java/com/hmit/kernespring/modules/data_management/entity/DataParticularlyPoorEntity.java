package com.hmit.kernespring.modules.data_management.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 特困人员名单表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-19 09:01:24
 */
@Data
@TableName("data_particularly_poor")
public class DataParticularlyPoorEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
@TableId
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 行政划分
	 */
	@Excel(name = "行政划分", height = 20, width = 30, isImportField = "true_st")
private String administrativeDivision;
	/**
	 * 人员姓名
	 */
	@Excel(name = "人员姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 与户主关系
	 */
	@Excel(name = "与户主关系", height = 20, width = 30, isImportField = "true_st")
private String relationshipWithHousehold;
	/**
	 * 身份证号
	 */
	@Excel(name = "身份证号", height = 20, width = 30, isImportField = "true_st")
private String idCard;
	/**
	 * 手机号
	 */
	@Excel(name = "手机号", height = 20, width = 30, isImportField = "true_st")
private String mobilePhone;
	/**
	 * 联系电话
	 */
	@Excel(name = "联系电话", height = 20, width = 30, isImportField = "true_st")
private String telephone;
	/**
	 * 银行账户
	 */
	@Excel(name = "银行账户", height = 20, width = 30, isImportField = "true_st")
private String bankAccount;
	/**
	 * 开户银行
	 */
	@Excel(name = "开户银行", height = 20, width = 30, isImportField = "true_st")
private String bankName;
	/**
	 * 人员银行账号 
	 */
	@Excel(name = "人员银行账号 ", height = 20, width = 30, isImportField = "true_st")
private String personBankAccount;
	/**
	 * 申请类别
	 */
	@Excel(name = "申请类别", height = 20, width = 30, isImportField = "true_st")
private String applicationCategory;
	/**
	 *  供养人数 
	 */
	@Excel(name = " 供养人数 ", height = 20, width = 30, isImportField = "true_st")
private Integer numberOfSupport;
	/**
	 * 供养金额
	 */
	@Excel(name = "供养金额", height = 20, width = 30, isImportField = "true_st")
private BigDecimal amountOfSupport;
	/**
	 *  健康状况
	 */
	@Excel(name = " 健康状况", height = 20, width = 30, isImportField = "true_st")
private String healthStatus;
	/**
	 *  护理等级 
	 */
	@Excel(name = " 护理等级 ", height = 20, width = 30, isImportField = "true_st")
private String nursingLevel;
	/**
	 * 护理标准
	 */
	@Excel(name = "护理标准", height = 20, width = 30, isImportField = "true_st")
private String nursingStandard;
	/**
	 * 救助证编号 
	 */
	@Excel(name = "救助证编号 ", height = 20, width = 30, isImportField = "true_st")
private String rescueCertificateNumber;
	/**
	 * 所在养老机构 
	 */
	@Excel(name = "所在养老机构 ", height = 20, width = 30, isImportField = "true_st")
private String agedInstitution;
	/**
	 * 性别
	 */
	@Excel(name = "性别", height = 20, width = 30, isImportField = "true_st")
private Integer sex;
	/**
	 * 出生日期
	 */
	@Excel(name = "出生日期", height = 20, width = 30, isImportField = "true_st")
private Date birthday;
	/**
	 *  申请救助原因 
	 */
	@Excel(name = " 申请救助原因 ", height = 20, width = 30, isImportField = "true_st")
private String reasonForApplying;
	/**
	 * 
	 */
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Long createId;
	/**
	 * 创建时间
	 */
	@Excel(name = "创建时间", height = 20, width = 30, isImportField = "true_st")
private Date createTime;

}
