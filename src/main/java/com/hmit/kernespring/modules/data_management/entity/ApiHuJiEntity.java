package com.hmit.kernespring.modules.data_management.entity;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-16 21:55
 */
public class ApiHuJiEntity {
    /**
     * name : 李承鑫
     * relation : 子
     * useToName :
     * sex : 男
     * birthplace : 广东省珠海市香洲区
     * nation : 汉族
     * country :
     * membership : 浙江省东阳市
     * dateOfBirth : ********
     * cityOtherAddress :
     * height :
     * bloodType :
     * culturalLevel :
     * maritalStatus :
     * veteranStatus :
     * serviceSpaces :
     * whenToLocal :
     * dueToLocal :
     * whatCountryToLocal :
     * provincialCitiesAndCountiesToLocal :
     * whereToLocal :
     * whenToLocalCity :
     * dueToLocalCity :
     * whereToLocalCityCountry :
     * whereToLocalArea :
     * whereToLocalCity :
     * contractor :
     * registrationDate : 2016-05-13 14:35:01
     * numbering : ********
     * accountType :
     * religion :
     * registrationAuthority : 东阳市公安局千祥派出所
     * householder : 李端
     * householderIDCard : 330724198103016913
     * idcard : ********
     */

    private String name;
    private String relation;
    private String useToName;
    private String sex;
    private String birthplace;
    private String nation;
    private String country;
    private String membership;
    private String dateOfBirth;
    private String cityOtherAddress;
    private String height;
    private String bloodType;
    private String culturalLevel;
    private String maritalStatus;
    private String veteranStatus;
    private String serviceSpaces;
    private String whenToLocal;
    private String dueToLocal;
    private String whatCountryToLocal;
    private String provincialCitiesAndCountiesToLocal;
    private String whereToLocal;
    private String whenToLocalCity;
    private String dueToLocalCity;
    private String whereToLocalCityCountry;
    private String whereToLocalArea;
    private String whereToLocalCity;
    private String contractor;
    private String registrationDate;
    private String numbering;
    private String accountType;
    private String religion;
    private String registrationAuthority;
    private String householder;
    private String householderIDCard;
    private String idcard;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRelation() {
        return relation;
    }

    public void setRelation(String relation) {
        this.relation = relation;
    }

    public String getUseToName() {
        return useToName;
    }

    public void setUseToName(String useToName) {
        this.useToName = useToName;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getBirthplace() {
        return birthplace;
    }

    public void setBirthplace(String birthplace) {
        this.birthplace = birthplace;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getMembership() {
        return membership;
    }

    public void setMembership(String membership) {
        this.membership = membership;
    }

    public String getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(String dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public String getCityOtherAddress() {
        return cityOtherAddress;
    }

    public void setCityOtherAddress(String cityOtherAddress) {
        this.cityOtherAddress = cityOtherAddress;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    public String getBloodType() {
        return bloodType;
    }

    public void setBloodType(String bloodType) {
        this.bloodType = bloodType;
    }

    public String getCulturalLevel() {
        return culturalLevel;
    }

    public void setCulturalLevel(String culturalLevel) {
        this.culturalLevel = culturalLevel;
    }

    public String getMaritalStatus() {
        return maritalStatus;
    }

    public void setMaritalStatus(String maritalStatus) {
        this.maritalStatus = maritalStatus;
    }

    public String getVeteranStatus() {
        return veteranStatus;
    }

    public void setVeteranStatus(String veteranStatus) {
        this.veteranStatus = veteranStatus;
    }

    public String getServiceSpaces() {
        return serviceSpaces;
    }

    public void setServiceSpaces(String serviceSpaces) {
        this.serviceSpaces = serviceSpaces;
    }

    public String getWhenToLocal() {
        return whenToLocal;
    }

    public void setWhenToLocal(String whenToLocal) {
        this.whenToLocal = whenToLocal;
    }

    public String getDueToLocal() {
        return dueToLocal;
    }

    public void setDueToLocal(String dueToLocal) {
        this.dueToLocal = dueToLocal;
    }

    public String getWhatCountryToLocal() {
        return whatCountryToLocal;
    }

    public void setWhatCountryToLocal(String whatCountryToLocal) {
        this.whatCountryToLocal = whatCountryToLocal;
    }

    public String getProvincialCitiesAndCountiesToLocal() {
        return provincialCitiesAndCountiesToLocal;
    }

    public void setProvincialCitiesAndCountiesToLocal(String provincialCitiesAndCountiesToLocal) {
        this.provincialCitiesAndCountiesToLocal = provincialCitiesAndCountiesToLocal;
    }

    public String getWhereToLocal() {
        return whereToLocal;
    }

    public void setWhereToLocal(String whereToLocal) {
        this.whereToLocal = whereToLocal;
    }

    public String getWhenToLocalCity() {
        return whenToLocalCity;
    }

    public void setWhenToLocalCity(String whenToLocalCity) {
        this.whenToLocalCity = whenToLocalCity;
    }

    public String getDueToLocalCity() {
        return dueToLocalCity;
    }

    public void setDueToLocalCity(String dueToLocalCity) {
        this.dueToLocalCity = dueToLocalCity;
    }

    public String getWhereToLocalCityCountry() {
        return whereToLocalCityCountry;
    }

    public void setWhereToLocalCityCountry(String whereToLocalCityCountry) {
        this.whereToLocalCityCountry = whereToLocalCityCountry;
    }

    public String getWhereToLocalArea() {
        return whereToLocalArea;
    }

    public void setWhereToLocalArea(String whereToLocalArea) {
        this.whereToLocalArea = whereToLocalArea;
    }

    public String getWhereToLocalCity() {
        return whereToLocalCity;
    }

    public void setWhereToLocalCity(String whereToLocalCity) {
        this.whereToLocalCity = whereToLocalCity;
    }

    public String getContractor() {
        return contractor;
    }

    public void setContractor(String contractor) {
        this.contractor = contractor;
    }

    public String getRegistrationDate() {
        return registrationDate;
    }

    public void setRegistrationDate(String registrationDate) {
        this.registrationDate = registrationDate;
    }

    public String getNumbering() {
        return numbering;
    }

    public void setNumbering(String numbering) {
        this.numbering = numbering;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getReligion() {
        return religion;
    }

    public void setReligion(String religion) {
        this.religion = religion;
    }

    public String getRegistrationAuthority() {
        return registrationAuthority;
    }

    public void setRegistrationAuthority(String registrationAuthority) {
        this.registrationAuthority = registrationAuthority;
    }

    public String getHouseholder() {
        return householder;
    }

    public void setHouseholder(String householder) {
        this.householder = householder;
    }

    public String getHouseholderIDCard() {
        return householderIDCard;
    }

    public void setHouseholderIDCard(String householderIDCard) {
        this.householderIDCard = householderIDCard;
    }

    public String getIdcard() {
        return idcard;
    }

    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }
}
