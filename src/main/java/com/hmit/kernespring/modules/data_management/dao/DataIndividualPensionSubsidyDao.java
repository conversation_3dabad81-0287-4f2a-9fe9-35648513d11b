package com.hmit.kernespring.modules.data_management.dao;

import com.hmit.kernespring.modules.data_management.entity.DataIndividualPensionSubsidyEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 个体工商户养老补贴
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-27 14:00:04
 */
@Mapper
public interface DataIndividualPensionSubsidyDao extends BaseMapper<DataIndividualPensionSubsidyEntity> {
    List<DataIndividualPensionSubsidyEntity> queryExportData(Map<String, Object> params);
	
}
