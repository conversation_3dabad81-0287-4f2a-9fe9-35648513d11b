package com.hmit.kernespring.modules.data_management.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.data_management.entity.DataPensionSubsidyEntity;

import java.util.Collection;
import java.util.Map;

import java.util.List;

/**
 * 60周岁养老补贴导入表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-27 13:58:44
 */
public interface DataPensionSubsidyService extends IService<DataPensionSubsidyEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<DataPensionSubsidyEntity> queryExportData(Map<String, Object> params);

    @Override
    boolean saveBatch(Collection<DataPensionSubsidyEntity> entityList);
}

