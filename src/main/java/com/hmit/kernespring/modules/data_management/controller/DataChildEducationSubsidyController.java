package com.hmit.kernespring.modules.data_management.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.common.utils.DateUtils;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.app.service.APIService;
import com.hmit.kernespring.modules.cjrone.entity.CjroneSignatureEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneSignatureService;
import com.hmit.kernespring.modules.data_management.entity.ApiZCszmEntity;
import com.hmit.kernespring.modules.data_management.entity.DataChildEducationSubsidyEntity;
import com.hmit.kernespring.modules.data_management.entity.DataChildEducationSubsidyMoneyEntity;
import com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity;
import com.hmit.kernespring.modules.data_management.service.DataChildEducationSubsidyMoneyService;
import com.hmit.kernespring.modules.data_management.service.DataChildEducationSubsidyService;
import com.hmit.kernespring.modules.data_management.service.DataDisabilityCertificateService;
import com.hmit.kernespring.modules.matter_application.entity.CjroneWelfareMatterApplicationEntity;
import com.hmit.kernespring.modules.matter_application.service.CjroneWelfareMatterApplicationService;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import com.hmit.kernespring.modules.sys.controller.AbstractController;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.*;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.Files;
import java.util.*;

/**
 * 残疾人子女教育补贴导入表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-27 13:58:44
 */
@RestController
@RequestMapping("data_management/datachildeducationsubsidy")
public class DataChildEducationSubsidyController  extends AbstractController {
    @Autowired
    private DataChildEducationSubsidyService dataChildEducationSubsidyService;
    @Autowired
    private DataDisabilityCertificateService dataDisabilityCertificateService;
    @Autowired
    private CjroneWelfareMatterApplicationService cjroneWelfareMatterApplicationService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;
    @Autowired
    private CjroneSignatureService cjroneSignatureService;
    @Autowired
    private APIService apiService;
    @Autowired
    private DataChildEducationSubsidyMoneyService dataChildEducationSubsidyMoneyService;


    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("data_management:datachildeducationsubsidy:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = dataChildEducationSubsidyService.queryPage(params);


        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("data_management:datachildeducationsubsidy:info")
    public R info(@PathVariable("id") Integer id){
		DataChildEducationSubsidyEntity dataChildEducationSubsidy = dataChildEducationSubsidyService.getById(id);

        return R.ok().put("dataChildEducationSubsidy", dataChildEducationSubsidy);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("data_management:datachildeducationsubsidy:save")
    public R save(@RequestBody DataChildEducationSubsidyEntity dataChildEducationSubsidy){
        //开始验证户籍
        if(dataChildEducationSubsidy.getApplicantType()!=null && dataChildEducationSubsidy.getApplicantType().equals("子女")){
            String is_childs = "否";
            Map<String, Object> doCszmInfo = apiService.queryCSZMInfo(dataChildEducationSubsidy.getFatherName(),dataChildEducationSubsidy.getMotherName(),dataChildEducationSubsidy.getFatherIdcard(),dataChildEducationSubsidy.getMotherIdcard(),null);
            if ("00".equals(doCszmInfo.get("code").toString())) {
                if (doCszmInfo.get("datas") != null) {
                    System.out.println("doCszmInfo: " + doCszmInfo);
                    List<ApiZCszmEntity> list1 = new Gson().fromJson(doCszmInfo.get("datas").toString().replaceAll(" ", "").replaceAll(":", "").replaceAll("-", ""), new TypeToken<List<ApiZCszmEntity>>() {
                    }.getType());
                    if (list1.size() != 0) {
                        is_childs = "是";
                    }
                }
            }else {
                is_childs = "异常";
            }
            dataChildEducationSubsidy.setIsHuJiRation(is_childs);
        }

		dataChildEducationSubsidyService.save(dataChildEducationSubsidy);

        return R.ok();
    }

    /**
     * 保存
     */
    @RequestMapping("/saveZljy")
    // @RequiresPermissions("data_management:datachildeducationsubsidy:saveZljy")
    public R saveZljy(@RequestBody DataChildEducationSubsidyEntity dataChildEducationSubsidy){
        // 判断是否存在申请记录
        dataChildEducationSubsidy.setFamilyEconoCondition("一般家庭");
        Map<String, Object> params_alive = new HashMap<>();
        params_alive.put("id_card",dataChildEducationSubsidy.getIdCard());
        List<DataChildEducationSubsidyEntity> is_alive = (List<DataChildEducationSubsidyEntity>) dataChildEducationSubsidyService.listByMap(params_alive);
        if (is_alive.size() == 0) {
            Date date = new Date();
            String dateTime = DateUtils.format(date, DateUtils.DATE_TIME_PATTERN);
            dataChildEducationSubsidy.setApplicationTime(DateUtils.format(date, DateUtils.DATE_TIME_PATTERN));
            System.out.println(new Gson().toJson(dataChildEducationSubsidy));
            if (dataChildEducationSubsidy.getApplicantType().indexOf("本人") != -1) {
                Map<String, Object> params = new HashMap<>();
                params.put("id_card", dataChildEducationSubsidy.getIdCard());
                List<DataDisabilityCertificateEntity> entities = (List<DataDisabilityCertificateEntity>) dataDisabilityCertificateService.listByMap(params);
                System.out.println(entities.size() + "aaa");
                if (entities.size() != 0) {
                    DataDisabilityCertificateEntity entity = entities.get(0);
                    dataChildEducationSubsidy.setDisableId(entity.getDisableId());
                    dataChildEducationSubsidy.setStatus("8");

                    Map<String, Object> params_fanc = new HashMap<>();
                    params_fanc.put("idCard",dataChildEducationSubsidy.getFatherIdcard());
                    List<Map<String,Object>>  familyEconoConditions = cjroneWelfareMatterApplicationService.queryFamilyEconoCondition(params_fanc);
                    if (familyEconoConditions.size()>0){
                        dataChildEducationSubsidy.setFamilyEconoCondition(familyEconoConditions.get(0).get("name") != null ? familyEconoConditions.get(0).get("name").toString() : "一般家庭");
                    }
                    dataChildEducationSubsidyService.save(dataChildEducationSubsidy);
                    // 保存到惠残事项表
                    CjroneWelfareMatterApplicationEntity welfareMatterApplicationEntity = new CjroneWelfareMatterApplicationEntity();
                    welfareMatterApplicationEntity.setMatterName("基础段残疾人子女教育补贴");
                    welfareMatterApplicationEntity.setMatterId(dataChildEducationSubsidy.getId());
                    welfareMatterApplicationEntity.setIdCard(entity.getIdCard());
                    welfareMatterApplicationEntity.setName(entity.getName());
                    welfareMatterApplicationEntity.setMobilePhone(dataChildEducationSubsidy.getMobilePhone());
                    welfareMatterApplicationEntity.setDisableId(entity.getDisableId());
                    welfareMatterApplicationEntity.setSignStatus("1");
                    welfareMatterApplicationEntity.setSignatureStatus("1");
                    welfareMatterApplicationEntity.setStatus("8");
                    welfareMatterApplicationEntity.setApplicationTime(DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN));
                    welfareMatterApplicationEntity.setNativeZhen(getUser().getZhen());
                    welfareMatterApplicationEntity.setCreateId(getUserId());
                    welfareMatterApplicationEntity.setCreateTime(dateTime);
                    cjroneWelfareMatterApplicationService.saveZljf(welfareMatterApplicationEntity);
                } else {
                    System.out.println("aaaaaaa");
                    return R.error().put("msg", "未找到残疾证相关信息，请检查身份证号码是否输入正确！");
                }
            } else if (dataChildEducationSubsidy.getApplicantType().indexOf("子女") != -1) {
                // 查询父母哪一位残疾
                Map<String, Object> params = new HashMap<>();
                params.put("id_card", dataChildEducationSubsidy.getFatherIdcard());
                List<DataDisabilityCertificateEntity> entitiesf = (List<DataDisabilityCertificateEntity>) dataDisabilityCertificateService.listByMap(params);
                Map<String, Object> params_m = new HashMap<>();
                params_m.put("id_card", dataChildEducationSubsidy.getMotherIdcard());
                List<DataDisabilityCertificateEntity> entitiesm = (List<DataDisabilityCertificateEntity>) dataDisabilityCertificateService.listByMap(params_m);
                if (entitiesf.size() > 0 && entitiesm.size() > 0) {
                    // 同时残疾
                    dataChildEducationSubsidy.setApplicantType("学生父母");
                    dataChildEducationSubsidy.setDisableId(entitiesf.get(0).getDisableId());
                    Map<String, Object> params_fanc = new HashMap<>();
                    params_fanc.put("idCard",dataChildEducationSubsidy.getFatherIdcard());
                    List<Map<String,Object>>  familyEconoConditions = cjroneWelfareMatterApplicationService.queryFamilyEconoCondition(params_fanc);
                    if (familyEconoConditions.size()>0){
                        dataChildEducationSubsidy.setFamilyEconoCondition(familyEconoConditions.get(0).get("name") != null ? familyEconoConditions.get(0).get("name").toString() : "一般家庭");
                    }
                } else if (entitiesf.size() > 0 && entitiesm.size() == 0) {
                    //父亲残疾
                    dataChildEducationSubsidy.setApplicantType("学生父亲");
                    dataChildEducationSubsidy.setDisableId(entitiesf.get(0).getDisableId());
                    Map<String, Object> params_fanc = new HashMap<>();
                    params_fanc.put("idCard",dataChildEducationSubsidy.getFatherIdcard());
                    List<Map<String,Object>>  familyEconoConditions = cjroneWelfareMatterApplicationService.queryFamilyEconoCondition(params_fanc);
                    if (familyEconoConditions.size()>0){
                        dataChildEducationSubsidy.setFamilyEconoCondition(familyEconoConditions.get(0).get("name") != null ? familyEconoConditions.get(0).get("name").toString() : "一般家庭");
                    }
                } else if (entitiesf.size() == 0 && entitiesm.size() > 0) {
                    // 母亲
                    dataChildEducationSubsidy.setApplicantType("学生母亲");
                    dataChildEducationSubsidy.setDisableId(entitiesm.get(0).getDisableId());
                    Map<String, Object> params_fanc = new HashMap<>();
                    params_fanc.put("idCard",dataChildEducationSubsidy.getMotherIdcard());
                    List<Map<String,Object>>  familyEconoConditions = cjroneWelfareMatterApplicationService.queryFamilyEconoCondition(params_fanc);
                    if (familyEconoConditions.size()>0){
                        dataChildEducationSubsidy.setFamilyEconoCondition(familyEconoConditions.get(0).get("name") != null ? familyEconoConditions.get(0).get("name").toString() : "一般家庭");
                    }
                } else {
                    // 都无
                    return R.error().put("msg", "通过父母双方身份证，未找到相关残疾证信息，请检查身份证是否填写错误！");
                }
                //开始验证户籍
                String is_childs = "否";
            Map<String, Object> doCszmInfo = apiService.queryCSZMInfo(dataChildEducationSubsidy.getFatherName(),dataChildEducationSubsidy.getMotherName(),dataChildEducationSubsidy.getFatherIdcard(),dataChildEducationSubsidy.getMotherIdcard(),null);
            if ("00".equals(doCszmInfo.get("code").toString())) {
                if (doCszmInfo.get("datas") != null) {
                    System.out.println("doCszmInfo: " + doCszmInfo);
                    List<ApiZCszmEntity> list1 = new Gson().fromJson(doCszmInfo.get("datas").toString().replaceAll(" ", "").replaceAll(":", "").replaceAll("-", ""), new TypeToken<List<ApiZCszmEntity>>() {
                    }.getType());
                    if (list1.size() != 0) {
                        System.out.println(new Gson().toJson("list1---->" + list1));
                        is_childs = "是";
                    }else {
                         //return R.error().put("msg","省公安户籍验证接口显示，申请人与所输入父母信息未查到子女关系！请检查后再提交。");
                    }
                }else {
                    System.out.println("aaaaaaa");
                     //return R.error().put("msg","省公安户籍验证接口显示，申请人与所输入父母信息未查到子女关系！请检查后再提交。");
                }
            }else {
                is_childs = "异常";
                 return R.error().put("msg","省公安户籍验证接口验证异常，请稍后再试！请检查后再提交。");
            }

                dataChildEducationSubsidy.setIsHuJiRation(is_childs);
                dataChildEducationSubsidy.setStatus("8");
                dataChildEducationSubsidyService.save(dataChildEducationSubsidy);
                // 保存到惠残事项表
                CjroneWelfareMatterApplicationEntity welfareMatterApplicationEntity = new CjroneWelfareMatterApplicationEntity();
                welfareMatterApplicationEntity.setMatterName("基础段残疾人子女教育补贴");
                welfareMatterApplicationEntity.setMatterId(dataChildEducationSubsidy.getId());
                welfareMatterApplicationEntity.setIdCard(dataChildEducationSubsidy.getIdCard());
                welfareMatterApplicationEntity.setName(dataChildEducationSubsidy.getName());
                welfareMatterApplicationEntity.setMobilePhone(dataChildEducationSubsidy.getMobilePhone());
                welfareMatterApplicationEntity.setDisableId(dataChildEducationSubsidy.getDisableId());
                welfareMatterApplicationEntity.setSignStatus("1");
                welfareMatterApplicationEntity.setSignatureStatus("1");
                welfareMatterApplicationEntity.setStatus("8");
                welfareMatterApplicationEntity.setApplicationTime(DateUtils.format(date, DateUtils.DATE_TIME_PATTERN));
                welfareMatterApplicationEntity.setNativeZhen(getUser().getZhen());
                welfareMatterApplicationEntity.setCreateTime(dateTime);
                welfareMatterApplicationEntity.setCreateId(getUserId());
                cjroneWelfareMatterApplicationService.saveZljf(welfareMatterApplicationEntity);
            }
            return R.ok().put("applyId",dataChildEducationSubsidy.getId());
        }else {
            return R.error().put("msg","该学生已申请过基础段残疾人子女教育补贴！");
        }
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("data_management:datachildeducationsubsidy:update")
    public R update(@RequestBody DataChildEducationSubsidyEntity dataChildEducationSubsidy){
        //开始验证户籍
        //开始验证户籍
        if(dataChildEducationSubsidy.getApplicantType()!=null && dataChildEducationSubsidy.getApplicantType().equals("子女")){
            String is_childs = "否";
            Map<String, Object> doCszmInfo = apiService.queryCSZMInfo(dataChildEducationSubsidy.getFatherName(),dataChildEducationSubsidy.getMotherName(),dataChildEducationSubsidy.getFatherIdcard(),dataChildEducationSubsidy.getMotherIdcard(),null);
            if ("00".equals(doCszmInfo.get("code").toString())) {
                if (doCszmInfo.get("datas") != null) {
                    System.out.println("doCszmInfo: " + doCszmInfo);
                    List<ApiZCszmEntity> list1 = new Gson().fromJson(doCszmInfo.get("datas").toString().replaceAll(" ", "").replaceAll(":", "").replaceAll("-", ""), new TypeToken<List<ApiZCszmEntity>>() {
                    }.getType());
                    if (list1.size() != 0) {
                        is_childs = "是";
                    }
                }
            }else {
                is_childs = "异常";
            }
            dataChildEducationSubsidy.setIsHuJiRation(is_childs);
        }

		dataChildEducationSubsidyService.updateById(dataChildEducationSubsidy);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("data_management:datachildeducationsubsidy:delete")
    public R delete(@RequestBody Integer[] ids){
		dataChildEducationSubsidyService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    // @RequiresPermissions("data_management:datachildeducationsubsidy:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<DataChildEducationSubsidyEntity> dataChildEducationSubsidyList = new ArrayList<>();
        list.forEach(item ->{
                    item.put("id",item.get(""));
                    item.put("name",item.get("姓名"));
                    item.put("idCard",item.get("身份证号"));
                    item.put("sex","男".equals(item.get("性别"))? 1:0);
                    item.put("mobilePhone",item.get("联系电话"));
                    item.put("presentAddress",item.get("家庭住址"));
                    item.put("disableId",item.get("残疾证号"));
                    item.put("familyEconoCondition",item.get("家庭经济情况"));
                    item.put("currentSchool",item.get("就读学校"));
                    item.put("grade",item.get("年级"));
                    item.put("admissionTime",item.get("入学时间"));
                    item.put("applicantType",item.get("申请人类型"));
                    item.put("applicationTime",item.get("申请时间"));
                    item.put("fatherName",item.get("父亲姓名"));
                    item.put("fatherIdcard",item.get("父亲身份证"));
                    item.put("motherName",item.get("母亲姓名"));
                    item.put("motherIdcard",item.get("母亲身份证"));
                    item.put("education",item.get("学历"));
                    dataChildEducationSubsidyList.add(new Gson().fromJson(new Gson().toJson(item), DataChildEducationSubsidyEntity.class));


        });
        //开始验证户籍
        dataChildEducationSubsidyList.forEach(item ->{
                if(item.getApplicantType()!=null && item.getApplicantType().equals("子女")){
                    String is_childs = "否";
                    Map<String, Object> doCszmInfo = apiService.queryCSZMInfo(item.getFatherName(),item.getMotherName(),item.getFatherIdcard(),item.getMotherIdcard(),null);
                    if ("00".equals(doCszmInfo.get("code").toString())) {
                        if (doCszmInfo.get("datas") != null) {
                            System.out.println("doCszmInfo: " + doCszmInfo);
                            List<ApiZCszmEntity> list1 = new Gson().fromJson(doCszmInfo.get("datas").toString().replaceAll(" ", "").replaceAll(":", "").replaceAll("-", ""), new TypeToken<List<ApiZCszmEntity>>() {
                            }.getType());
                            if (list1.size() != 0) {
                                is_childs = "是";
                            }
                        }
                    }else {
                        is_childs = "异常";
                    }
                    item.setIsHuJiRation(is_childs);
                }
            }
        );


        //开始关联补贴金额
        dataChildEducationSubsidyList.forEach(item ->{
            Map<String,Object> param=new HashMap<>();
            param.put("family_economic_situation",item.getFamilyEconoCondition());
            param.put("education",item.getEducation());
            List<DataChildEducationSubsidyMoneyEntity> dataChildEducationSubsidyMoneyEntities=dataChildEducationSubsidyMoneyService.queryByMap(param);
            if(dataChildEducationSubsidyMoneyEntities!=null && dataChildEducationSubsidyMoneyEntities.size()>0){
                item.setSubsidyMoney(dataChildEducationSubsidyMoneyEntities.get(0).getSubsidyMoney().toString());
            }

        });



        // 保存到数据库
        dataChildEducationSubsidyService.saveBatch(dataChildEducationSubsidyList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    // @RequiresPermissions("data_management:datachildeducationsubsidy:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<DataChildEducationSubsidyEntity> dataChildEducationSubsidyEntityList = dataChildEducationSubsidyService.queryExportData(mapArgs);

        dataChildEducationSubsidyEntityList.forEach(item ->{
            //开始处理性别
            if("1".equals(item.getSex())){
                item.setSex("男");
            }else{
                item.setSex("女");
            }
        });

        ExportParams params = new ExportParams("残疾人子女教育补贴导入表", null, "残疾人子女教育补贴导入表");
        Workbook workbook = ExcelExportUtil.exportExcel(params, DataChildEducationSubsidyEntity.class, dataChildEducationSubsidyEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "残疾人子女教育补贴导入表" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }


    /**
     * 生成电子签章 pdf
     */
    @RequestMapping("/printPDF/{id}")
    @Transactional(rollbackFor = Exception.class)
    public R printPDF(@PathVariable("id") Integer id) throws IOException {
        Map<String, Object> tmp_params = new HashMap<>();
        tmp_params.put("type","惠残事项基础段残疾人子女教育补贴");
        tmp_params.put("status","1");
        tmp_params.put("type_id",id);
        List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
        if (alive_list.size()>0) {
            CjroneSignatureEntity cjroneSignatureEntity = alive_list.get(0);
            if ("电子公章".equals("no")){
                String tmp_file_name = System.currentTimeMillis()+"_"+cjroneSignatureEntity.getFileName();
                Files.copy(new File(cjroneSignatureEntity.getFileActUrl()).toPath(),new File(cjroneProperties.getTempFilePath()+tmp_file_name).toPath());
                return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", tmp_file_name).put("signId",cjroneSignatureEntity.getId());
            }else {
                return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", cjroneSignatureEntity.getFileName()).put("signId",cjroneSignatureEntity.getId());
            }
        }else {
            //根据编号获得详细信息
            DataChildEducationSubsidyEntity dataChildEducationSubsidyEntity = dataChildEducationSubsidyService.getById(id);
            Calendar now = Calendar.getInstance();
            // 原pdf模板路径
            String templatePath = cjroneProperties.getTempletePath() + "基础段子女教育补助.pdf";
            // 生成的新文件路径
            String newPDFPath = cjroneProperties.getSignaturePath() + "child_education_subsidy_" + dataChildEducationSubsidyEntity.getIdCard() + ".pdf";

            // 获得待生成的实体文件
            Map<String, String> map = new HashMap<String, String>();


            // 获得待生成的实体文件
            map.put("name", dataChildEducationSubsidyEntity.getName() == null ? "" : dataChildEducationSubsidyEntity.getName());
            if ("1".equals(dataChildEducationSubsidyEntity.getSex())) {
                map.put("sex", "男");
            } else {
                map.put("sex", "女");
            }
            map.put("id_card", dataChildEducationSubsidyEntity.getIdCard() == null ? "" : dataChildEducationSubsidyEntity.getIdCard());
            map.put("mobile_phone", dataChildEducationSubsidyEntity.getMobilePhone() == null ? "" : dataChildEducationSubsidyEntity.getMobilePhone());
            map.put("present_address", dataChildEducationSubsidyEntity.getPresentAddress() == null ? "" : dataChildEducationSubsidyEntity.getPresentAddress());
            map.put("father_name", dataChildEducationSubsidyEntity.getFatherName() == null ? "" : dataChildEducationSubsidyEntity.getFatherName());
            map.put("father_idcard", dataChildEducationSubsidyEntity.getFatherIdcard() == null ? "" : dataChildEducationSubsidyEntity.getFatherIdcard());
            map.put("mother_name", dataChildEducationSubsidyEntity.getMotherName() == null ? "" : dataChildEducationSubsidyEntity.getMotherName());
            map.put("mother_idcard", dataChildEducationSubsidyEntity.getMotherIdcard() == null ? "" : dataChildEducationSubsidyEntity.getMotherIdcard());

            if (dataChildEducationSubsidyEntity.getApplicantType().indexOf("本人") != -1) {
                //本人申请
                map.put("apply_myself", "√");
                map.put("apply_myself_id", dataChildEducationSubsidyEntity.getDisableId() == null ? "" : dataChildEducationSubsidyEntity.getDisableId());
            }
            if (dataChildEducationSubsidyEntity.getApplicantType().indexOf("父") != -1) {
                //父申请
                map.put("apply_father", "√");
                map.put("apply_father_id", dataChildEducationSubsidyEntity.getDisableId() == null ? "" : dataChildEducationSubsidyEntity.getDisableId());
            }
            if (dataChildEducationSubsidyEntity.getApplicantType().indexOf("母") != -1) {
                //母申请
                map.put("apply_mother", "√");
                map.put("apply_mother_id", dataChildEducationSubsidyEntity.getDisableId() == null ? "" : dataChildEducationSubsidyEntity.getDisableId());
            }

            map.put("current_school", dataChildEducationSubsidyEntity.getCurrentSchool() == null ? "" : dataChildEducationSubsidyEntity.getCurrentSchool());
            map.put("grade", dataChildEducationSubsidyEntity.getGrade() == null ? "" : dataChildEducationSubsidyEntity.getGrade());
            map.put("admission_time", dataChildEducationSubsidyEntity.getAdmissionTime() == null ? "" : dataChildEducationSubsidyEntity.getAdmissionTime());
            map.put("year", now.get(Calendar.YEAR) + "");
            map.put("month", (now.get(Calendar.MONTH) + 1) + "");
            map.put("day", now.get(Calendar.DAY_OF_MONTH) + "");

            if ("幼儿园".equals(dataChildEducationSubsidyEntity.getEducation())) {
                map.put("edu_one", "√");
            }
            if ("小学".equals(dataChildEducationSubsidyEntity.getEducation())) {
                map.put("edu_two", "√");
            }
            if ("初中".equals(dataChildEducationSubsidyEntity.getEducation())) {
                map.put("edu_three", "√");
            }
            if ("高中（含职高）".equals(dataChildEducationSubsidyEntity.getEducation())) {
                map.put("edu_four", "√");
            }
            System.out.println(map);
            //map.put("applicantType",dataChildEducationSubsidyEntity.getApplicantType()==null?"":dataChildEducationSubsidyEntity.getApplicantType());
            //map.put("applicationTime",dataChildEducationSubsidyEntity.getApplicationTime()==null?"":dataChildEducationSubsidyEntity.getApplicationTime());
            //cesEntity.setXiaoTime(now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
            // cesEntity.setZhenTime(now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
            //cesEntity.setQuTime(now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");


            FileOutputStream out;
            int num = 1;//页数
            ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
            try {
                out = new FileOutputStream(newPDFPath);// 输出流
                Document doc = new Document();   //新建一个文档
                PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                doc.open();

                for (int i = 0; i < num; i++) {
                    bos[i] = new ByteArrayOutputStream();
                    PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                    PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                    AcroFields form = stamper.getAcroFields(); //获取文本域
                    // BaseFont
                    // 1、使用iTextAsian.jar中的字体
                    // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                    // 2、使用Windows系统字体(TrueType)
                    // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                    // 3、使用资源字体(ClassPath)
                    // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                    BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                    java.util.Iterator<String> it = form.getFields().keySet().iterator();
                    while (it.hasNext()) {
                        String name = it.next();
                        System.out.println(name + ":" + map.get(name));
                        form.setFieldProperty(name, "textfont", font, null);
                        // form.addSubstitutionFont(font);
                        form.setField(name, map.get(name) != null ? map.get(name) :"");
                    }

                    stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                    stamper.close();

                }
                PdfImportedPage page = null;
                for (int i = 0; i < num; i++) {
                    page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                    copy.addPage(page);
                }
                doc.close();
                out.close();
            } catch (IOException e) {
                e.printStackTrace();
                System.out.println("导出异常");
                throw e;
            } catch (DocumentException e) {
                e.printStackTrace();
                System.out.println("文档异常");

            }

            // 生成电子签章pdf 并保存
            //String filePath = pdfUtils.pdfNursingSubsidyApplyToSignature(cjroneProperties.getTempletePath(), cjroneProperties.getSignaturePath(), nsEntity);
            CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
            cjroneSignature.setUrl("/child_education_subsidy_" + dataChildEducationSubsidyEntity.getIdCard()+".pdf");
            cjroneSignature.setFileActUrl(newPDFPath);
            cjroneSignature.setCreateDate(new Date());
            cjroneSignature.setCreateId(getUserId());
            cjroneSignature.setType("惠残事项基础段残疾人子女教育补贴");
            cjroneSignature.setTypeId(dataChildEducationSubsidyEntity.getId());
            cjroneSignature.setFileName("child_education_subsidy_"+dataChildEducationSubsidyEntity.getIdCard()+".pdf");
            cjroneSignature.setAccountId(getUserId().toString());
            cjroneSignature.setAccountName(getUser().getUsername());
            cjroneSignature.setStatus("1");
            cjroneSignatureService.save(cjroneSignature);
            // return R.ok().put("fileName", "signature_"+ PinYinUtil.getFullSpell(cjroneDisabilityHospital.getName())+".pdf").put("signId",cjroneSignature.getId());
            return R.ok().put("fileUrl", cjroneSignature.getUrl()).put("fileName", cjroneSignature.getFileName()).put("signId",cjroneSignature.getId());
        }
    }


}
