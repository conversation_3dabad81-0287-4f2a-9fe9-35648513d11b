package com.hmit.kernespring.modules.data_management.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.data_management.dao.DataWorkInjuryInsuranceDao;
import com.hmit.kernespring.modules.data_management.entity.DataWorkInjuryInsuranceEntity;
import com.hmit.kernespring.modules.data_management.service.DataWorkInjuryInsuranceService;


@Service("dataWorkInjuryInsuranceService")
public class DataWorkInjuryInsuranceServiceImpl extends ServiceImpl<DataWorkInjuryInsuranceDao, DataWorkInjuryInsuranceEntity> implements DataWorkInjuryInsuranceService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private DataWorkInjuryInsuranceDao dataWorkInjuryInsuranceDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        DataWorkInjuryInsuranceEntity dataWorkInjuryInsuranceEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, DataWorkInjuryInsuranceEntity.class);
        IPage<DataWorkInjuryInsuranceEntity> page = this.page(
                new Query<DataWorkInjuryInsuranceEntity>().getPage(params),
                new QueryWrapper<DataWorkInjuryInsuranceEntity>()
            .eq(StringUtils.isNotBlank(dataWorkInjuryInsuranceEntity.getId ()!=null && !"".equals(dataWorkInjuryInsuranceEntity.getId ().toString())? dataWorkInjuryInsuranceEntity.getId ().toString():null),"id", dataWorkInjuryInsuranceEntity.getId ())
            .eq(StringUtils.isNotBlank(dataWorkInjuryInsuranceEntity.getName ()!=null && !"".equals(dataWorkInjuryInsuranceEntity.getName ().toString())? dataWorkInjuryInsuranceEntity.getName ().toString():null),"name", dataWorkInjuryInsuranceEntity.getName ())
            .eq(StringUtils.isNotBlank(dataWorkInjuryInsuranceEntity.getIdCard ()!=null && !"".equals(dataWorkInjuryInsuranceEntity.getIdCard ().toString())? dataWorkInjuryInsuranceEntity.getIdCard ().toString():null),"id_card", dataWorkInjuryInsuranceEntity.getIdCard ())
        );
        Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key","sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        page.getRecords().forEach( item -> {
        });
        return new PageUtils(page);
    }
    @Override
    public List<DataWorkInjuryInsuranceEntity> queryExportData(Map<String, Object> params) {
            return dataWorkInjuryInsuranceDao.queryExportData(params);
    }

}