package com.hmit.kernespring.modules.data_management.service.impl;


import com.google.gson.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.data_management.dao.FxinfoDao;
import com.hmit.kernespring.modules.data_management.entity.FxinfoEntity;
import com.hmit.kernespring.modules.data_management.service.FxinfoService;


@Service("fxinfoService")
public class FxinfoServiceImpl extends ServiceImpl<FxinfoDao, FxinfoEntity> implements FxinfoService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private FxinfoDao fxinfoDao;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        FxinfoEntity fxinfoEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, FxinfoEntity.class);
        IPage<FxinfoEntity> page = this.page(
                new Query<FxinfoEntity>().getPage(params),
                new QueryWrapper<FxinfoEntity>()
            .eq(StringUtils.isNotBlank(fxinfoEntity.getId ()!=null && !"".equals(fxinfoEntity.getId ().toString())? fxinfoEntity.getId ().toString():null),"id", fxinfoEntity.getId ())
            .eq(StringUtils.isNotBlank(fxinfoEntity.getName ()!=null && !"".equals(fxinfoEntity.getName ().toString())? fxinfoEntity.getName ().toString():null),"name", fxinfoEntity.getName ())
            .eq(StringUtils.isNotBlank(fxinfoEntity.getSex ()!=null && !"".equals(fxinfoEntity.getSex ().toString())? fxinfoEntity.getSex ().toString():null),"sex", fxinfoEntity.getSex ())
            .eq(StringUtils.isNotBlank(fxinfoEntity.getAge ()!=null && !"".equals(fxinfoEntity.getAge ().toString())? fxinfoEntity.getAge ().toString():null),"age", fxinfoEntity.getAge ())
            .eq(StringUtils.isNotBlank(fxinfoEntity.getBirthplace ()!=null && !"".equals(fxinfoEntity.getBirthplace ().toString())? fxinfoEntity.getBirthplace ().toString():null),"birthplace", fxinfoEntity.getBirthplace ())
            .eq(StringUtils.isNotBlank(fxinfoEntity.getIdCard ()!=null && !"".equals(fxinfoEntity.getIdCard ().toString())? fxinfoEntity.getIdCard ().toString():null),"id_card", fxinfoEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(fxinfoEntity.getFromInfo ()!=null && !"".equals(fxinfoEntity.getFromInfo ().toString())? fxinfoEntity.getFromInfo ().toString():null),"from_info", fxinfoEntity.getFromInfo ())
            .eq(StringUtils.isNotBlank(fxinfoEntity.getResultInfo ()!=null && !"".equals(fxinfoEntity.getResultInfo ().toString())? fxinfoEntity.getResultInfo ().toString():null),"result_info", fxinfoEntity.getResultInfo ())
        );
        return new PageUtils(page);
    }
    @Override
    public List<FxinfoEntity> queryExportData(Map<String, Object> params) {
            return fxinfoDao.queryExportData(params);
    }

}