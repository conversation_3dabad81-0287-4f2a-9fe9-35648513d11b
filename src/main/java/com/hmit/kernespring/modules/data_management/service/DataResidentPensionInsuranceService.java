package com.hmit.kernespring.modules.data_management.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.data_management.entity.DataResidentPensionInsuranceEntity;

import java.util.Map;

import java.util.List;

/**
 * 城乡居民养老保险补贴汇总表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-30 10:44:01
 */
public interface DataResidentPensionInsuranceService extends IService<DataResidentPensionInsuranceEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<DataResidentPensionInsuranceEntity> queryExportData(Map<String, Object> params);
}

