package com.hmit.kernespring.modules.data_management.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

/**
 * 残疾证申请表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-09 14:53:34
 */
@Data
public class DisabilityCertificateApplicationZCLEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	private String presentZhen;

	private Integer id;
	/**
	 * 序号
	 */
	@Excel(name = "序号", height = 20, width = 30, isImportField = "true_st")
	private Integer orderNum;

	/**
	 * 审核日期
	 */
	private String auditDate;

	/**
	 * 评定日期
	 */
	@Excel(name = "评定日期", height = 20, width = 30, isImportField = "true_st")
	private String assessmentDate;


	/**
	 * 镇街道
	 */
	@Excel(name = "镇街道", height = 20, width = 30, isImportField = "true_st")
	private String nativeZhenName;
	/**
	 * 残疾人姓名
	 */
	@Excel(name = "姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 性别  1 男  2女
	 */
	@Excel(name = "性别", height = 20, width = 30, isImportField = "true_st")
private String sex;
	/**
	 * 出生年月
	 */
	@Excel(name = "出生年月", height = 20, width = 30, isImportField = "true_st")
	private String birthday;
	/**
	 * 身份证
	 */
	@Excel(name = "身份证", height = 20, width = 30, isImportField = "true_st")
private String idCard;
	/**
	 * 婚姻情况  0未婚  1已婚
	 */
	@Excel(name = "残疾人证", height = 20, width = 30, isImportField = "true_st")
private String disableId;
	/**
	 * 户籍地址
	 */
	@Excel(name = "户籍地址", height = 20, width = 30, isImportField = "true_st")
private String nativeAddressName;
	/**
	 * 家庭地址
	 */
	@Excel(name = "家庭地址", height = 20, width = 30, isImportField = "true_st")
private String presentAddress;

	private String presentZhenName;

	private String presentCunName;
	/**
	 * 联系电话
	 */
	@Excel(name = "联系电话", height = 20, width = 30, isImportField = "true_st")
private String mobilePhone;
	/**
	 * 申请类型
	 */
	@Excel(name = "申请类型", height = 20, width = 30, isImportField = "true_st")
private String applicationType;
/**
	 * 残疾证号
	 */
	@Excel(name = "残疾评定类别", height = 20, width = 30, isImportField = "true_st")
private String disabilityType;
	/**
	 * 残疾类别名称
	 */
	@Excel(name = "具体评定类别", height = 20, width = 30, isImportField = "true_st")
	private String disabilityTypeName;
	/**
	 * 残疾等级
	 */
	private String disabilityDegree;
	/**
	 * 残疾等级
	 */
	@Excel(name = "残疾等级", height = 20, width = 30, isImportField = "true_st")
	private String disabilityDegreeName;
	/**
	 * 评定机构
	 */
private String pinCanAddress;

	/**
	 * 评定机构
	 */
	@Excel(name = "评定机构", height = 20, width = 30, isImportField = "true_st")
private String hospital;

	@Excel(name = "评定医生", height = 20, width = 30, isImportField = "true_st")
	private String physician;

	@Excel(name = "审核医生", height = 20, width = 30, isImportField = "true_st")
	private String auditPerson;

	/**
	 * 监护人或联系人姓名
	 */
	@Excel(name = "监护人或联系人姓名", height = 20, width = 30, isImportField = "true_st")
private String guardianName;

	/**
	 * 监护人或联系人联系电话
	 */
	@Excel(name = "监护人或联系人联系电话", height = 20, width = 30, isImportField = "true_st")
private String guardianPhone;

	@Excel(name = "关系", height = 20, width = 30, isImportField = "true_st")
private String guardianRelation;

	/**
	 * 备注
	 */
	@Excel(name = "备注", height = 20, width = 30, isImportField = "true_st")
private String remarks;

	/**
	 * 现地址
	 */
	@Excel(name = "精神致残原因", height = 20, width = 30, isImportField = "true_st")
private String disableReason;
}
