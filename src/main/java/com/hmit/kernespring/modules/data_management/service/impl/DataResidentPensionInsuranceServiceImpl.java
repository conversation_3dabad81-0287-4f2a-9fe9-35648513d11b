package com.hmit.kernespring.modules.data_management.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.data_management.dao.DataResidentPensionInsuranceDao;
import com.hmit.kernespring.modules.data_management.entity.DataResidentPensionInsuranceEntity;
import com.hmit.kernespring.modules.data_management.service.DataResidentPensionInsuranceService;


@Service("dataResidentPensionInsuranceService")
public class DataResidentPensionInsuranceServiceImpl extends ServiceImpl<DataResidentPensionInsuranceDao, DataResidentPensionInsuranceEntity> implements DataResidentPensionInsuranceService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private DataResidentPensionInsuranceDao dataResidentPensionInsuranceDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        DataResidentPensionInsuranceEntity dataResidentPensionInsuranceEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, DataResidentPensionInsuranceEntity.class);
        IPage<DataResidentPensionInsuranceEntity> page = this.page(
                new Query<DataResidentPensionInsuranceEntity>().getPage(params),
                new QueryWrapper<DataResidentPensionInsuranceEntity>()
            .eq(StringUtils.isNotBlank(dataResidentPensionInsuranceEntity.getId ()!=null && !"".equals(dataResidentPensionInsuranceEntity.getId ().toString())? dataResidentPensionInsuranceEntity.getId ().toString():null),"id", dataResidentPensionInsuranceEntity.getId ())
            .eq(StringUtils.isNotBlank(dataResidentPensionInsuranceEntity.getName ()!=null && !"".equals(dataResidentPensionInsuranceEntity.getName ().toString())? dataResidentPensionInsuranceEntity.getName ().toString():null),"name", dataResidentPensionInsuranceEntity.getName ())
            .eq(StringUtils.isNotBlank(dataResidentPensionInsuranceEntity.getIdCard ()!=null && !"".equals(dataResidentPensionInsuranceEntity.getIdCard ().toString())? dataResidentPensionInsuranceEntity.getIdCard ().toString():null),"id_card", dataResidentPensionInsuranceEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(dataResidentPensionInsuranceEntity.getProjectName ()!=null && !"".equals(dataResidentPensionInsuranceEntity.getProjectName ().toString())? dataResidentPensionInsuranceEntity.getProjectName ().toString():null),"project_name", dataResidentPensionInsuranceEntity.getProjectName ())
            .eq(StringUtils.isNotBlank(dataResidentPensionInsuranceEntity.getSubsidy ()!=null && !"".equals(dataResidentPensionInsuranceEntity.getSubsidy ().toString())? dataResidentPensionInsuranceEntity.getSubsidy ().toString():null),"subsidy", dataResidentPensionInsuranceEntity.getSubsidy ())
            .eq(StringUtils.isNotBlank(dataResidentPensionInsuranceEntity.getMobilePhone ()!=null && !"".equals(dataResidentPensionInsuranceEntity.getMobilePhone ().toString())? dataResidentPensionInsuranceEntity.getMobilePhone ().toString():null),"mobile_phone", dataResidentPensionInsuranceEntity.getMobilePhone ())
            .eq(StringUtils.isNotBlank(dataResidentPensionInsuranceEntity.getAddress ()!=null && !"".equals(dataResidentPensionInsuranceEntity.getAddress ().toString())? dataResidentPensionInsuranceEntity.getAddress ().toString():null),"address", dataResidentPensionInsuranceEntity.getAddress ())
            .eq(StringUtils.isNotBlank(dataResidentPensionInsuranceEntity.getStatus ()!=null && !"".equals(dataResidentPensionInsuranceEntity.getStatus ().toString())? dataResidentPensionInsuranceEntity.getStatus ().toString():null),"status", dataResidentPensionInsuranceEntity.getStatus ())
            .eq(StringUtils.isNotBlank(dataResidentPensionInsuranceEntity.getCreateId ()!=null && !"".equals(dataResidentPensionInsuranceEntity.getCreateId ().toString())? dataResidentPensionInsuranceEntity.getCreateId ().toString():null),"create_id", dataResidentPensionInsuranceEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(dataResidentPensionInsuranceEntity.getCreateTime ()!=null && !"".equals(dataResidentPensionInsuranceEntity.getCreateTime ().toString())? dataResidentPensionInsuranceEntity.getCreateTime ().toString():null),"create_time", dataResidentPensionInsuranceEntity.getCreateTime ())
        );
        Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key","sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        page.getRecords().forEach( item -> {
            SysDictEntity status_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("zt_0000") && iii.getValue().equals(
                        item.getStatus ())).findAny().orElse(null);
            if (status_sysDictEntity != null){
                item.setStatus (status_sysDictEntity.getLabel());
            }else{
                item.setStatus (null);
            }
        });
        return new PageUtils(page);
    }
    @Override
    public List<DataResidentPensionInsuranceEntity> queryExportData(Map<String, Object> params) {
            return dataResidentPensionInsuranceDao.queryExportData(params);
    }

}