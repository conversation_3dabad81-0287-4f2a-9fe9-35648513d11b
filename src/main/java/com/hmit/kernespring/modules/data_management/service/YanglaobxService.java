package com.hmit.kernespring.modules.data_management.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.data_management.entity.YanglaobxEntity;

import java.util.Map;

import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-09-15 11:13:17
 */
public interface YanglaobxService extends IService<YanglaobxEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<YanglaobxEntity> queryExportData(Map<String, Object> params);
}

