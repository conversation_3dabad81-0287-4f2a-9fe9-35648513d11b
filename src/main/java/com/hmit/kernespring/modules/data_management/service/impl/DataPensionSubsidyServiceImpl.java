package com.hmit.kernespring.modules.data_management.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;
import com.hmit.kernespring.modules.data_management.dao.DataPensionSubsidyDao;
import com.hmit.kernespring.modules.data_management.entity.DataPensionSubsidyEntity;
import com.hmit.kernespring.modules.data_management.service.DataPensionSubsidyService;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Type;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service("dataPensionSubsidyService")
public class DataPensionSubsidyServiceImpl extends ServiceImpl<DataPensionSubsidyDao, DataPensionSubsidyEntity> implements DataPensionSubsidyService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private DataPensionSubsidyDao dataPensionSubsidyDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        DataPensionSubsidyEntity dataPensionSubsidyEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, DataPensionSubsidyEntity.class);
        IPage<DataPensionSubsidyEntity> page = this.page(
                new Query<DataPensionSubsidyEntity>().getPage(params),
                new QueryWrapper<DataPensionSubsidyEntity>()
            .eq(StringUtils.isNotBlank(dataPensionSubsidyEntity.getId ()!=null && !"".equals(dataPensionSubsidyEntity.getId ().toString())? dataPensionSubsidyEntity.getId ().toString():null),"id", dataPensionSubsidyEntity.getId ())
            .eq(StringUtils.isNotBlank(dataPensionSubsidyEntity.getTwon ()!=null && !"".equals(dataPensionSubsidyEntity.getTwon ().toString())? dataPensionSubsidyEntity.getTwon ().toString():null),"twon", dataPensionSubsidyEntity.getTwon ())
            .eq(StringUtils.isNotBlank(dataPensionSubsidyEntity.getVillage ()!=null && !"".equals(dataPensionSubsidyEntity.getVillage ().toString())? dataPensionSubsidyEntity.getVillage ().toString():null),"village", dataPensionSubsidyEntity.getVillage ())
            .eq(StringUtils.isNotBlank(dataPensionSubsidyEntity.getName ()!=null && !"".equals(dataPensionSubsidyEntity.getName ().toString())? dataPensionSubsidyEntity.getName ().toString():null),"name", dataPensionSubsidyEntity.getName ())
            .eq(StringUtils.isNotBlank(dataPensionSubsidyEntity.getSex ()!=null && !"".equals(dataPensionSubsidyEntity.getSex ().toString())? dataPensionSubsidyEntity.getSex ().toString():null),"sex", dataPensionSubsidyEntity.getSex ())
            .eq(StringUtils.isNotBlank(dataPensionSubsidyEntity.getIdCard ()!=null && !"".equals(dataPensionSubsidyEntity.getIdCard ().toString())? dataPensionSubsidyEntity.getIdCard ().toString():null),"id_card", dataPensionSubsidyEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(dataPensionSubsidyEntity.getDementiaDegree ()!=null && !"".equals(dataPensionSubsidyEntity.getDementiaDegree ().toString())? dataPensionSubsidyEntity.getDementiaDegree ().toString():null),"dementia_degree", dataPensionSubsidyEntity.getDementiaDegree ())
            .eq(StringUtils.isNotBlank(dataPensionSubsidyEntity.getSubsidyAmount ()!=null && !"".equals(dataPensionSubsidyEntity.getSubsidyAmount ().toString())? dataPensionSubsidyEntity.getSubsidyAmount ().toString():null),"subsidy_amount", dataPensionSubsidyEntity.getSubsidyAmount ())
            .eq(StringUtils.isNotBlank(dataPensionSubsidyEntity.getSubsidyMonth ()!=null && !"".equals(dataPensionSubsidyEntity.getSubsidyMonth ().toString())? dataPensionSubsidyEntity.getSubsidyMonth ().toString():null),"subsidy_month", dataPensionSubsidyEntity.getSubsidyMonth ())
            .eq(StringUtils.isNotBlank(dataPensionSubsidyEntity.getTotal ()!=null && !"".equals(dataPensionSubsidyEntity.getTotal ().toString())? dataPensionSubsidyEntity.getTotal ().toString():null),"total", dataPensionSubsidyEntity.getTotal ())
            .eq(StringUtils.isNotBlank(dataPensionSubsidyEntity.getRemark ()!=null && !"".equals(dataPensionSubsidyEntity.getRemark ().toString())? dataPensionSubsidyEntity.getRemark ().toString():null),"remark", dataPensionSubsidyEntity.getRemark ())
        );
        Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key","sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        page.getRecords().forEach( item -> {
        });
        return new PageUtils(page);
    }
    @Override
    public List<DataPensionSubsidyEntity> queryExportData(Map<String, Object> params) {
            return dataPensionSubsidyDao.queryExportData(params);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatch(Collection<DataPensionSubsidyEntity> entityList) {

        dataPensionSubsidyDao.deleteAllData(null);

        return super.saveBatch(entityList);
    }

}