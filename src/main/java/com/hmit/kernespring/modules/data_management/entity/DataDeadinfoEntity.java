package com.hmit.kernespring.modules.data_management.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 死亡名单导入信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-07 10:23:14
 */
@Data
@TableName("data_deadinfo")
public class DataDeadinfoEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 *
	 */

@TableId
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 业务编号
	 */
	@Excel(name = "业务编号", height = 20, width = 30, isImportField = "true_st")
private String businessNumber;
	/**
	 * 姓名
	 */
	@Excel(name = "姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 性别
	 */
	@Excel(name = "性别", height = 20, width = 30, isImportField = "true_st")
private String sex;
	/**
	 * 年龄
	 */
	@Excel(name = "年龄", height = 20, width = 30, isImportField = "true_st")
private String age;
	/**
	 * 籍贯
	 */
	@Excel(name = "籍贯", height = 20, width = 30, isImportField = "true_st")
private String birthplace;
	/**
	 * 身份证号
	 */
	@Excel(name = "身份证号", height = 20, width = 30, isImportField = "true_st")
private String idCard;
	/**
	 * 户籍地址
	 */
	@Excel(name = "户籍地址", height = 20, width = 30, isImportField = "true_st")
private String residenceAddress;
	/**
	 * 死亡日期
	 */
	@Excel(name = "死亡日期", height = 20, width = 30, isImportField = "true_st")
private String deadDate;
	/**
	 * 火化日期
	 */
	@Excel(name = "火化日期", height = 20, width = 30, isImportField = "true_st")
private String cremationDate;
	/**
	 * 接尸日期
	 */
	@Excel(name = "接尸日期", height = 20, width = 30, isImportField = "true_st")
private String getDate;
	/**
	 * 接尸地址
	 */
	@Excel(name = "接尸地址", height = 20, width = 30, isImportField = "true_st")
private String getAddress;
	/**
	 * 家属姓名
	 */
	@Excel(name = "家属姓名", height = 20, width = 30, isImportField = "true_st")
private String familyName;
	/**
	 * 与死者关系
	 */
	@Excel(name = "与死者关系", height = 20, width = 30, isImportField = "true_st")
private String relation;
	/**
	 * 联系电话
	 */
	@Excel(name = "联系电话", height = 20, width = 30, isImportField = "true_st")
private String phone;

	// 接口来源
	private String fromInfo;

	//返回JSON
	private String resultInfo;

}
