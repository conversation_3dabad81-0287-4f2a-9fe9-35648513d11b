package com.hmit.kernespring.modules.data_management.dao;

import com.hmit.kernespring.modules.data_management.entity.YanglaobxEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-09-15 11:13:17
 */
@Mapper
public interface YanglaobxDao extends BaseMapper<YanglaobxEntity> {
    List<YanglaobxEntity> queryExportData(Map<String, Object> params);
	
}
