package com.hmit.kernespring.modules.data_management.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-14 17:09:45
 */
@Data
@TableName("data_child_education_subsidy_money")
public class DataChildEducationSubsidyMoneyEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
@TableId
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 家庭经济情况
	 */
	@Excel(name = "家庭经济情况", height = 20, width = 30, isImportField = "true_st")
private String familyEconomicSituation;
	/**
	 * 学历
	 */
	@Excel(name = "学历", height = 20, width = 30, isImportField = "true_st")
private String education;
	/**
	 * 补贴金额
	 */
	@Excel(name = "补贴金额", height = 20, width = 30, isImportField = "true_st")
private Double subsidyMoney;

}
