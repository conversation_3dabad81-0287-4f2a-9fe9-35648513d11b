package com.hmit.kernespring.modules.data_management.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.data_management.dao.DataLowSecurityDao;
import com.hmit.kernespring.modules.data_management.entity.DataLowSecurityEntity;
import com.hmit.kernespring.modules.data_management.service.DataLowSecurityService;
import org.springframework.transaction.annotation.Transactional;


@Service("dataLowSecurityService")
public class DataLowSecurityServiceImpl extends ServiceImpl<DataLowSecurityDao, DataLowSecurityEntity> implements DataLowSecurityService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private DataLowSecurityDao dataLowSecurityDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        DataLowSecurityEntity dataLowSecurityEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, DataLowSecurityEntity.class);
        IPage<DataLowSecurityEntity> page = this.page(
                new Query<DataLowSecurityEntity>().getPage(params),
                new QueryWrapper<DataLowSecurityEntity>()
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getId ()!=null && !"".equals(dataLowSecurityEntity.getId ().toString())? dataLowSecurityEntity.getId ().toString():null),"id", dataLowSecurityEntity.getId ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getAdministrativeDivision ()!=null && !"".equals(dataLowSecurityEntity.getAdministrativeDivision ().toString())? dataLowSecurityEntity.getAdministrativeDivision ().toString():null),"administrative_division", dataLowSecurityEntity.getAdministrativeDivision ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getApplicant ()!=null && !"".equals(dataLowSecurityEntity.getApplicant ().toString())? dataLowSecurityEntity.getApplicant ().toString():null),"applicant", dataLowSecurityEntity.getApplicant ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getApplyIdCard ()!=null && !"".equals(dataLowSecurityEntity.getApplyIdCard ().toString())? dataLowSecurityEntity.getApplyIdCard ().toString():null),"apply_id_card", dataLowSecurityEntity.getApplyIdCard ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getName ()!=null && !"".equals(dataLowSecurityEntity.getName ().toString())? dataLowSecurityEntity.getName ().toString():null),"name", dataLowSecurityEntity.getName ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getRelationshipWithHousehold ()!=null && !"".equals(dataLowSecurityEntity.getRelationshipWithHousehold ().toString())? dataLowSecurityEntity.getRelationshipWithHousehold ().toString():null),"relationship_with_household", dataLowSecurityEntity.getRelationshipWithHousehold ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getIdCard ()!=null && !"".equals(dataLowSecurityEntity.getIdCard ().toString())? dataLowSecurityEntity.getIdCard ().toString():null),"id_card", dataLowSecurityEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getPersonnelInformationCategory ()!=null && !"".equals(dataLowSecurityEntity.getPersonnelInformationCategory ().toString())? dataLowSecurityEntity.getPersonnelInformationCategory ().toString():null),"personnel_information_category", dataLowSecurityEntity.getPersonnelInformationCategory ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getSex ()!=null && !"".equals(dataLowSecurityEntity.getSex ().toString())? dataLowSecurityEntity.getSex ().toString():null),"sex", dataLowSecurityEntity.getSex ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getBirthday ()!=null && !"".equals(dataLowSecurityEntity.getBirthday ().toString())? dataLowSecurityEntity.getBirthday ().toString():null),"birthday", dataLowSecurityEntity.getBirthday ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getAge ()!=null && !"".equals(dataLowSecurityEntity.getAge ().toString())? dataLowSecurityEntity.getAge ().toString():null),"age", dataLowSecurityEntity.getAge ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getApplicationCategory ()!=null && !"".equals(dataLowSecurityEntity.getApplicationCategory ().toString())? dataLowSecurityEntity.getApplicationCategory ().toString():null),"application_category", dataLowSecurityEntity.getApplicationCategory ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getRescueCertificateNumber ()!=null && !"".equals(dataLowSecurityEntity.getRescueCertificateNumber ().toString())? dataLowSecurityEntity.getRescueCertificateNumber ().toString():null),"rescue_certificate_number", dataLowSecurityEntity.getRescueCertificateNumber ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getBankName ()!=null && !"".equals(dataLowSecurityEntity.getBankName ().toString())? dataLowSecurityEntity.getBankName ().toString():null),"bank_name", dataLowSecurityEntity.getBankName ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getPersonBankAccount ()!=null && !"".equals(dataLowSecurityEntity.getPersonBankAccount ().toString())? dataLowSecurityEntity.getPersonBankAccount ().toString():null),"person_bank_account", dataLowSecurityEntity.getPersonBankAccount ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getEducationalLevel ()!=null && !"".equals(dataLowSecurityEntity.getEducationalLevel ().toString())? dataLowSecurityEntity.getEducationalLevel ().toString():null),"educational_level", dataLowSecurityEntity.getEducationalLevel ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getPoliticalStatus ()!=null && !"".equals(dataLowSecurityEntity.getPoliticalStatus ().toString())? dataLowSecurityEntity.getPoliticalStatus ().toString():null),"political_status", dataLowSecurityEntity.getPoliticalStatus ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getMaritalStatus ()!=null && !"".equals(dataLowSecurityEntity.getMaritalStatus ().toString())? dataLowSecurityEntity.getMaritalStatus ().toString():null),"marital_status", dataLowSecurityEntity.getMaritalStatus ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getNationality ()!=null && !"".equals(dataLowSecurityEntity.getNationality ().toString())? dataLowSecurityEntity.getNationality ().toString():null),"nationality", dataLowSecurityEntity.getNationality ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getMonthlyPerCapitaIncome ()!=null && !"".equals(dataLowSecurityEntity.getMonthlyPerCapitaIncome ().toString())? dataLowSecurityEntity.getMonthlyPerCapitaIncome ().toString():null),"monthly_per_capita_income", dataLowSecurityEntity.getMonthlyPerCapitaIncome ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getGuaranteeTheTotalPopulation ()!=null && !"".equals(dataLowSecurityEntity.getGuaranteeTheTotalPopulation ().toString())? dataLowSecurityEntity.getGuaranteeTheTotalPopulation ().toString():null),"guarantee_the_total_population", dataLowSecurityEntity.getGuaranteeTheTotalPopulation ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getHouseholdSecurity ()!=null && !"".equals(dataLowSecurityEntity.getHouseholdSecurity ().toString())? dataLowSecurityEntity.getHouseholdSecurity ().toString():null),"household_security", dataLowSecurityEntity.getHouseholdSecurity ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getOtherSubsidies ()!=null && !"".equals(dataLowSecurityEntity.getOtherSubsidies ().toString())? dataLowSecurityEntity.getOtherSubsidies ().toString():null),"other_subsidies", dataLowSecurityEntity.getOtherSubsidies ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getTotalAmountOfProtection ()!=null && !"".equals(dataLowSecurityEntity.getTotalAmountOfProtection ().toString())? dataLowSecurityEntity.getTotalAmountOfProtection ().toString():null),"total_amount_of_protection", dataLowSecurityEntity.getTotalAmountOfProtection ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getAccountNature ()!=null && !"".equals(dataLowSecurityEntity.getAccountNature ().toString())? dataLowSecurityEntity.getAccountNature ().toString():null),"account_nature", dataLowSecurityEntity.getAccountNature ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getPersonnelCategory ()!=null && !"".equals(dataLowSecurityEntity.getPersonnelCategory ().toString())? dataLowSecurityEntity.getPersonnelCategory ().toString():null),"personnel_category", dataLowSecurityEntity.getPersonnelCategory ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getHealthStatus ()!=null && !"".equals(dataLowSecurityEntity.getHealthStatus ().toString())? dataLowSecurityEntity.getHealthStatus ().toString():null),"health_status", dataLowSecurityEntity.getHealthStatus ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getCareerStatus ()!=null && !"".equals(dataLowSecurityEntity.getCareerStatus ().toString())? dataLowSecurityEntity.getCareerStatus ().toString():null),"career_status", dataLowSecurityEntity.getCareerStatus ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getDisabilityCategory ()!=null && !"".equals(dataLowSecurityEntity.getDisabilityCategory ().toString())? dataLowSecurityEntity.getDisabilityCategory ().toString():null),"disability_category", dataLowSecurityEntity.getDisabilityCategory ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getDisabilityLevel ()!=null && !"".equals(dataLowSecurityEntity.getDisabilityLevel ().toString())? dataLowSecurityEntity.getDisabilityLevel ().toString():null),"disability_level", dataLowSecurityEntity.getDisabilityLevel ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getDisableId ()!=null && !"".equals(dataLowSecurityEntity.getDisableId ().toString())? dataLowSecurityEntity.getDisableId ().toString():null),"disable_id", dataLowSecurityEntity.getDisableId ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getDateOfSalvage ()!=null && !"".equals(dataLowSecurityEntity.getDateOfSalvage ().toString())? dataLowSecurityEntity.getDateOfSalvage ().toString():null),"date_of_salvage", dataLowSecurityEntity.getDateOfSalvage ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getSubsidiaryCategory ()!=null && !"".equals(dataLowSecurityEntity.getSubsidiaryCategory ().toString())? dataLowSecurityEntity.getSubsidiaryCategory ().toString():null),"subsidiary_category", dataLowSecurityEntity.getSubsidiaryCategory ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getTelephone ()!=null && !"".equals(dataLowSecurityEntity.getTelephone ().toString())? dataLowSecurityEntity.getTelephone ().toString():null),"telephone", dataLowSecurityEntity.getTelephone ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getMobilePhone ()!=null && !"".equals(dataLowSecurityEntity.getMobilePhone ().toString())? dataLowSecurityEntity.getMobilePhone ().toString():null),"mobile_phone", dataLowSecurityEntity.getMobilePhone ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getBankAccount ()!=null && !"".equals(dataLowSecurityEntity.getBankAccount ().toString())? dataLowSecurityEntity.getBankAccount ().toString():null),"bank_account", dataLowSecurityEntity.getBankAccount ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getPerCapitaSecurity ()!=null && !"".equals(dataLowSecurityEntity.getPerCapitaSecurity ().toString())? dataLowSecurityEntity.getPerCapitaSecurity ().toString():null),"per_capita_security", dataLowSecurityEntity.getPerCapitaSecurity ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getCreateId ()!=null && !"".equals(dataLowSecurityEntity.getCreateId ().toString())? dataLowSecurityEntity.getCreateId ().toString():null),"create_id", dataLowSecurityEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(dataLowSecurityEntity.getCreateTime ()!=null && !"".equals(dataLowSecurityEntity.getCreateTime ().toString())? dataLowSecurityEntity.getCreateTime ().toString():null),"create_time", dataLowSecurityEntity.getCreateTime ())
        );
        Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key","sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        page.getRecords().forEach( item -> {
            SysDictEntity personnelInformationCategory_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("ryxxlb_0000") && iii.getValue().equals(
                        item.getPersonnelInformationCategory ())).findAny().orElse(null);
            if (personnelInformationCategory_sysDictEntity != null){
                item.setPersonnelInformationCategory (personnelInformationCategory_sysDictEntity.getLabel());
            }else{
                item.setPersonnelInformationCategory (null);
            }
           /* SysDictEntity applicationCategory_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("sqlb_0000") && iii.getValue().equals(
                        item.getApplicationCategory ())).findAny().orElse(null);
            if (applicationCategory_sysDictEntity != null){
                item.setApplicationCategory (applicationCategory_sysDictEntity.getLabel());
            }else{
                item.setApplicationCategory (null);
            }*/
            SysDictEntity personnelCategory_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("rylb_0000") && iii.getValue().equals(
                        item.getPersonnelCategory ())).findAny().orElse(null);
            if (personnelCategory_sysDictEntity != null){
                item.setPersonnelCategory (personnelCategory_sysDictEntity.getLabel());
            }else{
                item.setPersonnelCategory (null);
            }
           /* SysDictEntity disabilityCategory_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("cjlb_0000") && iii.getValue().equals(
                        item.getDisabilityCategory ())).findAny().orElse(null);
            if (disabilityCategory_sysDictEntity != null){
                item.setDisabilityCategory (disabilityCategory_sysDictEntity.getLabel());
            }else{
                item.setDisabilityCategory (null);
            }*/
            /*SysDictEntity disabilityLevel_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("cjdj_0000") && iii.getValue().equals(
                        item.getDisabilityLevel ())).findAny().orElse(null);
            if (disabilityLevel_sysDictEntity != null){
                item.setDisabilityLevel (disabilityLevel_sysDictEntity.getLabel());
            }else{
                item.setDisabilityLevel (null);
            }*/
            SysDictEntity subsidiaryCategory_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("dblb_0000") && iii.getValue().equals(
                        item.getSubsidiaryCategory ())).findAny().orElse(null);
            if (subsidiaryCategory_sysDictEntity != null){
                item.setSubsidiaryCategory (subsidiaryCategory_sysDictEntity.getLabel());
            }else{
                item.setSubsidiaryCategory (null);
            }
        });
        return new PageUtils(page);
    }
    @Override
    public List<DataLowSecurityEntity> queryExportData(Map<String, Object> params) {
            return dataLowSecurityDao.queryExportData(params);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatch(Collection<DataLowSecurityEntity> entityList) {
        //删除
        dataLowSecurityDao.deleteAllData(null);

        return super.saveBatch(entityList);
    }

}