package com.hmit.kernespring.modules.data_management.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;
import com.hmit.kernespring.modules.data_management.dao.DataChildEducationSubsidyMoneyDao;
import com.hmit.kernespring.modules.data_management.entity.DataChildEducationSubsidyMoneyEntity;
import com.hmit.kernespring.modules.data_management.service.DataChildEducationSubsidyMoneyService;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service("dataChildEducationSubsidyMoneyService")
public class DataChildEducationSubsidyMoneyServiceImpl extends ServiceImpl<DataChildEducationSubsidyMoneyDao, DataChildEducationSubsidyMoneyEntity> implements DataChildEducationSubsidyMoneyService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private DataChildEducationSubsidyMoneyDao dataChildEducationSubsidyMoneyDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        DataChildEducationSubsidyMoneyEntity dataChildEducationSubsidyMoneyEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, DataChildEducationSubsidyMoneyEntity.class);
        IPage<DataChildEducationSubsidyMoneyEntity> page = this.page(
                new Query<DataChildEducationSubsidyMoneyEntity>().getPage(params),
                new QueryWrapper<DataChildEducationSubsidyMoneyEntity>()
            .eq(StringUtils.isNotBlank(dataChildEducationSubsidyMoneyEntity.getId ()!=null && !"".equals(dataChildEducationSubsidyMoneyEntity.getId ().toString())? dataChildEducationSubsidyMoneyEntity.getId ().toString():null),"id", dataChildEducationSubsidyMoneyEntity.getId ())
            .eq(StringUtils.isNotBlank(dataChildEducationSubsidyMoneyEntity.getFamilyEconomicSituation ()!=null && !"".equals(dataChildEducationSubsidyMoneyEntity.getFamilyEconomicSituation ().toString())? dataChildEducationSubsidyMoneyEntity.getFamilyEconomicSituation ().toString():null),"family_economic_situation", dataChildEducationSubsidyMoneyEntity.getFamilyEconomicSituation ())
            .eq(StringUtils.isNotBlank(dataChildEducationSubsidyMoneyEntity.getEducation ()!=null && !"".equals(dataChildEducationSubsidyMoneyEntity.getEducation ().toString())? dataChildEducationSubsidyMoneyEntity.getEducation ().toString():null),"education", dataChildEducationSubsidyMoneyEntity.getEducation ())
            .eq(StringUtils.isNotBlank(dataChildEducationSubsidyMoneyEntity.getSubsidyMoney ()!=null && !"".equals(dataChildEducationSubsidyMoneyEntity.getSubsidyMoney ().toString())? dataChildEducationSubsidyMoneyEntity.getSubsidyMoney ().toString():null),"subsidy_money", dataChildEducationSubsidyMoneyEntity.getSubsidyMoney ())
        );
        Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key","sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        page.getRecords().forEach( item -> {
            SysDictEntity familyEconomicSituation_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("jtjjqk_0000") && iii.getValue().equals(
                        item.getFamilyEconomicSituation ())).findAny().orElse(null);
            if (familyEconomicSituation_sysDictEntity != null){
                item.setFamilyEconomicSituation (familyEconomicSituation_sysDictEntity.getLabel());
            }else{
                item.setFamilyEconomicSituation (null);
            }
        });
        return new PageUtils(page);
    }

    @Override
    public List<DataChildEducationSubsidyMoneyEntity> queryExportData(Map<String, Object> params) {
            return dataChildEducationSubsidyMoneyDao.queryExportData(params);
    }

    @Override
    public List<DataChildEducationSubsidyMoneyEntity> queryByMap(Map<String, Object> params) {
        return dataChildEducationSubsidyMoneyDao.queryByMap(params);
    }

}