package com.hmit.kernespring.modules.data_management.controller;

import java.io.File;
import java.io.IOException;
import java.util.*;

import java.io.OutputStream;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import org.apache.poi.ss.usermodel.Workbook;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hmit.kernespring.modules.data_management.entity.DataPensionSubsidyEntity;
import com.hmit.kernespring.modules.data_management.service.DataPensionSubsidyService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 60周岁养老补贴导入表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-27 13:58:44
 */
@RestController
@RequestMapping("data_management/datapensionsubsidy")
public class DataPensionSubsidyController {
    @Autowired
    private DataPensionSubsidyService dataPensionSubsidyService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("data_management:datapensionsubsidy:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = dataPensionSubsidyService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("data_management:datapensionsubsidy:info")
    public R info(@PathVariable("id") Integer id){
		DataPensionSubsidyEntity dataPensionSubsidy = dataPensionSubsidyService.getById(id);

        return R.ok().put("dataPensionSubsidy", dataPensionSubsidy);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("data_management:datapensionsubsidy:save")
    public R save(@RequestBody DataPensionSubsidyEntity dataPensionSubsidy){
		dataPensionSubsidyService.save(dataPensionSubsidy);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("data_management:datapensionsubsidy:update")
    public R update(@RequestBody DataPensionSubsidyEntity dataPensionSubsidy){
		dataPensionSubsidyService.updateById(dataPensionSubsidy);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("data_management:datapensionsubsidy:delete")
    public R delete(@RequestBody Integer[] ids){
		dataPensionSubsidyService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    // @RequiresPermissions("data_management:datapensionsubsidy:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<DataPensionSubsidyEntity> dataPensionSubsidyList = new ArrayList<>();

        list.forEach(item ->{
                    if (null != item.get("身份证") && !"".equals(item.get("身份证").toString()) || (null != item.get("身份证号") && !"".equals(item.get("身份证号").toString()))) {
                        item.put("id", item.get(""));
                        item.put("twon", item.get("镇（街道）"));
                        item.put("village", item.get("村"));
                        item.put("name", item.get("姓名"));
                        item.put("sex", item.get("性别"));
                        item.put("idCard", item.get("身份证"));
                        item.put("dementiaDegree", item.get("失智失能程度"));
                        item.put("subsidyAmount", item.get("补助金额（元）"));
                        item.put("subsidyMonth", item.get("补助月数（个）"));
                        item.put("total", item.get("合计（元）"));
                        item.put("remark", item.get("备注"));
                        dataPensionSubsidyList.add(new Gson().fromJson(new Gson().toJson(item), DataPensionSubsidyEntity.class));
                    }
        });
        // 保存到数据库
        dataPensionSubsidyService.saveBatch(dataPensionSubsidyList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    // @RequiresPermissions("data_management:datapensionsubsidy:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<DataPensionSubsidyEntity> dataPensionSubsidyEntityList = dataPensionSubsidyService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("60周岁养老补贴导入表", null, "60周岁养老补贴导入表");
        Workbook workbook = ExcelExportUtil.exportExcel(params, DataPensionSubsidyEntity.class, dataPensionSubsidyEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "60周岁养老补贴导入表" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

}
