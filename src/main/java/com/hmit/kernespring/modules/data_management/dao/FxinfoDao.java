package com.hmit.kernespring.modules.data_management.dao;

import com.hmit.kernespring.modules.data_management.entity.FxinfoEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 服刑数据信息

 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-12-18 16:37:13
 */
@Mapper
public interface FxinfoDao extends BaseMapper<FxinfoEntity> {
    List<FxinfoEntity> queryExportData(Map<String, Object> params);
	
}
