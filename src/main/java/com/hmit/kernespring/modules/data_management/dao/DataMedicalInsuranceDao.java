package com.hmit.kernespring.modules.data_management.dao;

import com.hmit.kernespring.modules.data_management.entity.DataMedicalInsuranceEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 医保参保导入表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-27 14:00:03
 */
@Mapper
public interface DataMedicalInsuranceDao extends BaseMapper<DataMedicalInsuranceEntity> {
    List<DataMedicalInsuranceEntity> queryExportData(Map<String, Object> params);
	
}
