package com.hmit.kernespring.modules.data_management.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.data_management.entity.FxinfoEntity;

import java.util.Map;

import java.util.List;

/**
 * 服刑数据信息

 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-12-18 16:37:13
 */
public interface FxinfoService extends IService<FxinfoEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<FxinfoEntity> queryExportData(Map<String, Object> params);
}

