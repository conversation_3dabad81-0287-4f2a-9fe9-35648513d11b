package com.hmit.kernespring.modules.data_management.controller;

import java.io.File;
import java.io.IOException;
import java.util.*;

import java.io.OutputStream;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import org.apache.poi.ss.usermodel.Workbook;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hmit.kernespring.modules.data_management.entity.DataIndividualPensionSubsidyEntity;
import com.hmit.kernespring.modules.data_management.service.DataIndividualPensionSubsidyService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 个体工商户养老补贴
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-27 14:00:04
 */
@RestController
@RequestMapping("data_management/dataindividualpensionsubsidy")
public class DataIndividualPensionSubsidyController {
    @Autowired
    private DataIndividualPensionSubsidyService dataIndividualPensionSubsidyService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("data_management:dataindividualpensionsubsidy:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = dataIndividualPensionSubsidyService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("data_management:dataindividualpensionsubsidy:info")
    public R info(@PathVariable("id") Integer id){
		DataIndividualPensionSubsidyEntity dataIndividualPensionSubsidy = dataIndividualPensionSubsidyService.getById(id);

        return R.ok().put("dataIndividualPensionSubsidy", dataIndividualPensionSubsidy);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("data_management:dataindividualpensionsubsidy:save")
    public R save(@RequestBody DataIndividualPensionSubsidyEntity dataIndividualPensionSubsidy){
		dataIndividualPensionSubsidyService.save(dataIndividualPensionSubsidy);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("data_management:dataindividualpensionsubsidy:update")
    public R update(@RequestBody DataIndividualPensionSubsidyEntity dataIndividualPensionSubsidy){
		dataIndividualPensionSubsidyService.updateById(dataIndividualPensionSubsidy);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("data_management:dataindividualpensionsubsidy:delete")
    public R delete(@RequestBody Integer[] ids){
		dataIndividualPensionSubsidyService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    // @RequiresPermissions("data_management:dataindividualpensionsubsidy:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<DataIndividualPensionSubsidyEntity> dataIndividualPensionSubsidyList = new ArrayList<>();
        list.forEach(item ->{
                    item.put("id",item.get(""));
                    item.put("name",item.get("残疾人姓名"));
                    item.put("idCard",item.get("身份证号码"));
                    item.put("isInsurance",item.get("是否参保"));
                    item.put("insuranceMonth",item.get("参保月数"));
                    dataIndividualPensionSubsidyList.add(new Gson().fromJson(new Gson().toJson(item), DataIndividualPensionSubsidyEntity.class));
        });
        // 保存到数据库
        dataIndividualPensionSubsidyService.saveBatch(dataIndividualPensionSubsidyList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    // @RequiresPermissions("data_management:dataindividualpensionsubsidy:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<DataIndividualPensionSubsidyEntity> dataIndividualPensionSubsidyEntityList = dataIndividualPensionSubsidyService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("个体工商户养老补贴", null, "个体工商户养老补贴");
        Workbook workbook = ExcelExportUtil.exportExcel(params, DataIndividualPensionSubsidyEntity.class, dataIndividualPensionSubsidyEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "个体工商户养老补贴" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

}
