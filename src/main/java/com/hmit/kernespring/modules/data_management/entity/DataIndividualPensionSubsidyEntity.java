package com.hmit.kernespring.modules.data_management.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 个体工商户养老补贴
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-27 14:00:04
 */
@Data
@TableName("data_individual_pension_subsidy")
public class DataIndividualPensionSubsidyEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
@TableId
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 残疾人姓名
	 */
	@Excel(name = "残疾人姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 身份证号码
	 */
	@Excel(name = "身份证号码", height = 20, width = 30, isImportField = "true_st")
private String idCard;
	/**
	 * 是否参保
	 */
	@Excel(name = "是否参保", height = 20, width = 30, isImportField = "true_st")
private String isInsurance;
	/**
	 * 参保月数
	 */
	@Excel(name = "参保月数", height = 20, width = 30, isImportField = "true_st")
private String insuranceMonth;

}
