package com.hmit.kernespring.modules.data_management.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.data_management.entity.DataDeadinfoEntity;

import java.util.Map;

import java.util.List;

/**
 * 死亡名单导入信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-07 10:23:14
 */
public interface DataDeadinfoService extends IService<DataDeadinfoEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<DataDeadinfoEntity> queryExportData(Map<String, Object> params);
}

