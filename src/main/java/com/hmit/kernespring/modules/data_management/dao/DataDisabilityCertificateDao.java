package com.hmit.kernespring.modules.data_management.dao;

import com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 残疾人汇总表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-19 10:54:28
 */
@Mapper
public interface DataDisabilityCertificateDao extends BaseMapper<DataDisabilityCertificateEntity> {
    List<DataDisabilityCertificateEntity> queryExportData(Map<String, Object> params);
    void updateOthersADisableIdByMap(Map<String, Object> params);
    void updateOthersBDisableIdByMap(Map<String, Object> params);
    void updateOthersCDisableIdByMap(Map<String, Object> params);
    void updateOthersDDisableIdByMap(Map<String, Object> params);
    void updateOthersDisableIdByMap(Map<String, Object> params);
    List<DataDisabilityCertificateEntity> queryListByMEntity(DataDisabilityCertificateEntity dataDisabilityCertificateEntity);
    List<DataDisabilityCertificateEntity> queryListDataByMap(Map<String, Object> params);

    DataDisabilityCertificateEntity getByIDCard(String idCard);

    //分街道发送短信 （本人有电话）
    List<DataDisabilityCertificateEntity> querysend1(String jiedao);

    //分街道发送短信 （本人无电话，监护人有电话）
    List<DataDisabilityCertificateEntity> querysend2(String jiedao);

}
