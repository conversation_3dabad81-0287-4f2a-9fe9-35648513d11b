package com.hmit.kernespring.modules.data_management.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.data_management.dao.DataDistressedChildDao;
import com.hmit.kernespring.modules.data_management.entity.DataDistressedChildEntity;
import com.hmit.kernespring.modules.data_management.service.DataDistressedChildService;
import org.springframework.transaction.annotation.Transactional;


@Service("dataDistressedChildService")
public class DataDistressedChildServiceImpl extends ServiceImpl<DataDistressedChildDao, DataDistressedChildEntity> implements DataDistressedChildService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private DataDistressedChildDao dataDistressedChildDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        DataDistressedChildEntity dataDistressedChildEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, DataDistressedChildEntity.class);
        IPage<DataDistressedChildEntity> page = this.page(
                new Query<DataDistressedChildEntity>().getPage(params),
                new QueryWrapper<DataDistressedChildEntity>()
            .eq(StringUtils.isNotBlank(dataDistressedChildEntity.getId ()!=null && !"".equals(dataDistressedChildEntity.getId ().toString())? dataDistressedChildEntity.getId ().toString():null),"id", dataDistressedChildEntity.getId ())
            .eq(StringUtils.isNotBlank(dataDistressedChildEntity.getRegistrationDate ()!=null && !"".equals(dataDistressedChildEntity.getRegistrationDate ().toString())? dataDistressedChildEntity.getRegistrationDate ().toString():null),"registration_date", dataDistressedChildEntity.getRegistrationDate ())
            .eq(StringUtils.isNotBlank(dataDistressedChildEntity.getName ()!=null && !"".equals(dataDistressedChildEntity.getName ().toString())? dataDistressedChildEntity.getName ().toString():null),"name", dataDistressedChildEntity.getName ())
            .eq(StringUtils.isNotBlank(dataDistressedChildEntity.getIdCard ()!=null && !"".equals(dataDistressedChildEntity.getIdCard ().toString())? dataDistressedChildEntity.getIdCard ().toString():null),"id_card", dataDistressedChildEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(dataDistressedChildEntity.getSex ()!=null && !"".equals(dataDistressedChildEntity.getSex ().toString())? dataDistressedChildEntity.getSex ().toString():null),"sex", dataDistressedChildEntity.getSex ())
            .eq(StringUtils.isNotBlank(dataDistressedChildEntity.getBirthday ()!=null && !"".equals(dataDistressedChildEntity.getBirthday ().toString())? dataDistressedChildEntity.getBirthday ().toString():null),"birthday", dataDistressedChildEntity.getBirthday ())
            .eq(StringUtils.isNotBlank(dataDistressedChildEntity.getDistressedChildrenCategory ()!=null && !"".equals(dataDistressedChildEntity.getDistressedChildrenCategory ().toString())? dataDistressedChildEntity.getDistressedChildrenCategory ().toString():null),"distressed_children_category", dataDistressedChildEntity.getDistressedChildrenCategory ())
            .eq(StringUtils.isNotBlank(dataDistressedChildEntity.getHouseholdRegistration ()!=null && !"".equals(dataDistressedChildEntity.getHouseholdRegistration ().toString())? dataDistressedChildEntity.getHouseholdRegistration ().toString():null),"household_registration", dataDistressedChildEntity.getHouseholdRegistration ())
            .eq(StringUtils.isNotBlank(dataDistressedChildEntity.getCurrentAddress ()!=null && !"".equals(dataDistressedChildEntity.getCurrentAddress ().toString())? dataDistressedChildEntity.getCurrentAddress ().toString():null),"current_address", dataDistressedChildEntity.getCurrentAddress ())
            .eq(StringUtils.isNotBlank(dataDistressedChildEntity.getFamilySituation ()!=null && !"".equals(dataDistressedChildEntity.getFamilySituation ().toString())? dataDistressedChildEntity.getFamilySituation ().toString():null),"family_situation", dataDistressedChildEntity.getFamilySituation ())
            .eq(StringUtils.isNotBlank(dataDistressedChildEntity.getBankAccount ()!=null && !"".equals(dataDistressedChildEntity.getBankAccount ().toString())? dataDistressedChildEntity.getBankAccount ().toString():null),"bank_account", dataDistressedChildEntity.getBankAccount ())
            .eq(StringUtils.isNotBlank(dataDistressedChildEntity.getGuardianName ()!=null && !"".equals(dataDistressedChildEntity.getGuardianName ().toString())? dataDistressedChildEntity.getGuardianName ().toString():null),"guardian_name", dataDistressedChildEntity.getGuardianName ())
            .eq(StringUtils.isNotBlank(dataDistressedChildEntity.getGuardianIdCard ()!=null && !"".equals(dataDistressedChildEntity.getGuardianIdCard ().toString())? dataDistressedChildEntity.getGuardianIdCard ().toString():null),"guardian_id_card", dataDistressedChildEntity.getGuardianIdCard ())
            .eq(StringUtils.isNotBlank(dataDistressedChildEntity.getGuardianPhone ()!=null && !"".equals(dataDistressedChildEntity.getGuardianPhone ().toString())? dataDistressedChildEntity.getGuardianPhone ().toString():null),"guardian_phone", dataDistressedChildEntity.getGuardianPhone ())
            .eq(StringUtils.isNotBlank(dataDistressedChildEntity.getGuardianRelationship ()!=null && !"".equals(dataDistressedChildEntity.getGuardianRelationship ().toString())? dataDistressedChildEntity.getGuardianRelationship ().toString():null),"guardian_relationship", dataDistressedChildEntity.getGuardianRelationship ())
            .eq(StringUtils.isNotBlank(dataDistressedChildEntity.getLivingTogether ()!=null && !"".equals(dataDistressedChildEntity.getLivingTogether ().toString())? dataDistressedChildEntity.getLivingTogether ().toString():null),"living_together", dataDistressedChildEntity.getLivingTogether ())
            .eq(StringUtils.isNotBlank(dataDistressedChildEntity.getRemark ()!=null && !"".equals(dataDistressedChildEntity.getRemark ().toString())? dataDistressedChildEntity.getRemark ().toString():null),"remark", dataDistressedChildEntity.getRemark ())
            .eq(StringUtils.isNotBlank(dataDistressedChildEntity.getCreateId ()!=null && !"".equals(dataDistressedChildEntity.getCreateId ().toString())? dataDistressedChildEntity.getCreateId ().toString():null),"create_id", dataDistressedChildEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(dataDistressedChildEntity.getCreateTime ()!=null && !"".equals(dataDistressedChildEntity.getCreateTime ().toString())? dataDistressedChildEntity.getCreateTime ().toString():null),"create_time", dataDistressedChildEntity.getCreateTime ())
        );
        /*Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key","sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        page.getRecords().forEach( item -> {
            SysDictEntity distressedChildrenCategory_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("kjetlb_0000") && iii.getValue().equals(
                        item.getDistressedChildrenCategory ())).findAny().orElse(null);
            if (distressedChildrenCategory_sysDictEntity != null){
                item.setDistressedChildrenCategory (distressedChildrenCategory_sysDictEntity.getLabel());
            }else{
                item.setDistressedChildrenCategory (null);
            }
        });*/
        return new PageUtils(page);
    }
    @Override
    public List<DataDistressedChildEntity> queryExportData(Map<String, Object> params) {
            return dataDistressedChildDao.queryExportData(params);
    }


}