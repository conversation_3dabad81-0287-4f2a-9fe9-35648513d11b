package com.hmit.kernespring.modules.data_management.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-27 15:39:33
 */
@Data
@TableName("data_sms_template")
public class DataSmsTemplateEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
@TableId
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 模板类型 1 视力评残   2听力语言评残 3 肢体评残 4智力评残 5精神评残 6 领证模板
	 */
	@Excel(name = "模板类型 1 视力评残   2听力语言评残 3 肢体评残 4智力评残 5精神评残 6 领证模板", height = 20, width = 30, isImportField = "true_st")
private Integer type;
	/**
	 * 时间
	 */
	@Excel(name = "时间", height = 20, width = 30, isImportField = "true_st")
private String time;
	/**
	 * 地点
	 */
	@Excel(name = "地点", height = 20, width = 30, isImportField = "true_st")
private String address;
	/**
	 * 所携带材料
	 */
	@Excel(name = "所携带材料", height = 20, width = 30, isImportField = "true_st")
private String material;
	/**
	 * 注意事项
	 */
	@Excel(name = "注意事项", height = 20, width = 30, isImportField = "true_st")
private String precautions;
	/**
	 * 
	 */
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer status;
	/**
	 * 发送短信内容
	 */
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
private String smsContent;

	//短信是否发送本人，1 本人，2 监护人
	@TableField(exist=false)
	private Integer isMyself;

	//残疾人的身份证编号
	@TableField(exist=false)
	private String idCard;
	//短信接收人
	@TableField(exist=false)
	private String sendWho;
	//短信接收人号码
	@TableField(exist=false)
	private String mobile;

	private String memo;

}
