package com.hmit.kernespring.modules.data_management.dao;

import com.hmit.kernespring.modules.data_management.entity.DataSmsTemplateEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-27 15:39:33
 */
@Mapper
public interface DataSmsTemplateDao extends BaseMapper<DataSmsTemplateEntity> {
    List<DataSmsTemplateEntity> queryExportData(Map<String, Object> params);
	
}
