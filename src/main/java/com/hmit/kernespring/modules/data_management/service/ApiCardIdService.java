package com.hmit.kernespring.modules.data_management.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.data_management.entity.ApiCardIdEntity;

import java.util.Map;

import java.util.List;

/**
 * 省公安身份证信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-13 15:39:30
 */
public interface ApiCardIdService extends IService<ApiCardIdEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<ApiCardIdEntity> queryExportData(Map<String, Object> params);
    ApiCardIdEntity queryByIdnum(String idnum);
}

