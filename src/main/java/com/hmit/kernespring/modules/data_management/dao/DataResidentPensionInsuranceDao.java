package com.hmit.kernespring.modules.data_management.dao;

import com.hmit.kernespring.modules.data_management.entity.DataResidentPensionInsuranceEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 城乡居民养老保险补贴汇总表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-30 10:44:01
 */
@Mapper
public interface DataResidentPensionInsuranceDao extends BaseMapper<DataResidentPensionInsuranceEntity> {
    List<DataResidentPensionInsuranceEntity> queryExportData(Map<String, Object> params);
	
}
