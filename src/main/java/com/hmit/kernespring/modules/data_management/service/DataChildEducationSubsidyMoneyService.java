package com.hmit.kernespring.modules.data_management.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.data_management.entity.DataChildEducationSubsidyMoneyEntity;

import java.util.List;
import java.util.Map;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-14 17:09:45
 */
public interface DataChildEducationSubsidyMoneyService extends IService<DataChildEducationSubsidyMoneyEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<DataChildEducationSubsidyMoneyEntity> queryExportData(Map<String, Object> params);
    List<DataChildEducationSubsidyMoneyEntity> queryByMap(Map<String, Object> params);

}

