package com.hmit.kernespring.modules.data_management.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.data_management.entity.DataChildEducationSubsidyMoneyEntity;
import com.hmit.kernespring.modules.data_management.service.DataChildEducationSubsidyMoneyService;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-14 17:09:45
 */
@RestController
@RequestMapping("data_management/datachildeducationsubsidymoney")
public class DataChildEducationSubsidyMoneyController {
    @Autowired
    private DataChildEducationSubsidyMoneyService dataChildEducationSubsidyMoneyService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("data_management:datachildeducationsubsidymoney:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = dataChildEducationSubsidyMoneyService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("data_management:datachildeducationsubsidymoney:info")
    public R info(@PathVariable("id") Integer id){
		DataChildEducationSubsidyMoneyEntity dataChildEducationSubsidyMoney = dataChildEducationSubsidyMoneyService.getById(id);

        return R.ok().put("dataChildEducationSubsidyMoney", dataChildEducationSubsidyMoney);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("data_management:datachildeducationsubsidymoney:save")
    public R save(@RequestBody DataChildEducationSubsidyMoneyEntity dataChildEducationSubsidyMoney){
		dataChildEducationSubsidyMoneyService.save(dataChildEducationSubsidyMoney);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("data_management:datachildeducationsubsidymoney:update")
    public R update(@RequestBody DataChildEducationSubsidyMoneyEntity dataChildEducationSubsidyMoney){
		dataChildEducationSubsidyMoneyService.updateById(dataChildEducationSubsidyMoney);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("data_management:datachildeducationsubsidymoney:delete")
    public R delete(@RequestBody Integer[] ids){
		dataChildEducationSubsidyMoneyService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    // @RequiresPermissions("data_management:datachildeducationsubsidymoney:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<DataChildEducationSubsidyMoneyEntity> dataChildEducationSubsidyMoneyList = new ArrayList<>();
        list.forEach(item ->{
                    item.put("id",item.get(""));
                    item.put("familyEconomicSituation",item.get("家庭经济情况"));
                    item.put("education",item.get("学历"));
                    item.put("subsidyMoney",item.get("补贴金额"));
                    dataChildEducationSubsidyMoneyList.add(new Gson().fromJson(new Gson().toJson(item), DataChildEducationSubsidyMoneyEntity.class));
        });
        // 保存到数据库
        dataChildEducationSubsidyMoneyService.saveBatch(dataChildEducationSubsidyMoneyList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    // @RequiresPermissions("data_management:datachildeducationsubsidymoney:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<DataChildEducationSubsidyMoneyEntity> dataChildEducationSubsidyMoneyEntityList = dataChildEducationSubsidyMoneyService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("", null, "");
        Workbook workbook = ExcelExportUtil.exportExcel(params, DataChildEducationSubsidyMoneyEntity.class, dataChildEducationSubsidyMoneyEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

}
