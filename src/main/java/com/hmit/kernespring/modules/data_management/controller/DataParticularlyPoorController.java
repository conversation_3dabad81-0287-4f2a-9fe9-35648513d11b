package com.hmit.kernespring.modules.data_management.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.data_management.entity.DataParticularlyPoorEntity;
import com.hmit.kernespring.modules.data_management.service.DataParticularlyPoorService;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import com.hmit.kernespring.modules.sys.controller.AbstractController;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;

/**
 * 特困人员名单表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-19 10:49:28
 */
@RestController
@RequestMapping("data_management/dataparticularlypoor")
public class DataParticularlyPoorController extends AbstractController {
    @Autowired
    private DataParticularlyPoorService dataParticularlyPoorService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("data_management:dataparticularlypoor:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = dataParticularlyPoorService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("data_management:dataparticularlypoor:info")
    public R info(@PathVariable("id") Integer id){
		DataParticularlyPoorEntity dataParticularlyPoor = dataParticularlyPoorService.getById(id);

        return R.ok().put("dataParticularlyPoor", dataParticularlyPoor);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("data_management:dataparticularlypoor:save")
    public R save(@RequestBody DataParticularlyPoorEntity dataParticularlyPoor){
        dataParticularlyPoor.setCreateTime(new Date());
        dataParticularlyPoor.setCreateId(getUserId());
		dataParticularlyPoorService.save(dataParticularlyPoor);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("data_management:dataparticularlypoor:update")
    public R update(@RequestBody DataParticularlyPoorEntity dataParticularlyPoor){
		dataParticularlyPoorService.updateById(dataParticularlyPoor);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("data_management:dataparticularlypoor:delete")
    public R delete(@RequestBody Integer[] ids){
		dataParticularlyPoorService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    // @RequiresPermissions("data_management:dataparticularlypoor:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        params_import.setHeadRows(1);
        params_import.setTitleRows(1);
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<DataParticularlyPoorEntity> dataParticularlyPoorList = new ArrayList<>();
        System.out.println("当前导入数据特困人员名单表条数：" + list.size());
        list.forEach(item ->{
            System.out.println(item);
            if (null != item.get("身份证") && !"".equals(item.get("身份证").toString()) || (null != item.get("身份证号") && !"".equals(item.get("身份证号").toString()))) {
                item.put("id",item.get(""));
                item.put("administrativeDivision",item.get("行政区划"));
                item.put("name",item.get("人员姓名"));
                item.put("relationshipWithHousehold",item.get("与户主关系"));
                item.put("idCard",item.get("身份证"));
                item.put("mobilePhone",item.get("手机号码"));
                item.put("telephone",item.get("联系电话"));
                item.put("bankAccount",item.get("银行帐号"));
                item.put("bankName",item.get("开户银行"));
                item.put("personBankAccount",item.get("人员银行账号"));
                item.put("applicationCategory",item.get("申请类别"));
                item.put("numberOfSupport",item.get("供养人数"));
                item.put("amountOfSupport",item.get("供养金额"));
                item.put("healthStatus",item.get("健康状况"));
                item.put("nursingLevel",item.get("护理等级"));
                item.put("nursingStandard",item.get("护理标准"));
                item.put("rescueCertificateNumber",item.get("救助证编号"));
                item.put("agedInstitution",item.get("所在养老机构"));
                item.put("sex","男".equals(item.get("性别"))?1:0);
                item.put("birthday",item.get("出生日期"));
                item.put("reasonForApplying",item.get("申请救助原因"));
                item.put("createId",getUserId());
                item.put("createTime",new Date());
                dataParticularlyPoorList.add(new Gson().fromJson(new Gson().toJson(item), DataParticularlyPoorEntity.class));
            }
        });
        // 保存到数据库
        dataParticularlyPoorService.saveBatch(dataParticularlyPoorList);

        //((DataParticularlyPoorService)AopContext.currentProxy()).saveBatch(dataParticularlyPoorList);
        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    //@RequiresPermissions("data_management:dataparticularlypoor:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<DataParticularlyPoorEntity> dataParticularlyPoorEntityList = dataParticularlyPoorService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("特困人员名单表", null, "特困人员名单表");
        Workbook workbook = ExcelExportUtil.exportExcel(params, DataParticularlyPoorEntity.class, dataParticularlyPoorEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "特困人员名单表" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

}
