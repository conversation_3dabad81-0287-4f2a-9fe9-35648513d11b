package com.hmit.kernespring.modules.disability_certificate.dao;

import com.hmit.kernespring.modules.disability_certificate.entity.DisabilityCertificateMzjEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 民政局残疾证
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-03 14:27:47
 */
@Mapper
public interface DisabilityCertificateMzjDao extends BaseMapper<DisabilityCertificateMzjEntity> {
    List<DisabilityCertificateMzjEntity> queryExportData(Map<String, Object> params);
	
}
