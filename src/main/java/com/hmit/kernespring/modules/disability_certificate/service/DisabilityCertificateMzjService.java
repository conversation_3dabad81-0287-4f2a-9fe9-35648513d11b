package com.hmit.kernespring.modules.disability_certificate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.disability_certificate.entity.DisabilityCertificateMzjEntity;

import java.util.Map;

import java.util.List;

/**
 * 民政局残疾证
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-03 14:27:47
 */
public interface DisabilityCertificateMzjService extends IService<DisabilityCertificateMzjEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<DisabilityCertificateMzjEntity> queryExportData(Map<String, Object> params);
}

