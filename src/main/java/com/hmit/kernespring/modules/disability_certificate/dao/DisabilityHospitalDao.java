package com.hmit.kernespring.modules.disability_certificate.dao;

import com.hmit.kernespring.modules.disability_certificate.entity.DisabilityExportEntity;
import com.hmit.kernespring.modules.disability_certificate.entity.DisabilityHospitalEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 医院评残
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-03 15:56:39
 */
@Mapper
public interface DisabilityHospitalDao extends BaseMapper<DisabilityHospitalEntity> {

    List<DisabilityHospitalEntity> queryExportData(Map<String, Object> params);

    List<DisabilityExportEntity> queryExportDataToZCL();

}
