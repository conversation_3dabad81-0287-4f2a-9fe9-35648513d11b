package com.hmit.kernespring.modules.disability_certificate.entity;


import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;

/**
 *  导出至中残联系统的excel数据模板
 */
@Data
public class DisabilityExportEntity implements Serializable {

    private static final long serialVersionUID = 1L;



    /**
     * 残疾人姓名
     */
    @Excel(name = "姓名", height = 20, width = 30, isImportField = "true_st")
    private String name;

    /**
     * 身份证号码
     */
    @Excel(name = "身份证号", height = 20, width = 30, isImportField = "true_st")
    private String idCard;

    /**
     * 性别  1 男  2女
     */
    @Excel(name = "性别", height = 20, width = 30, isImportField = "true_st")
    private String sex;
    /**
     * 民族
     */
    @Excel(name = "民族", height = 20, width = 30, isImportField = "true_st")
    private String nationality;

    /**
     * 文化程度
     */
    @Excel(name = "文化程度", height = 20, width = 30, isImportField = "true_st")
    private String educationDegree;

    /**
     * 婚姻情况  0未婚  1已婚
     */
    @Excel(name = "婚姻状况", height = 20, width = 30, isImportField = "true_st")
    private String maritalStatus;

    /**
     * 户口性质
     */
    @Excel(name = "户口性质", height = 20, width = 30, isImportField = "true_st")
    private String accountNature;


    /**
     * 出生日期
     */
    @Excel(name = "出生日期", height = 20, width = 30, isImportField = "true_st")
    private String birthday;

    /**
     * 联系电话
     */
    @Excel(name = "手机", height = 20, width = 30, isImportField = "true_st")
    private Integer mobilePhone;

    /**
     * 联系电话
     */
    @Excel(name = "固定电话", height = 20, width = 30, isImportField = "true_st")
    private String  fixedTelephone;


    /**
     * 户籍地址  镇
     */
    @Excel(name = "乡镇", height = 20, width = 30, isImportField = "true_st")
    private String presentZhen;
    /**
     * 户籍地址 村
     */
    @Excel(name = "村", height = 20, width = 30, isImportField = "true_st")
    private String presentCun;
    /**
     * 户籍地址
     */
    @Excel(name = "户籍地址", height = 20, width = 30, isImportField = "true_st")
    private String nativeAddress;

    /**
     * 现地址
     */
    @Excel(name = "居住地地址", height = 20, width = 30, isImportField = "true_st")
    private String presentAddress;
    /**
     * 申请残疾类别
     */
    @Excel(name = "申请残疾类别", height = 20, width = 30, isImportField = "true_st")
    private String typename;

    /**
     * 监护人姓名
     */
    @Excel(name = "监护人姓名", height = 20, width = 30, isImportField = "true_st")
    private String guardianName;
    /**
     * 监护人手机
     */
    @Excel(name = "监护人手机", height = 20, width = 30, isImportField = "true_st")
    private Integer guardianPhone;

    /**
     * 监护人固定电话
     */
    @Excel(name = "监护人固定电话", height = 20, width = 30, isImportField = "true_st")
    private String  guardianFixedPhone;

    /**
     * 与申请人关系
     */
    @Excel(name = "与本人关系", height = 20, width = 30, isImportField = "true_st")
    private String guardianRelation;
    /**
     * 二寸照
     */
    @Excel(name = "二寸照", height = 20, width = 30, isImportField = "true_st")
    private String photo;


}
