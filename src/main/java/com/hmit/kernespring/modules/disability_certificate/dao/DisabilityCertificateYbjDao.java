package com.hmit.kernespring.modules.disability_certificate.dao;

import com.hmit.kernespring.modules.disability_certificate.entity.DisabilityCertificateYbjEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 医保局残疾证
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-03 16:05:52
 */
@Mapper
public interface DisabilityCertificateYbjDao extends BaseMapper<DisabilityCertificateYbjEntity> {
    List<DisabilityCertificateYbjEntity> queryExportData(Map<String, Object> params);
	
}
