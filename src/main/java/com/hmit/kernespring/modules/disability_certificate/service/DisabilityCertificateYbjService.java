package com.hmit.kernespring.modules.disability_certificate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.disability_certificate.entity.DisabilityCertificateYbjEntity;

import java.util.Map;

import java.util.List;

/**
 * 医保局残疾证
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-03 16:05:52
 */
public interface DisabilityCertificateYbjService extends IService<DisabilityCertificateYbjEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<DisabilityCertificateYbjEntity> queryExportData(Map<String, Object> params);
}

