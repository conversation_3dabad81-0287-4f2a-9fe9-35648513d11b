package com.hmit.kernespring.modules.disability_certificate.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.disability_certificate.dao.DisabilityCertificateYbjDao;
import com.hmit.kernespring.modules.disability_certificate.entity.DisabilityCertificateYbjEntity;
import com.hmit.kernespring.modules.disability_certificate.service.DisabilityCertificateYbjService;


@Service("disabilityCertificateYbjService")
public class DisabilityCertificateYbjServiceImpl extends ServiceImpl<DisabilityCertificateYbjDao, DisabilityCertificateYbjEntity> implements DisabilityCertificateYbjService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private DisabilityCertificateYbjDao disabilityCertificateYbjDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        DisabilityCertificateYbjEntity disabilityCertificateYbjEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, DisabilityCertificateYbjEntity.class);
        IPage<DisabilityCertificateYbjEntity> page = this.page(
                new Query<DisabilityCertificateYbjEntity>().getPage(params),
                new QueryWrapper<DisabilityCertificateYbjEntity>()
            .eq(StringUtils.isNotBlank(disabilityCertificateYbjEntity.getId ()!=null && !"".equals(disabilityCertificateYbjEntity.getId ().toString())? disabilityCertificateYbjEntity.getId ().toString():null),"id", disabilityCertificateYbjEntity.getId ())
        .eq(StringUtils.isNotBlank(disabilityCertificateYbjEntity.getName ()!=null && !"".equals(disabilityCertificateYbjEntity.getName ().toString())? disabilityCertificateYbjEntity.getName ().toString():null),"name", disabilityCertificateYbjEntity.getName ())
        .eq(StringUtils.isNotBlank(disabilityCertificateYbjEntity.getIdCard ()!=null && !"".equals(disabilityCertificateYbjEntity.getIdCard ().toString())? disabilityCertificateYbjEntity.getIdCard ().toString():null),"id_card", disabilityCertificateYbjEntity.getIdCard ())
        .eq(StringUtils.isNotBlank(disabilityCertificateYbjEntity.getMobilePhone ()!=null && !"".equals(disabilityCertificateYbjEntity.getMobilePhone ().toString())? disabilityCertificateYbjEntity.getMobilePhone ().toString():null),"mobile_phone", disabilityCertificateYbjEntity.getMobilePhone ())
        .eq(StringUtils.isNotBlank(disabilityCertificateYbjEntity.getDisableId ()!=null && !"".equals(disabilityCertificateYbjEntity.getDisableId ().toString())? disabilityCertificateYbjEntity.getDisableId ().toString():null),"disable_id", disabilityCertificateYbjEntity.getDisableId ())
        .eq(StringUtils.isNotBlank(disabilityCertificateYbjEntity.getGuardianName ()!=null && !"".equals(disabilityCertificateYbjEntity.getGuardianName ().toString())? disabilityCertificateYbjEntity.getGuardianName ().toString():null),"guardian_name", disabilityCertificateYbjEntity.getGuardianName ())
        .eq(StringUtils.isNotBlank(disabilityCertificateYbjEntity.getGuardianPhone ()!=null && !"".equals(disabilityCertificateYbjEntity.getGuardianPhone ().toString())? disabilityCertificateYbjEntity.getGuardianPhone ().toString():null),"guardian_phone", disabilityCertificateYbjEntity.getGuardianPhone ())
        .eq(StringUtils.isNotBlank(disabilityCertificateYbjEntity.getApplicationType ()!=null && !"".equals(disabilityCertificateYbjEntity.getApplicationType ().toString())? disabilityCertificateYbjEntity.getApplicationType ().toString():null),"application_type", disabilityCertificateYbjEntity.getApplicationType ())
        .eq(StringUtils.isNotBlank(disabilityCertificateYbjEntity.getHealthCareCondition ()!=null && !"".equals(disabilityCertificateYbjEntity.getHealthCareCondition ().toString())? disabilityCertificateYbjEntity.getHealthCareCondition ().toString():null),"health_care_condition", disabilityCertificateYbjEntity.getHealthCareCondition ())
        );
                                        Map<String, Object> applicationTypemap = new HashMap<>();
            applicationTypemap.put("code","sqlx_0000");
            applicationTypemap.put("redis_key","sys_dict:"+applicationTypemap.get("code"));
        List<SysDictEntity> applicationTypelist = sysDictService.queryDataByMap(applicationTypemap);
        page.getRecords().forEach(item -> {
                applicationTypelist.forEach(inner_item ->{
                if (inner_item.getValue().equals(item.getApplicationType ())){
                    item.setApplicationType (inner_item.getLabel());
                }
            });
        });
                Map<String, Object> healthCareConditionmap = new HashMap<>();
            healthCareConditionmap.put("code","ybqk_0000");
            healthCareConditionmap.put("redis_key","sys_dict:"+healthCareConditionmap.get("code"));
        List<SysDictEntity> healthCareConditionlist = sysDictService.queryDataByMap(healthCareConditionmap);
        page.getRecords().forEach(item -> {
                healthCareConditionlist.forEach(inner_item ->{
                if (inner_item.getValue().equals(item.getHealthCareCondition ())){
                    item.setHealthCareCondition (inner_item.getLabel());
                }
            });
        });
            return new PageUtils(page);
    }
    @Override
    public List<DisabilityCertificateYbjEntity> queryExportData(Map<String, Object> params) {
            return disabilityCertificateYbjDao.queryExportData(params);
    }

}