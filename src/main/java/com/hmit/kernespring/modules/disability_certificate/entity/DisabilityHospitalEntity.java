package com.hmit.kernespring.modules.disability_certificate.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 医院评残
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-03 15:56:39
 */
@Data
@TableName("disability_hospital")
public class DisabilityHospitalEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	
@TableId
	@Excel(name = "主键ID", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 姓名
	 */
	@Excel(name = "姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 身份证号
	 */
	@Excel(name = "身份证号", height = 20, width = 30, isImportField = "true_st")
private Integer idCard;
	/**
	 * 手机号
	 */
	@Excel(name = "手机号", height = 20, width = 30, isImportField = "true_st")
private Integer mobilePhone;
	/**
	 * 残疾证号
	 */
	@Excel(name = "残疾证号", height = 20, width = 30, isImportField = "true_st")
private Integer disableId;
	/**
	 * 监护人姓名
	 */
	@Excel(name = "监护人姓名", height = 20, width = 30, isImportField = "true_st")
private String guardianName;
	/**
	 * 监护人手机
	 */
	@Excel(name = "监护人手机", height = 20, width = 30, isImportField = "true_st")
private Integer guardianPhone;
	/**
	 * 申请类型
	 */
	@Excel(name = "申请类型", height = 20, width = 30, isImportField = "true_st")
private String applicationType;
	/**
	 * 残疾等级
	 */
	@Excel(name = "残疾等级", height = 20, width = 30, isImportField = "true_st")
private String disabilityLevel;
	/**
	 * 残疾类别
	 */
	@Excel(name = "残疾类别", height = 20, width = 30, isImportField = "true_st")
private String disabilityType;

}
