package com.hmit.kernespring.modules.disability_certificate.controller;

import java.io.File;
import java.io.IOException;
import java.util.*;

import java.io.OutputStream;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import org.apache.poi.ss.usermodel.Workbook;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hmit.kernespring.modules.disability_certificate.entity.DisabilityCertificateYbjEntity;
import com.hmit.kernespring.modules.disability_certificate.service.DisabilityCertificateYbjService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 医保局残疾证
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-03 16:05:52
 */
@RestController
@RequestMapping("disability_certificate/disabilitycertificateybj")
public class DisabilityCertificateYbjController {
    @Autowired
    private DisabilityCertificateYbjService disabilityCertificateYbjService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("disability_certificate:disabilitycertificateybj:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = disabilityCertificateYbjService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("disability_certificate:disabilitycertificateybj:info")
    public R info(@PathVariable("id") Integer id){
		DisabilityCertificateYbjEntity disabilityCertificateYbj = disabilityCertificateYbjService.getById(id);

        return R.ok().put("disabilityCertificateYbj", disabilityCertificateYbj);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("disability_certificate:disabilitycertificateybj:save")
    public R save(@RequestBody DisabilityCertificateYbjEntity disabilityCertificateYbj){
		disabilityCertificateYbjService.save(disabilityCertificateYbj);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("disability_certificate:disabilitycertificateybj:update")
    public R update(@RequestBody DisabilityCertificateYbjEntity disabilityCertificateYbj){
		disabilityCertificateYbjService.updateById(disabilityCertificateYbj);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("disability_certificate:disabilitycertificateybj:delete")
    public R delete(@RequestBody Integer[] ids){
		disabilityCertificateYbjService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    // @RequiresPermissions("disability_certificate:disabilitycertificateybj:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<DisabilityCertificateYbjEntity> disabilityCertificateYbjList = new ArrayList<>();
        list.forEach(item ->{
                    item.put("id",item.get("主键ID"));
                    item.put("name",item.get("姓名"));
                    item.put("idCard",item.get("身份证号"));
                    item.put("mobilePhone",item.get("手机号"));
                    item.put("disableId",item.get("残疾证号"));
                    item.put("guardianName",item.get("监护人姓名"));
                    item.put("guardianPhone",item.get("监护人手机"));
                    item.put("applicationType",item.get("申请类型"));
                    item.put("healthCareCondition",item.get("医保情况"));
                    disabilityCertificateYbjList.add(new Gson().fromJson(new Gson().toJson(item), DisabilityCertificateYbjEntity.class));
        });
        // 保存到数据库
        disabilityCertificateYbjService.saveBatch(disabilityCertificateYbjList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    //@RequiresPermissions("disability_certificate:disabilitycertificateybj:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<DisabilityCertificateYbjEntity> disabilityCertificateYbjEntityList = disabilityCertificateYbjService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("医保局残疾证", null, "医保局残疾证");
        Workbook workbook = ExcelExportUtil.exportExcel(params, DisabilityCertificateYbjEntity.class, disabilityCertificateYbjEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "医保局残疾证" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

}
