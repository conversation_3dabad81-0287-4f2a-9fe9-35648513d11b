package com.hmit.kernespring.modules.disability_certificate.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 残疾证
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-02 14:25:04
 */
@Data
@TableName("disability_certificate")
public class DisabilityCertificateEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	
@TableId
	@Excel(name = "主键ID", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 姓名
	 */
	@Excel(name = "姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 身份证号
	 */
	@Excel(name = "身份证号", height = 20, width = 30, isImportField = "true_st")
private Integer idCard;
	/**
	 * 手机号
	 */
	@Excel(name = "手机号", height = 20, width = 30, isImportField = "true_st")
private Integer mobilePhone;
	/**
	 * 残疾证号
	 */
	@Excel(name = "残疾证号", height = 20, width = 30, isImportField = "true_st")
private Integer disableId;
	/**
	 * 监护人姓名
	 */
	@Excel(name = "监护人姓名", height = 20, width = 30, isImportField = "true_st")
private String guardianName;
	/**
	 * 监护人手机
	 */
	@Excel(name = "监护人手机", height = 20, width = 30, isImportField = "true_st")
private Integer guardianPhone;
	/**
	 * 申请类型
	 */
	@Excel(name = "申请类型", height = 20, width = 30, isImportField = "true_st")
private String applicationType;
	/**
	 * 家庭经济情况
	 */
	@Excel(name = "家庭经济情况", height = 20, width = 30, isImportField = "true_st")
private String familyEconoCondition;

}
