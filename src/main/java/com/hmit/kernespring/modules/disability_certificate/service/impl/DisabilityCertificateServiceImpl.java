package com.hmit.kernespring.modules.disability_certificate.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.disability_certificate.dao.DisabilityCertificateDao;
import com.hmit.kernespring.modules.disability_certificate.entity.DisabilityCertificateEntity;
import com.hmit.kernespring.modules.disability_certificate.service.DisabilityCertificateService;


@Service("disabilityCertificateService")
public class DisabilityCertificateServiceImpl extends ServiceImpl<DisabilityCertificateDao, DisabilityCertificateEntity> implements DisabilityCertificateService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
            .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

                @Override
                public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                    if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                        //定义为int类型,如果后台返回""或者null,则返回0
                        return null;
                    }
                    return json.getAsInt();
                }
            })
            .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

                @Override
                public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                    if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                        //定义为int类型,如果后台返回""或者null,则返回0
                        return null;
                    }
                    return json.getAsInt();
                }
            })
            .create();
    @Autowired
    private DisabilityCertificateDao disabilityCertificateDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {

        DisabilityCertificateEntity disabilityCertificateEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, DisabilityCertificateEntity.class);
        IPage<DisabilityCertificateEntity> page = this.page(
                new Query<DisabilityCertificateEntity>().getPage(params),
                new QueryWrapper<DisabilityCertificateEntity>()
                .eq(StringUtils.isNotBlank(disabilityCertificateEntity.getId ()!=null && !"".equals(disabilityCertificateEntity.getId ().toString())? disabilityCertificateEntity.getId ().toString():null),"id", disabilityCertificateEntity.getId ())
                .eq(StringUtils.isNotBlank(disabilityCertificateEntity.getName ()!=null && !"".equals(disabilityCertificateEntity.getName ().toString())? disabilityCertificateEntity.getName ().toString():null),"name", disabilityCertificateEntity.getName ())
                .eq(StringUtils.isNotBlank(disabilityCertificateEntity.getIdCard ()!=null && !"".equals(disabilityCertificateEntity.getIdCard ().toString())? disabilityCertificateEntity.getIdCard ().toString():null),"id_card", disabilityCertificateEntity.getIdCard ())
                .eq(StringUtils.isNotBlank(disabilityCertificateEntity.getMobilePhone ()!=null && !"".equals(disabilityCertificateEntity.getMobilePhone ().toString())? disabilityCertificateEntity.getMobilePhone ().toString():null),"mobile_phone", disabilityCertificateEntity.getMobilePhone ())
                .eq(StringUtils.isNotBlank(disabilityCertificateEntity.getDisableId ()!=null && !"".equals(disabilityCertificateEntity.getDisableId ().toString())? disabilityCertificateEntity.getDisableId ().toString():null),"disable_id", disabilityCertificateEntity.getDisableId ())
                .eq(StringUtils.isNotBlank(disabilityCertificateEntity.getGuardianName ()!=null && !"".equals(disabilityCertificateEntity.getGuardianName ().toString())? disabilityCertificateEntity.getGuardianName ().toString():null),"guardian_name", disabilityCertificateEntity.getGuardianName ())
                .eq(StringUtils.isNotBlank(disabilityCertificateEntity.getGuardianPhone ()!=null && !"".equals(disabilityCertificateEntity.getGuardianPhone ().toString())? disabilityCertificateEntity.getGuardianPhone ().toString():null),"guardian_phone", disabilityCertificateEntity.getGuardianPhone ())
                .eq(StringUtils.isNotBlank(disabilityCertificateEntity.getApplicationType ()!=null && !"".equals(disabilityCertificateEntity.getApplicationType ().toString())? disabilityCertificateEntity.getApplicationType ().toString():null),"application_type", disabilityCertificateEntity.getApplicationType ())
                .eq(StringUtils.isNotBlank(disabilityCertificateEntity.getFamilyEconoCondition ()!=null && !"".equals(disabilityCertificateEntity.getFamilyEconoCondition ().toString())? disabilityCertificateEntity.getFamilyEconoCondition ().toString():null),"family_econo_condition", disabilityCertificateEntity.getFamilyEconoCondition ())
        );
        Map<String, Object> map = new HashMap<>();
        map.put("code","sqlx_0000");
        map.put("redis_key","sys_dict:"+map.get("code"));
        List<SysDictEntity> list = sysDictService.queryDataByMap(map);
        page.getRecords().forEach(item -> {
            list.forEach(inner_item ->{
                if (inner_item.getValue().equals(item.getFamilyEconoCondition())){
                    item.setFamilyEconoCondition(inner_item.getLabel());
                }
            });
        });

        return new PageUtils(page);
    }
    @Override
    public List<DisabilityCertificateEntity> queryExportData(Map<String, Object> params) {
            return disabilityCertificateDao.queryExportData(params);
    }

}