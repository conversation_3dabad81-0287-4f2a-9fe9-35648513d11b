package com.hmit.kernespring.modules.disability_certificate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.disability_certificate.entity.DisabilityExportEntity;
import com.hmit.kernespring.modules.disability_certificate.entity.DisabilityHospitalEntity;

import java.util.Map;

import java.util.List;

/**
 * 医院评残
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-03 15:56:39
 */
public interface DisabilityHospitalService extends IService<DisabilityHospitalEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<DisabilityHospitalEntity> queryExportData(Map<String, Object> params);
    //导出数据至中残联
    List<DisabilityExportEntity> queryExportDataToZCL();

}

