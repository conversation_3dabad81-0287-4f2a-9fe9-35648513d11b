package com.hmit.kernespring.modules.disability_certificate.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.disability_certificate.entity.DisabilityExportEntity;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.disability_certificate.dao.DisabilityHospitalDao;
import com.hmit.kernespring.modules.disability_certificate.entity.DisabilityHospitalEntity;
import com.hmit.kernespring.modules.disability_certificate.service.DisabilityHospitalService;


@Service("disabilityHospitalService")
public class DisabilityHospitalServiceImpl extends ServiceImpl<DisabilityHospitalDao, DisabilityHospitalEntity> implements DisabilityHospitalService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private DisabilityHospitalDao disabilityHospitalDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        DisabilityHospitalEntity disabilityHospitalEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, DisabilityHospitalEntity.class);
        IPage<DisabilityHospitalEntity> page = this.page(
                new Query<DisabilityHospitalEntity>().getPage(params),
                new QueryWrapper<DisabilityHospitalEntity>()
            .eq(StringUtils.isNotBlank(disabilityHospitalEntity.getId ()!=null && !"".equals(disabilityHospitalEntity.getId ().toString())? disabilityHospitalEntity.getId ().toString():null),"id", disabilityHospitalEntity.getId ())
        .eq(StringUtils.isNotBlank(disabilityHospitalEntity.getName ()!=null && !"".equals(disabilityHospitalEntity.getName ().toString())? disabilityHospitalEntity.getName ().toString():null),"name", disabilityHospitalEntity.getName ())
        .eq(StringUtils.isNotBlank(disabilityHospitalEntity.getIdCard ()!=null && !"".equals(disabilityHospitalEntity.getIdCard ().toString())? disabilityHospitalEntity.getIdCard ().toString():null),"id_card", disabilityHospitalEntity.getIdCard ())
        .eq(StringUtils.isNotBlank(disabilityHospitalEntity.getMobilePhone ()!=null && !"".equals(disabilityHospitalEntity.getMobilePhone ().toString())? disabilityHospitalEntity.getMobilePhone ().toString():null),"mobile_phone", disabilityHospitalEntity.getMobilePhone ())
        .eq(StringUtils.isNotBlank(disabilityHospitalEntity.getDisableId ()!=null && !"".equals(disabilityHospitalEntity.getDisableId ().toString())? disabilityHospitalEntity.getDisableId ().toString():null),"disable_id", disabilityHospitalEntity.getDisableId ())
        .eq(StringUtils.isNotBlank(disabilityHospitalEntity.getGuardianName ()!=null && !"".equals(disabilityHospitalEntity.getGuardianName ().toString())? disabilityHospitalEntity.getGuardianName ().toString():null),"guardian_name", disabilityHospitalEntity.getGuardianName ())
        .eq(StringUtils.isNotBlank(disabilityHospitalEntity.getGuardianPhone ()!=null && !"".equals(disabilityHospitalEntity.getGuardianPhone ().toString())? disabilityHospitalEntity.getGuardianPhone ().toString():null),"guardian_phone", disabilityHospitalEntity.getGuardianPhone ())
        .eq(StringUtils.isNotBlank(disabilityHospitalEntity.getApplicationType ()!=null && !"".equals(disabilityHospitalEntity.getApplicationType ().toString())? disabilityHospitalEntity.getApplicationType ().toString():null),"application_type", disabilityHospitalEntity.getApplicationType ())
        .eq(StringUtils.isNotBlank(disabilityHospitalEntity.getDisabilityLevel ()!=null && !"".equals(disabilityHospitalEntity.getDisabilityLevel ().toString())? disabilityHospitalEntity.getDisabilityLevel ().toString():null),"disability_level", disabilityHospitalEntity.getDisabilityLevel ())
        .eq(StringUtils.isNotBlank(disabilityHospitalEntity.getDisabilityType ()!=null && !"".equals(disabilityHospitalEntity.getDisabilityType ().toString())? disabilityHospitalEntity.getDisabilityType ().toString():null),"disability_type", disabilityHospitalEntity.getDisabilityType ())
        );
                                        Map<String, Object> applicationTypemap = new HashMap<>();
            applicationTypemap.put("code","sqlx_0000");
            applicationTypemap.put("redis_key","sys_dict:"+applicationTypemap.get("code"));
        List<SysDictEntity> applicationTypelist = sysDictService.queryDataByMap(applicationTypemap);
        page.getRecords().forEach(item -> {
                applicationTypelist.forEach(inner_item ->{
                if (inner_item.getValue().equals(item.getApplicationType ())){
                    item.setApplicationType (inner_item.getLabel());
                }
            });
        });
                Map<String, Object> disabilityLevelmap = new HashMap<>();
            disabilityLevelmap.put("code","cjdj_0001");
            disabilityLevelmap.put("redis_key","sys_dict:"+disabilityLevelmap.get("code"));
        List<SysDictEntity> disabilityLevellist = sysDictService.queryDataByMap(disabilityLevelmap);
        page.getRecords().forEach(item -> {
                disabilityLevellist.forEach(inner_item ->{
                if (inner_item.getValue().equals(item.getDisabilityLevel ())){
                    item.setDisabilityLevel (inner_item.getLabel());
                }
            });
        });
                Map<String, Object> disabilityTypemap = new HashMap<>();
            disabilityTypemap.put("code","cjlb_0001");
            disabilityTypemap.put("redis_key","sys_dict:"+disabilityTypemap.get("code"));
        List<SysDictEntity> disabilityTypelist = sysDictService.queryDataByMap(disabilityTypemap);
        page.getRecords().forEach(item -> {
                disabilityTypelist.forEach(inner_item ->{
                if (inner_item.getValue().equals(item.getDisabilityType ())){
                    item.setDisabilityType (inner_item.getLabel());
                }
            });
        });
            return new PageUtils(page);
    }
    @Override
    public List<DisabilityHospitalEntity> queryExportData(Map<String, Object> params) {
            return disabilityHospitalDao.queryExportData(params);
    }

    @Override
    public List<DisabilityExportEntity> queryExportDataToZCL() {
            return disabilityHospitalDao.queryExportDataToZCL();
    }

}