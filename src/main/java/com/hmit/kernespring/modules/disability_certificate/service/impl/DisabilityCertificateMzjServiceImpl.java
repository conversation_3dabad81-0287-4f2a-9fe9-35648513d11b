package com.hmit.kernespring.modules.disability_certificate.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.disability_certificate.dao.DisabilityCertificateMzjDao;
import com.hmit.kernespring.modules.disability_certificate.entity.DisabilityCertificateMzjEntity;
import com.hmit.kernespring.modules.disability_certificate.service.DisabilityCertificateMzjService;


@Service("disabilityCertificateMzjService")
public class DisabilityCertificateMzjServiceImpl extends ServiceImpl<DisabilityCertificateMzjDao, DisabilityCertificateMzjEntity> implements DisabilityCertificateMzjService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private DisabilityCertificateMzjDao disabilityCertificateMzjDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        DisabilityCertificateMzjEntity disabilityCertificateMzjEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, DisabilityCertificateMzjEntity.class);
        IPage<DisabilityCertificateMzjEntity> page = this.page(
                new Query<DisabilityCertificateMzjEntity>().getPage(params),
                new QueryWrapper<DisabilityCertificateMzjEntity>()
            .eq(StringUtils.isNotBlank(disabilityCertificateMzjEntity.getId ()!=null && !"".equals(disabilityCertificateMzjEntity.getId ().toString())? disabilityCertificateMzjEntity.getId ().toString():null),"id", disabilityCertificateMzjEntity.getId ())
        .eq(StringUtils.isNotBlank(disabilityCertificateMzjEntity.getName ()!=null && !"".equals(disabilityCertificateMzjEntity.getName ().toString())? disabilityCertificateMzjEntity.getName ().toString():null),"name", disabilityCertificateMzjEntity.getName ())
        .eq(StringUtils.isNotBlank(disabilityCertificateMzjEntity.getIdCard ()!=null && !"".equals(disabilityCertificateMzjEntity.getIdCard ().toString())? disabilityCertificateMzjEntity.getIdCard ().toString():null),"id_card", disabilityCertificateMzjEntity.getIdCard ())
        .eq(StringUtils.isNotBlank(disabilityCertificateMzjEntity.getMobilePhone ()!=null && !"".equals(disabilityCertificateMzjEntity.getMobilePhone ().toString())? disabilityCertificateMzjEntity.getMobilePhone ().toString():null),"mobile_phone", disabilityCertificateMzjEntity.getMobilePhone ())
        .eq(StringUtils.isNotBlank(disabilityCertificateMzjEntity.getDisableId ()!=null && !"".equals(disabilityCertificateMzjEntity.getDisableId ().toString())? disabilityCertificateMzjEntity.getDisableId ().toString():null),"disable_id", disabilityCertificateMzjEntity.getDisableId ())
        .eq(StringUtils.isNotBlank(disabilityCertificateMzjEntity.getGuardianName ()!=null && !"".equals(disabilityCertificateMzjEntity.getGuardianName ().toString())? disabilityCertificateMzjEntity.getGuardianName ().toString():null),"guardian_name", disabilityCertificateMzjEntity.getGuardianName ())
        .eq(StringUtils.isNotBlank(disabilityCertificateMzjEntity.getGuardianPhone ()!=null && !"".equals(disabilityCertificateMzjEntity.getGuardianPhone ().toString())? disabilityCertificateMzjEntity.getGuardianPhone ().toString():null),"guardian_phone", disabilityCertificateMzjEntity.getGuardianPhone ())
        .eq(StringUtils.isNotBlank(disabilityCertificateMzjEntity.getApplicationType ()!=null && !"".equals(disabilityCertificateMzjEntity.getApplicationType ().toString())? disabilityCertificateMzjEntity.getApplicationType ().toString():null),"application_type", disabilityCertificateMzjEntity.getApplicationType ())
        .eq(StringUtils.isNotBlank(disabilityCertificateMzjEntity.getFamilyEconoCondition ()!=null && !"".equals(disabilityCertificateMzjEntity.getFamilyEconoCondition ().toString())? disabilityCertificateMzjEntity.getFamilyEconoCondition ().toString():null),"family_econo_condition", disabilityCertificateMzjEntity.getFamilyEconoCondition ())
        );
                                        Map<String, Object> applicationTypemap = new HashMap<>();
            applicationTypemap.put("code","sqlx_0000");
            applicationTypemap.put("redis_key","sys_dict:"+applicationTypemap.get("code"));
        List<SysDictEntity> applicationTypelist = sysDictService.queryDataByMap(applicationTypemap);
        page.getRecords().forEach(item -> {
            applicationTypelist.forEach(inner_item ->{
                if (inner_item.getValue().equals(item.getApplicationType ())){
                    item.setApplicationType (inner_item.getLabel());
                }
            });
        });
                Map<String, Object> familyEconoConditionmap = new HashMap<>();
            familyEconoConditionmap.put("code","jtjj_0001");
            familyEconoConditionmap.put("redis_key","sys_dict:"+familyEconoConditionmap.get("code"));
        List<SysDictEntity> familyEconoConditionlist = sysDictService.queryDataByMap(familyEconoConditionmap);
        page.getRecords().forEach(item -> {
            familyEconoConditionlist.forEach(inner_item ->{
                if (inner_item.getValue().equals(item.getFamilyEconoCondition ())){
                    item.setFamilyEconoCondition (inner_item.getLabel());
                }
            });
        });
            return new PageUtils(page);
    }
    @Override
    public List<DisabilityCertificateMzjEntity> queryExportData(Map<String, Object> params) {
            return disabilityCertificateMzjDao.queryExportData(params);
    }

}