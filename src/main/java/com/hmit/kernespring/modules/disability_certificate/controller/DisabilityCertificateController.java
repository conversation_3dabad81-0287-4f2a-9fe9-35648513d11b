package com.hmit.kernespring.modules.disability_certificate.controller;

import java.io.File;
import java.io.IOException;
import java.util.*;

import java.io.OutputStream;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import org.apache.poi.ss.usermodel.Workbook;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.hmit.kernespring.modules.disability_certificate.entity.DisabilityCertificateEntity;
import com.hmit.kernespring.modules.disability_certificate.service.DisabilityCertificateService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


/**
 * 残疾证
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-02 14:25:04
 */
@RestController
@RequestMapping("disability_certificate/disabilitycertificate")
public class DisabilityCertificateController {
    @Autowired
    private DisabilityCertificateService disabilityCertificateService;
    @Autowired
    private SysOssService sysOssService;
    @Autowired
    CjroneProperties cjroneProperties ;

    /**
     * 列表
     */
    @RequestMapping("/list")
    //@RequiresPermissions("disability_certificate:disabilitycertificate:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = disabilityCertificateService.queryPage(params);
        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    //@RequiresPermissions("disability_certificate:disabilitycertificate:info")
    public R info(@PathVariable("id") Integer id){
		DisabilityCertificateEntity disabilityCertificate = disabilityCertificateService.getById(id);

        return R.ok().put("disabilityCertificate", disabilityCertificate);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    //@RequiresPermissions("disability_certificate:disabilitycertificate:save")
    public R save(@RequestBody DisabilityCertificateEntity disabilityCertificate){
		disabilityCertificateService.save(disabilityCertificate);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    //@RequiresPermissions("disability_certificate:disabilitycertificate:update")
    public R update(@RequestBody DisabilityCertificateEntity disabilityCertificate){
		disabilityCertificateService.updateById(disabilityCertificate);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    //@RequiresPermissions("disability_certificate:disabilitycertificate:delete")
    public R delete(@RequestBody Integer[] ids){
		disabilityCertificateService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    //@RequiresPermissions("disability_certificate:disabilitycertificate:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        System.out.println(list.size());
        List<DisabilityCertificateEntity> disabilityCertificateList = new ArrayList<>();
        list.forEach(item-> {
            item.put("name",item.get("姓名"));
            item.put("idCard",item.get("身份证号"));
            item.put("mobilePhone",item.get("手机号"));
            item.put("disableId",item.get("残疾证号"));
            item.put("guardianName",item.get("监护人姓名"));
            item.put("guardianPhone",item.get("监护人手机"));
            item.put("applicationType",item.get("申请类型"));
            item.put("familyEconoCondition",item.get("家庭经济情况"));
            disabilityCertificateList.add(new Gson().fromJson(new Gson().toJson(item), DisabilityCertificateEntity.class));
        });
        // 保存到数据库
        disabilityCertificateService.saveBatch(disabilityCertificateList);
        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    // @RequiresPermissions("disability_certificate:disabilitycertificate:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {

        List<DisabilityCertificateEntity> isabilityCertificateEntityList = disabilityCertificateService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("残疾证", null, "残疾证");
        Workbook workbook = ExcelExportUtil.exportExcel(params, DisabilityCertificateEntity.class, isabilityCertificateEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "残疾证" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }
}
