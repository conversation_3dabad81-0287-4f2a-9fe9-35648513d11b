package com.hmit.kernespring.modules.disability_certificate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.disability_certificate.entity.DisabilityCertificateEntity;

import java.util.List;
import java.util.Map;

/**
 * 残疾证
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-02 14:25:04
 */
public interface DisabilityCertificateService extends IService<DisabilityCertificateEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<DisabilityCertificateEntity> queryExportData(Map<String, Object> params);
}

