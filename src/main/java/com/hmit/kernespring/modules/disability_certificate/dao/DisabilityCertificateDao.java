package com.hmit.kernespring.modules.disability_certificate.dao;

import com.hmit.kernespring.modules.disability_certificate.entity.DisabilityCertificateEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 残疾证
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-02 14:25:04
 */
@Mapper
public interface DisabilityCertificateDao extends BaseMapper<DisabilityCertificateEntity> {
    List<DisabilityCertificateEntity> queryExportData(Map<String, Object> params);
	
}
