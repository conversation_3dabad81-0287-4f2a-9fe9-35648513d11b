package com.hmit.kernespring.modules.matter_application.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.matter_application.entity.DisabilityAssessmentCategoryEntity;

import java.util.Map;

import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-08 15:29:03
 */
public interface DisabilityAssessmentCategoryService extends IService<DisabilityAssessmentCategoryEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<DisabilityAssessmentCategoryEntity> queryExportData(Map<String, Object> params);
}

