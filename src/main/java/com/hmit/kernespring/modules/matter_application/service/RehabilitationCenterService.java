package com.hmit.kernespring.modules.matter_application.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.matter_application.entity.RehabilitationCenterEntity;

import java.util.List;
import java.util.Map;

/**
 * 康复中心
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-07
 */
public interface RehabilitationCenterService extends IService<RehabilitationCenterEntity> {

    PageUtils queryPage(Map<String, Object> params);

    List<RehabilitationCenterEntity> list(Map<String, Object> params);

}
