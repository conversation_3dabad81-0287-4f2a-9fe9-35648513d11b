package com.hmit.kernespring.modules.matter_application.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hmit.kernespring.modules.cjrone.entity.CjroneChildrenRehabilitationSubsidyEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-10 09:42:23
 */
@Data
@TableName("cjrone_welfare_matter_application")
public class CjroneWelfareMatterApplicationEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */

@TableId
private Integer id;
	/**
	 * 残疾人姓名
	 */
	@Excel(name = "残疾人姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 身份证号
	 */
	@Excel(name = "身份证号", height = 20, width = 30, isImportField = "true_st")
private String idCard;
	/**
	 * 手机号
	 */
	@Excel(name = "手机号", height = 20, width = 30, isImportField = "true_st")
private String mobilePhone;
	/**
	 * 残疾证号
	 */
	@Excel(name = "残疾证号", height = 20, width = 30, isImportField = "true_st")
private String disableId;
	/**
	 * 事项名称
	 */
	@Excel(name = "事项名称", height = 20, width = 30, isImportField = "true_st")
private String matterName;
	/**
	 * 申请时间
	 */
	@Excel(name = "申请时间", height = 20, width = 30, isImportField = "true_st")
private String applicationTime;
	/**
	 * 审核时间
	 */
	@Excel(name = "审核时间", height = 20, width = 30, isImportField = "true_st")
private String verifyTime;
	/**
	 * 事项ID
	 */
private Integer matterId;

	/**
	 * 性别  1 男  2女
	 */
	// @Excel(name = "性别  1 男  2女", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String sex;
	/**
	 * 民族
	 */
	// @Excel(name = "民族", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String nationality;
	/**
	 * 婚姻情况  0未婚  1已婚
	 */
	// @Excel(name = "婚姻情况  0未婚  1已婚", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String maritalStatus;
	/**
	 * 出生日期
	 */
	// @Excel(name = "出生日期", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String birthday;
	/**
	 * 籍贯
	 */
	// @Excel(name = "籍贯", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String nativePlace;
	/**
	 * 文化程度
	 */
	// @Excel(name = "文化程度", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String educationDegree;
	/**
	 * 户籍地址  镇
	 */
	// @Excel(name = "户籍地址  镇", height = 20, width = 30, isImportField = "true_st")
	private String nativeZhen;
	/**
	 * 户籍地址 村
	 */
	// @Excel(name = "户籍地址 村", height = 20, width = 30, isImportField = "true_st")
	private String nativeCun;
	/**
	 * 户籍地址
	 */
	// @Excel(name = "户籍地址", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String nativeAddress;
	/**
	 * 现住址 镇
	 */
	// @Excel(name = "现住址 镇", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String presentZhen;
	/**
	 * 现地址  村
	 */
	// @Excel(name = "现地址  村", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String presentCun;
	/**
	 * 现地址
	 */
	// @Excel(name = "现地址", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String presentAddress;
	/**
	 * 邮编
	 */
	// @Excel(name = "邮编", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String postcode;
	/**
	 * 监护人姓名
	 */
	// @Excel(name = "监护人姓名", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String guardianName;
	/**
	 * 监护人手机
	 */
	// @Excel(name = "监护人手机", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String guardianPhone;
	/**
	 * 监护人身份证号
	 */
	// @Excel(name = "监护人身份证号", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String guardianIdcard;
	/**
	 * 与申请人关系
	 */
	// @Excel(name = "与申请人关系", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String guardianRelation;
	/**
	 * 二寸照
	 */
	// @Excel(name = "二寸照", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String photo;
	/**
	 * 申请类型
	 */
	// @Excel(name = "申请类型", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String applicationType;
	/**
	 * 状态
	 */
	// @Excel(name = "状态", height = 20, width = 30, isImportField = "true_st")
	//@TableField(exist = false)
	/**
	 * 状态说明：
	 * "0" - 禁用
	 * "1" - 申请人待手签
	 * "2" - 街道待审核
	 * "4" - 民政局经办人待审核
	 * "5" - 民政负责人待审核
	 * "6" - 区残联经办人待审核
	 * "7" - 区残联负责人待审核
	 * "8" - 通过
	 * "9" - 街道退回-社区
	 * "10" - 民政退回-街道
	 * "11" - 区残联-民政
	 * "12" - 区残联退回-街道
	 */
	private String status;
	/**
	 * 状态说明
	 */
	// @Excel(name = "状态说明", height = 20, width = 30, isImportField = "true_st")
	//@TableField(exist = false)
	private String statusOptions;
	/**
	 * 创建时间
	 */
	// @Excel(name = "创建时间", height = 20, width = 30, isImportField = "true_st")
	//@TableField(exist = false)
	private String createTime;
	/**
	 * 创建者
	 */
	// @Excel(name = "创建者", height = 20, width = 30, isImportField = "true_st")
	//@TableField(exist = false)
	private Long createId;

	//申请类型
	private Integer applyType;

	//退回状态
	private Integer returnStatus;

	//创建者姓名
	private String createName;

	//诊断证明照片URL
	private String diagnosisCertificateImage;

	//诊断证明时间 起始时间
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date diagnosisCertificateStartTime;

	//诊断证明时间 结束时间
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date diagnosisCertificateEndTime;

	//少年儿童康复训练补助
	@TableField(exist = false)
	private String isSnetkfxlApply;

	@TableField(exist = false)
	private List<CjroneChildrenRehabilitationSubsidyEntity>childrenRehabilitationSubsidyEntityList;

	//精神病住院补助
	@TableField(exist = false)
	private String isJsbApply;

	@TableField(exist = false)
	private List<CjroneMentalIllnessSubsidyEntity>mentalIllnessSubsidyEntitiyList;

	/**
	 * 查询时间筛选
	 */
	@TableField(exist = false)
	private String startDay;

	@TableField(exist = false)
	private String endDay;

	/**
	 * 残疾类别
	 */
	@Excel(name = "残疾类别", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String disabilityCategory;

	/**
	 * 残疾等级
	 */
	@Excel(name = "残疾等级", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String disabilityDegree;

	/**
	 * 银行账户
	 */
	@Excel(name = "银行账户", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String bankAccount;
	/**
	 * 开户银行
	 */
	@Excel(name = "开户银行", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String bankName;


	/**
	 * 附件列表
	 */
	@TableField(exist = false)
	private String documentList;
	/**
	 * 家庭经济情况
	 */
	@TableField(exist = false)
	private String familyEconoCondition;
	/**
	 * 享受医保医疗情况
	 */
	@TableField(exist = false)
	private String healthCareCondition;


	//生活补助金
	@TableField(exist = false)
	private String  isShbzjApply;

	//生活补贴
	@TableField(exist = false)
	private String isShbtApply;

	// 护理补贴
	@TableField(exist = false)
	private String isHlbtApply;

	// 职工基本养老保险
	@TableField(exist = false)
	private String isZgjbylApply;

	// 城乡居民养老保险
	@TableField(exist = false)
	private String isCxjmylApply;

	// 职工基本医疗保险
	@TableField(exist = false)
	private String isZgjbylbxApply;

	// 城乡基本医疗保险
	@TableField(exist = false)
	private String isCxjbylbxApply;

	//残疾人子女教育补助
	@TableField(exist=false)
	private String  isChildeduApply;

	//残疾人大学生补助
	@TableField(exist=false)
	private String isCollegeeduApply;

	//康复补助
	@TableField(exist=false)
	private String isKfbzApply;

	// 创业补助
	@TableField(exist = false)
	private String isCybzApply;

	//住院补助
	@TableField(exist = false)
	private String isZybzApply;

	// 医疗救助
	@TableField(exist = false)
	private String isYljzApply;

	// 临时救助
	@TableField(exist = false)
	private String isLsjzApply;

	//残疾人证到期换领
	@TableField(exist = false)
	private String isCjzhlApply;

	// 智慧爱心24小时
	@TableField(exist = false)
	private String isLove24Apply;

	// 护理补贴集中托养附件
	@TableField(exist = false)
	private String jztyDoc;

	//护理补贴的照料方式
	@TableField(exist = false)
	private String isHlbtApplyOptions;

	//康复补助项目
	@TableField(exist = false)
	private String rehabilitationProject;
	//康复补助项目
	@TableField(exist = false)
	private String rehabilitationProjectName;
	//医疗保险情况
	@TableField(exist = false)
	private String medicalInsurance;
	private String signStatus;
	/**
	 * 电子签章状态
	 */
	private String signatureStatus;

	//==============下面是就业创业补助的数据========================

	//经营类别
	@TableField(exist = false)
	private String 	managementType;

	//经营地址
	@TableField(exist=false)
	private String  managementAddress;

	//规模
	@TableField(exist=false)
	private String guimo;
	//创业类型
	@TableField(exist=false)
	private String employeeType;

	//申请补助金额
	@TableField(exist=false)
	private String subsidyMoney;

	//申请原因
	@TableField(exist=false)
	private String applyReason;

	//生活补贴补录
	@TableField(exist=false)
	private String isShbtBl;

	//生活补贴补录时间
	@TableField(exist=false)
	private String isShbtBlTime;

	//护理补贴补录
	@TableField(exist=false)
	private String isHlbtBl;

	//护理补贴补录时间
	@TableField(exist=false)
	private String isHlbtBlTime;

	// 生活补贴使用的家庭经济情况
	@TableField(exist=false)
	private String familyEconomy;

	// 生活补贴使用的月收入
	@TableField(exist=false)
	private String income;

	//=========护理补贴数据==========

	@TableField(exist=false)
	private String sixMonth;

	@TableField(exist=false)
	private String careType;

	@TableField(exist=false)
	private String lifeStatus;

	@TableField(exist=false)
	private String careMonths;

	//=========生活补助金数据=======
	@TableField(exist=false)
	private String pension;

	@TableField(exist=false)
	private String livingallowancefamilyEcono;

	@TableField(exist=false)
	private String shbzjDoc;

	@TableField(exist=false)
	private String kfbzDoc;

	//===============养老、医疗补贴数据=============
	@TableField(exist=false)
	private String seTime;

	@TableField(exist=false)
	private String payMoney;

	@TableField(exist=false)
	private String otherSubsidy;

	@TableField(exist=false)
	private String seTime2;

	@TableField(exist=false)
	private String payMoney2;

	@TableField(exist=false)
	private String otherSubsidy2;

	@TableField(exist=false)
	private String seTime3;

	@TableField(exist=false)
	private String payMoney3;

	@TableField(exist=false)
	private String otherSubsidy3;

	@TableField(exist=false)
	private String seTime4;

	@TableField(exist=false)
	private String payMoney4;

	@TableField(exist=false)
	private String otherSubsidy4;

	//============残疾人临时救助=================

	@TableField(exist=false)
	private String mingzhenSubsidy;

	@TableField(exist=false)
	private String lsjzpayMoney;

	@TableField(exist=false)
	private String lsjzsubsidyMoney;

	@TableField(exist=false)
	private String lsjzapplyReason;

	// =================创业补助===============
	@TableField(exist=false)
	private String startTime;

	@TableField(exist=false)
	private String manageAddress;

	@TableField(exist=false)
	private String manageRange;

	@TableField(exist=false)
	private String isCollege;

	@TableField(exist=false)
	private String subsidyReason;

	@TableField(exist=false)
	private String cysubsidyMoney;

	@TableField(exist=false)
	private String isEmployment;

	@TableField(exist=false)
	private String isFirsttime;

	@TableField(exist=false)
	private String isSixManage;

	// ===============大学生补助=========================
	@TableField(exist=false)
	private String collegeName;

	@TableField(exist=false)
	private String majorName;

	@TableField(exist=false)
	private String collegeTime;

	@TableField(exist=false)
	private String tuition;

	@TableField(exist=false)
	private String actuallyTuition;

	@TableField(exist=false)
	private String accommodationFee;


	@TableField(exist=false)
	private String actuallyAccommodationFee;

	@TableField(exist=false)
	private String hukouNature;

	@TableField(exist=false)
	private String familyCount;

	@TableField(exist=false)
	private String familyFinances;

	@TableField(exist=false)
	private String familyIncome;

	@TableField(exist=false)
	private String collegesubsidyReason;

	@TableField(exist=false)
	private String zybzzyTime;

	@TableField(exist=false)
	private String yljzzyTime;

	// =====智慧  爱心 24小时======
	@TableField(exist=false)
	private String relation1;

	@TableField(exist=false)
	private String name1;

	@TableField(exist=false)
	private String idcard1;

	@TableField(exist=false)
	private String tel1;

	@TableField(exist=false)
	private String relation2;

	@TableField(exist=false)
	private String name2;

	@TableField(exist=false)
	private String idcard2;

	@TableField(exist=false)
	private String tel2;

	@TableField(exist=false)
	private String relation3;

	@TableField(exist=false)
	private String name3;

	@TableField(exist=false)
	private String idcard3;

	@TableField(exist=false)
	private String tel3;

	@TableField(exist=false)
	private String relation4;

	@TableField(exist=false)
	private String name4;

	@TableField(exist=false)
	private String idcard4;

	@TableField(exist=false)
	private String tel4;

	// 1固定电话，2智能手机
	@TableField(exist=false)
	private String type;

	@TableField(exist=false)
	private String typeTelephone;

	@TableField(exist=false)
	private String dianxinTelephone;

	@TableField(exist=false)
	private String typeTelephone2;

	@TableField(exist=false)
	private String relation5;

	@TableField(exist=false)
	private String name5;

	@TableField(exist=false)
	private String tel5;

	@TableField(exist=false)
	private String relation6;

	@TableField(exist=false)
	private String name6;

	@TableField(exist=false)
	private String tel6;

	@TableField(exist=false)
	private String relation7;

	@TableField(exist=false)
	private String name7;

	@TableField(exist=false)
	private String tel7;

	@TableField(exist=false)
	private String relation8;

	@TableField(exist=false)
	private String name8;

	@TableField(exist=false)
	private String tel8;

	@TableField(exist=false)
	private String familyLove;

    //是否省外
    private Boolean isOutOfProvince;

}
