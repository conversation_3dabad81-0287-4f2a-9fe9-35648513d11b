package com.hmit.kernespring.modules.matter_application.dao;

import com.hmit.kernespring.modules.matter_application.entity.DisabilityAssessmentCategoryEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-08 15:29:03
 */
@Mapper
public interface DisabilityAssessmentCategoryDao extends BaseMapper<DisabilityAssessmentCategoryEntity> {
    List<DisabilityAssessmentCategoryEntity> queryExportData(Map<String, Object> params);
	
}
