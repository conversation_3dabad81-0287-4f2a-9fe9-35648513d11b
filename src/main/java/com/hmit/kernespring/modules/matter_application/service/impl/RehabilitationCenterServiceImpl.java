package com.hmit.kernespring.modules.matter_application.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;
import com.hmit.kernespring.modules.matter_application.dao.RehabilitationCenterDao;
import com.hmit.kernespring.modules.matter_application.entity.RehabilitationCenterEntity;
import com.hmit.kernespring.modules.matter_application.service.RehabilitationCenterService;
import com.sun.media.sound.SoftTuning;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.*;

@Service("rehabilitationCenterService")
public class RehabilitationCenterServiceImpl extends ServiceImpl<RehabilitationCenterDao, RehabilitationCenterEntity> implements RehabilitationCenterService {

    private Gson gson = new GsonBuilder()
            .setDateFormat("yyyy-MM-dd HH:mm:ss")
            .registerTypeAdapter(java.util.Date.class, new JsonDeserializer<java.util.Date>() {
                public java.util.Date deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
                    return new java.util.Date(json.getAsJsonPrimitive().getAsLong());
                }
            })
            .create();

    @Autowired
    private RehabilitationCenterDao rehabilitationCenterDao;

    public List<RehabilitationCenterEntity> list(Map<String, Object> params) {
        RehabilitationCenterEntity rehabilitationCenterEntity = gson.fromJson(params.get("key") != null ? params.get("key").toString() : null, RehabilitationCenterEntity.class);
        Date now = new Date();
        
        QueryWrapper<RehabilitationCenterEntity> wrapper = new QueryWrapper<RehabilitationCenterEntity>()
                .eq(rehabilitationCenterEntity.getId() != null, "id", rehabilitationCenterEntity.getId())
                .like(StringUtils.isNotBlank(rehabilitationCenterEntity.getDistrict()), "district", rehabilitationCenterEntity.getDistrict())
                .like(StringUtils.isNotBlank(rehabilitationCenterEntity.getInstitutionName()), "institution_name", rehabilitationCenterEntity.getInstitutionName())
                .like(StringUtils.isNotBlank(rehabilitationCenterEntity.getInstitutionType()), "institution_type", rehabilitationCenterEntity.getInstitutionType())
                .like(StringUtils.isNotBlank(rehabilitationCenterEntity.getInstitutionQualification()), "institution_qualification", rehabilitationCenterEntity.getInstitutionQualification())
                .like(StringUtils.isNotBlank(rehabilitationCenterEntity.getAddress()), "address", rehabilitationCenterEntity.getAddress())
                .like(StringUtils.isNotBlank(rehabilitationCenterEntity.getServiceCategory()), "service_category", rehabilitationCenterEntity.getServiceCategory())
                .and(Boolean.TRUE.equals(rehabilitationCenterEntity.getIsInAgreementPeriod()), w ->
                        w.ge("agreement_period_end", now)  // 协议结束时间 >= 当前时间
                         .le("agreement_period", now)     // 协议开始时间 <= 当前时间
                )
                .and(Boolean.FALSE.equals(rehabilitationCenterEntity.getIsInAgreementPeriod()), w ->
                        w.lt("agreement_period_end", now)  // 协议结束时间 < 当前时间 (已过期)
                         .or()
                         .gt("agreement_period", now)     // 协议开始时间 > 当前时间 (未开始)
                )
                .orderByDesc("id");

        return rehabilitationCenterDao.selectList(wrapper);
    }

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        RehabilitationCenterEntity rehabilitationCenterEntity = gson.fromJson(params.get("key") != null ? params.get("key").toString() : null, RehabilitationCenterEntity.class);
        Date now = new Date();
        IPage<RehabilitationCenterEntity> page = this.page(
                new Query<RehabilitationCenterEntity>().getPage(params),
                new QueryWrapper<RehabilitationCenterEntity>()
                        .eq(rehabilitationCenterEntity.getId() != null, "id", rehabilitationCenterEntity.getId())
                        .like(StringUtils.isNotBlank(rehabilitationCenterEntity.getDistrict()), "district", rehabilitationCenterEntity.getDistrict())
                        .like(StringUtils.isNotBlank(rehabilitationCenterEntity.getInstitutionName()), "institution_name", rehabilitationCenterEntity.getInstitutionName())
                        .like(StringUtils.isNotBlank(rehabilitationCenterEntity.getInstitutionType()), "institution_type", rehabilitationCenterEntity.getInstitutionType())
                        .like(StringUtils.isNotBlank(rehabilitationCenterEntity.getInstitutionQualification()), "institution_qualification", rehabilitationCenterEntity.getInstitutionQualification())
                        .like(StringUtils.isNotBlank(rehabilitationCenterEntity.getAddress()), "address", rehabilitationCenterEntity.getAddress())
                        .like(StringUtils.isNotBlank(rehabilitationCenterEntity.getServiceCategory()), "service_category", rehabilitationCenterEntity.getServiceCategory())
                        .and(Boolean.TRUE.equals(rehabilitationCenterEntity.getIsInAgreementPeriod()), wrapper ->
                                wrapper.ge("agreement_period_end", now)  // 协议结束时间 >= 当前时间
                                        .le("agreement_period", now)     // 协议开始时间 <= 当前时间
                        )
                        .and(Boolean.FALSE.equals(rehabilitationCenterEntity.getIsInAgreementPeriod()), wrapper ->
                                wrapper.lt("agreement_period_end", now)  // 协议结束时间 < 当前时间 (已过期)
                                        .or()
                                        .gt("agreement_period", now)     // 协议开始时间 > 当前时间 (未开始)
                        )
                        .orderByDesc("id")

        );


        return new PageUtils(page);
    }

}
