package com.hmit.kernespring.modules.matter_application.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 精神病住院补贴
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-06 14:22:35
 */
@Data
@TableName("cjrone_mental_illness_subsidy")
public class CjroneMentalIllnessSubsidyEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 自增主键ID
	 */
	
@TableId
@ApiModelProperty(value = "自增主键ID")
private Long id;

    /**
     * 姓名
     */
    @Excel(name = "姓名", height = 20, width = 30, isImportField = "true_st")
    @ApiModelProperty(value = "姓名")
    private String name;

	/**
	 * 年度累计金额
	 */
//	@Excel(name = "年度累计金额", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "年度累计金额")
private BigDecimal annualAccumulatedAmount;
	/**
	 * 是否精神科
	 */
private Boolean isPsychiatric;

	@Excel(name = "是否精神科", height = 20, width = 30, isImportField = "true_st", replace = {"是_true", "否_false", "否_null"})
	@TableField(exist = false)
	private Boolean isPsychiatricDisplay;
	/**
	 * 医院名称
	 */
	@Excel(name = "医院名称", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "医院名称")
private String hospitalName;
	/**
	 * 序号
	 */
//	@Excel(name = "序号", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "序号")
private String serialNumber;
	/**
	 * 申请日期
	 */
	@Excel(name = "申请日期", height = 20, width = 30, isImportField = "true_st", format = "yyyy-MM-dd")
@ApiModelProperty(value = "申请日期")
@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
private Date applicationDate;
	/**
	 * 住院时间（起）
	 */
	@Excel(name = "住院时间（起）", height = 20, width = 30, isImportField = "true_st", format = "yyyy-MM-dd")
@ApiModelProperty(value = "住院时间（起）")
@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
private Date hospitalizationStartDate;
	/**
	 * 住院时间（止）
	 */
	@Excel(name = "住院时间（止）", height = 20, width = 30, isImportField = "true_st", format = "yyyy-MM-dd")
@ApiModelProperty(value = "住院时间（止）")
@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
private Date hospitalizationEndDate;
	/**
	 * 补助年份
	 */
	@Excel(name = "补助年份", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "补助年份")
private Integer subsidyYear;
	/**
	 * 住院次数
	 */
//	@Excel(name = "住院次数", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "住院次数")
private Integer hospitalizationCount;
	/**
	 * 个人自付
	 */
	@Excel(name = "个人自付", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "个人自付")
private BigDecimal personalPayment;
	/**
	 * 乙类(先行)自付
	 */
	@Excel(name = "乙类(先行)自付", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "乙类(先行)自付")
private BigDecimal classBPayment;
	/**
	 * 医保超限价
	 */
	@Excel(name = "医保超限价", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "医保超限价")
private BigDecimal insuranceOverageLimit;
	/**
	 * 自付有效医疗费用
	 */
	@Excel(name = "自付有效医疗费用", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "自付有效医疗费用")
private BigDecimal effectiveOopMedicalExpense;
	/**
	 * 补助金额
	 */
	@Excel(name = "补助金额", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "补助金额")
private BigDecimal subsidyAmount;
	/**
	 * 申请备注
	 */
	@Excel(name = "申请备注", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "申请备注")
private String applyRemarks;
	/**
	 * 记录创建时间
	 */
	@Excel(name = "记录创建时间", height = 20, width = 30, isImportField = "true_st", format = "yyyy-MM-dd HH:mm:ss")
@ApiModelProperty(value = "记录创建时间")
@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
private Date createdAt;
	/**
	 * 记录更新时间
	 */
	@Excel(name = "记录更新时间", height = 20, width = 30, isImportField = "true_st", format = "yyyy-MM-dd HH:mm:ss")
@ApiModelProperty(value = "记录更新时间")
@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
private Date updatedAt;
	/**
	 * 附件
	 */
	@Excel(name = "附件", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "附件")
private String attach;
	/**
	 * 审批备注
	 */
	@Excel(name = "审批备注", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "审批备注")
private String reviewRemarks;
	/**
	 * 惠残事项申请ID
	 */
	@Excel(name = "惠残事项申请ID", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "惠残事项申请ID")
private Integer welfareMatterApplicationId;
	/**
	 * 经办人ID
	 */
	@Excel(name = "经办人ID", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "经办人ID")
private Long operatorId;
	/**
	 * 经办人姓名
	 */
	@Excel(name = "经办人姓名", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "经办人姓名")
private String operatorName;
	/**
	 * 审核人ID
	 */
	@Excel(name = "审核人ID", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "审核人ID")
private Integer auditorId;
	/**
	 * 审核人姓名
	 */
	@Excel(name = "审核人姓名", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "审核人姓名")
private String auditorName;
	/**
	 * 状态
	 */
	@Excel(name = "状态", height = 20, width = 30, isImportField = "true_st")
@ApiModelProperty(value = "状态")
private String status;
	/**
	 * 诊断证明图片URL
	 */
@ApiModelProperty(value = "诊断证明图片URL")
private String diagnosisCertificateImage;
	/**
	 * 诊断证明时间-起始时间
	 */
	@Excel(name = "诊断证明时间-起始时间", height = 20, width = 30, isImportField = "true_st", format = "yyyy-MM-dd")
@ApiModelProperty(value = "诊断证明时间-起始时间")
@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
private Date diagnosisCertificateStartTime;
	/**
	 * 诊断证明时间-结束时间
	 */
	@Excel(name = "诊断证明时间-结束时间", height = 20, width = 30, isImportField = "true_st", format = "yyyy-MM-dd")
@ApiModelProperty(value = "诊断证明时间-结束时间")
@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
private Date diagnosisCertificateEndTime;


	/**
	 * 性别 1男 0女
	 */
	private String sex;

	@Excel(name = "性别", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String sexName;

	/**
	 * 出生日期
	 */
	@Excel(name = "出生日期", height = 20, width = 30, isImportField = "true_st", format = "yyyy-MM-dd")
	@ApiModelProperty(value = "出生日期")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date birthday;

	/**
	 * 国籍
	 */
	@Excel(name = "国籍", height = 20, width = 30, isImportField = "true_st")
	@ApiModelProperty(value = "国籍")
	private String nationality;

	/**
	 * 身份证号
	 */
	@Excel(name = "身份证号", height = 20, width = 30, isImportField = "true_st")
	@ApiModelProperty(value = "身份证号")
	private String idCard;

	/**
	 * 手机号码
	 */
	@Excel(name = "手机号码", height = 20, width = 30, isImportField = "true_st")
	@ApiModelProperty(value = "手机号码")
	private String mobilePhone;

    /**
     * 审批时间
     */
    @Excel(name = "审批时间", height = 20, width = 30, isImportField = "true_st", format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date approvalTime;

    @Excel(name = "街道", height = 20, width = 30, isImportField = "true_st")
    private String nativeZhen;
}
