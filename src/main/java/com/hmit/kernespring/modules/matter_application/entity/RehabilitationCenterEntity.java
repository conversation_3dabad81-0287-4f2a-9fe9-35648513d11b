package com.hmit.kernespring.modules.matter_application.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("rehabilitation_center")
public class RehabilitationCenterEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    private Integer id;

    /**
     * 区域名称
     */
    private String district;

    /**
     * 机构名称
     */
    private String institutionName;

    /**
     * 机构类型
     */
    private String institutionType;

    /**
     * 机构资格
     */
    private String institutionQualification;

    /**
     * 地址
     */
    private String address;

    /**
     * 服务类别
     */
    private String serviceCategory;

    /**
     * 协议周期(起）
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date agreementPeriod;

    /**
     * 协议周期(止）
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date agreementPeriodEnd;

    /**
     * 是否在协议期内
     */
    @TableField(exist = false)
    private Boolean isInAgreementPeriod;

}
