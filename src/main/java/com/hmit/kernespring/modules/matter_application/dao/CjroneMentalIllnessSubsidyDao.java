package com.hmit.kernespring.modules.matter_application.dao;

import com.hmit.kernespring.modules.matter_application.entity.CjroneMentalIllnessSubsidyEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * ${comments}
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-06 14:22:35
 */
@Mapper
public interface CjroneMentalIllnessSubsidyDao extends BaseMapper<CjroneMentalIllnessSubsidyEntity> {
    List<CjroneMentalIllnessSubsidyEntity> queryExportData(Map<String, Object> params);
	Map<String,Object> queryStatistics(@Param("approvalYear") String approvalYear);
	
	/**
	 * 计算年度累计金额
	 * @param idCard 身份证号
	 * @param subsidyYear 补助年份
	 * @return 年度累计金额
	 */
	BigDecimal calculateYearTotalAmount(@Param("idCard") String idCard, @Param("subsidyYear") Integer subsidyYear);
}
