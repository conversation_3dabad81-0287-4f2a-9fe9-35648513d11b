package com.hmit.kernespring.modules.matter_application.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.matter_application.dao.DisabilityAssessmentCategoryDao;
import com.hmit.kernespring.modules.matter_application.entity.DisabilityAssessmentCategoryEntity;
import com.hmit.kernespring.modules.matter_application.service.DisabilityAssessmentCategoryService;


@Service("disabilityAssessmentCategoryService")
public class DisabilityAssessmentCategoryServiceImpl extends ServiceImpl<DisabilityAssessmentCategoryDao, DisabilityAssessmentCategoryEntity> implements DisabilityAssessmentCategoryService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private DisabilityAssessmentCategoryDao disabilityAssessmentCategoryDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        DisabilityAssessmentCategoryEntity disabilityAssessmentCategoryEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, DisabilityAssessmentCategoryEntity.class);
        IPage<DisabilityAssessmentCategoryEntity> page = this.page(
                new Query<DisabilityAssessmentCategoryEntity>().getPage(params),
                new QueryWrapper<DisabilityAssessmentCategoryEntity>()
            .eq(StringUtils.isNotBlank(disabilityAssessmentCategoryEntity.getId ()!=null && !"".equals(disabilityAssessmentCategoryEntity.getId ().toString())? disabilityAssessmentCategoryEntity.getId ().toString():null),"id", disabilityAssessmentCategoryEntity.getId ())
            .eq(StringUtils.isNotBlank(disabilityAssessmentCategoryEntity.getDisabilityType ()!=null && !"".equals(disabilityAssessmentCategoryEntity.getDisabilityType ().toString())? disabilityAssessmentCategoryEntity.getDisabilityType ().toString():null),"disability_type", disabilityAssessmentCategoryEntity.getDisabilityType ())
            .eq(StringUtils.isNotBlank(disabilityAssessmentCategoryEntity.getRatingTime ()!=null && !"".equals(disabilityAssessmentCategoryEntity.getRatingTime ().toString())? disabilityAssessmentCategoryEntity.getRatingTime ().toString():null),"rating_time", disabilityAssessmentCategoryEntity.getRatingTime ())
            .eq(StringUtils.isNotBlank(disabilityAssessmentCategoryEntity.getRatingLocation ()!=null && !"".equals(disabilityAssessmentCategoryEntity.getRatingLocation ().toString())? disabilityAssessmentCategoryEntity.getRatingLocation ().toString():null),"rating_location", disabilityAssessmentCategoryEntity.getRatingLocation ())
            .eq(StringUtils.isNotBlank(disabilityAssessmentCategoryEntity.getMaterial ()!=null && !"".equals(disabilityAssessmentCategoryEntity.getMaterial ().toString())? disabilityAssessmentCategoryEntity.getMaterial ().toString():null),"material", disabilityAssessmentCategoryEntity.getMaterial ())
        );
        return new PageUtils(page);
    }
    @Override
    public List<DisabilityAssessmentCategoryEntity> queryExportData(Map<String, Object> params) {
            return disabilityAssessmentCategoryDao.queryExportData(params);
    }

}