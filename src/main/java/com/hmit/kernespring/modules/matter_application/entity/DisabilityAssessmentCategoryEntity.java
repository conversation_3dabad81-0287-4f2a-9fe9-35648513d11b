package com.hmit.kernespring.modules.matter_application.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-08 15:29:03
 */
@Data
@TableName("disability_assessment_category")
public class DisabilityAssessmentCategoryEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
@TableId
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 类别名称
	 */
	@Excel(name = "类别名称", height = 20, width = 30, isImportField = "true_st")
private String disabilityType;
	/**
	 * 评定时间
	 */
	@Excel(name = "评定时间", height = 20, width = 30, isImportField = "true_st")
private String ratingTime;
	/**
	 * 评定地点
	 */
	@Excel(name = "评定地点", height = 20, width = 30, isImportField = "true_st")
private String ratingLocation;
	/**
	 * 附件资料
	 */
	@Excel(name = "附件资料", height = 20, width = 30, isImportField = "true_st")
private String material;

	/**
	 * 评定医院
	 */
	@Excel(name = "评定医院", height = 20, width = 30, isImportField = "true_st")
	private String hospital;
	/**
	 * 评定医院ID
	 */
	private String hospitalId;
}
