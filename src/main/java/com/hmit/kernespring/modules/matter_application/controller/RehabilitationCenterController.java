package com.hmit.kernespring.modules.matter_application.controller;

import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;
import com.hmit.kernespring.modules.matter_application.entity.RehabilitationCenterEntity;
import com.hmit.kernespring.modules.matter_application.service.RehabilitationCenterService;
import com.hmit.kernespring.modules.sys.controller.AbstractController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("matter_application/rehabilitationCenter")
public class RehabilitationCenterController extends AbstractController {

    @Autowired
    private RehabilitationCenterService rehabilitationCenterService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    public R list(@RequestParam Map<String, Object> params){
        System.out.println("list params: "+params);
        List<RehabilitationCenterEntity> list = rehabilitationCenterService.list(params);
        return R.ok().put("list", list);
    }

    /**
     * 分页查询
     */
    @RequestMapping("/page")
    public R page(@RequestParam Map<String, Object> params){
        PageUtils page = rehabilitationCenterService.queryPage(params);
        return R.ok().put("page", page);
    }

    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    public R info(@PathVariable("id") Integer id){
        RehabilitationCenterEntity rehabilitationCenter = rehabilitationCenterService.getById(id);
        return R.ok().put("rehabilitationCenter", rehabilitationCenter);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    public R save(@RequestBody RehabilitationCenterEntity rehabilitationCenter){
        rehabilitationCenterService.save(rehabilitationCenter);
        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    public R update(@RequestBody RehabilitationCenterEntity rehabilitationCenter){
        rehabilitationCenterService.updateById(rehabilitationCenter);
        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    public R delete(@RequestBody Integer[] ids){
        rehabilitationCenterService.removeByIds(Arrays.asList(ids));
        return R.ok();
    }
}
