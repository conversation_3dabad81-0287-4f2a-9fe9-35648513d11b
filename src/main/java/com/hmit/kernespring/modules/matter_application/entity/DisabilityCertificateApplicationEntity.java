package com.hmit.kernespring.modules.matter_application.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hmit.kernespring.modules.cjrone.entity.CjroneCertificateApplyDocEntity;
import com.hmit.kernespring.modules.cjrone.entity.CjroneDocumentEntity;
import com.hmit.kernespring.modules.cjrone.entity.CjroneSignatureEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 残疾证申请表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-09 14:53:34
 */
@Data
@TableName("disability_certificate_application")
public class DisabilityCertificateApplicationEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
	@TableId
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 残疾人姓名
	 */
	@Excel(name = "残疾人姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 性别  1 男  2女
	 */
	@Excel(name = "性别", height = 20, width = 30, isImportField = "true_st")
private String sex;
	/**
	 * 民族
	 */
	@Excel(name = "民族", height = 20, width = 30, isImportField = "true_st")
private String nationality;
	/**
	 * 婚姻情况  0未婚  1已婚
	 */
	@Excel(name = "婚姻情况", height = 20, width = 30, isImportField = "true_st")
private String maritalStatus;
	/**
	 * 出生日期
	 */
	@Excel(name = "出生日期", height = 20, width = 30, isImportField = "true_st")
private String birthday;
	/**
	 * 籍贯
	 */
	@Excel(name = "籍贯", height = 20, width = 30, isImportField = "true_st")
private String nativePlace;
	/**
	 * 文化程度
	 */
	@Excel(name = "文化程度", height = 20, width = 30, isImportField = "true_st")
private String educationDegree;
	/**
	 * 身份证号码
	 */
	@Excel(name = "身份证号码", height = 20, width = 30, isImportField = "true_st")
private String idCard;
/**
	 * 残疾证号
	 */
	@Excel(name = "残疾证号", height = 20, width = 30, isImportField = "true_st")
private String disableId;
	/**
	 * 残疾类别名称
	 */
	@Excel(name = "残疾类别", height = 20, width = 30, isImportField = "true_st")
	private String disabilityTypeName;
	/**
	 * 残疾等级
	 */
	@Excel(name = "残疾等级", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String disabilityDegreeName;
	/**
	 * 户籍地址  镇
	 */
private String nativeZhen;

	@Excel(name = "户籍所在镇", height = 20, width = 30, isImportField = "true_st")
private String nativeZhenName;

	/**
	 * 户籍地址 村
	 */
private String nativeCun;

	@Excel(name = "户籍所在村", height = 20, width = 30, isImportField = "true_st")
private String nativeCunName;

	/**
	 * 户籍地址
	 */
	@Excel(name = "户籍地址", height = 20, width = 30, isImportField = "true_st")
private String nativeAddress;
	/**
	 * 现住址 镇
	 */
private String presentZhen;

	@Excel(name = "现住址所在镇", height = 20, width = 30, isImportField = "true_st")
private String presentZhenName;

	/**
	 * 现地址  村
	 */
private String presentCun;

	@Excel(name = "现住址所在村", height = 20, width = 30, isImportField = "true_st")
private String presentCunName;

	@TableField(exist = false)
	private String presentShen;

	@TableField(exist = false)
	private String presentShi;

	@TableField(exist = false)
	private String presentQu;


	/**
	 * 现地址
	 */
	@Excel(name = "现地址", height = 20, width = 30, isImportField = "true_st")
private String presentAddress;
	/**
	 * 邮编
	 */
	@Excel(name = "邮编", height = 20, width = 30, isImportField = "true_st")
private String postcode;
	/**
	 * 联系电话
	 */
	@Excel(name = "联系电话", height = 20, width = 30, isImportField = "true_st")
private String mobilePhone;
	/**
	 * 联系人姓名
	 */
	@Excel(name = "联系人姓名", height = 20, width = 30, isImportField = "true_st")
private String guardianName;
	/**
	 * 联系人手机
	 */
	@Excel(name = "联系人手机", height = 20, width = 30, isImportField = "true_st")
private String guardianPhone;
	/**
	 * 联系人身份证号
	 */
	@Excel(name = "联系人身份证号", height = 20, width = 30, isImportField = "true_st")
private String guardianIdcard;
	/**
	 * 与申请人关系
	 */
	@Excel(name = "与申请人关系", height = 20, width = 30, isImportField = "true_st")
private String guardianRelation;

	/**
	 * 申请类型
	 */
	@Excel(name = "申请类型", height = 20, width = 30, isImportField = "true_st")
private String applicationType;
/**
	 * 评残地点
	 */
	@Excel(name = "评残地点", height = 20, width = 30, isImportField = "true_st")
private String pingCanAddress;
/**
	 * 评残时间
	 */
	@Excel(name = "评残时间", height = 20, width = 30, isImportField = "true_st")
private String pingCanTime;

	//评残结束时间
	@TableField(exist = false)
	private String pingCanjsTime;

	/**
	 * 状态
	 */
private String status;
	/**
	 * 创建时间
	 */
	private String createTime;
	/**
	 * 申请时间
	 */
	@Excel(name = "申请时间", height = 20, width = 30, isImportField = "true_st")
	private String applicationTime;
	/**
	 * 创建者
	 */
private Long createId;
	/**
	 * 残疾类别
	 */
private String disabilityType;


	@Excel(name = "银行账号", height = 20, width = 30, isImportField = "true_st")
	private String bankAccount;

	@Excel(name = "开户银行", height = 20, width = 30, isImportField = "true_st")
	private String bankName;

	private String isRemote;

	/**
	 * 附件列表
	 */
	@TableField(exist = false)
    private String documentList;
	/**
	 * 附件列表
	 */
	@TableField(exist = false)
	private List<CjroneCertificateApplyDocEntity> docList;
	/**
	 * 附件详细列表
	 */
	@TableField(exist = false)
	private List<CjroneDocumentEntity> documentEntityList;
	 /**
	 * 附件映射列表
	 */
	@TableField(exist = false)
	private List<CjroneCertificateApplyDocEntity> documentDocList;
	/**
	 * 签章信息
	 */
	@TableField(exist = false)
	private CjroneSignatureEntity cjroneSignatureEntity;
	/**
	 * 家庭经济情况
	 */
@TableField(exist = false)
private String familyEconoCondition;
	/**
	 * 享受医保医疗情况
	 */
@TableField(exist = false)
private String healthCareCondition;
/**
	 * 电子签证 名称
	 */
@TableField(exist = false)
private String templateName;

	/**
	 * 残疾类别
	 */
	@TableField(exist = false)
	private String disabilityCategory;

	/**
	 * 残疾等级
	 */
	@TableField(exist = false)
	private String disabilityDegree;
/**
	 * 残疾类别
	 */
	@TableField(exist = false)
	private String disabilityCategoryName;


/**
	 * 状态说明
	 */
	private String statusOptions;

/**
	 * 是否资料齐全
	 */
	private String isPczlQq;

	/**
	 * 手签状态
	 */
	private String signStatus;


	/**
	 * 二寸照
	 */
	@Excel(name = "2寸照", type = 2 ,width = 28.75 , height = 84.5,imageType = 1)
	private String photo;

	@TableField(exist = false)
	private String datastatus;

	@TableField(exist = false)
	private String isFx;

	@TableField(exist = false)
	private String isDead;
	/**
	 * 评残时间
	 */
	@TableField(exist = false)
	private String pinCanTime;
	/**
	 * 退回状态
	 */
	private String backStatus;
	/**
	 * 电子签章状态
	 */
	private String signatureStatus;
	/**
	 * 排序号
	 */
	@TableField(exist = false)
	private String orderNum;

	private String zhenOperator;

	private String zhenOperateTime;

	private String qclOperator;

	private String qclOperateTime;

	private String completeTime;

	private String pingCanCompleteTime;

	private String isShow;
	@TableField(exist = false)
	private String isZjdShow;
	private String isQclShow;

}
