package com.hmit.kernespring.modules.matter_application.dao;

import com.hmit.kernespring.modules.matter_application.entity.RehabilitationCenterEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 康复中心
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-07
 */
@Mapper
public interface RehabilitationCenterDao extends BaseMapper<RehabilitationCenterEntity> {

    /**
     * 查询所有康复中心数据
     * @return 康复中心列表
     */
    List<RehabilitationCenterEntity> queryAll();

    /**
     * 根据区域查询康复中心
     * @param district 区域名称
     * @return 康复中心列表
     */
    List<RehabilitationCenterEntity> queryByDistrict(String district);

    /**
     * 根据机构类型查询康复中心
     * @param institutionType 机构类型
     * @return 康复中心列表
     */
    List<RehabilitationCenterEntity> queryByInstitutionType(String institutionType);

    /**
     * 根据服务类别查询康复中心
     * @param serviceCategory 服务类别
     * @return 康复中心列表
     */
    List<RehabilitationCenterEntity> queryByServiceCategory(String serviceCategory);


}
