package com.hmit.kernespring.modules.matter_application.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.matter_application.entity.CjroneMentalIllnessSubsidyEntity;

import java.util.Map;

import java.util.List;

/**
 * 精神病住院补贴
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-06 14:22:35
 */
public interface CjroneMentalIllnessSubsidyService extends IService<CjroneMentalIllnessSubsidyEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneMentalIllnessSubsidyEntity> queryExportData(Map<String, Object> params);
    List<CjroneMentalIllnessSubsidyEntity> queryHistoryByIdCard(String idCard, Integer status, String subsidyYear);
    boolean updateAudioById(CjroneMentalIllnessSubsidyEntity cjroneMentalIllnessSubsidy);

    Map<String, Object> statistics(String approvalYear);
    
    /**
     * 计算年度累计金额
     * @param idCard 身份证号
     * @param subsidyYear 补助年份
     * @return 年度累计金额
     */
    java.math.BigDecimal calculateYearTotalAmount(String idCard, Integer subsidyYear);
    
    /**
     * 检查年度累计金额是否超过限制
     * @param idCard 身份证号
     * @param subsidyYear 补助年份
     * @param newAmount 新申请金额
     * @return 是否超过限制
     */
    boolean checkYearTotalAmountLimit(String idCard, Integer subsidyYear, java.math.BigDecimal newAmount);
}

