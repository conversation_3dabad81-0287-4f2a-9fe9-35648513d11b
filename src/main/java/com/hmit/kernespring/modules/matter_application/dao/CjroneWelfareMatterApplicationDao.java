package com.hmit.kernespring.modules.matter_application.dao;

import com.hmit.kernespring.modules.matter_application.entity.CjroneWelfareMatterApplicationEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-10 09:42:23
 */
@Mapper
public interface CjroneWelfareMatterApplicationDao extends BaseMapper<CjroneWelfareMatterApplicationEntity> {
    List<CjroneWelfareMatterApplicationEntity> queryExportData(Map<String, Object> params);
    void updateStatusByMap(Map<String, Object> params);
    Map<String, Object> queryMattersByMap(Map<String, Object> params);
    Map<String, Object> queryMattersByMaB(Map<String, Object> params);
    Map<String, Object> queryMattersIdByMap(Map<String, Object> params);
    void updateStatusById(CjroneWelfareMatterApplicationEntity welfareMatterApplicationEntity);
    void updateSignStatusById(CjroneWelfareMatterApplicationEntity welfareMatterApplicationEntity);
    CjroneWelfareMatterApplicationEntity queryWelfareMatterByEnt(CjroneWelfareMatterApplicationEntity welfareMatterApplicationEntity);
    List<Map<String, Object>> queryFamilyEconoCondition(Map<String, Object> params);
}
