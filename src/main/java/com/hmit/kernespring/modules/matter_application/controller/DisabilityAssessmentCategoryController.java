package com.hmit.kernespring.modules.matter_application.controller;

import java.io.File;
import java.io.IOException;
import java.util.*;

import java.io.OutputStream;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import org.apache.poi.ss.usermodel.Workbook;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hmit.kernespring.modules.matter_application.entity.DisabilityAssessmentCategoryEntity;
import com.hmit.kernespring.modules.matter_application.service.DisabilityAssessmentCategoryService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-08 15:29:03
 */
@RestController
@RequestMapping("matter_application/disabilityassessmentcategory")
public class DisabilityAssessmentCategoryController {
    @Autowired
    private DisabilityAssessmentCategoryService disabilityAssessmentCategoryService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    // @RequiresPermissions("matter_application:disabilityassessmentcategory:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = disabilityAssessmentCategoryService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("matter_application:disabilityassessmentcategory:info")
    public R info(@PathVariable("id") Integer id){
		DisabilityAssessmentCategoryEntity disabilityAssessmentCategory = disabilityAssessmentCategoryService.getById(id);

        return R.ok().put("disabilityAssessmentCategory", disabilityAssessmentCategory);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("matter_application:disabilityassessmentcategory:save")
    public R save(@RequestBody DisabilityAssessmentCategoryEntity disabilityAssessmentCategory){
		disabilityAssessmentCategoryService.save(disabilityAssessmentCategory);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("matter_application:disabilityassessmentcategory:update")
    public R update(@RequestBody DisabilityAssessmentCategoryEntity disabilityAssessmentCategory){
		disabilityAssessmentCategoryService.updateById(disabilityAssessmentCategory);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("matter_application:disabilityassessmentcategory:delete")
    public R delete(@RequestBody Integer[] ids){
		disabilityAssessmentCategoryService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    // @RequiresPermissions("matter_application:disabilityassessmentcategory:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<DisabilityAssessmentCategoryEntity> disabilityAssessmentCategoryList = new ArrayList<>();
        list.forEach(item ->{
                    item.put("id",item.get(""));
                    item.put("disabilityType",item.get("类别名称"));
                    item.put("ratingTime",item.get("评定时间"));
                    item.put("ratingLocation",item.get("评定地点"));
                    item.put("material",item.get("附件资料"));
                    disabilityAssessmentCategoryList.add(new Gson().fromJson(new Gson().toJson(item), DisabilityAssessmentCategoryEntity.class));
        });
        // 保存到数据库
        disabilityAssessmentCategoryService.saveBatch(disabilityAssessmentCategoryList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    // @RequiresPermissions("matter_application:disabilityassessmentcategory:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<DisabilityAssessmentCategoryEntity> disabilityAssessmentCategoryEntityList = disabilityAssessmentCategoryService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("", null, "");
        Workbook workbook = ExcelExportUtil.exportExcel(params, DisabilityAssessmentCategoryEntity.class, disabilityAssessmentCategoryEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

}
