package com.hmit.kernespring.modules.matter_application.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.data_management.entity.DisabilityCertificateApplicationZCLEntity;
import com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity;


import java.util.List;
import java.util.Map;

/**
 * 残疾证申请表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-04 16:03:42
 */
public interface DisabilityCertificateApplicationService extends IService<DisabilityCertificateApplicationEntity> {

    PageUtils queryPage(Map<String, Object> params);
    PageUtils queryPageBsdj(Map<String, Object> params);
    PageUtils queryPagee(Map<String, Object> params);
    PageUtils queryPageQcl(Map<String, Object> params);
    PageUtils queryPageZjdFz(Map<String, Object> params);
    List<DisabilityCertificateApplicationEntity> queryExportData(Map<String, Object> params);
    List<DisabilityCertificateApplicationEntity> listDouble(Map<String, Object> params);
    List<DisabilityCertificateApplicationZCLEntity> queryExportDataFz(Map<String, Object> params);
    List<DisabilityCertificateApplicationZCLEntity> queryExportDataZjdFz(Map<String, Object> params);
    List<DisabilityCertificateApplicationEntity> queryExportData1(Map<String, Object> params);
    DisabilityCertificateApplicationEntity getByIDCard(String idCard);
    DisabilityCertificateApplicationEntity getByIDCardN(String idCard);
    DisabilityCertificateApplicationEntity getByMap(Map<String, Object> params);
    DisabilityCertificateApplicationEntity getMapByMap(Map<String, Object> params);
    List<DisabilityCertificateApplicationEntity> getListByMap(Map<String, Object> params);
    void updateByMap(Map<String, Object> params);
    void updateStatusById(DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity);
    void updateSignStatusById(DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity);
    void updateSignatureStatusById(DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity);
    void updateStatusById(DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity,boolean isPingCan);
    boolean reApply(DisabilityCertificateApplicationEntity disabilityCertificateApplication);
    void faZheng(DisabilityCertificateApplicationEntity disabilityCertificateApplication);
    void editById(DisabilityCertificateApplicationEntity disabilityCertificateApplication);
    void editNewById(DisabilityCertificateApplicationEntity disabilityCertificateApplication);
    void rePinCan(DisabilityCertificateApplicationEntity disabilityCertificateApplication);
}

