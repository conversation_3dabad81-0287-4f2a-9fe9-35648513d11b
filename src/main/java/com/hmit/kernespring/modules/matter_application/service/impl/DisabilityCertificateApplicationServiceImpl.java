package com.hmit.kernespring.modules.matter_application.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.google.gson.reflect.TypeToken;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;
import com.hmit.kernespring.modules.cjrone.entity.CjroneCertificateApplyDocEntity;
import com.hmit.kernespring.modules.cjrone.entity.CjroneDocumentEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneCertificateApplyDocService;
import com.hmit.kernespring.modules.cjrone.service.CjroneDocumentService;
import com.hmit.kernespring.modules.cjrone.service.CjroneSignatureService;
import com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity;
import com.hmit.kernespring.modules.data_management.entity.DisabilityCertificateApplicationZCLEntity;
import com.hmit.kernespring.modules.data_management.service.DataDisabilityCertificateService;
import com.hmit.kernespring.modules.matter_application.dao.DisabilityCertificateApplicationDao;
import com.hmit.kernespring.modules.matter_application.entity.DisabilityAssessmentCategoryEntity;
import com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity;
import com.hmit.kernespring.modules.matter_application.service.DisabilityAssessmentCategoryService;
import com.hmit.kernespring.modules.matter_application.service.DisabilityCertificateApplicationService;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;


@Service("disabilityCertificateApplicationService")
public class DisabilityCertificateApplicationServiceImpl extends ServiceImpl<DisabilityCertificateApplicationDao, DisabilityCertificateApplicationEntity> implements DisabilityCertificateApplicationService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss")
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();

    @Autowired
    private CjroneCertificateApplyDocService cjroneCertificateApplyDocService;
    @Autowired
    private DisabilityCertificateApplicationDao disabilityCertificateApplicationDao;
    @Autowired
    private SysDictService sysDictService;
    @Autowired
    private DisabilityAssessmentCategoryService disabilityAssessmentCategoryService;
    @Autowired
    private CjroneSignatureService cjroneSignatureService;
    @Autowired
    private CjroneDocumentService cjroneDocumentService;
    @Autowired
    private DisabilityCertificateApplicationService disabilityCertificateApplicationService;
    @Autowired
    private DataDisabilityCertificateService dataDisabilityCertificateService;
    @Override
    public PageUtils    queryPage(Map<String, Object> params) {
        String type = params.get("type") !=null && !"".equals(params.get("type").toString()) ? params.get("type").toString() : null;
        DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, DisabilityCertificateApplicationEntity.class);
        System.out.println("============key============");
        System.out.println(new Gson().toJson(disabilityCertificateApplicationEntity));
        List<String> list = new ArrayList<>();
        if (type != null){
            list.add("4");
        }
        System.out.println("type"+type);
        if (disabilityCertificateApplicationEntity.getStatus () != null && !"".equals(disabilityCertificateApplicationEntity.getStatus ().toString())){
            list.add(disabilityCertificateApplicationEntity.getStatus ());
        }
        List<String> listFinal = list;
        IPage<DisabilityCertificateApplicationEntity> page = this.page(
                new Query<DisabilityCertificateApplicationEntity>().getPage(params),
                new QueryWrapper<DisabilityCertificateApplicationEntity>()
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getId ()!=null && !"".equals(disabilityCertificateApplicationEntity.getId ().toString())? disabilityCertificateApplicationEntity.getId().toString():null),"id", disabilityCertificateApplicationEntity.getId ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getName ()!=null && !"".equals(disabilityCertificateApplicationEntity.getName ().toString())? disabilityCertificateApplicationEntity.getName().toString():null),"name", disabilityCertificateApplicationEntity.getName ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getSex ()!=null && !"".equals(disabilityCertificateApplicationEntity.getSex ().toString())? disabilityCertificateApplicationEntity.getSex().toString():null),"sex", disabilityCertificateApplicationEntity.getSex ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getNationality ()!=null && !"".equals(disabilityCertificateApplicationEntity.getNationality ().toString())? disabilityCertificateApplicationEntity.getNationality().toString():null),"nationality", disabilityCertificateApplicationEntity.getNationality ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getMaritalStatus ()!=null && !"".equals(disabilityCertificateApplicationEntity.getMaritalStatus ().toString())? disabilityCertificateApplicationEntity.getMaritalStatus().toString():null),"marital_status", disabilityCertificateApplicationEntity.getMaritalStatus ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getBirthday ()!=null && !"".equals(disabilityCertificateApplicationEntity.getBirthday ().toString())? disabilityCertificateApplicationEntity.getBirthday().toString():null),"birthday", disabilityCertificateApplicationEntity.getBirthday ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getNativePlace ()!=null && !"".equals(disabilityCertificateApplicationEntity.getNativePlace ().toString())? disabilityCertificateApplicationEntity.getNativePlace().toString():null),"native_place", disabilityCertificateApplicationEntity.getNativePlace ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getEducationDegree ()!=null && !"".equals(disabilityCertificateApplicationEntity.getEducationDegree ().toString())? disabilityCertificateApplicationEntity.getEducationDegree ().toString():null),"education_degree", disabilityCertificateApplicationEntity.getEducationDegree ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getIdCard ()!=null && !"".equals(disabilityCertificateApplicationEntity.getIdCard ().toString())? disabilityCertificateApplicationEntity.getIdCard().toString():null),"id_card", disabilityCertificateApplicationEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getNativeZhen ()!=null && !"".equals(disabilityCertificateApplicationEntity.getNativeZhen ().toString())? disabilityCertificateApplicationEntity.getNativeZhen().toString():null),"native_zhen", disabilityCertificateApplicationEntity.getNativeZhen ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getNativeCun ()!=null && !"".equals(disabilityCertificateApplicationEntity.getNativeCun ().toString())? disabilityCertificateApplicationEntity.getNativeCun().toString():null),"native_cun", disabilityCertificateApplicationEntity.getNativeCun ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getNativeAddress ()!=null && !"".equals(disabilityCertificateApplicationEntity.getNativeAddress ().toString())? disabilityCertificateApplicationEntity.getNativeAddress().toString():null),"native_address", disabilityCertificateApplicationEntity.getNativeAddress ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getPresentZhen ()!=null && !"".equals(disabilityCertificateApplicationEntity.getPresentZhen ().toString())? disabilityCertificateApplicationEntity.getPresentZhen().toString():null),"present_zhen", disabilityCertificateApplicationEntity.getPresentZhen ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getPresentCun ()!=null && !"".equals(disabilityCertificateApplicationEntity.getPresentCun ().toString())? disabilityCertificateApplicationEntity.getPresentCun ().toString():null),"present_cun", disabilityCertificateApplicationEntity.getPresentCun ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getPresentAddress ()!=null && !"".equals(disabilityCertificateApplicationEntity.getPresentAddress ().toString())? disabilityCertificateApplicationEntity.getPresentAddress ().toString():null),"present_address", disabilityCertificateApplicationEntity.getPresentAddress ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getPostcode ()!=null && !"".equals(disabilityCertificateApplicationEntity.getPostcode ().toString())? disabilityCertificateApplicationEntity.getPostcode ().toString():null),"postcode", disabilityCertificateApplicationEntity.getPostcode ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getMobilePhone ()!=null && !"".equals(disabilityCertificateApplicationEntity.getMobilePhone ().toString())? disabilityCertificateApplicationEntity.getMobilePhone ().toString():null),"mobile_phone", disabilityCertificateApplicationEntity.getMobilePhone ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getGuardianName ()!=null && !"".equals(disabilityCertificateApplicationEntity.getGuardianName ().toString())? disabilityCertificateApplicationEntity.getGuardianName ().toString():null),"guardian_name", disabilityCertificateApplicationEntity.getGuardianName ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getGuardianPhone ()!=null && !"".equals(disabilityCertificateApplicationEntity.getGuardianPhone ().toString())? disabilityCertificateApplicationEntity.getGuardianPhone ().toString():null),"guardian_phone", disabilityCertificateApplicationEntity.getGuardianPhone ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getGuardianIdcard ()!=null && !"".equals(disabilityCertificateApplicationEntity.getGuardianIdcard ().toString())? disabilityCertificateApplicationEntity.getGuardianIdcard ().toString():null),"guardian_idcard", disabilityCertificateApplicationEntity.getGuardianIdcard ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getGuardianRelation ()!=null && !"".equals(disabilityCertificateApplicationEntity.getGuardianRelation ().toString())? disabilityCertificateApplicationEntity.getGuardianRelation ().toString():null),"guardian_relation", disabilityCertificateApplicationEntity.getGuardianRelation ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getPhoto ()!=null && !"".equals(disabilityCertificateApplicationEntity.getPhoto ().toString())? disabilityCertificateApplicationEntity.getPhoto ().toString():null),"photo", disabilityCertificateApplicationEntity.getPhoto ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getApplicationType ()!=null && !"".equals(disabilityCertificateApplicationEntity.getApplicationType ().toString())? disabilityCertificateApplicationEntity.getApplicationType ().toString():null),"application_type", disabilityCertificateApplicationEntity.getApplicationType ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getStatus ()!=null && type == null && !"".equals(disabilityCertificateApplicationEntity.getStatus ().toString())? disabilityCertificateApplicationEntity.getStatus ().toString():null),"status",disabilityCertificateApplicationEntity.getStatus ()) //(int)(Float.parseFloat(disabilityCertificateApplicationEntity.getStatus ()))
            .and(type != null,wrapper -> wrapper.in("status",listFinal))
                        .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getBackStatus()!=null  && !"".equals(disabilityCertificateApplicationEntity.getBackStatus().toString())? disabilityCertificateApplicationEntity.getBackStatus().toString():null),"back_status",disabilityCertificateApplicationEntity.getBackStatus())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getDisabilityType ()!=null && !"".equals(disabilityCertificateApplicationEntity.getDisabilityType ().toString())? disabilityCertificateApplicationEntity.getDisabilityType ().toString():null),"disability_type",disabilityCertificateApplicationEntity.getDisabilityType ()) //(int)(Float.parseFloat(disabilityCertificateApplicationEntity.getStatus ()))
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getCreateTime ()!=null && !"".equals(disabilityCertificateApplicationEntity.getCreateTime ().toString())? disabilityCertificateApplicationEntity.getCreateTime ().toString():null),"create_time", disabilityCertificateApplicationEntity.getCreateTime ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getApplicationTime ()!=null && !"".equals(disabilityCertificateApplicationEntity.getApplicationTime ().toString())? disabilityCertificateApplicationEntity.getApplicationTime ().toString():null),"application_time", disabilityCertificateApplicationEntity.getApplicationTime ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getCreateId ()!=null && !"".equals(disabilityCertificateApplicationEntity.getCreateId ().toString())? disabilityCertificateApplicationEntity.getCreateId ().toString():null),"create_id", disabilityCertificateApplicationEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getSignStatus ()!=null && !"".equals(disabilityCertificateApplicationEntity.getSignStatus ().toString())? disabilityCertificateApplicationEntity.getSignStatus ().toString():null),"sign_status", disabilityCertificateApplicationEntity.getSignStatus ())
            .orderByDesc("application_time")
        );
        final int[] orderNum = {1};
        page.getRecords().forEach( item -> {
            if ("1".equals(item.getSignStatus())) {
                item.setSignStatus("申请人待手签");
            } else if ("2".equals(item.getSignStatus())) {
                item.setSignStatus("申请人已手签");
            } else if ("3".equals(item.getSignStatus())) {
                item.setSignStatus("镇街道待手签");
            } else if ("4".equals(item.getSignStatus())) {
                item.setSignStatus("镇街道已手签");
            } else if ("5".equals(item.getSignStatus())) {
                item.setSignStatus("区残联未手签");
            } else if ("6".equals(item.getSignStatus())) {
                item.setSignStatus("区残联已手签");
            }
            if ("1".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("镇街道待电子公章");
            } else if ("2".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("镇街道已电子公章");
            } else if ("3".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("民政待电子公章");
            } else if ("4".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("已完成");
            } else {
                item.setSignatureStatus("无");
            }
            item.setOrderNum(String.valueOf(orderNum[0]));
            orderNum[0] = orderNum[0] +1;
        });
        return new PageUtils(page);
    }
    @Override
    public PageUtils    queryPageZjdFz(Map<String, Object> params) {
        String type = params.get("type") !=null && !"".equals(params.get("type").toString()) ? params.get("type").toString() : null;
        DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, DisabilityCertificateApplicationEntity.class);
        System.out.println("============key============");
        System.out.println(new Gson().toJson(disabilityCertificateApplicationEntity));
        List<String> list = new ArrayList<>();
        if (type != null){
            list.add("9");
        }
        System.out.println("type"+type);
        if (disabilityCertificateApplicationEntity.getStatus () != null && !"".equals(disabilityCertificateApplicationEntity.getStatus ().toString())){
            list.add(disabilityCertificateApplicationEntity.getStatus ());
        }
        List<String> listFinal = list;
        IPage<DisabilityCertificateApplicationEntity> page = this.page(
                new Query<DisabilityCertificateApplicationEntity>().getPage(params),
                new QueryWrapper<DisabilityCertificateApplicationEntity>()
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getId ()!=null && !"".equals(disabilityCertificateApplicationEntity.getId ().toString())? disabilityCertificateApplicationEntity.getId().toString():null),"id", disabilityCertificateApplicationEntity.getId ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getName ()!=null && !"".equals(disabilityCertificateApplicationEntity.getName ().toString())? disabilityCertificateApplicationEntity.getName().toString():null),"name", disabilityCertificateApplicationEntity.getName ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getSex ()!=null && !"".equals(disabilityCertificateApplicationEntity.getSex ().toString())? disabilityCertificateApplicationEntity.getSex().toString():null),"sex", disabilityCertificateApplicationEntity.getSex ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getNationality ()!=null && !"".equals(disabilityCertificateApplicationEntity.getNationality ().toString())? disabilityCertificateApplicationEntity.getNationality().toString():null),"nationality", disabilityCertificateApplicationEntity.getNationality ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getMaritalStatus ()!=null && !"".equals(disabilityCertificateApplicationEntity.getMaritalStatus ().toString())? disabilityCertificateApplicationEntity.getMaritalStatus().toString():null),"marital_status", disabilityCertificateApplicationEntity.getMaritalStatus ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getBirthday ()!=null && !"".equals(disabilityCertificateApplicationEntity.getBirthday ().toString())? disabilityCertificateApplicationEntity.getBirthday().toString():null),"birthday", disabilityCertificateApplicationEntity.getBirthday ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getNativePlace ()!=null && !"".equals(disabilityCertificateApplicationEntity.getNativePlace ().toString())? disabilityCertificateApplicationEntity.getNativePlace().toString():null),"native_place", disabilityCertificateApplicationEntity.getNativePlace ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getEducationDegree ()!=null && !"".equals(disabilityCertificateApplicationEntity.getEducationDegree ().toString())? disabilityCertificateApplicationEntity.getEducationDegree ().toString():null),"education_degree", disabilityCertificateApplicationEntity.getEducationDegree ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getIdCard ()!=null && !"".equals(disabilityCertificateApplicationEntity.getIdCard ().toString())? disabilityCertificateApplicationEntity.getIdCard().toString():null),"id_card", disabilityCertificateApplicationEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getNativeZhen ()!=null && !"".equals(disabilityCertificateApplicationEntity.getNativeZhen ().toString())? disabilityCertificateApplicationEntity.getNativeZhen().toString():null),"native_zhen", disabilityCertificateApplicationEntity.getNativeZhen ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getNativeCun ()!=null && !"".equals(disabilityCertificateApplicationEntity.getNativeCun ().toString())? disabilityCertificateApplicationEntity.getNativeCun().toString():null),"native_cun", disabilityCertificateApplicationEntity.getNativeCun ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getNativeAddress ()!=null && !"".equals(disabilityCertificateApplicationEntity.getNativeAddress ().toString())? disabilityCertificateApplicationEntity.getNativeAddress().toString():null),"native_address", disabilityCertificateApplicationEntity.getNativeAddress ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getPresentZhen ()!=null && !"".equals(disabilityCertificateApplicationEntity.getPresentZhen ().toString())? disabilityCertificateApplicationEntity.getPresentZhen().toString():null),"present_zhen", disabilityCertificateApplicationEntity.getPresentZhen ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getPresentCun ()!=null && !"".equals(disabilityCertificateApplicationEntity.getPresentCun ().toString())? disabilityCertificateApplicationEntity.getPresentCun ().toString():null),"present_cun", disabilityCertificateApplicationEntity.getPresentCun ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getPresentAddress ()!=null && !"".equals(disabilityCertificateApplicationEntity.getPresentAddress ().toString())? disabilityCertificateApplicationEntity.getPresentAddress ().toString():null),"present_address", disabilityCertificateApplicationEntity.getPresentAddress ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getPostcode ()!=null && !"".equals(disabilityCertificateApplicationEntity.getPostcode ().toString())? disabilityCertificateApplicationEntity.getPostcode ().toString():null),"postcode", disabilityCertificateApplicationEntity.getPostcode ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getMobilePhone ()!=null && !"".equals(disabilityCertificateApplicationEntity.getMobilePhone ().toString())? disabilityCertificateApplicationEntity.getMobilePhone ().toString():null),"mobile_phone", disabilityCertificateApplicationEntity.getMobilePhone ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getGuardianName ()!=null && !"".equals(disabilityCertificateApplicationEntity.getGuardianName ().toString())? disabilityCertificateApplicationEntity.getGuardianName ().toString():null),"guardian_name", disabilityCertificateApplicationEntity.getGuardianName ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getGuardianPhone ()!=null && !"".equals(disabilityCertificateApplicationEntity.getGuardianPhone ().toString())? disabilityCertificateApplicationEntity.getGuardianPhone ().toString():null),"guardian_phone", disabilityCertificateApplicationEntity.getGuardianPhone ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getGuardianIdcard ()!=null && !"".equals(disabilityCertificateApplicationEntity.getGuardianIdcard ().toString())? disabilityCertificateApplicationEntity.getGuardianIdcard ().toString():null),"guardian_idcard", disabilityCertificateApplicationEntity.getGuardianIdcard ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getGuardianRelation ()!=null && !"".equals(disabilityCertificateApplicationEntity.getGuardianRelation ().toString())? disabilityCertificateApplicationEntity.getGuardianRelation ().toString():null),"guardian_relation", disabilityCertificateApplicationEntity.getGuardianRelation ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getPhoto ()!=null && !"".equals(disabilityCertificateApplicationEntity.getPhoto ().toString())? disabilityCertificateApplicationEntity.getPhoto ().toString():null),"photo", disabilityCertificateApplicationEntity.getPhoto ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getApplicationType ()!=null && !"".equals(disabilityCertificateApplicationEntity.getApplicationType ().toString())? disabilityCertificateApplicationEntity.getApplicationType ().toString():null),"application_type", disabilityCertificateApplicationEntity.getApplicationType ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getStatus ()!=null && type == null && !"".equals(disabilityCertificateApplicationEntity.getStatus ().toString())? disabilityCertificateApplicationEntity.getStatus ().toString():null),"status",disabilityCertificateApplicationEntity.getStatus ()) //(int)(Float.parseFloat(disabilityCertificateApplicationEntity.getStatus ()))
            .and(type != null,wrapper -> wrapper.in("status",listFinal))
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getDisabilityType ()!=null && !"".equals(disabilityCertificateApplicationEntity.getDisabilityType ().toString())? disabilityCertificateApplicationEntity.getDisabilityType ().toString():null),"disability_type",disabilityCertificateApplicationEntity.getDisabilityType ()) //(int)(Float.parseFloat(disabilityCertificateApplicationEntity.getStatus ()))
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getCreateTime ()!=null && !"".equals(disabilityCertificateApplicationEntity.getCreateTime ().toString())? disabilityCertificateApplicationEntity.getCreateTime ().toString():null),"create_time", disabilityCertificateApplicationEntity.getCreateTime ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getApplicationTime ()!=null && !"".equals(disabilityCertificateApplicationEntity.getApplicationTime ().toString())? disabilityCertificateApplicationEntity.getApplicationTime ().toString():null),"application_time", disabilityCertificateApplicationEntity.getApplicationTime ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getCreateId ()!=null && !"".equals(disabilityCertificateApplicationEntity.getCreateId ().toString())? disabilityCertificateApplicationEntity.getCreateId ().toString():null),"create_id", disabilityCertificateApplicationEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getSignStatus ()!=null && !"".equals(disabilityCertificateApplicationEntity.getSignStatus ().toString())? disabilityCertificateApplicationEntity.getSignStatus ().toString():null),"sign_status", disabilityCertificateApplicationEntity.getSignStatus ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getIsShow ()!=null && !"".equals(disabilityCertificateApplicationEntity.getIsShow ().toString())? disabilityCertificateApplicationEntity.getIsShow ().toString():null),"is_show", disabilityCertificateApplicationEntity.getIsShow ())
            .orderByAsc("complete_time")
        );
        final int[] orderNum = {1};
        page.getRecords().forEach( item -> {
            if ("1".equals(item.getSignStatus())) {
                item.setSignStatus("申请人待手签");
            } else if ("2".equals(item.getSignStatus())) {
                item.setSignStatus("申请人已手签");
            } else if ("3".equals(item.getSignStatus())) {
                item.setSignStatus("镇街道待手签");
            } else if ("4".equals(item.getSignStatus())) {
                item.setSignStatus("镇街道已手签");
            } else if ("5".equals(item.getSignStatus())) {
                item.setSignStatus("区残联未手签");
            } else if ("6".equals(item.getSignStatus())) {
                item.setSignStatus("区残联已手签");
            }
            if ("1".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("镇街道待电子公章");
            } else if ("2".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("镇街道已电子公章");
            } else if ("3".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("民政待电子公章");
            } else if ("4".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("已完成");
            } else {
                item.setSignatureStatus("无");
            }
            item.setOrderNum(String.valueOf(orderNum[0]));
            orderNum[0] = orderNum[0] +1;
        });
        return new PageUtils(page);
    }

    @Override
    public PageUtils    queryPageBsdj(Map<String, Object> params) {
        String type = params.get("type") !=null && !"".equals(params.get("type").toString()) ? params.get("type").toString() : null;
        DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, DisabilityCertificateApplicationEntity.class);
        System.out.println("============key============");
        System.out.println(new Gson().toJson(disabilityCertificateApplicationEntity));
        List<String> list = new ArrayList<>();
        if (type != null){
            list.add("9");
        }
        System.out.println("type"+type);

        List<String> listFinal = list;
        IPage<DisabilityCertificateApplicationEntity> page = this.page(
                new Query<DisabilityCertificateApplicationEntity>().getPage(params),
                new QueryWrapper<DisabilityCertificateApplicationEntity>()
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getId ()!=null && !"".equals(disabilityCertificateApplicationEntity.getId ().toString())? disabilityCertificateApplicationEntity.getId().toString():null),"id", disabilityCertificateApplicationEntity.getId ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getName ()!=null && !"".equals(disabilityCertificateApplicationEntity.getName ().toString())? disabilityCertificateApplicationEntity.getName().toString():null),"name", disabilityCertificateApplicationEntity.getName ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getSex ()!=null && !"".equals(disabilityCertificateApplicationEntity.getSex ().toString())? disabilityCertificateApplicationEntity.getSex().toString():null),"sex", disabilityCertificateApplicationEntity.getSex ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getNationality ()!=null && !"".equals(disabilityCertificateApplicationEntity.getNationality ().toString())? disabilityCertificateApplicationEntity.getNationality().toString():null),"nationality", disabilityCertificateApplicationEntity.getNationality ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getMaritalStatus ()!=null && !"".equals(disabilityCertificateApplicationEntity.getMaritalStatus ().toString())? disabilityCertificateApplicationEntity.getMaritalStatus().toString():null),"marital_status", disabilityCertificateApplicationEntity.getMaritalStatus ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getBirthday ()!=null && !"".equals(disabilityCertificateApplicationEntity.getBirthday ().toString())? disabilityCertificateApplicationEntity.getBirthday().toString():null),"birthday", disabilityCertificateApplicationEntity.getBirthday ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getNativePlace ()!=null && !"".equals(disabilityCertificateApplicationEntity.getNativePlace ().toString())? disabilityCertificateApplicationEntity.getNativePlace().toString():null),"native_place", disabilityCertificateApplicationEntity.getNativePlace ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getEducationDegree ()!=null && !"".equals(disabilityCertificateApplicationEntity.getEducationDegree ().toString())? disabilityCertificateApplicationEntity.getEducationDegree ().toString():null),"education_degree", disabilityCertificateApplicationEntity.getEducationDegree ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getIdCard ()!=null && !"".equals(disabilityCertificateApplicationEntity.getIdCard ().toString())? disabilityCertificateApplicationEntity.getIdCard().toString():null),"id_card", disabilityCertificateApplicationEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getNativeZhen ()!=null && !"".equals(disabilityCertificateApplicationEntity.getNativeZhen ().toString())? disabilityCertificateApplicationEntity.getNativeZhen().toString():null),"native_zhen", disabilityCertificateApplicationEntity.getNativeZhen ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getNativeCun ()!=null && !"".equals(disabilityCertificateApplicationEntity.getNativeCun ().toString())? disabilityCertificateApplicationEntity.getNativeCun().toString():null),"native_cun", disabilityCertificateApplicationEntity.getNativeCun ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getNativeAddress ()!=null && !"".equals(disabilityCertificateApplicationEntity.getNativeAddress ().toString())? disabilityCertificateApplicationEntity.getNativeAddress().toString():null),"native_address", disabilityCertificateApplicationEntity.getNativeAddress ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getPresentZhen ()!=null && !"".equals(disabilityCertificateApplicationEntity.getPresentZhen ().toString())? disabilityCertificateApplicationEntity.getPresentZhen().toString():null),"present_zhen", disabilityCertificateApplicationEntity.getPresentZhen ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getPresentCun ()!=null && !"".equals(disabilityCertificateApplicationEntity.getPresentCun ().toString())? disabilityCertificateApplicationEntity.getPresentCun ().toString():null),"present_cun", disabilityCertificateApplicationEntity.getPresentCun ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getPresentAddress ()!=null && !"".equals(disabilityCertificateApplicationEntity.getPresentAddress ().toString())? disabilityCertificateApplicationEntity.getPresentAddress ().toString():null),"present_address", disabilityCertificateApplicationEntity.getPresentAddress ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getPostcode ()!=null && !"".equals(disabilityCertificateApplicationEntity.getPostcode ().toString())? disabilityCertificateApplicationEntity.getPostcode ().toString():null),"postcode", disabilityCertificateApplicationEntity.getPostcode ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getMobilePhone ()!=null && !"".equals(disabilityCertificateApplicationEntity.getMobilePhone ().toString())? disabilityCertificateApplicationEntity.getMobilePhone ().toString():null),"mobile_phone", disabilityCertificateApplicationEntity.getMobilePhone ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getGuardianName ()!=null && !"".equals(disabilityCertificateApplicationEntity.getGuardianName ().toString())? disabilityCertificateApplicationEntity.getGuardianName ().toString():null),"guardian_name", disabilityCertificateApplicationEntity.getGuardianName ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getGuardianPhone ()!=null && !"".equals(disabilityCertificateApplicationEntity.getGuardianPhone ().toString())? disabilityCertificateApplicationEntity.getGuardianPhone ().toString():null),"guardian_phone", disabilityCertificateApplicationEntity.getGuardianPhone ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getGuardianIdcard ()!=null && !"".equals(disabilityCertificateApplicationEntity.getGuardianIdcard ().toString())? disabilityCertificateApplicationEntity.getGuardianIdcard ().toString():null),"guardian_idcard", disabilityCertificateApplicationEntity.getGuardianIdcard ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getGuardianRelation ()!=null && !"".equals(disabilityCertificateApplicationEntity.getGuardianRelation ().toString())? disabilityCertificateApplicationEntity.getGuardianRelation ().toString():null),"guardian_relation", disabilityCertificateApplicationEntity.getGuardianRelation ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getPhoto ()!=null && !"".equals(disabilityCertificateApplicationEntity.getPhoto ().toString())? disabilityCertificateApplicationEntity.getPhoto ().toString():null),"photo", disabilityCertificateApplicationEntity.getPhoto ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getApplicationType ()!=null && !"".equals(disabilityCertificateApplicationEntity.getApplicationType ().toString())? disabilityCertificateApplicationEntity.getApplicationType ().toString():null),"application_type", disabilityCertificateApplicationEntity.getApplicationType ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getStatus ()!=null && type == null && !"".equals(disabilityCertificateApplicationEntity.getStatus ().toString())? disabilityCertificateApplicationEntity.getStatus ().toString():null),"status",disabilityCertificateApplicationEntity.getStatus ()) //(int)(Float.parseFloat(disabilityCertificateApplicationEntity.getStatus ()))
            .and(type != null,wrapper -> wrapper.in("status",listFinal))
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getDisabilityType ()!=null && !"".equals(disabilityCertificateApplicationEntity.getDisabilityType ().toString())? disabilityCertificateApplicationEntity.getDisabilityType ().toString():null),"disability_type",disabilityCertificateApplicationEntity.getDisabilityType ()) //(int)(Float.parseFloat(disabilityCertificateApplicationEntity.getStatus ()))
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getCreateTime ()!=null && !"".equals(disabilityCertificateApplicationEntity.getCreateTime ().toString())? disabilityCertificateApplicationEntity.getCreateTime ().toString():null),"create_time", disabilityCertificateApplicationEntity.getCreateTime ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getApplicationTime ()!=null && !"".equals(disabilityCertificateApplicationEntity.getApplicationTime ().toString())? disabilityCertificateApplicationEntity.getApplicationTime ().toString():null),"application_time", disabilityCertificateApplicationEntity.getApplicationTime ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getCreateId ()!=null && !"".equals(disabilityCertificateApplicationEntity.getCreateId ().toString())? disabilityCertificateApplicationEntity.getCreateId ().toString():null),"create_id", disabilityCertificateApplicationEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getSignStatus ()!=null && !"".equals(disabilityCertificateApplicationEntity.getSignStatus ().toString())? disabilityCertificateApplicationEntity.getSignStatus ().toString():null),"sign_status", disabilityCertificateApplicationEntity.getSignStatus ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getIsZjdShow ()!=null && !"".equals(disabilityCertificateApplicationEntity.getIsZjdShow ().toString())? disabilityCertificateApplicationEntity.getIsZjdShow ().toString():null),"is_zjd_show", disabilityCertificateApplicationEntity.getIsZjdShow ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getIsQclShow ()!=null && !"".equals(disabilityCertificateApplicationEntity.getIsQclShow ().toString())? disabilityCertificateApplicationEntity.getIsQclShow ().toString():null),"is_qcl_show", disabilityCertificateApplicationEntity.getIsQclShow ())
            .orderByAsc("ping_can_complete_time")
        );
        final int[] orderNum = {1};
        page.getRecords().forEach( item -> {
            if ("1".equals(item.getSignStatus())) {
                item.setSignStatus("申请人待手签");
            } else if ("2".equals(item.getSignStatus())) {
                item.setSignStatus("申请人已手签");
            } else if ("3".equals(item.getSignStatus())) {
                item.setSignStatus("镇街道待手签");
            } else if ("4".equals(item.getSignStatus())) {
                item.setSignStatus("镇街道已手签");
            } else if ("5".equals(item.getSignStatus())) {
                item.setSignStatus("区残联未手签");
            } else if ("6".equals(item.getSignStatus())) {
                item.setSignStatus("区残联已手签");
            }
            if ("1".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("镇街道待电子公章");
            } else if ("2".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("镇街道已电子公章");
            } else if ("3".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("民政待电子公章");
            } else if ("4".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("已完成");
            } else {
                item.setSignatureStatus("无");
            }
            item.setOrderNum(String.valueOf(orderNum[0]));
            orderNum[0] = orderNum[0] +1;

            //去获得评残结束时间  待优化


        });
        return new PageUtils(page);
    }
    @Override
    public PageUtils    queryPageQcl(Map<String, Object> params) {
        String type = params.get("type") !=null && !"".equals(params.get("type").toString()) ? params.get("type").toString() : null;
        DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, DisabilityCertificateApplicationEntity.class);
        System.out.println("============key============");
        System.out.println(new Gson().toJson(disabilityCertificateApplicationEntity));
        List<String> list = new ArrayList<>();
        if (type != null){
              list.add("4");
        }
        System.out.println("type"+type);
        if (disabilityCertificateApplicationEntity.getStatus () != null && !"".equals(disabilityCertificateApplicationEntity.getStatus ().toString())){
            list.add(disabilityCertificateApplicationEntity.getStatus ());
        }
        List<String> listFinal = list;
        IPage<DisabilityCertificateApplicationEntity> page = this.page(
                new Query<DisabilityCertificateApplicationEntity>().getPage(params),
                new QueryWrapper<DisabilityCertificateApplicationEntity>()
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getId ()!=null && !"".equals(disabilityCertificateApplicationEntity.getId ().toString())? disabilityCertificateApplicationEntity.getId().toString():null),"id", disabilityCertificateApplicationEntity.getId ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getName ()!=null && !"".equals(disabilityCertificateApplicationEntity.getName ().toString())? disabilityCertificateApplicationEntity.getName().toString():null),"name", disabilityCertificateApplicationEntity.getName ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getSex ()!=null && !"".equals(disabilityCertificateApplicationEntity.getSex ().toString())? disabilityCertificateApplicationEntity.getSex().toString():null),"sex", disabilityCertificateApplicationEntity.getSex ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getNationality ()!=null && !"".equals(disabilityCertificateApplicationEntity.getNationality ().toString())? disabilityCertificateApplicationEntity.getNationality().toString():null),"nationality", disabilityCertificateApplicationEntity.getNationality ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getMaritalStatus ()!=null && !"".equals(disabilityCertificateApplicationEntity.getMaritalStatus ().toString())? disabilityCertificateApplicationEntity.getMaritalStatus().toString():null),"marital_status", disabilityCertificateApplicationEntity.getMaritalStatus ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getBirthday ()!=null && !"".equals(disabilityCertificateApplicationEntity.getBirthday ().toString())? disabilityCertificateApplicationEntity.getBirthday().toString():null),"birthday", disabilityCertificateApplicationEntity.getBirthday ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getNativePlace ()!=null && !"".equals(disabilityCertificateApplicationEntity.getNativePlace ().toString())? disabilityCertificateApplicationEntity.getNativePlace().toString():null),"native_place", disabilityCertificateApplicationEntity.getNativePlace ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getEducationDegree ()!=null && !"".equals(disabilityCertificateApplicationEntity.getEducationDegree ().toString())? disabilityCertificateApplicationEntity.getEducationDegree ().toString():null),"education_degree", disabilityCertificateApplicationEntity.getEducationDegree ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getIdCard ()!=null && !"".equals(disabilityCertificateApplicationEntity.getIdCard ().toString())? disabilityCertificateApplicationEntity.getIdCard().toString():null),"id_card", disabilityCertificateApplicationEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getNativeZhen ()!=null && !"".equals(disabilityCertificateApplicationEntity.getNativeZhen ().toString())? disabilityCertificateApplicationEntity.getNativeZhen().toString():null),"native_zhen", disabilityCertificateApplicationEntity.getNativeZhen ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getNativeCun ()!=null && !"".equals(disabilityCertificateApplicationEntity.getNativeCun ().toString())? disabilityCertificateApplicationEntity.getNativeCun().toString():null),"native_cun", disabilityCertificateApplicationEntity.getNativeCun ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getNativeAddress ()!=null && !"".equals(disabilityCertificateApplicationEntity.getNativeAddress ().toString())? disabilityCertificateApplicationEntity.getNativeAddress().toString():null),"native_address", disabilityCertificateApplicationEntity.getNativeAddress ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getPresentZhen ()!=null && !"".equals(disabilityCertificateApplicationEntity.getPresentZhen ().toString())? disabilityCertificateApplicationEntity.getPresentZhen().toString():null),"present_zhen", disabilityCertificateApplicationEntity.getPresentZhen ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getPresentCun ()!=null && !"".equals(disabilityCertificateApplicationEntity.getPresentCun ().toString())? disabilityCertificateApplicationEntity.getPresentCun ().toString():null),"present_cun", disabilityCertificateApplicationEntity.getPresentCun ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getPresentAddress ()!=null && !"".equals(disabilityCertificateApplicationEntity.getPresentAddress ().toString())? disabilityCertificateApplicationEntity.getPresentAddress ().toString():null),"present_address", disabilityCertificateApplicationEntity.getPresentAddress ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getPostcode ()!=null && !"".equals(disabilityCertificateApplicationEntity.getPostcode ().toString())? disabilityCertificateApplicationEntity.getPostcode ().toString():null),"postcode", disabilityCertificateApplicationEntity.getPostcode ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getMobilePhone ()!=null && !"".equals(disabilityCertificateApplicationEntity.getMobilePhone ().toString())? disabilityCertificateApplicationEntity.getMobilePhone ().toString():null),"mobile_phone", disabilityCertificateApplicationEntity.getMobilePhone ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getGuardianName ()!=null && !"".equals(disabilityCertificateApplicationEntity.getGuardianName ().toString())? disabilityCertificateApplicationEntity.getGuardianName ().toString():null),"guardian_name", disabilityCertificateApplicationEntity.getGuardianName ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getGuardianPhone ()!=null && !"".equals(disabilityCertificateApplicationEntity.getGuardianPhone ().toString())? disabilityCertificateApplicationEntity.getGuardianPhone ().toString():null),"guardian_phone", disabilityCertificateApplicationEntity.getGuardianPhone ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getGuardianIdcard ()!=null && !"".equals(disabilityCertificateApplicationEntity.getGuardianIdcard ().toString())? disabilityCertificateApplicationEntity.getGuardianIdcard ().toString():null),"guardian_idcard", disabilityCertificateApplicationEntity.getGuardianIdcard ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getGuardianRelation ()!=null && !"".equals(disabilityCertificateApplicationEntity.getGuardianRelation ().toString())? disabilityCertificateApplicationEntity.getGuardianRelation ().toString():null),"guardian_relation", disabilityCertificateApplicationEntity.getGuardianRelation ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getPhoto ()!=null && !"".equals(disabilityCertificateApplicationEntity.getPhoto ().toString())? disabilityCertificateApplicationEntity.getPhoto ().toString():null),"photo", disabilityCertificateApplicationEntity.getPhoto ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getApplicationType ()!=null && !"".equals(disabilityCertificateApplicationEntity.getApplicationType ().toString())? disabilityCertificateApplicationEntity.getApplicationType ().toString():null),"application_type", disabilityCertificateApplicationEntity.getApplicationType ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getStatus ()!=null && type == null && !"".equals(disabilityCertificateApplicationEntity.getStatus ().toString())? disabilityCertificateApplicationEntity.getStatus ().toString():null),"status",disabilityCertificateApplicationEntity.getStatus ()) //(int)(Float.parseFloat(disabilityCertificateApplicationEntity.getStatus ()))
            .and(type != null,wrapper -> wrapper.in("status",listFinal))
                        .notIn(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getBackStatus()!=null  && !"".equals(disabilityCertificateApplicationEntity.getBackStatus().toString())? disabilityCertificateApplicationEntity.getBackStatus().toString():null),"back_status",disabilityCertificateApplicationEntity.getBackStatus())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getDisabilityType ()!=null && !"".equals(disabilityCertificateApplicationEntity.getDisabilityType ().toString())? disabilityCertificateApplicationEntity.getDisabilityType ().toString():null),"disability_type",disabilityCertificateApplicationEntity.getDisabilityType ()) //(int)(Float.parseFloat(disabilityCertificateApplicationEntity.getStatus ()))
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getCreateTime ()!=null && !"".equals(disabilityCertificateApplicationEntity.getCreateTime ().toString())? disabilityCertificateApplicationEntity.getCreateTime ().toString():null),"create_time", disabilityCertificateApplicationEntity.getCreateTime ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getApplicationTime ()!=null && !"".equals(disabilityCertificateApplicationEntity.getApplicationTime ().toString())? disabilityCertificateApplicationEntity.getApplicationTime ().toString():null),"application_time", disabilityCertificateApplicationEntity.getApplicationTime ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getCreateId ()!=null && !"".equals(disabilityCertificateApplicationEntity.getCreateId ().toString())? disabilityCertificateApplicationEntity.getCreateId ().toString():null),"create_id", disabilityCertificateApplicationEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getSignStatus ()!=null && !"".equals(disabilityCertificateApplicationEntity.getSignStatus ().toString())? disabilityCertificateApplicationEntity.getSignStatus ().toString():null),"sign_status", disabilityCertificateApplicationEntity.getSignStatus ())
            .orderByAsc("zhen_operate_time")
        );
        final int[] orderNum = {1};
        page.getRecords().forEach( item -> {
            if ("1".equals(item.getSignStatus())) {
                item.setSignStatus("申请人待手签");
            } else if ("2".equals(item.getSignStatus())) {
                item.setSignStatus("申请人已手签");
            } else if ("3".equals(item.getSignStatus())) {
                item.setSignStatus("镇街道待手签");
            } else if ("4".equals(item.getSignStatus())) {
                item.setSignStatus("镇街道已手签");
            } else if ("5".equals(item.getSignStatus())) {
                item.setSignStatus("区残联未手签");
            } else if ("6".equals(item.getSignStatus())) {
                item.setSignStatus("区残联已手签");
            }
            if ("1".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("镇街道待电子公章");
            } else if ("2".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("镇街道已电子公章");
            } else if ("3".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("民政待电子公章");
            } else if ("4".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("已完成");
            } else {
                item.setSignatureStatus("无");
            }

            if("1".equals(item.getIsRemote())){
                item.setIsRemote("是");
            }else{
                item.setIsRemote("否");
            }

            item.setOrderNum(String.valueOf(orderNum[0]));
            orderNum[0] = orderNum[0] +1;
        });
        return new PageUtils(page);
    }
    @Override
    public PageUtils queryPagee(Map<String, Object> params) {
        DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, DisabilityCertificateApplicationEntity.class);
        System.out.println("============key============");
        System.out.println(new Gson().toJson(disabilityCertificateApplicationEntity));
        IPage<DisabilityCertificateApplicationEntity> page = this.page(
                new Query<DisabilityCertificateApplicationEntity>().getPage(params),
                new QueryWrapper<DisabilityCertificateApplicationEntity>()
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getId ()!=null && !"".equals(disabilityCertificateApplicationEntity.getId ().toString())? disabilityCertificateApplicationEntity.getId().toString():null),"id", disabilityCertificateApplicationEntity.getId ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getName ()!=null && !"".equals(disabilityCertificateApplicationEntity.getName ().toString())? disabilityCertificateApplicationEntity.getName().toString():null),"name", disabilityCertificateApplicationEntity.getName ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getSex ()!=null && !"".equals(disabilityCertificateApplicationEntity.getSex ().toString())? disabilityCertificateApplicationEntity.getSex().toString():null),"sex", disabilityCertificateApplicationEntity.getSex ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getNationality ()!=null && !"".equals(disabilityCertificateApplicationEntity.getNationality ().toString())? disabilityCertificateApplicationEntity.getNationality().toString():null),"nationality", disabilityCertificateApplicationEntity.getNationality ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getMaritalStatus ()!=null && !"".equals(disabilityCertificateApplicationEntity.getMaritalStatus ().toString())? disabilityCertificateApplicationEntity.getMaritalStatus().toString():null),"marital_status", disabilityCertificateApplicationEntity.getMaritalStatus ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getBirthday ()!=null && !"".equals(disabilityCertificateApplicationEntity.getBirthday ().toString())? disabilityCertificateApplicationEntity.getBirthday().toString():null),"birthday", disabilityCertificateApplicationEntity.getBirthday ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getNativePlace ()!=null && !"".equals(disabilityCertificateApplicationEntity.getNativePlace ().toString())? disabilityCertificateApplicationEntity.getNativePlace().toString():null),"native_place", disabilityCertificateApplicationEntity.getNativePlace ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getEducationDegree ()!=null && !"".equals(disabilityCertificateApplicationEntity.getEducationDegree ().toString())? disabilityCertificateApplicationEntity.getEducationDegree ().toString():null),"education_degree", disabilityCertificateApplicationEntity.getEducationDegree ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getIdCard ()!=null && !"".equals(disabilityCertificateApplicationEntity.getIdCard ().toString())? disabilityCertificateApplicationEntity.getIdCard().toString():null),"id_card", disabilityCertificateApplicationEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getNativeZhen ()!=null && !"".equals(disabilityCertificateApplicationEntity.getNativeZhen ().toString())? disabilityCertificateApplicationEntity.getNativeZhen().toString():null),"native_zhen", disabilityCertificateApplicationEntity.getNativeZhen ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getNativeCun ()!=null && !"".equals(disabilityCertificateApplicationEntity.getNativeCun ().toString())? disabilityCertificateApplicationEntity.getNativeCun().toString():null),"native_cun", disabilityCertificateApplicationEntity.getNativeCun ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getNativeAddress ()!=null && !"".equals(disabilityCertificateApplicationEntity.getNativeAddress ().toString())? disabilityCertificateApplicationEntity.getNativeAddress().toString():null),"native_address", disabilityCertificateApplicationEntity.getNativeAddress ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getPresentZhen ()!=null && !"".equals(disabilityCertificateApplicationEntity.getPresentZhen ().toString())? disabilityCertificateApplicationEntity.getPresentZhen().toString():null),"present_zhen", disabilityCertificateApplicationEntity.getPresentZhen ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getPresentCun ()!=null && !"".equals(disabilityCertificateApplicationEntity.getPresentCun ().toString())? disabilityCertificateApplicationEntity.getPresentCun ().toString():null),"present_cun", disabilityCertificateApplicationEntity.getPresentCun ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getPresentAddress ()!=null && !"".equals(disabilityCertificateApplicationEntity.getPresentAddress ().toString())? disabilityCertificateApplicationEntity.getPresentAddress ().toString():null),"present_address", disabilityCertificateApplicationEntity.getPresentAddress ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getPostcode ()!=null && !"".equals(disabilityCertificateApplicationEntity.getPostcode ().toString())? disabilityCertificateApplicationEntity.getPostcode ().toString():null),"postcode", disabilityCertificateApplicationEntity.getPostcode ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getMobilePhone ()!=null && !"".equals(disabilityCertificateApplicationEntity.getMobilePhone ().toString())? disabilityCertificateApplicationEntity.getMobilePhone ().toString():null),"mobile_phone", disabilityCertificateApplicationEntity.getMobilePhone ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getGuardianName ()!=null && !"".equals(disabilityCertificateApplicationEntity.getGuardianName ().toString())? disabilityCertificateApplicationEntity.getGuardianName ().toString():null),"guardian_name", disabilityCertificateApplicationEntity.getGuardianName ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getGuardianPhone ()!=null && !"".equals(disabilityCertificateApplicationEntity.getGuardianPhone ().toString())? disabilityCertificateApplicationEntity.getGuardianPhone ().toString():null),"guardian_phone", disabilityCertificateApplicationEntity.getGuardianPhone ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getGuardianIdcard ()!=null && !"".equals(disabilityCertificateApplicationEntity.getGuardianIdcard ().toString())? disabilityCertificateApplicationEntity.getGuardianIdcard ().toString():null),"guardian_idcard", disabilityCertificateApplicationEntity.getGuardianIdcard ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getGuardianRelation ()!=null && !"".equals(disabilityCertificateApplicationEntity.getGuardianRelation ().toString())? disabilityCertificateApplicationEntity.getGuardianRelation ().toString():null),"guardian_relation", disabilityCertificateApplicationEntity.getGuardianRelation ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getPhoto ()!=null && !"".equals(disabilityCertificateApplicationEntity.getPhoto ().toString())? disabilityCertificateApplicationEntity.getPhoto ().toString():null),"photo", disabilityCertificateApplicationEntity.getPhoto ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getApplicationType ()!=null && !"".equals(disabilityCertificateApplicationEntity.getApplicationType ().toString())? disabilityCertificateApplicationEntity.getApplicationType ().toString():null),"application_type", disabilityCertificateApplicationEntity.getApplicationType ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getStatus ()!=null && !"".equals(disabilityCertificateApplicationEntity.getStatus ().toString())? disabilityCertificateApplicationEntity.getStatus ().toString():null),"status",disabilityCertificateApplicationEntity.getStatus ()) //(int)(Float.parseFloat(disabilityCertificateApplicationEntity.getStatus ()))
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getDisabilityType ()!=null && !"".equals(disabilityCertificateApplicationEntity.getDisabilityType ().toString())? disabilityCertificateApplicationEntity.getDisabilityType ().toString():null),"disability_type",disabilityCertificateApplicationEntity.getDisabilityType ()) //(int)(Float.parseFloat(disabilityCertificateApplicationEntity.getStatus ()))
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getCreateTime ()!=null && !"".equals(disabilityCertificateApplicationEntity.getCreateTime ().toString())? disabilityCertificateApplicationEntity.getCreateTime ().toString():null),"create_time", disabilityCertificateApplicationEntity.getCreateTime ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getApplicationTime ()!=null && !"".equals(disabilityCertificateApplicationEntity.getApplicationTime ().toString())? disabilityCertificateApplicationEntity.getApplicationTime ().toString():null),"application_time", disabilityCertificateApplicationEntity.getApplicationTime ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getCreateId ()!=null && !"".equals(disabilityCertificateApplicationEntity.getCreateId ().toString())? disabilityCertificateApplicationEntity.getCreateId ().toString():null),"create_id", disabilityCertificateApplicationEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(disabilityCertificateApplicationEntity.getSignStatus ()!=null && !"".equals(disabilityCertificateApplicationEntity.getSignStatus ().toString())? disabilityCertificateApplicationEntity.getSignStatus ().toString():null),"sign_status", disabilityCertificateApplicationEntity.getSignStatus ())
            .orderByDesc("create_time")
        );
        page.getRecords().forEach( item -> {
            if ("1".equals(item.getSignStatus())) {
                item.setSignStatus("申请人待手签");
            } else if ("2".equals(item.getSignStatus())) {
                item.setSignStatus("申请人已手签");
            } else if ("3".equals(item.getSignStatus())) {
                item.setSignStatus("镇街道待手签");
            } else if ("4".equals(item.getSignStatus())) {
                item.setSignStatus("镇街道已手签");
            } else if ("5".equals(item.getSignStatus())) {
                item.setSignStatus("区残联未手签");
            } else if ("6".equals(item.getSignStatus())) {
                item.setSignStatus("区残联已手签");
            }
            if ("1".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("镇街道待电子公章");
            } else if ("2".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("镇街道已电子公章");
            } else if ("3".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("民政待电子公章");
            } else if ("4".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("已完成");
            } else {
                item.setSignatureStatus("无");
            }
            Map<String, Object> params_tmp = new HashMap<>();
            params_tmp.put("idCard",item.getIdCard());
            params_tmp.put("disabilityCategory",item.getDisabilityType());
            String orderNum = disabilityCertificateApplicationDao.queryOrderNum(params_tmp);
            System.out.println(orderNum);
            if (orderNum != null && !"".equals(orderNum) && !"0".equals(orderNum)){
                item.setOrderNum(disabilityCertificateApplicationDao.queryOrderNum(params_tmp));
            }else {
                item.setOrderNum("0");
            }

        });
        // 根据orderNum排序
        List<DisabilityCertificateApplicationEntity> result_list = page.getRecords().stream().sorted((h1,h2)->h1.getOrderNum().compareTo(h2.getOrderNum())).collect(Collectors.toList());
        page.setRecords(result_list);
        return new PageUtils(page);
    }
    @Override
    public List<DisabilityCertificateApplicationEntity> queryExportData(Map<String, Object> params) {
            return disabilityCertificateApplicationDao.queryExportData(params);
    }

    @Override
    public List<DisabilityCertificateApplicationEntity> listDouble(Map<String, Object> params){
        return disabilityCertificateApplicationDao.listDouble(params);
    }

    @Override
    public List<DisabilityCertificateApplicationZCLEntity> queryExportDataFz(Map<String, Object> params) {
            return disabilityCertificateApplicationDao.queryExportDataFz(params);
    }
@Override
    public List<DisabilityCertificateApplicationZCLEntity> queryExportDataZjdFz(Map<String, Object> params) {
            return disabilityCertificateApplicationDao.exportDataZjdFz(params);
    }

    @Override
    public List<DisabilityCertificateApplicationEntity> queryExportData1(Map<String, Object> params) {
        return disabilityCertificateApplicationDao.queryExportData1(params);
    }

    @Override
    public DisabilityCertificateApplicationEntity getByIDCard(String idCard) {
        return disabilityCertificateApplicationDao.getByIDCard(idCard);
    }



    @Override
    public DisabilityCertificateApplicationEntity getByIDCardN(String idCard) {
        return disabilityCertificateApplicationDao.getByIDCardN(idCard);
    }


    @Override
    public DisabilityCertificateApplicationEntity getByMap(Map<String, Object> params) {
        return disabilityCertificateApplicationDao.getByMap(params);
    }
    @Override
    public DisabilityCertificateApplicationEntity getMapByMap(Map<String, Object> params) {
        return disabilityCertificateApplicationDao.getMapByMap(params);
    }

    @Override
    public List<DisabilityCertificateApplicationEntity> getListByMap(Map<String, Object> params) {
        return disabilityCertificateApplicationDao.getListByMap(params);
    }

    @Override
    public void updateByMap(Map<String, Object> params) {
        disabilityCertificateApplicationDao.updateByMap(params);
    }

    @Override
    public void updateStatusById(DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity) {

    }

    @Override
    @Transactional(rollbackFor=Exception.class)
    public void updateStatusById(DisabilityCertificateApplicationEntity entity_tmp,boolean isPingCan) {
        //System.out.println("------------1----2--"+entity_tmp);
        disabilityCertificateApplicationDao.updateStatusById(entity_tmp);

        DisabilityCertificateApplicationEntity entity = disabilityCertificateApplicationDao.selectById(entity_tmp.getId());
        // 设置状态说明为null 如果不是退回，那就置空
        if (!"4".equals(entity_tmp.getStatus())){
            entity.setStatusOptions(null);
        }
        disabilityCertificateApplicationDao.updateStatusOptions(entity);

        /*// 插入残疾人汇总表
        Map<String, Object> params = new HashMap<>();
        params.put("id_card",entity.getIdCard());
        List<DisabilityCertificateApplicationEntity> applyLists = (List<DisabilityCertificateApplicationEntity>) disabilityCertificateApplicationService.listByMap(params);
        boolean isDcDisable = false;

        Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key", "sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        if (applyLists.size()>1){
            isDcDisable = true;
            applyLists = applyLists.stream().sorted((a,b)->b.getDisabilityType().compareTo(a.getDisabilityType())).collect(Collectors.toList());
            entity.setDisabilityDegree(applyLists.get(0).getDisabilityDegree());
            entity.setDisabilityCategory(applyLists.get(0).getDisabilityCategory());
            entity.setDisabilityType(applyLists.get(0).getDisabilityType());

            String disabilityCategoryName= null;
            for (DisabilityCertificateApplicationEntity item: applyLists){
                SysDictEntity disabilityCategory_sysDictEntity = sys_dict_all_list.stream().filter(
                        iii -> iii.getCode().equals("cjlb_0000") && iii.getValue().equals(
                                item.getDisabilityType())).findAny().orElse(null);
                if (disabilityCategory_sysDictEntity != null && disabilityCategoryName == null) {
                    disabilityCategoryName = disabilityCategory_sysDictEntity.getLabel();
                } else if (disabilityCategory_sysDictEntity != null && disabilityCategoryName != null) {
                    disabilityCategoryName = disabilityCategoryName + "、"+disabilityCategory_sysDictEntity.getLabel();
                }
            }
            entity.setDisabilityCategoryName("多重("+disabilityCategoryName+")");

        }else {
            SysDictEntity disabilityCategory_sysDictEntity = sys_dict_all_list.stream().filter(
                    iii -> iii.getCode().equals("cjlb_0000") && iii.getValue().equals(
                            entity.getDisabilityType())).findAny().orElse(null);
            if (disabilityCategory_sysDictEntity != null) {
                entity.setDisabilityCategoryName(disabilityCategory_sysDictEntity.getLabel());
            } else {
                entity.setDisabilityCategoryName(null);
            }
        }
        // params.put("disability_category",entity.getDisabilityType());
        List<DataDisabilityCertificateEntity> list = (List<DataDisabilityCertificateEntity>) dataDisabilityCertificateService.listByMap(params);
        if (list.size() !=0 ){
            DataDisabilityCertificateEntity dataDisabilityCertificate = list.get(0);
            dataDisabilityCertificate.setName(entity.getName());
            dataDisabilityCertificate.setCreateTime(new Date());
            dataDisabilityCertificate.setIdCard(entity.getIdCard());
            dataDisabilityCertificate.setDisableId(sysDictService.generaterDisableID(isDcDisable,entity.getIdCard(),entity.getDisabilityCategoryName(),entity.getDisabilityDegree()));
            dataDisabilityCertificate.setSex(entity.getSex());
            dataDisabilityCertificate.setDisabilityCategory(entity.getDisabilityType());
            dataDisabilityCertificate.setDisabilityCategoryName(entity.getDisabilityCategoryName());
            dataDisabilityCertificate.setDisabilityDegree(entity.getDisabilityDegree());
            dataDisabilityCertificate.setGuardianName(entity.getName());
            dataDisabilityCertificate.setGuardianPhone(entity.getGuardianPhone());
            dataDisabilityCertificate.setCreateTime(new Date());
            dataDisabilityCertificate.setCreateId(entity.getCreateId());
            dataDisabilityCertificateService.updateById(dataDisabilityCertificate);
        }else {
            // 生成残疾证号
            DataDisabilityCertificateEntity dataDisabilityCertificate = new DataDisabilityCertificateEntity();
            dataDisabilityCertificate.setName(entity.getName());
            dataDisabilityCertificate.setCreateTime(new Date());
            dataDisabilityCertificate.setIdCard(entity.getIdCard());
            dataDisabilityCertificate.setDisableId(sysDictService.generaterDisableID(isDcDisable,entity.getIdCard(),entity.getDisabilityCategoryName(),entity.getDisabilityDegree()));
            dataDisabilityCertificate.setSex(entity.getSex());
            dataDisabilityCertificate.setDisabilityCategory(entity.getDisabilityCategory());
            dataDisabilityCertificate.setDisabilityCategoryName(entity.getDisabilityCategoryName());
            dataDisabilityCertificate.setDisabilityDegree(entity.getDisabilityDegree());
            dataDisabilityCertificate.setGuardianName(entity.getName());
            dataDisabilityCertificate.setGuardianPhone(entity.getGuardianPhone());
            dataDisabilityCertificate.setCreateTime(new Date());
            dataDisabilityCertificate.setCreateId(entity.getCreateId());
            dataDisabilityCertificateService.save(dataDisabilityCertificate);
        }*/
    }

    @Override
    public void updateSignStatusById(DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity){
        disabilityCertificateApplicationDao.updateSignStatusById(disabilityCertificateApplicationEntity);
    }

@Override
    public void updateSignatureStatusById(DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity){
        disabilityCertificateApplicationDao.updateSignatureStatusById(disabilityCertificateApplicationEntity);
    }


    @Override
    @Transactional(rollbackFor=Exception.class)
    public boolean save(DisabilityCertificateApplicationEntity disabilityCertificateApplication) {
        System.out.println(gson.toJson(disabilityCertificateApplication));
        disabilityCertificateApplication.setSignatureStatus("1");
        super.save(disabilityCertificateApplication);
        // Gson gson = new Gson();
        System.out.println(disabilityCertificateApplication.getDocList());
        System.out.println(disabilityCertificateApplication.getDocumentList());
        // String str = gson.toJson(disabilityCertificateApplication.getDocumentList());
        List<CjroneCertificateApplyDocEntity> documentList = gson.fromJson(disabilityCertificateApplication.getDocumentList(), new TypeToken<List<CjroneCertificateApplyDocEntity>>() {
        }.getType());
        if(documentList!=null){
            documentList.forEach(item ->{
                System.out.println(new Gson().toJson(item));
                item.setDisabilityAssessmentId(disabilityCertificateApplication.getDisabilityType());
                item.setDisabilityCertificateApplicationId(disabilityCertificateApplication.getId().toString());
                item.setDocumentId(item.getDocumentId());
                cjroneCertificateApplyDocService.save(item);
            });
        }
        /*DisabilityAssessmentCategoryEntity disabilityAssessmentCategoryEntity = disabilityAssessmentCategoryService.getById(disabilityCertificateApplication.getDisabilityType());

        CjroneDisabilityHospitalEntity cjroneDisabilityHospital = gson.fromJson(gson.toJson(disabilityCertificateApplication),CjroneDisabilityHospitalEntity.class);
        cjroneDisabilityHospital.setHospital(disabilityAssessmentCategoryEntity.getHospital());
        cjroneDisabilityHospital.setHospitalId(disabilityAssessmentCategoryEntity.getHospitalId());
        cjroneDisabilityHospital.setDisabilityCategory(disabilityCertificateApplication.getDisabilityType());
        // 生成电子签章pdf 并保存
        String filePath = pdfUtils.pdfCjzApplyToSignature(cjroneDisabilityHospital);
        CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
        cjroneSignature.setCreateDate(new Date());
        cjroneSignature.setCreateId(disabilityCertificateApplication.getCreateId());
        cjroneSignature.setType("残疾证申请");
        cjroneSignature.setTypeId(disabilityCertificateApplication.getId());
        cjroneSignature.setUrl(filePath);
        cjroneSignatureService.save(cjroneSignature);*//*

        // 插入医院评残表
        cjroneDisabilityHospital.setStatus("1");
        Map<String, Object> params_tatal = new HashMap<>();
        params_tatal.put("status","1");
        int order_num = cjroneDisabilityHospitalService.queryTotalByParams(params_tatal);
        cjroneDisabilityHospital.setOrderNum(String.valueOf(order_num+1));
        cjroneDisabilityHospitalService.save(cjroneDisabilityHospital);*/

        return true;
    }
    @Override
    @Transactional(rollbackFor=Exception.class)
    public boolean reApply(DisabilityCertificateApplicationEntity disabilityCertificateApplication) {
        System.out.println(gson.toJson(disabilityCertificateApplication));
        super.save(disabilityCertificateApplication);
        disabilityCertificateApplication.getDocumentDocList().forEach(item ->{
            System.out.println(new Gson().toJson(item));
            item.setDisabilityCertificateApplicationId(disabilityCertificateApplication.getId().toString());
            cjroneCertificateApplyDocService.save(item);
        });
        if (disabilityCertificateApplication.getCjroneSignatureEntity() != null){
            disabilityCertificateApplication.getCjroneSignatureEntity().setTypeId(disabilityCertificateApplication.getId());
            cjroneSignatureService.save(disabilityCertificateApplication.getCjroneSignatureEntity());
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor=Exception.class)
    public void faZheng(DisabilityCertificateApplicationEntity disabilityCertificateApplication) {
        super.updateById(disabilityCertificateApplication);
        System.out.println("fazhengdisabilityCertificateApplication"+new Gson().toJson(disabilityCertificateApplication));
        disabilityCertificateApplicationDao.faZheng(disabilityCertificateApplication);
    }



    @Override
    @Transactional(rollbackFor=Exception.class)
    public void editById(DisabilityCertificateApplicationEntity entity) {
        // super.updateById(entity);
        List<CjroneCertificateApplyDocEntity> documentList = gson.fromJson(entity.getDocumentList(), new TypeToken<List<CjroneCertificateApplyDocEntity>>() {
        }.getType());
        Map<String, Object> params = new HashMap<>();
        params.put("disabilityCertificateApplicationId",entity.getId());
        cjroneCertificateApplyDocService.deleteByApplicationId(params);
        if (documentList != null && documentList.size()>0) {
            documentList.forEach(item -> {
                System.out.println(new Gson().toJson(item));
                item.setDisabilityAssessmentId(entity.getDisabilityType());
                item.setDisabilityCertificateApplicationId(entity.getId().toString());
                item.setDocumentId(item.getDocumentId());
                cjroneCertificateApplyDocService.save(item);
            });
        }
    }

    @Override
    @Transactional(rollbackFor=Exception.class)
    public void editNewById(DisabilityCertificateApplicationEntity disabilityCertificateApplication) {
        System.out.println(new Gson().toJson(disabilityCertificateApplication));
        // 查询残疾证申请表数据
        DisabilityCertificateApplicationEntity entity = disabilityCertificateApplicationDao.selectById(disabilityCertificateApplication.getId());
        // 更新更换过的残疾类别
        if (!disabilityCertificateApplication.getDisabilityType().equals(entity.getDisabilityType())){
            entity.setDisabilityType(disabilityCertificateApplication.getDisabilityType());
            entity.setDisabilityTypeName(disabilityCertificateApplication.getDisabilityTypeName());
            disabilityCertificateApplicationDao.updateById(entity);
        }
        // 插入附件
        List<CjroneCertificateApplyDocEntity> documentList = gson.fromJson(disabilityCertificateApplication.getDocumentList(), new TypeToken<List<CjroneCertificateApplyDocEntity>>() {
        }.getType());
        Map<String, Object> params = new HashMap<>();
        params.put("disabilityCertificateApplicationId",entity.getId());
        System.out.println(documentList.size()+"qqq");
        if (documentList != null && documentList.size()>0) {
            cjroneCertificateApplyDocService.deleteByApplicationId(params);
            documentList.forEach(item -> {
                System.out.println(new Gson().toJson(item));
                item.setDisabilityAssessmentId(entity.getDisabilityType());
                item.setDisabilityCertificateApplicationId(entity.getId().toString());
                item.setDocumentId(item.getDocumentId());
                cjroneCertificateApplyDocService.save(item);
            });
        }
        // 更新残疾类别相关附件
        Map<String, Object> params_tmp = new HashMap<>();
        params_tmp.put("disabilityCertificateApplicationId",entity.getId());
        List<CjroneDocumentEntity> documentListFinal = cjroneCertificateApplyDocService.listDocumentByMap(params_tmp);
        documentListFinal.forEach(item ->{
            item.setDisabilityAssessmentId(Integer.parseInt(entity.getDisabilityType()));
            if (item.getDisabilityAssessmentDetailName()!=null && !"jhrFj".equals(item.getDisabilityAssessmentDetailName())){
                item.setDisabilityAssessmentDetailName(entity.getDisabilityTypeName());
            }
        });
        if (documentList.size()>0){
            // 更新附件表
            cjroneDocumentService.updateBatchById(documentListFinal);
        }
    }

    @Override
    public void rePinCan(DisabilityCertificateApplicationEntity entity) {
        super.updateById(entity);
    }

 @Override
    @Transactional(rollbackFor=Exception.class)
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        idList.forEach(item ->{
            DisabilityCertificateApplicationEntity applicationEntity = super.getById(item);
            Map<String, Object> params = new HashMap<>();
            params.put("id_card",applicationEntity.getIdCard());
            params.put("disability_category",applicationEntity.getDisabilityType());


            super.removeById(item);
        });
        return true;
    }

    @Override
    @Transactional(rollbackFor=Exception.class)
    public boolean updateById(DisabilityCertificateApplicationEntity entity) {
        super.updateById(entity);
        List<CjroneCertificateApplyDocEntity> documentList = gson.fromJson(entity.getDocumentList(), new TypeToken<List<CjroneCertificateApplyDocEntity>>() {
        }.getType());
        Map<String, Object> params = new HashMap<>();
        params.put("disabilityCertificateApplicationId",entity.getId());
        if (documentList != null && documentList.size()>0) {
            cjroneCertificateApplyDocService.deleteByApplicationId(params);
            documentList.forEach(item -> {
                System.out.println(new Gson().toJson(item));
                item.setDisabilityAssessmentId(entity.getDisabilityType());
                item.setDisabilityCertificateApplicationId(entity.getId().toString());
                item.setDocumentId(item.getDocumentId());
                cjroneCertificateApplyDocService.save(item);
            });
        }
        /*DisabilityAssessmentCategoryEntity disabilityAssessmentCategoryEntity = disabilityAssessmentCategoryService.getById(entity.getDisabilityType());

        Map<String, Object> params_h = new HashMap<>();
        params_h.put("id_card",entity.getIdCard());
        params_h.put("disability_category",entity.getDisabilityType());
        List<CjroneDisabilityHospitalEntity> cjroneDisabilityHospital_alive = (List<CjroneDisabilityHospitalEntity>) cjroneDisabilityHospitalService.listByMap(params_h);
        if (cjroneDisabilityHospital_alive.size()>0){
            cjroneDisabilityHospital_alive.forEach(item->{
                cjroneDisabilityHospitalService.removeById(item.getId());
            });
        }
        CjroneDisabilityHospitalEntity cjroneDisabilityHospital = gson.fromJson(gson.toJson(entity),CjroneDisabilityHospitalEntity.class);
        cjroneDisabilityHospital.setHospital(disabilityAssessmentCategoryEntity.getHospital());
        cjroneDisabilityHospital.setHospitalId(disabilityAssessmentCategoryEntity.getHospitalId());
        cjroneDisabilityHospital.setDisabilityCategory(entity.getDisabilityType());
        /// 生成电子签章pdf 并保存
        String filePath = pdfUtils.pdfCjzApplyToSignature(cjroneDisabilityHospital);
        CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
        cjroneSignature.setCreateDate(new Date());
        cjroneSignature.setCreateId(disabilityCertificateApplication.getCreateId());
        cjroneSignature.setType("残疾证申请");
        cjroneSignature.setTypeId(disabilityCertificateApplication.getId());
        cjroneSignature.setUrl(filePath);
        cjroneSignatureService.save(cjroneSignature);*//*

        // 插入医院评残表
        cjroneDisabilityHospital.setStatus("1");
        Map<String, Object> params_tatal = new HashMap<>();
        params_tatal.put("status","1");
        int order_num = cjroneDisabilityHospitalService.queryTotalByParams(params_tatal);
        cjroneDisabilityHospital.setOrderNum(String.valueOf(order_num+1));
        cjroneDisabilityHospitalService.save(cjroneDisabilityHospital);*/
        return true;
    }
}
