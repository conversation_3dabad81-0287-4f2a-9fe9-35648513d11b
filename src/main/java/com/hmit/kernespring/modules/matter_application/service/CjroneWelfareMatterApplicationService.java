package com.hmit.kernespring.modules.matter_application.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.matter_application.entity.CjroneWelfareMatterApplicationEntity;

import java.util.List;
import java.util.Map;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-10 09:42:23
 */
public interface CjroneWelfareMatterApplicationService extends IService<CjroneWelfareMatterApplicationEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneWelfareMatterApplicationEntity> queryExportData(Map<String, Object> params);
    Map<String, Object> queryMattersByMap(Map<String, Object> params);
    Map<String, Object> queryMattersIdByMap(Map<String, Object> params);
    void updateStatusById(CjroneWelfareMatterApplicationEntity welfareMatterApplicationEntity);
    void updateSignStatusByMap(CjroneWelfareMatterApplicationEntity cjroneWelfareMatterApplicationEntity);
    void enable(CjroneWelfareMatterApplicationEntity cjroneWelfareMatterApplicationEntity);
    void saveZljf(CjroneWelfareMatterApplicationEntity cjroneWelfareMatterApplicationEntity);
    List<Map<String, Object>> queryFamilyEconoCondition(Map<String, Object> params);
}



