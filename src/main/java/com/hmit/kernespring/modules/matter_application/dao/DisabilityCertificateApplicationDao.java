package com.hmit.kernespring.modules.matter_application.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hmit.kernespring.modules.data_management.entity.DisabilityCertificateApplicationZCLEntity;
import com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 残疾证申请表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-04 16:03:42
 */
@Mapper
public interface DisabilityCertificateApplicationDao extends BaseMapper<DisabilityCertificateApplicationEntity> {
    List<DisabilityCertificateApplicationEntity> queryExportData(Map<String, Object> params);
    List<DisabilityCertificateApplicationZCLEntity> queryExportDataFz(Map<String, Object> params);
    List<DisabilityCertificateApplicationZCLEntity> exportDataZjdFz(Map<String, Object> params);
    DisabilityCertificateApplicationEntity getByIDCard(String idCard);
    DisabilityCertificateApplicationEntity getByIDCardN(String idCard);
    DisabilityCertificateApplicationEntity getByMap(Map<String, Object> params);
    DisabilityCertificateApplicationEntity getMapByMap(Map<String, Object> params);
    void updateByMap(Map<String, Object> params);
    void updateStatusById(DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity);
    void updateStatusOptions(DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity);
    void updateSignStatusById(DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity);
    void updateSignatureStatusById(DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity);

    List<DisabilityCertificateApplicationEntity> getListByMap(Map<String, Object> params);

    List<DisabilityCertificateApplicationEntity> queryExportData1(Map<String, Object> params);
    String queryOrderNum(Map<String, Object> params);

    List<DisabilityCertificateApplicationEntity> listDouble(Map<String, Object> params);
    void faZheng(DisabilityCertificateApplicationEntity disabilityCertificateApplication);
}
