package com.hmit.kernespring.modules.cjrone.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone.entity.CjroneCertificateApplyDocEntity;
import com.hmit.kernespring.modules.cjrone.entity.CjroneDocumentEntity;

import java.util.Map;

import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-09 14:42:18
 */
public interface CjroneCertificateApplyDocService extends IService<CjroneCertificateApplyDocEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneCertificateApplyDocEntity> queryExportData(Map<String, Object> params);
    List<CjroneCertificateApplyDocEntity> queryApplyDocByMap(Map<String, Object> params);
    List<CjroneDocumentEntity> listDocumentByMap(Map<String, Object> params);
    void deleteByApplicationId(Map<String, Object> params);
}

