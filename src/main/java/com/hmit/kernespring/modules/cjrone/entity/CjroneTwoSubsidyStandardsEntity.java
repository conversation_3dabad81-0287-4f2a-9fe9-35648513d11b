package com.hmit.kernespring.modules.cjrone.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 两项补贴发放标准表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-06-06 11:12:08
 */
@Data
@TableName("cjrone_two_subsidy_standards")
public class CjroneTwoSubsidyStandardsEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
@TableId
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 补贴类型
	 */
	@Excel(name = "补贴类型", height = 20, width = 30, isImportField = "true_st")
private String type;
	/**
	 * 残疾类型
	 */
	@Excel(name = "残疾类型", height = 20, width = 30, isImportField = "true_st")
private String disabilityType;
	/**
	 * 残疾等级
	 */
	@Excel(name = "残疾等级", height = 20, width = 30, isImportField = "true_st")
private Integer disabilityDegree;
	/**
	 * 是否集中托养
	 */
	@Excel(name = "是否集中托养", height = 20, width = 30, isImportField = "true_st")
private Integer isConcentratedCare;
	/**
	 * 补贴金额
	 */
	@Excel(name = "补贴金额", height = 20, width = 30, isImportField = "true_st")
private Double money;

}
