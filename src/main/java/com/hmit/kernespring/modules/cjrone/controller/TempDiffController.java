package com.hmit.kernespring.modules.cjrone.controller;

import java.io.File;
import java.io.IOException;
import java.util.*;

import java.io.OutputStream;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import org.apache.poi.ss.usermodel.Workbook;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hmit.kernespring.modules.cjrone.entity.TempDiffEntity;
import com.hmit.kernespring.modules.cjrone.service.TempDiffService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-04-01 16:28:09
 */
@RestController
@RequestMapping("cjrone/tempdiff")
public class TempDiffController {
    @Autowired
    private TempDiffService tempDiffService;
    @Autowired
    private SysOssService sysOssService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("cjrone:tempdiff:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = tempDiffService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("cjrone:tempdiff:info")
    public R info(@PathVariable("id") Integer id){
		TempDiffEntity tempDiff = tempDiffService.getById(id);

        return R.ok().put("tempDiff", tempDiff);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("cjrone:tempdiff:save")
    public R save(@RequestBody TempDiffEntity tempDiff){
		tempDiffService.save(tempDiff);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("cjrone:tempdiff:update")
    public R update(@RequestBody TempDiffEntity tempDiff){
		tempDiffService.updateById(tempDiff);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("cjrone:tempdiff:delete")
    public R delete(@RequestBody Integer[] ids){
		tempDiffService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }

    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    @RequiresPermissions("cjrone:tempdiff:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<TempDiffEntity> tempDiffEntityList = tempDiffService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("", null, "");
        Workbook workbook = ExcelExportUtil.exportExcel(params, TempDiffEntity.class, tempDiffEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

}
