package com.hmit.kernespring.modules.cjrone.dao;

import com.hmit.kernespring.modules.cjrone.entity.CjroneNursingSubsidyEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 护理补贴申请
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-10 14:28:57
 */
@Mapper
public interface CjroneNursingSubsidyDao extends BaseMapper<CjroneNursingSubsidyEntity> {
    List<CjroneNursingSubsidyEntity> queryExportData(Map<String, Object> params);
    void deleteAllData(Map<String, Object> params);
}
