package com.hmit.kernespring.modules.cjrone.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.cjrone.entity.CjroneTwoSubsidyStandardsEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneTwoSubsidyStandardsService;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;

/**
 * 两项补贴发放标准表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-06-06 11:12:08
 */
@RestController
@RequestMapping("cjrone/cjronetwosubsidystandards")
public class CjroneTwoSubsidyStandardsController {
    @Autowired
    private CjroneTwoSubsidyStandardsService cjroneTwoSubsidyStandardsService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("cjrone:cjronetwosubsidystandards:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = cjroneTwoSubsidyStandardsService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("cjrone:cjronetwosubsidystandards:info")
    public R info(@PathVariable("id") Integer id){
		CjroneTwoSubsidyStandardsEntity cjroneTwoSubsidyStandards = cjroneTwoSubsidyStandardsService.getById(id);

        return R.ok().put("cjroneTwoSubsidyStandards", cjroneTwoSubsidyStandards);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("cjrone:cjronetwosubsidystandards:save")
    public R save(@RequestBody CjroneTwoSubsidyStandardsEntity cjroneTwoSubsidyStandards){
		cjroneTwoSubsidyStandardsService.save(cjroneTwoSubsidyStandards);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("cjrone:cjronetwosubsidystandards:update")
    public R update(@RequestBody CjroneTwoSubsidyStandardsEntity cjroneTwoSubsidyStandards){
		cjroneTwoSubsidyStandardsService.updateById(cjroneTwoSubsidyStandards);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("cjrone:cjronetwosubsidystandards:delete")
    public R delete(@RequestBody Integer[] ids){
		cjroneTwoSubsidyStandardsService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    @RequiresPermissions("cjrone:cjronetwosubsidystandards:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<CjroneTwoSubsidyStandardsEntity> cjroneTwoSubsidyStandardsList = new ArrayList<>();
        list.forEach(item ->{
                    item.put("id",item.get(""));
                    item.put("type",item.get("补贴类型"));
                    item.put("disabilityType",item.get("残疾类型"));
                    item.put("disabilityDegree",item.get("残疾等级"));
                    item.put("isConcentratedCare",item.get("是否集中托养"));
                    item.put("money",item.get("补贴金额"));
                    cjroneTwoSubsidyStandardsList.add(new Gson().fromJson(new Gson().toJson(item), CjroneTwoSubsidyStandardsEntity.class));
        });
        // 保存到数据库
        cjroneTwoSubsidyStandardsService.saveBatch(cjroneTwoSubsidyStandardsList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    @RequiresPermissions("cjrone:cjronetwosubsidystandards:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<CjroneTwoSubsidyStandardsEntity> cjroneTwoSubsidyStandardsEntityList = cjroneTwoSubsidyStandardsService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("两项补贴发放标准表", null, "两项补贴发放标准表");
        Workbook workbook = ExcelExportUtil.exportExcel(params, CjroneTwoSubsidyStandardsEntity.class, cjroneTwoSubsidyStandardsEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "两项补贴发放标准表" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

}
