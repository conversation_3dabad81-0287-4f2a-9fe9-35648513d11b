package com.hmit.kernespring.modules.cjrone.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.app.service.APIService;
import com.hmit.kernespring.modules.cjrone.entity.CjroneResidentPensionInsuranceEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneResidentPensionInsuranceService;
import com.hmit.kernespring.modules.data_management.entity.ApiFdDbrEntity;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import com.hmit.kernespring.modules.sys.controller.AbstractController;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;

/**
 * 城乡居民养老保险补贴汇总表---个体工商户
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-10 14:28:57
 */
@RestController
@RequestMapping("cjrone/cjroneresidentpensioninsurance")
public class CjroneResidentPensionInsuranceController extends AbstractController {
    @Autowired
    private CjroneResidentPensionInsuranceService cjroneResidentPensionInsuranceService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;
    @Autowired
    private APIService apiService;


    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("cjrone:cjroneresidentpensioninsurance:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = cjroneResidentPensionInsuranceService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("cjrone:cjroneresidentpensioninsurance:info")
    public R info(@PathVariable("id") Integer id){
		CjroneResidentPensionInsuranceEntity cjroneResidentPensionInsurance = cjroneResidentPensionInsuranceService.getById(id);

        return R.ok().put("cjroneResidentPensionInsurance", cjroneResidentPensionInsurance);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("cjrone:cjroneresidentpensioninsurance:save")
    public R save(@RequestBody CjroneResidentPensionInsuranceEntity cjroneResidentPensionInsurance){
        cjroneResidentPensionInsurance.setCreateTime(new Date().toString());
        cjroneResidentPensionInsurance.setCreateId(getUserId());
        cjroneResidentPensionInsurance.setStatus("2");
        //验证是否个体工商户
        Map<String, Object> doFdDbInfo = apiService.queryFDDBRInfo(cjroneResidentPensionInsurance.getIdCard(), null);
        String is_fdDbr = "否";
        if ("00".equals(doFdDbInfo.get("code").toString())) {
            System.out.println("doFdDbInfo: "+doFdDbInfo);
            if (doFdDbInfo.get("datas") != null) {
                List<ApiFdDbrEntity> list1 = new Gson().fromJson(doFdDbInfo.get("datas").toString(), new TypeToken<List<ApiFdDbrEntity>>() {
                }.getType());
                if (list1.size() != 0) {
                    is_fdDbr = "是";
                }
            }
        }else {
            is_fdDbr = "异常";
        }
        cjroneResidentPensionInsurance.setIsResident(is_fdDbr);  //保存验证的结果

		cjroneResidentPensionInsuranceService.save(cjroneResidentPensionInsurance);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("cjrone:cjroneresidentpensioninsurance:update")
    public R update(@RequestBody CjroneResidentPensionInsuranceEntity cjroneResidentPensionInsurance){

        //验证是否个体工商户
        Map<String, Object> doFdDbInfo = apiService.queryFDDBRInfo(cjroneResidentPensionInsurance.getIdCard(), null);
        String is_fdDbr = "否";
        if ("00".equals(doFdDbInfo.get("code").toString())) {
            System.out.println("doFdDbInfo: "+doFdDbInfo);
            if (doFdDbInfo.get("datas") != null) {
                List<ApiFdDbrEntity> list1 = new Gson().fromJson(doFdDbInfo.get("datas").toString(), new TypeToken<List<ApiFdDbrEntity>>() {
                }.getType());
                if (list1.size() != 0) {
                    is_fdDbr = "是";
                }
            }
        }else {
            is_fdDbr = "异常";
        }
        cjroneResidentPensionInsurance.setIsResident(is_fdDbr);  //保存验证的结果

		cjroneResidentPensionInsuranceService.updateById(cjroneResidentPensionInsurance);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("cjrone:cjroneresidentpensioninsurance:delete")
    public R delete(@RequestBody Integer[] ids){
		cjroneResidentPensionInsuranceService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    // @RequiresPermissions("cjrone:cjroneresidentpensioninsurance:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<CjroneResidentPensionInsuranceEntity> cjroneResidentPensionInsuranceList = new ArrayList<>();
        list.forEach(item ->{
                    item.put("id",item.get(""));
                    item.put("name",item.get("姓名"));
                    item.put("idCard",item.get("身份证号"));
                    item.put("insureMonth",item.get("参保月数"));
                    item.put("subsidy",item.get("补贴金额"));

                    cjroneResidentPensionInsuranceList.add(new Gson().fromJson(new Gson().toJson(item), CjroneResidentPensionInsuranceEntity.class));
        });
        //开始验证是否个体工商户
        cjroneResidentPensionInsuranceList.forEach(item -> {
            Map<String, Object> doFdDbInfo = apiService.queryFDDBRInfo(item.getIdCard(), null);
            String is_fdDbr = "否";
            if ("00".equals(doFdDbInfo.get("code").toString())) {
                System.out.println("doFdDbInfo: "+doFdDbInfo);
                if (doFdDbInfo.get("datas") != null) {
                    List<ApiFdDbrEntity> list1 = new Gson().fromJson(doFdDbInfo.get("datas").toString(), new TypeToken<List<ApiFdDbrEntity>>() {
                    }.getType());
                    if (list1.size() != 0) {
                        is_fdDbr = "是";
                    }
                }
            }else {
                is_fdDbr = "异常";
            }
            item.setIsResident(is_fdDbr);  //保存验证的结果
        });

        // 保存到数据库
        cjroneResidentPensionInsuranceService.saveBatch(cjroneResidentPensionInsuranceList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    // @RequiresPermissions("cjrone:cjroneresidentpensioninsurance:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<CjroneResidentPensionInsuranceEntity> cjroneResidentPensionInsuranceEntityList = cjroneResidentPensionInsuranceService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("个体工商户保险补贴汇总表", null, "个体工商户保险补贴汇总表");
        Workbook workbook = ExcelExportUtil.exportExcel(params, CjroneResidentPensionInsuranceEntity.class, cjroneResidentPensionInsuranceEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "个体工商户保险补贴汇总表" ;
        System.out.println("export fileName --->"+fileName);
        response.setHeader("Content-disposition", "attachment;filename="+new String("hcashdsa".getBytes(),"iso-8859-1")+".xls");
        System.out.println("Content-disposition-->"+response.getHeader("Content-disposition"));
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

}
