package com.hmit.kernespring.modules.cjrone.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.cjrone.entity.CjroneMessageHistoryEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneMessageHistoryService;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-06-28 14:53:50
 */
@RestController
@RequestMapping("cjrone/cjronemessagehistory")
public class CjroneMessageHistoryController {
    @Autowired
    private CjroneMessageHistoryService cjroneMessageHistoryService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    // @RequiresPermissions("cjrone:cjronemessagehistory:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = cjroneMessageHistoryService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("cjrone:cjronemessagehistory:info")
    public R info(@PathVariable("id") Integer id){
		CjroneMessageHistoryEntity cjroneMessageHistory = cjroneMessageHistoryService.getById(id);

        return R.ok().put("cjroneMessageHistory", cjroneMessageHistory);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("cjrone:cjronemessagehistory:save")
    public R save(@RequestBody CjroneMessageHistoryEntity cjroneMessageHistory){
		cjroneMessageHistoryService.save(cjroneMessageHistory);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("cjrone:cjronemessagehistory:update")
    public R update(@RequestBody CjroneMessageHistoryEntity cjroneMessageHistory){
		cjroneMessageHistoryService.updateById(cjroneMessageHistory);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("cjrone:cjronemessagehistory:delete")
    public R delete(@RequestBody Integer[] ids){
		cjroneMessageHistoryService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    @RequiresPermissions("cjrone:cjronemessagehistory:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<CjroneMessageHistoryEntity> cjroneMessageHistoryList = new ArrayList<>();
        list.forEach(item ->{
                    item.put("id",item.get(""));
                    item.put("receiveName",item.get("接收人姓名"));
                    item.put("receiveIdCard",item.get("接收人身份证"));
                    item.put("messageContent",item.get("短信内容"));
                    item.put("matterType",item.get("事项类型"));
                    item.put("sendTime",item.get("发送时间"));
                    cjroneMessageHistoryList.add(new Gson().fromJson(new Gson().toJson(item), CjroneMessageHistoryEntity.class));
        });
        // 保存到数据库
        cjroneMessageHistoryService.saveBatch(cjroneMessageHistoryList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    @RequiresPermissions("cjrone:cjronemessagehistory:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<CjroneMessageHistoryEntity> cjroneMessageHistoryEntityList = cjroneMessageHistoryService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("", null, "");
        Workbook workbook = ExcelExportUtil.exportExcel(params, CjroneMessageHistoryEntity.class, cjroneMessageHistoryEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

}
