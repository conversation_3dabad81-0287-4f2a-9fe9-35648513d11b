package com.hmit.kernespring.modules.cjrone.dao;

import com.hmit.kernespring.modules.cjrone.entity.CjroneEmploymentSubsidyEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 就业创业补助
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-06-24 09:40:30
 */
@Mapper
public interface CjroneEmploymentSubsidyDao extends BaseMapper<CjroneEmploymentSubsidyEntity> {
    List<CjroneEmploymentSubsidyEntity> queryExportData(Map<String, Object> params);
	
}
