package com.hmit.kernespring.modules.cjrone.dao;

import com.hmit.kernespring.modules.cjrone.entity.CjroneTwoSubsidyStandardsEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 两项补贴发放标准表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-06-06 11:12:08
 */
@Mapper
public interface CjroneTwoSubsidyStandardsDao extends BaseMapper<CjroneTwoSubsidyStandardsEntity> {
    List<CjroneTwoSubsidyStandardsEntity> queryExportData(Map<String, Object> params);

    List<CjroneTwoSubsidyStandardsEntity> queryByMap(Map<String, Object> params);
}
