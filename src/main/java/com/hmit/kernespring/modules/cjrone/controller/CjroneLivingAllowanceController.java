package com.hmit.kernespring.modules.cjrone.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.cjrone.entity.CjroneLivingAllowanceEntity;
import com.hmit.kernespring.modules.cjrone.entity.CjroneSignatureEntity;
import com.hmit.kernespring.modules.cjrone.entity.CjroneTwoSubsidyStandardsEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneLivingAllowanceService;
import com.hmit.kernespring.modules.cjrone.service.CjroneSignatureService;
import com.hmit.kernespring.modules.cjrone.service.CjroneTwoSubsidyStandardsService;
import com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity;
import com.hmit.kernespring.modules.matter_application.service.DisabilityCertificateApplicationService;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import com.hmit.kernespring.modules.sys.controller.AbstractController;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.*;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.Files;
import java.util.*;

/**
 * 生活补贴申请
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-10 14:28:57
 */
@RestController
@RequestMapping("cjrone/cjronelivingallowance")
public class CjroneLivingAllowanceController extends AbstractController {
    @Autowired
    private CjroneLivingAllowanceService cjroneLivingAllowanceService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;
    @Autowired
    private CjroneSignatureService cjroneSignatureService;
    @Autowired
    private CjroneTwoSubsidyStandardsService cjroneTwoSubsidyStandardsService;
    @Autowired
    private DisabilityCertificateApplicationService disabilityCertificateApplicationService;


    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("cjrone:cjronelivingallowance:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = cjroneLivingAllowanceService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{name}")
    @RequiresPermissions("cjrone:cjronelivingallowance:info")
    public R info(@PathVariable("name") String name){
		CjroneLivingAllowanceEntity cjroneLivingAllowance = cjroneLivingAllowanceService.getById(name);

        return R.ok().put("cjroneLivingAllowance", cjroneLivingAllowance);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("cjrone:cjronelivingallowance:save")
    public R save(@RequestBody CjroneLivingAllowanceEntity cjroneLivingAllowance){
        cjroneLivingAllowance.setCreateTime(new Date());
        cjroneLivingAllowance.setCreateId(getUserId());
        cjroneLivingAllowance.setStatus("2");
		cjroneLivingAllowanceService.save(cjroneLivingAllowance);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("cjrone:cjronelivingallowance:update")
    public R update(@RequestBody CjroneLivingAllowanceEntity cjroneLivingAllowance){
        CjroneLivingAllowanceEntity entity = cjroneLivingAllowanceService.getById(cjroneLivingAllowance.getId());
        System.out.println(new Gson().toJson(entity));
        if (entity != null){
            if (getUser().getRoleName() != null && getUser().getRoleName().indexOf("区残联") != -1){
                if ("6".equals(entity.getSignStatus())){
                    if (!"3".equals(entity.getSignatureStatus())){
                        return R.error().put("code",100).put("msg","审核未通过，区残联未电子公章！").put("applyId",cjroneLivingAllowance.getId());
                    }else {
                        System.out.println("aaaaaaa");
                    }
                }else {
                    return R.error().put("code",100).put("msg","审核未通过，区残联未手签！").put("applyId",cjroneLivingAllowance.getId());
                }
            }else if (getUser().getRoleName() != null && getUser().getRoleName().indexOf("民政") != -1){
                if ("8".equals(entity.getSignStatus())){
                    if (!"4".equals(entity.getSignatureStatus())){
                        return R.error().put("code",100).put("msg","审核未通过，民政未电子公章！").put("applyId",cjroneLivingAllowance.getId());
                    }else {
                        System.out.println("aaaaaaa");
                    }
                }else {
                    return R.error().put("code",100).put("msg","审核未通过，民政未手签！").put("applyId",cjroneLivingAllowance.getId());
                }
            }
        }
		cjroneLivingAllowanceService.updateById(cjroneLivingAllowance);
        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("cjrone:cjronelivingallowance:delete")
    public R delete(@RequestBody String[] names){
		cjroneLivingAllowanceService.removeByIds(Arrays.asList(names));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    // @RequiresPermissions("cjrone:cjronelivingallowance:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<CjroneLivingAllowanceEntity> cjroneLivingAllowanceList = new ArrayList<>();
        list.forEach(item ->{
                    item.put("name",item.get("残疾人姓名"));
                    item.put("sex",item.get("性别  1 男  2女"));
                    item.put("birthday",item.get("出生日期"));
                    item.put("nativePlace",item.get("籍贯"));
                    item.put("idCard",item.get("身份证号码"));
                    item.put("mobilePhone",item.get("手机号码"));
                    item.put("guardianName",item.get("监护人姓名"));
                    item.put("guardianPhone",item.get("监护人手机"));
                    item.put("disabilityCategory",item.get("残疾类别"));
                    item.put("disabilityDegree",item.get("残疾等级"));
                    item.put("disableId",item.get("残疾证号"));
                    item.put("bankAccount",item.get("银行账户"));
                    item.put("bankName",item.get("开户银行"));
                    item.put("familyEconoCondition",item.get("家庭经济情况"));
                    item.put("applicationDate",item.get("申请日期"));
                    item.put("auditPerson",item.get("审核人"));
                    item.put("auditDate",item.get("审核时间"));
                    item.put("status",item.get("状态"));
                    item.put("createId",item.get("创建人编号"));
                    item.put("createTime",item.get("创建时间"));
                    cjroneLivingAllowanceList.add(new Gson().fromJson(new Gson().toJson(item), CjroneLivingAllowanceEntity.class));
        });
        // 保存到数据库
        cjroneLivingAllowanceService.saveBatch(cjroneLivingAllowanceList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    //@RequiresPermissions("cjrone:cjronelivingallowance:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<CjroneLivingAllowanceEntity> cjroneLivingAllowanceEntityList = cjroneLivingAllowanceService.queryExportData(mapArgs);

        cjroneLivingAllowanceEntityList.forEach(item ->{
           /* //开始处理籍贯数据
            DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity = disabilityCertificateApplicationService.getByIDCard(item.getIdCard());
            if(disabilityCertificateApplicationEntity!=null){
                item.setNativePlace(disabilityCertificateApplicationEntity.getNativeZhenName()+disabilityCertificateApplicationEntity.getNativeCunName());
            }*/

            if ("1".equals(item.getDisabilityCategory())){
                item.setDisabilityCategory("视力");
            }else  if ("2".equals(item.getDisabilityCategory())){
                item.setDisabilityCategory("听力");
            }else  if ("3".equals(item.getDisabilityCategory())){
                item.setDisabilityCategory("智力");
            }else  if ("4".equals(item.getDisabilityCategory())){
                item.setDisabilityCategory("精神");
            }else  if ("5".equals(item.getDisabilityCategory())){
                item.setDisabilityCategory("肢体");
            }else  if ("6".equals(item.getDisabilityCategory())){
                item.setDisabilityCategory("言语");
            }else  if ("7".equals(item.getDisabilityCategory())){
                item.setDisabilityCategory("肢体");
            }

            //开始处理性别
            if(item.getSex()==1){
                item.setSexName("男");
            }else{
                item.setSexName("女");
            }

        });

        ExportParams params = new ExportParams("生活补贴申请", null, "生活补贴申请");
        Workbook workbook = ExcelExportUtil.exportExcel(params, CjroneLivingAllowanceEntity.class, cjroneLivingAllowanceEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "生活补贴申请" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }
    /**
     * 生成电子签章 pdf
     */
    @RequestMapping("/printPDF/{id}")
    public R printPDF(@PathVariable("id") Integer id,@RequestParam("type") String type) throws IOException {
        System.out.print("id is :"+id);

        Map<String, Object> tmp_params = new HashMap<>();
        tmp_params.put("type","惠残事项生活补贴");
        tmp_params.put("status","1");
        tmp_params.put("type_id",id);
        List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
        if (alive_list.size()>0) {
            CjroneSignatureEntity cjroneSignatureEntity = alive_list.get(0);
            if ("电子公章".equals(type)){
                String tmp_file_name = System.currentTimeMillis()+"_"+cjroneSignatureEntity.getFileName();
                Files.copy(new File(cjroneSignatureEntity.getFileActUrl()).toPath(),new File(cjroneProperties.getTempFilePath()+tmp_file_name).toPath());

                return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", tmp_file_name).put("signId",cjroneSignatureEntity.getId());

            }else {
                return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", cjroneSignatureEntity.getFileName()).put("signId",cjroneSignatureEntity.getId());
            }
        }else {


            //根据编号获得详细信息
            CjroneLivingAllowanceEntity cjroneLivingAllowance = cjroneLivingAllowanceService.getById(id);
            //获得残疾证信息
            DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity = disabilityCertificateApplicationService.getByIDCard(cjroneLivingAllowance.getIdCard());

            String nums[] = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
            Calendar now = Calendar.getInstance();

            // 原pdf模板路径
            String templatePath = cjroneProperties.getTempletePath()+"生活补助模板.pdf";
            // 生成的新文件路径
            String newPDFPath = cjroneProperties.getSignaturePath()+"living_allowance_"+cjroneLivingAllowance.getIdCard()+".pdf";

            // 获得待生成的实体文件
            Map<String, String> map =new HashMap<String, String>();
            map.put("name", cjroneLivingAllowance.getName()==null?"":cjroneLivingAllowance.getName());
            if (cjroneLivingAllowance.getSex() == 1)
                map.put("sex", "男");
            else
                map.put("sex", "女");
            map.put("birthday", cjroneLivingAllowance.getBirthday()==null?"":cjroneLivingAllowance.getBirthday());
            map.put("mobile", cjroneLivingAllowance.getMobilePhone()==null?"":cjroneLivingAllowance.getMobilePhone());
            map.put("idCard", cjroneLivingAllowance.getIdCard()==null?"":cjroneLivingAllowance.getIdCard());

            if(cjroneLivingAllowance.getDisabilityCategory()==null){
                map.put("disabilityType", "");
            }
            else{
                if("1".equals(cjroneLivingAllowance.getDisabilityCategory())){
                    map.put("disabilityType", "视力残疾");
                }
                else if("2".equals(cjroneLivingAllowance.getDisabilityCategory())){
                    map.put("disabilityType", "听力残疾");
                }
                else if("3".equals(cjroneLivingAllowance.getDisabilityCategory())){
                    map.put("disabilityType", "智力残疾");
                }
                else if("4".equals(cjroneLivingAllowance.getDisabilityCategory())){
                    map.put("disabilityType", "精神残疾");
                }
                else if("5".equals(cjroneLivingAllowance.getDisabilityCategory())){
                    map.put("disabilityType", "肢体");
                }
                else if("6".equals(cjroneLivingAllowance.getDisabilityCategory())){
                    map.put("disabilityType", "言语残疾");
                }
                else if("7".equals(cjroneLivingAllowance.getDisabilityCategory())){
                    map.put("disabilityType", "肢体");
                }
                else{
                    map.put("disabilityType", cjroneLivingAllowance.getDisabilityCategory());
                }
            }
            map.put("disabilityDegree", cjroneLivingAllowance.getDisabilityDegree()==null?"无等":nums[Integer.parseInt(cjroneLivingAllowance.getDisabilityDegree())] + "级");
            map.put("disableId", cjroneLivingAllowance.getDisableId()==null?"":cjroneLivingAllowance.getDisableId());
            // 从残疾证表中获得详细的镇街道，村社区地址
            if(disabilityCertificateApplicationEntity!=null)
                map.put("nativeAddress", disabilityCertificateApplicationEntity.getNativeZhenName()==null?"":disabilityCertificateApplicationEntity.getNativeZhenName()+disabilityCertificateApplicationEntity.getNativeCunName());
            else
                map.put("nativeAddress", cjroneLivingAllowance.getNativePlace()==null?"":cjroneLivingAllowance.getNativePlace());
            map.put("bankName", cjroneLivingAllowance.getBankName()==null?"":cjroneLivingAllowance.getBankName());
            map.put("bankAccount", cjroneLivingAllowance.getBankAccount()==null?"":cjroneLivingAllowance.getBankAccount());
            map.put("guardianName", cjroneLivingAllowance.getGuardianName()==null?"":cjroneLivingAllowance.getGuardianName());
            map.put("guardianPhone", cjroneLivingAllowance.getGuardianPhone()==null?"":cjroneLivingAllowance.getGuardianPhone());
            map.put("economicSituation", cjroneLivingAllowance.getFamilyEconoCondition()==null?"":cjroneLivingAllowance.getFamilyEconoCondition());
            map.put("applicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
            //map.put("zhenApplicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
            //map.put("shiApplicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
            //开始关联补助金额
            Map<String, Object> mapparams =new HashMap<String, Object>();
            mapparams.put("type","生活补贴");
            List<CjroneTwoSubsidyStandardsEntity> cjroneTwoSubsidyStandardsEntityList=cjroneTwoSubsidyStandardsService.queryByMap(mapparams);
            if(cjroneTwoSubsidyStandardsEntityList!=null&&cjroneTwoSubsidyStandardsEntityList.size()>0){
                map.put("subsidymoney",cjroneTwoSubsidyStandardsEntityList.get(0).getMoney().toString());
            }
            else{
                map.put("subsidymoney"," ");
            }


            FileOutputStream out;
            int num = 1;//页数
            ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
            try {
                out = new FileOutputStream(newPDFPath);// 输出流
                Document doc = new Document();   //新建一个文档
                PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                doc.open();

                for (int i = 0; i < num; i++) {
                    bos[i] = new ByteArrayOutputStream();
                    PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                    PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                    AcroFields form = stamper.getAcroFields(); //获取文本域
                    // BaseFont
                    // 1、使用iTextAsian.jar中的字体
                    // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                    // 2、使用Windows系统字体(TrueType)
                    // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                    // 3、使用资源字体(ClassPath)
                    // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                    BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                    java.util.Iterator<String> it = form.getFields().keySet().iterator();
                    while (it.hasNext()) {
                        String name = it.next();
                        System.out.println(name + ":"+map.get(name));
                        form.setFieldProperty(name,"textfont",font,null);
                        // form.addSubstitutionFont(font);
                        form.setField(name, map.get(name));
                    }

                    stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                    stamper.close();

                }
                PdfImportedPage page = null;
                for (int i = 0; i < num; i++) {
                    page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                    copy.addPage(page);
                }
                doc.close();
                out.close();
            } catch (IOException e) {
                System.out.println("导出异常");
            } catch (DocumentException e) {
                System.out.println("文档异常");
            }

            // 生成电子签章pdf 并保存
            //String filePath = pdfUtils.pdfLivingAllowanceApplyToSignature(cjroneProperties.getTempletePath(), cjroneProperties.getSignaturePath(), livingentity);
            CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
            cjroneSignature.setUrl("/living_allowance_" + cjroneLivingAllowance.getIdCard()+".pdf");
            cjroneSignature.setFileActUrl(newPDFPath);
            cjroneSignature.setCreateDate(new Date());
            cjroneSignature.setCreateId(getUserId());
            cjroneSignature.setType("惠残事项生活补贴");
            cjroneSignature.setTypeId(cjroneLivingAllowance.getId());
            cjroneSignature.setFileName("living_allowance_"+cjroneLivingAllowance.getIdCard()+".pdf");
            cjroneSignature.setAccountId(getUserId().toString());
            cjroneSignature.setAccountName(getUser().getUsername());
            cjroneSignature.setStatus("1");

            cjroneSignatureService.save(cjroneSignature);
            // return R.ok().put("fileName", "signature_"+ PinYinUtil.getFullSpell(cjroneDisabilityHospital.getName())+".pdf").put("signId",cjroneSignature.getId());
            return R.ok().put("fileUrl", cjroneSignature.getUrl()).put("fileName", cjroneSignature.getFileName()).put("signId",cjroneSignature.getId());
        }
    }




}
