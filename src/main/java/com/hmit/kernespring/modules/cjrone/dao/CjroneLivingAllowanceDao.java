package com.hmit.kernespring.modules.cjrone.dao;

import com.hmit.kernespring.modules.cjrone.entity.CjroneLivingAllowanceEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 生活补贴申请
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-10 14:28:57
 */
@Mapper
public interface CjroneLivingAllowanceDao extends BaseMapper<CjroneLivingAllowanceEntity> {
    List<CjroneLivingAllowanceEntity> queryExportData(Map<String, Object> params);
	
}
