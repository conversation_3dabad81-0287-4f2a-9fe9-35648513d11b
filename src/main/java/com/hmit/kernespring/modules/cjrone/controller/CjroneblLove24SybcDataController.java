package com.hmit.kernespring.modules.cjrone.controller;

import java.io.File;
import java.io.IOException;
import java.util.*;

import java.io.OutputStream;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import com.hmit.kernespring.modules.sys.controller.AbstractController;
import org.apache.poi.ss.usermodel.Workbook;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hmit.kernespring.modules.cjrone.entity.CjroneblLove24SybcDataEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneblLove24SybcDataService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 爱心24小时异常数据
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-03-22 16:57:32
 */
@RestController
@RequestMapping("cjrone/cjronebllove24sybcdata")
public class CjroneblLove24SybcDataController extends AbstractController {
    @Autowired
    private CjroneblLove24SybcDataService cjroneblLove24SybcDataService;
    @Autowired
    private SysOssService sysOssService;

    /**
     * 列表
     */
    @RequestMapping("/list")
//    @RequiresPermissions("cjrone:cjronebllove24sybcdata:list")
    public R list(@RequestParam Map<String, Object> params){
        Map<String, Object> map =  new Gson().fromJson(params.get("key").toString(),Map.class);

        if(getUser().getRoleName() != null && !getUser().getRoleName().contains("区残联")){
            map.put("applicationType",getUser().getRoleName().substring(0,2));
        }
        map.put("isDisable",0);
        params.put("key",new Gson().toJson(map));
        PageUtils page = cjroneblLove24SybcDataService.queryPage(params);

        return R.ok().put("page", page);
    }

    @RequestMapping("/listAll")
//    @RequiresPermissions("cjrone:cjronebllove24sybcdata:list")
    public R listAll(@RequestParam Map<String, Object> params){
        Map<String, Object> map =  new Gson().fromJson(params.get("key").toString(),Map.class);

        if(getUser().getRoleName() != null && !getUser().getRoleName().contains("区残联")){
            map.put("applicationType",getUser().getRoleName().substring(0,2));
        }
        params.put("key",new Gson().toJson(map));
        PageUtils page = cjroneblLove24SybcDataService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
//    @RequiresPermissions("cjrone:cjronebllove24sybcdata:info")
    public R info(@PathVariable("id") Integer id){
		CjroneblLove24SybcDataEntity cjroneblLove24SybcData = cjroneblLove24SybcDataService.getById(id);

        return R.ok().put("cjroneblLove24SybcData", cjroneblLove24SybcData);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
//    @RequiresPermissions("cjrone:cjronebllove24sybcdata:save")
    public R save(@RequestBody CjroneblLove24SybcDataEntity cjroneblLove24SybcData){
		cjroneblLove24SybcDataService.save(cjroneblLove24SybcData);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
//    @RequiresPermissions("cjrone:cjronebllove24sybcdata:update")
    public R update(@RequestBody CjroneblLove24SybcDataEntity cjroneblLove24SybcData){
		cjroneblLove24SybcDataService.updateById(cjroneblLove24SybcData);

        return R.ok();
    }

    /**
     * 禁用
     */
    @RequestMapping("/disable")
    public R disable(@RequestBody Integer[] ids){
		cjroneblLove24SybcDataService.disable(ids);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
//    @RequiresPermissions("cjrone:cjronebllove24sybcdata:delete")
    public R delete(@RequestBody Integer[] ids){
		cjroneblLove24SybcDataService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }

    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
//    @RequiresPermissions("cjrone:cjronebllove24sybcdata:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        if(getUser().getRoleName() != null && !getUser().getRoleName().contains("区残联")){
            mapArgs.put("applicationType",getUser().getRoleName().substring(0,2));
        }
//        mapArgs.put("key",new Gson().toJson(mapArgs));

        System.out.println("参数："+mapArgs);
        List<CjroneblLove24SybcDataEntity> cjroneblLove24SybcDataEntityList = cjroneblLove24SybcDataService.queryExportData(mapArgs);

        cjroneblLove24SybcDataEntityList.forEach(item ->{
            //开始处理性别
            if("1".equals(item.getIsDead())){
                item.setIsDead("已死亡");
            }else{
                item.setIsDead("未死亡");
            }
            //开始处理户籍迁移
            if("1".equals(item.getIsHkqy())){
                item.setIsHkqy("户籍已迁移");
            }else{
                item.setIsHkqy("户籍未迁移");
            }
            //开始处理是否异常
            if("1".equals(item.getStatus())){
                item.setStatus("异常数据");
            }else{
                item.setStatus("正常");
            }
            //开始处理是否禁用
            if("1".equals(item.getIsDisable())){
                item.setIsDisable("已禁用");
            }else{
                item.setIsDisable("未禁用");
            }
        });

        ExportParams params = new ExportParams("爱心24小时异常数据", null, "爱心24小时异常数据");
        Workbook workbook = ExcelExportUtil.exportExcel(params, CjroneblLove24SybcDataEntity.class, cjroneblLove24SybcDataEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "爱心24小时异常数据" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

}
