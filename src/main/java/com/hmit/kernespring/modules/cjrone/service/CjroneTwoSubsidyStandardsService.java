package com.hmit.kernespring.modules.cjrone.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone.entity.CjroneTwoSubsidyStandardsEntity;

import java.util.Map;

import java.util.List;

/**
 * 两项补贴发放标准表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-06-06 11:12:08
 */
public interface CjroneTwoSubsidyStandardsService extends IService<CjroneTwoSubsidyStandardsEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneTwoSubsidyStandardsEntity> queryExportData(Map<String, Object> params);
    List<CjroneTwoSubsidyStandardsEntity>  queryByMap(Map<String, Object> params);
}

