package com.hmit.kernespring.modules.cjrone.dao;

import com.hmit.kernespring.modules.cjrone.entity.CjroneWelfareApplyDocEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-29 15:54:11
 */
@Mapper
public interface CjroneWelfareApplyDocDao extends BaseMapper<CjroneWelfareApplyDocEntity> {
    List<CjroneWelfareApplyDocEntity> queryExportData(Map<String, Object> params);
	
}
