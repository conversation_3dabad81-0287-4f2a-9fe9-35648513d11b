package com.hmit.kernespring.modules.cjrone.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;
import com.hmit.kernespring.modules.cjrone.dao.CjroneRehabilitationSubsidyDao;
import com.hmit.kernespring.modules.cjrone.entity.CjroneRehabilitationSubsidyEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneRehabilitationSubsidyService;
import com.hmit.kernespring.modules.matter_application.dao.CjroneWelfareMatterApplicationDao;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.*;


@Service("cjroneRehabilitationSubsidyService")
public class CjroneRehabilitationSubsidyServiceImpl extends ServiceImpl<CjroneRehabilitationSubsidyDao, CjroneRehabilitationSubsidyEntity> implements CjroneRehabilitationSubsidyService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneRehabilitationSubsidyDao cjroneRehabilitationSubsidyDao;
    @Autowired
    private CjroneWelfareMatterApplicationDao cjroneWelfareMatterApplicationDao;

    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        List<String> kangfuList = new ArrayList<>();
        if (params.get("roleName") != null){
            kangfuList = cjroneRehabilitationSubsidyDao.queryCategoryList(params);
        }
        List<String> finalKangfuList = kangfuList;
        cjroneRehabilitationSubsidyDao.queryCategoryList(params);
        CjroneRehabilitationSubsidyEntity cjroneRehabilitationSubsidyEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneRehabilitationSubsidyEntity.class);
        IPage<CjroneRehabilitationSubsidyEntity> page = this.page(
                new Query<CjroneRehabilitationSubsidyEntity>().getPage(params),
                new QueryWrapper<CjroneRehabilitationSubsidyEntity>()
            .eq(StringUtils.isNotBlank(cjroneRehabilitationSubsidyEntity.getName ()!=null && !"".equals(cjroneRehabilitationSubsidyEntity.getName ().toString())? cjroneRehabilitationSubsidyEntity.getName ().toString():null),"name", cjroneRehabilitationSubsidyEntity.getName ())
            .eq(StringUtils.isNotBlank(cjroneRehabilitationSubsidyEntity.getSex ()!=null && !"".equals(cjroneRehabilitationSubsidyEntity.getSex ().toString())? cjroneRehabilitationSubsidyEntity.getSex ().toString():null),"sex", cjroneRehabilitationSubsidyEntity.getSex ())
            .eq(StringUtils.isNotBlank(cjroneRehabilitationSubsidyEntity.getNationality ()!=null && !"".equals(cjroneRehabilitationSubsidyEntity.getNationality ().toString())? cjroneRehabilitationSubsidyEntity.getNationality ().toString():null),"nationality", cjroneRehabilitationSubsidyEntity.getNationality ())
            .eq(StringUtils.isNotBlank(cjroneRehabilitationSubsidyEntity.getBirthday ()!=null && !"".equals(cjroneRehabilitationSubsidyEntity.getBirthday ().toString())? cjroneRehabilitationSubsidyEntity.getBirthday ().toString():null),"birthday", cjroneRehabilitationSubsidyEntity.getBirthday ())
            .eq(StringUtils.isNotBlank(cjroneRehabilitationSubsidyEntity.getIdCard ()!=null && !"".equals(cjroneRehabilitationSubsidyEntity.getIdCard ().toString())? cjroneRehabilitationSubsidyEntity.getIdCard ().toString():null),"id_card", cjroneRehabilitationSubsidyEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(cjroneRehabilitationSubsidyEntity.getDisableId ()!=null && !"".equals(cjroneRehabilitationSubsidyEntity.getDisableId ().toString())? cjroneRehabilitationSubsidyEntity.getDisableId ().toString():null),"disable_id", cjroneRehabilitationSubsidyEntity.getDisableId ())
            .eq(StringUtils.isNotBlank(cjroneRehabilitationSubsidyEntity.getDisabilityCategory ()!=null && !"".equals(cjroneRehabilitationSubsidyEntity.getDisabilityCategory ().toString())? cjroneRehabilitationSubsidyEntity.getDisabilityCategory ().toString():null),"disability_category", cjroneRehabilitationSubsidyEntity.getDisabilityCategory ())
            .eq(StringUtils.isNotBlank(cjroneRehabilitationSubsidyEntity.getDisabilityDegree ()!=null && !"".equals(cjroneRehabilitationSubsidyEntity.getDisabilityDegree ().toString())? cjroneRehabilitationSubsidyEntity.getDisabilityDegree ().toString():null),"disability_degree", cjroneRehabilitationSubsidyEntity.getDisabilityDegree ())
            .eq(StringUtils.isNotBlank(cjroneRehabilitationSubsidyEntity.getPresentZhen ()!=null && !"".equals(cjroneRehabilitationSubsidyEntity.getPresentZhen ().toString())? cjroneRehabilitationSubsidyEntity.getPresentZhen ().toString():null),"present_zhen", cjroneRehabilitationSubsidyEntity.getPresentZhen ())
            .eq(StringUtils.isNotBlank(cjroneRehabilitationSubsidyEntity.getPresentCun ()!=null && !"".equals(cjroneRehabilitationSubsidyEntity.getPresentCun ().toString())? cjroneRehabilitationSubsidyEntity.getPresentCun ().toString():null),"present_cun", cjroneRehabilitationSubsidyEntity.getPresentCun ())
            .eq(StringUtils.isNotBlank(cjroneRehabilitationSubsidyEntity.getPresentAddress ()!=null && !"".equals(cjroneRehabilitationSubsidyEntity.getPresentAddress ().toString())? cjroneRehabilitationSubsidyEntity.getPresentAddress ().toString():null),"present_address", cjroneRehabilitationSubsidyEntity.getPresentAddress ())
            .eq(StringUtils.isNotBlank(cjroneRehabilitationSubsidyEntity.getContactPhone ()!=null && !"".equals(cjroneRehabilitationSubsidyEntity.getContactPhone ().toString())? cjroneRehabilitationSubsidyEntity.getContactPhone ().toString():null),"contact_phone", cjroneRehabilitationSubsidyEntity.getContactPhone ())
            .eq(StringUtils.isNotBlank(cjroneRehabilitationSubsidyEntity.getMobilePhone ()!=null && !"".equals(cjroneRehabilitationSubsidyEntity.getMobilePhone ().toString())? cjroneRehabilitationSubsidyEntity.getMobilePhone ().toString():null),"mobile_phone", cjroneRehabilitationSubsidyEntity.getMobilePhone ())
            .eq(StringUtils.isNotBlank(cjroneRehabilitationSubsidyEntity.getFamilyEconoCondition ()!=null && !"".equals(cjroneRehabilitationSubsidyEntity.getFamilyEconoCondition ().toString())? cjroneRehabilitationSubsidyEntity.getFamilyEconoCondition ().toString():null),"family_econo_condition", cjroneRehabilitationSubsidyEntity.getFamilyEconoCondition ())
            .eq(StringUtils.isNotBlank(cjroneRehabilitationSubsidyEntity.getMedicalInsurance ()!=null && !"".equals(cjroneRehabilitationSubsidyEntity.getMedicalInsurance ().toString())? cjroneRehabilitationSubsidyEntity.getMedicalInsurance ().toString():null),"medical_insurance", cjroneRehabilitationSubsidyEntity.getMedicalInsurance ())
            .eq(StringUtils.isNotBlank(cjroneRehabilitationSubsidyEntity.getRehabilitationProject ()!=null && !"".equals(cjroneRehabilitationSubsidyEntity.getRehabilitationProject ().toString())? cjroneRehabilitationSubsidyEntity.getRehabilitationProject ().toString():null),"rehabilitation_project", cjroneRehabilitationSubsidyEntity.getRehabilitationProject ())
            .eq(StringUtils.isNotBlank(cjroneRehabilitationSubsidyEntity.getApplicationDate ()!=null && !"".equals(cjroneRehabilitationSubsidyEntity.getApplicationDate ().toString())? cjroneRehabilitationSubsidyEntity.getApplicationDate ().toString():null),"application_date", cjroneRehabilitationSubsidyEntity.getApplicationDate ())
            .eq(StringUtils.isNotBlank(cjroneRehabilitationSubsidyEntity.getAuditPerson ()!=null && !"".equals(cjroneRehabilitationSubsidyEntity.getAuditPerson ().toString())? cjroneRehabilitationSubsidyEntity.getAuditPerson ().toString():null),"audit_person", cjroneRehabilitationSubsidyEntity.getAuditPerson ())
            .eq(StringUtils.isNotBlank(cjroneRehabilitationSubsidyEntity.getAuditDate ()!=null && !"".equals(cjroneRehabilitationSubsidyEntity.getAuditDate ().toString())? cjroneRehabilitationSubsidyEntity.getAuditDate ().toString():null),"audit_date", cjroneRehabilitationSubsidyEntity.getAuditDate ())
            .eq(StringUtils.isNotBlank(cjroneRehabilitationSubsidyEntity.getStatus ()!=null && !"".equals(cjroneRehabilitationSubsidyEntity.getStatus ().toString())? cjroneRehabilitationSubsidyEntity.getStatus ().toString():null),"status", cjroneRehabilitationSubsidyEntity.getStatus ())
            .eq(StringUtils.isNotBlank(cjroneRehabilitationSubsidyEntity.getCreateId ()!=null && !"".equals(cjroneRehabilitationSubsidyEntity.getCreateId ().toString())? cjroneRehabilitationSubsidyEntity.getCreateId ().toString():null),"create_id", cjroneRehabilitationSubsidyEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(cjroneRehabilitationSubsidyEntity.getCreateTime ()!=null && !"".equals(cjroneRehabilitationSubsidyEntity.getCreateTime ().toString())? cjroneRehabilitationSubsidyEntity.getCreateTime ().toString():null),"create_time", cjroneRehabilitationSubsidyEntity.getCreateTime ())
            .and(params.get("roleName") != null && "kangfu".equals(params.get("roleName").toString())?true:false,wrapper -> wrapper.in("rehabilitation_project",finalKangfuList))
            .and(params.get("roleName") != null && "kangfu-center".equals(params.get("roleName").toString())?true:false,wrapper -> wrapper.in("rehabilitation_project",finalKangfuList))
            .orderByDesc("create_time")
        );
        Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key","sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        page.getRecords().forEach( item -> {
            SysDictEntity disabilityCategory_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("cjlb_0000") && iii.getValue().equals(
                        item.getDisabilityCategory ())).findAny().orElse(null);
            if (disabilityCategory_sysDictEntity != null){
                item.setDisabilityCategory (disabilityCategory_sysDictEntity.getLabel());
            }else{
                item.setDisabilityCategory (null);
            }
            SysDictEntity disabilityDegree_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("cjdj_0000") && iii.getValue().equals(
                        item.getDisabilityDegree ())).findAny().orElse(null);
            if (disabilityDegree_sysDictEntity != null){
                item.setDisabilityDegree (disabilityDegree_sysDictEntity.getLabel());
            }else{
                item.setDisabilityDegree (null);
            }
            /*SysDictEntity status_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("zt_0000") && iii.getValue().equals(
                        item.getStatus ())).findAny().orElse(null);
            if (status_sysDictEntity != null){
                item.setStatus (Integer.parseInt(status_sysDictEntity.getLabel()));
            }else{
                item.setStatus (null);
            }*/
            if ("1".equals(item.getStatus())){
                item.setStatus("已通过");
            }else if ("2".equals(item.getStatus())){
                item.setStatus("待审核");
            }else if ("0".equals(item.getStatus())){
                item.setStatus("已禁用");
            }else if ("3".equals(item.getStatus())){
                item.setStatus("退回");
            }
            if ("1".equals(item.getSignStatus())) {
                item.setSignStatus("申请人待手签");
            } else if ("2".equals(item.getSignStatus())) {
                item.setSignStatus("申请人已手签");
            } else if ("3".equals(item.getSignStatus())) {
                item.setSignStatus("镇街道待手签");
            } else if ("4".equals(item.getSignStatus())) {
                item.setSignStatus("镇街道已手签");
            } else if ("5".equals(item.getSignStatus())) {
                item.setSignStatus("区残联未手签");
            } else if ("6".equals(item.getSignStatus())) {
                item.setSignStatus("区残联已手签");
            }else {
                item.setSignStatus("无");
            }
            if ("1".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("镇街道待电子公章");
            } else if ("2".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("区残联待电子公章");
            } else if ("3".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("民政待电子公章");
            } else if ("4".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("已完成");
            } else if ("5".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("镇街道已电子公章");
            } else if ("6".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("区残联已电子公章");
            } else if ("7".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("民政待已电子公章");
            } else {
                item.setSignatureStatus("无");
            }
        });
        return new PageUtils(page);
    }
    @Override
    public List<CjroneRehabilitationSubsidyEntity> queryExportData(Map<String, Object> params) {
            return cjroneRehabilitationSubsidyDao.queryExportData(params);
    }
    @Override
    public boolean updateById(CjroneRehabilitationSubsidyEntity entity) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",entity.getId());
        map.put("matter_name","康复补助");
        if ("1".equals(entity.getStatus())){
            map.put("status","3");
        } else if ("3".equals(entity.getStatus())){
            map.put("status","4");
        }else {
            map.put("status",entity.getStatus());
        }
        map.put("verify_time",new Date());
        if (entity.getStatus() != null){
            map.put("signatureStatus",entity.getSignatureStatus());
            cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        }
        return super.updateById(entity);
    }
}