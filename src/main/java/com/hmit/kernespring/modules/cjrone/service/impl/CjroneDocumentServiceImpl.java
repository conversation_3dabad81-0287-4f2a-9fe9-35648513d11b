package com.hmit.kernespring.modules.cjrone.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;
import com.hmit.kernespring.common.utils.ZipUtils;
import com.hmit.kernespring.modules.cjrone.dao.CjroneDocumentDao;
import com.hmit.kernespring.modules.cjrone.entity.CjroneCertificateApplyDocEntity;
import com.hmit.kernespring.modules.cjrone.entity.CjroneDocumentEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneDocumentService;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service("cjroneDocumentService")
public class CjroneDocumentServiceImpl extends ServiceImpl<CjroneDocumentDao, CjroneDocumentEntity> implements CjroneDocumentService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneDocumentDao cjroneDocumentDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneDocumentEntity cjroneDocumentEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneDocumentEntity.class);
        IPage<CjroneDocumentEntity> page = this.page(
                new Query<CjroneDocumentEntity>().getPage(params),
                new QueryWrapper<CjroneDocumentEntity>()
            .eq(StringUtils.isNotBlank(cjroneDocumentEntity.getId ()!=null && !"".equals(cjroneDocumentEntity.getId ().toString())? cjroneDocumentEntity.getId ().toString():null),"id", cjroneDocumentEntity.getId ())
            .eq(StringUtils.isNotBlank(cjroneDocumentEntity.getDisabilityCertificateApplicationId ()!=null && !"".equals(cjroneDocumentEntity.getDisabilityCertificateApplicationId ().toString())? cjroneDocumentEntity.getDisabilityCertificateApplicationId ().toString():null),"disability_certificate_application_id", cjroneDocumentEntity.getDisabilityCertificateApplicationId ())
            .eq(StringUtils.isNotBlank(cjroneDocumentEntity.getFileName ()!=null && !"".equals(cjroneDocumentEntity.getFileName ().toString())? cjroneDocumentEntity.getFileName ().toString():null),"file_name", cjroneDocumentEntity.getFileName ())
            .eq(StringUtils.isNotBlank(cjroneDocumentEntity.getFileSize ()!=null && !"".equals(cjroneDocumentEntity.getFileSize ().toString())? cjroneDocumentEntity.getFileSize ().toString():null),"file_size", cjroneDocumentEntity.getFileSize ())
            .eq(StringUtils.isNotBlank(cjroneDocumentEntity.getCreateId ()!=null && !"".equals(cjroneDocumentEntity.getCreateId ().toString())? cjroneDocumentEntity.getCreateId ().toString():null),"create_id", cjroneDocumentEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(cjroneDocumentEntity.getCreateTime ()!=null && !"".equals(cjroneDocumentEntity.getCreateTime ().toString())? cjroneDocumentEntity.getCreateTime ().toString():null),"create_time", cjroneDocumentEntity.getCreateTime ())
            .eq(StringUtils.isNotBlank(cjroneDocumentEntity.getFilePath ()!=null && !"".equals(cjroneDocumentEntity.getFilePath ().toString())? cjroneDocumentEntity.getFilePath ().toString():null),"file_path", cjroneDocumentEntity.getFilePath ())
            .eq(StringUtils.isNotBlank(cjroneDocumentEntity.getFilePathAct ()!=null && !"".equals(cjroneDocumentEntity.getFilePathAct ().toString())? cjroneDocumentEntity.getFilePathAct ().toString():null),"file_path_act", cjroneDocumentEntity.getFilePathAct ())
            .eq(StringUtils.isNotBlank(cjroneDocumentEntity.getFileType ()!=null && !"".equals(cjroneDocumentEntity.getFileType ().toString())? cjroneDocumentEntity.getFileType ().toString():null),"file_type", cjroneDocumentEntity.getFileType ())
            .eq(StringUtils.isNotBlank(cjroneDocumentEntity.getRemark ()!=null && !"".equals(cjroneDocumentEntity.getRemark ().toString())? cjroneDocumentEntity.getRemark ().toString():null),"remark", cjroneDocumentEntity.getRemark ())
            .eq(StringUtils.isNotBlank(cjroneDocumentEntity.getStatus ()!=null && !"".equals(cjroneDocumentEntity.getStatus ().toString())? cjroneDocumentEntity.getStatus ().toString():null),"status", cjroneDocumentEntity.getStatus ())
            .eq(StringUtils.isNotBlank(cjroneDocumentEntity.getDisabilityAssessmentId ()!=null && !"".equals(cjroneDocumentEntity.getDisabilityAssessmentId ().toString())? cjroneDocumentEntity.getDisabilityAssessmentId ().toString():null),"disability_assessment_id", cjroneDocumentEntity.getDisabilityAssessmentId ())
            .eq(StringUtils.isNotBlank(cjroneDocumentEntity.getDisabilityAssessmentDetailName ()!=null && !"".equals(cjroneDocumentEntity.getDisabilityAssessmentDetailName ().toString())? cjroneDocumentEntity.getDisabilityAssessmentDetailName ().toString():null),"disability_assessment_detail_name", cjroneDocumentEntity.getDisabilityAssessmentDetailName ())
        );
        Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key","sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        page.getRecords().forEach( item -> {
        });
        return new PageUtils(page);
    }
    @Override
    public List<CjroneDocumentEntity> queryExportData(Map<String, Object> params) {
            return cjroneDocumentDao.queryExportData(params);
    }
    @Override
    public List<CjroneDocumentEntity> queryDocumentDataByMap(Map<String, Object> params) {
            return cjroneDocumentDao.queryDocumentDataByMap(params);
    }
    @Override
    public List<CjroneDocumentEntity> queryDocumentDataByMapN(Map<String, Object> params) {
            return cjroneDocumentDao.queryDocumentDataByMapN(params);
    }
    @Override
    public List<CjroneCertificateApplyDocEntity> queryDocumentDocDataByMap(Map<String, Object> params) {
            return cjroneDocumentDao.queryDocumentDocDataByMap(params);
    }

    @Override
    public String downloadFj(List<File> fileList,Map<String, Object> params) throws FileNotFoundException {
        String file_path = params.get("download_path") != null ? params.get("download_path").toString():"/home/<USER>/test.zip";

        FileOutputStream fos2 = new FileOutputStream(new File(file_path));

        ZipUtils.toZip(fileList, fos2);

        return file_path;
    }

}