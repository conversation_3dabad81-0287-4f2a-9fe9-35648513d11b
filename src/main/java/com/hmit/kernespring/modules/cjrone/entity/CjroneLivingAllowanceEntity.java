package com.hmit.kernespring.modules.cjrone.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 生活补贴申请
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-10 14:28:57
 */
@Data
@TableName("cjrone_living_allowance")
public class CjroneLivingAllowanceEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 残疾人姓名
	 */

	@TableId
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
	private Integer id;
	@Excel(name = "残疾人姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 性别  1 男  2女
	 */

private Integer sex;

	@Excel(name = "性别", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String sexName;

	/**
	 * 出生日期
	 */
	@Excel(name = "出生日期", height = 20, width = 30, isImportField = "true_st")
private String birthday;
	/**
	 * 籍贯
	 */
	@Excel(name = "籍贯", height = 20, width = 30, isImportField = "true_st")
private String nativePlace;
	/**
	 * 身份证号码
	 */
	@Excel(name = "身份证号码", height = 20, width = 30, isImportField = "true_st")
private String idCard;
	/**
	 * 手机号码
	 */
	@Excel(name = "手机号码", height = 20, width = 30, isImportField = "true_st")
private String mobilePhone;
	/**
	 * 监护人姓名
	 */
	@Excel(name = "监护人姓名", height = 20, width = 30, isImportField = "true_st")
private String guardianName;
	/**
	 * 监护人手机
	 */
	@Excel(name = "监护人手机", height = 20, width = 30, isImportField = "true_st")
private String guardianPhone;
	/**
	 * 残疾类别
	 */
	@Excel(name = "残疾类别", height = 20, width = 30, isImportField = "true_st")
private String  disabilityCategory;
	/**
	 * 残疾等级
	 */
	@Excel(name = "残疾等级", height = 20, width = 30, isImportField = "true_st")
private String disabilityDegree;
	/**
	 * 残疾证号
	 */
	@Excel(name = "残疾证号", height = 20, width = 30, isImportField = "true_st")
private String disableId;
	/**
	 * 银行账户
	 */
	@Excel(name = "银行账户", height = 20, width = 30, isImportField = "true_st")
private String bankAccount;
	/**
	 * 开户银行
	 */
	@Excel(name = "开户银行", height = 20, width = 30, isImportField = "true_st")
private String bankName;
	/**
	 * 家庭经济情况
	 */
	@Excel(name = "家庭经济情况", height = 20, width = 30, isImportField = "true_st")
private String familyEconoCondition;
	/**
	 * 申请日期
	 */
	@Excel(name = "申请日期", height = 20, width = 30, isImportField = "true_st")
private String applicationDate;
	/**
	 * 审核人
	 */
	@Excel(name = "审核人", height = 20, width = 30, isImportField = "true_st")
private String auditPerson;
	/**
	 * 审核时间
	 */
	@Excel(name = "审核时间", height = 20, width = 30, isImportField = "true_st")
private String auditDate;
	/**
	 * 状态
	 */
	/*@Excel(name = "状态", height = 20, width = 30, isImportField = "true_st")*/
private String status;
	/**
	 * 创建人编号
	 */
	/*@Excel(name = "创建人编号", height = 20, width = 30, isImportField = "true_st")*/
private Long createId;
	/**
	 * 创建时间
	 */
	/*@Excel(name = "创建时间", height = 20, width = 30, isImportField = "true_st")*/
private Date createTime;
	private String signStatus;
	/**
	 * 电子签章状态
	 */
	private String signatureStatus;

	@Excel(name = "补贴金额", height = 20, width = 30, isImportField = "true_st")
	private String standardSubsidy;
}
