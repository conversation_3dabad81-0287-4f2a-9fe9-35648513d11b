package com.hmit.kernespring.modules.cjrone.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone.entity.CjroneResidentPensionInsuranceEntity;

import java.util.Map;

import java.util.List;

/**
 * 城乡居民养老保险补贴汇总表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-10 14:28:57
 */
public interface CjroneResidentPensionInsuranceService extends IService<CjroneResidentPensionInsuranceEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneResidentPensionInsuranceEntity> queryExportData(Map<String, Object> params);
}

