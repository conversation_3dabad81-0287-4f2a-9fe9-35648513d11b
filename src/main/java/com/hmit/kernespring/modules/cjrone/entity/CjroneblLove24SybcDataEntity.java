package com.hmit.kernespring.modules.cjrone.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 爱心24小时异常数据
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-03-22 16:57:32
 */
@Data
@TableName("cjronebl_love24_sybc_data")
public class CjroneblLove24SybcDataEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
@TableId
	@ApiModelProperty(value="")
@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 残疾人姓名
	 */
	@ApiModelProperty(value="残疾人姓名")
@Excel(name = "残疾人姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 身份证号码
	 */
	@ApiModelProperty(value="身份证号码")
@Excel(name = "身份证号码", height = 20, width = 30, isImportField = "true_st")
private String idCard;
	/**
	 * 居住地
	 */
	@ApiModelProperty(value="居住地")
@Excel(name = "居住地", height = 20, width = 30, isImportField = "true_st")
private String liveAddress;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value="创建时间")
@Excel(name = "创建时间", height = 20, width = 30, isImportField = "true_st")
private String createTime;
	/**
	 * 是否死亡
	 */
	@ApiModelProperty(value="是否死亡")
@Excel(name = "是否死亡", height = 20, width = 30, isImportField = "true_st")
private String isDead;
	/**
	 * 运营机构
	 */
	@ApiModelProperty(value="运营机构")
@Excel(name = "运营机构", height = 20, width = 30, isImportField = "true_st")
private String applicationType;

	/**
	 * 户口是否迁移
	 * 1 迁出
	 * 0 没迁出
	 */
	@ApiModelProperty(value="户口是否迁移")
@Excel(name = "户口是否迁移", height = 20, width = 30, isImportField = "true_st")
private String isHkqy;

	/**
	 * 户籍迁移信息
	 */
	@ApiModelProperty(value="户籍迁移信息")
@Excel(name = "户籍迁移信息", height = 20, width = 30, isImportField = "true_st")
private String hkqyInfo;
	/**
	 * 是否禁用
	 * 1 禁用
	 * 0 不禁用
	 */
	@ApiModelProperty(value="是否禁用")
	@Excel(name = "是否禁用", height = 20, width = 30, isImportField = "true_st")
	private String isDisable;
	/**
	 * 0 异常 1正常
	 */
	@ApiModelProperty(value="0 异常 1正常")
	@Excel(name = "是否异常", height = 20, width = 30, isImportField = "true_st")
	private String status;


	/**
	 *异常状态 1 死亡 2 户籍迁出
	 */
	@TableField(exist=false)
	private String errorStatus;

	//查询使用
	@TableField(exist=false)
	private String startDay;

	@TableField(exist=false)
	private String endDay;


}
