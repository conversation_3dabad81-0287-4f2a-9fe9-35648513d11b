package com.hmit.kernespring.modules.cjrone.dao;

import com.hmit.kernespring.modules.cjrone.entity.TempDiffEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-04-01 16:28:09
 */
@Mapper
public interface TempDiffDao extends BaseMapper<TempDiffEntity> {
    List<TempDiffEntity> queryExportData(Map<String, Object> params);
	
}
