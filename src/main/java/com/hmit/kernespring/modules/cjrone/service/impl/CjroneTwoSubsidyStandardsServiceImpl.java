package com.hmit.kernespring.modules.cjrone.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;
import com.hmit.kernespring.modules.cjrone.dao.CjroneTwoSubsidyStandardsDao;
import com.hmit.kernespring.modules.cjrone.entity.CjroneTwoSubsidyStandardsEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneTwoSubsidyStandardsService;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service("cjroneTwoSubsidyStandardsService")
public class CjroneTwoSubsidyStandardsServiceImpl extends ServiceImpl<CjroneTwoSubsidyStandardsDao, CjroneTwoSubsidyStandardsEntity> implements CjroneTwoSubsidyStandardsService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneTwoSubsidyStandardsDao cjroneTwoSubsidyStandardsDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneTwoSubsidyStandardsEntity cjroneTwoSubsidyStandardsEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneTwoSubsidyStandardsEntity.class);
        IPage<CjroneTwoSubsidyStandardsEntity> page = this.page(
                new Query<CjroneTwoSubsidyStandardsEntity>().getPage(params),
                new QueryWrapper<CjroneTwoSubsidyStandardsEntity>()
            .eq(StringUtils.isNotBlank(cjroneTwoSubsidyStandardsEntity.getId ()!=null && !"".equals(cjroneTwoSubsidyStandardsEntity.getId ().toString())? cjroneTwoSubsidyStandardsEntity.getId ().toString():null),"id", cjroneTwoSubsidyStandardsEntity.getId ())
            .eq(StringUtils.isNotBlank(cjroneTwoSubsidyStandardsEntity.getType ()!=null && !"".equals(cjroneTwoSubsidyStandardsEntity.getType ().toString())? cjroneTwoSubsidyStandardsEntity.getType ().toString():null),"type", cjroneTwoSubsidyStandardsEntity.getType ())
            .eq(StringUtils.isNotBlank(cjroneTwoSubsidyStandardsEntity.getDisabilityType ()!=null && !"".equals(cjroneTwoSubsidyStandardsEntity.getDisabilityType ().toString())? cjroneTwoSubsidyStandardsEntity.getDisabilityType ().toString():null),"disability_type", cjroneTwoSubsidyStandardsEntity.getDisabilityType ())
            .eq(StringUtils.isNotBlank(cjroneTwoSubsidyStandardsEntity.getDisabilityDegree ()!=null && !"".equals(cjroneTwoSubsidyStandardsEntity.getDisabilityDegree ().toString())? cjroneTwoSubsidyStandardsEntity.getDisabilityDegree ().toString():null),"disability_degree", cjroneTwoSubsidyStandardsEntity.getDisabilityDegree ())
            .eq(StringUtils.isNotBlank(cjroneTwoSubsidyStandardsEntity.getIsConcentratedCare ()!=null && !"".equals(cjroneTwoSubsidyStandardsEntity.getIsConcentratedCare ().toString())? cjroneTwoSubsidyStandardsEntity.getIsConcentratedCare ().toString():null),"is_concentrated_care", cjroneTwoSubsidyStandardsEntity.getIsConcentratedCare ())
            .eq(StringUtils.isNotBlank(cjroneTwoSubsidyStandardsEntity.getMoney ()!=null && !"".equals(cjroneTwoSubsidyStandardsEntity.getMoney ().toString())? cjroneTwoSubsidyStandardsEntity.getMoney ().toString():null),"money", cjroneTwoSubsidyStandardsEntity.getMoney ())
        );
        Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key","sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        page.getRecords().forEach( item -> {
            SysDictEntity type_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("btlx_0000") && iii.getValue().equals(
                        item.getType ())).findAny().orElse(null);
            if (type_sysDictEntity != null){
                item.setType (type_sysDictEntity.getLabel());
            }else{
                item.setType (null);
            }
            SysDictEntity disabilityType_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("cjlx_0000") && iii.getValue().equals(
                        item.getDisabilityType ())).findAny().orElse(null);
            if (disabilityType_sysDictEntity != null){
                item.setDisabilityType (disabilityType_sysDictEntity.getLabel());
            }else{
                item.setDisabilityType (null);
            }
            SysDictEntity disabilityDegree_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("cjdj_0000") && iii.getValue().equals(
                        item.getDisabilityDegree ())).findAny().orElse(null);
            if (disabilityDegree_sysDictEntity != null){
                item.setDisabilityDegree (Integer.parseInt(disabilityDegree_sysDictEntity.getLabel()));
            }else{
                item.setDisabilityDegree (null);
            }
        });
        return new PageUtils(page);
    }
    @Override
    public List<CjroneTwoSubsidyStandardsEntity> queryExportData(Map<String, Object> params) {
            return cjroneTwoSubsidyStandardsDao.queryExportData(params);
    }

    @Override
    public List<CjroneTwoSubsidyStandardsEntity> queryByMap(Map<String, Object> params) {
        return cjroneTwoSubsidyStandardsDao.queryByMap(params);
    }

}