package com.hmit.kernespring.modules.cjrone.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hmit.kernespring.modules.cjrone.entity.CjroneAdministrativeDivisionEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface CjroneVillageDao extends BaseMapper<CjroneAdministrativeDivisionEntity> {

    List<CjroneAdministrativeDivisionEntity> getAdministrativeDivisionList(String code) ;

    List<Map<String,Object>> getAdministrativeDivisionListForAPP(String code) ;

    CjroneAdministrativeDivisionEntity getVillageById(String code);
}
