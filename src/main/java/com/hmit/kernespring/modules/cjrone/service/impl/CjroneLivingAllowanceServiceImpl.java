package com.hmit.kernespring.modules.cjrone.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;
import com.hmit.kernespring.modules.cjrone.dao.CjroneLivingAllowanceDao;
import com.hmit.kernespring.modules.cjrone.entity.CjroneLivingAllowanceEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneLivingAllowanceService;
import com.hmit.kernespring.modules.matter_application.dao.CjroneWelfareMatterApplicationDao;
import com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity;
import com.hmit.kernespring.modules.matter_application.service.DisabilityCertificateApplicationService;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service("cjroneLivingAllowanceService")
public class CjroneLivingAllowanceServiceImpl extends ServiceImpl<CjroneLivingAllowanceDao, CjroneLivingAllowanceEntity> implements CjroneLivingAllowanceService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneLivingAllowanceDao cjroneLivingAllowanceDao;
    @Autowired
    private CjroneWelfareMatterApplicationDao cjroneWelfareMatterApplicationDao;

    @Autowired
    private SysDictService sysDictService;
    @Autowired
    private DisabilityCertificateApplicationService disabilityCertificateApplicationService;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneLivingAllowanceEntity cjroneLivingAllowanceEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneLivingAllowanceEntity.class);
        IPage<CjroneLivingAllowanceEntity> page = this.page(
                new Query<CjroneLivingAllowanceEntity>().getPage(params),
                new QueryWrapper<CjroneLivingAllowanceEntity>()
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceEntity.getName ()!=null && !"".equals(cjroneLivingAllowanceEntity.getName ().toString())? cjroneLivingAllowanceEntity.getName ().toString():null),"name", cjroneLivingAllowanceEntity.getName ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceEntity.getSex ()!=null && !"".equals(cjroneLivingAllowanceEntity.getSex ().toString())? cjroneLivingAllowanceEntity.getSex ().toString():null),"sex", cjroneLivingAllowanceEntity.getSex ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceEntity.getBirthday ()!=null && !"".equals(cjroneLivingAllowanceEntity.getBirthday ().toString())? cjroneLivingAllowanceEntity.getBirthday ().toString():null),"birthday", cjroneLivingAllowanceEntity.getBirthday ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceEntity.getNativePlace ()!=null && !"".equals(cjroneLivingAllowanceEntity.getNativePlace ().toString())? cjroneLivingAllowanceEntity.getNativePlace ().toString():null),"native_place", cjroneLivingAllowanceEntity.getNativePlace ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceEntity.getIdCard ()!=null && !"".equals(cjroneLivingAllowanceEntity.getIdCard ().toString())? cjroneLivingAllowanceEntity.getIdCard ().toString():null),"id_card", cjroneLivingAllowanceEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceEntity.getMobilePhone ()!=null && !"".equals(cjroneLivingAllowanceEntity.getMobilePhone ().toString())? cjroneLivingAllowanceEntity.getMobilePhone ().toString():null),"mobile_phone", cjroneLivingAllowanceEntity.getMobilePhone ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceEntity.getGuardianName ()!=null && !"".equals(cjroneLivingAllowanceEntity.getGuardianName ().toString())? cjroneLivingAllowanceEntity.getGuardianName ().toString():null),"guardian_name", cjroneLivingAllowanceEntity.getGuardianName ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceEntity.getGuardianPhone ()!=null && !"".equals(cjroneLivingAllowanceEntity.getGuardianPhone ().toString())? cjroneLivingAllowanceEntity.getGuardianPhone ().toString():null),"guardian_phone", cjroneLivingAllowanceEntity.getGuardianPhone ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceEntity.getDisabilityCategory ()!=null && !"".equals(cjroneLivingAllowanceEntity.getDisabilityCategory ().toString())? cjroneLivingAllowanceEntity.getDisabilityCategory ().toString():null),"disability_category", cjroneLivingAllowanceEntity.getDisabilityCategory ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceEntity.getDisabilityDegree ()!=null && !"".equals(cjroneLivingAllowanceEntity.getDisabilityDegree ().toString())? cjroneLivingAllowanceEntity.getDisabilityDegree ().toString():null),"disability_degree", cjroneLivingAllowanceEntity.getDisabilityDegree ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceEntity.getDisableId ()!=null && !"".equals(cjroneLivingAllowanceEntity.getDisableId ().toString())? cjroneLivingAllowanceEntity.getDisableId ().toString():null),"disable_id", cjroneLivingAllowanceEntity.getDisableId ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceEntity.getBankAccount ()!=null && !"".equals(cjroneLivingAllowanceEntity.getBankAccount ().toString())? cjroneLivingAllowanceEntity.getBankAccount ().toString():null),"bank_account", cjroneLivingAllowanceEntity.getBankAccount ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceEntity.getBankName ()!=null && !"".equals(cjroneLivingAllowanceEntity.getBankName ().toString())? cjroneLivingAllowanceEntity.getBankName ().toString():null),"bank_name", cjroneLivingAllowanceEntity.getBankName ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceEntity.getFamilyEconoCondition ()!=null && !"".equals(cjroneLivingAllowanceEntity.getFamilyEconoCondition ().toString())? cjroneLivingAllowanceEntity.getFamilyEconoCondition ().toString():null),"family_econo_condition", cjroneLivingAllowanceEntity.getFamilyEconoCondition ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceEntity.getApplicationDate ()!=null && !"".equals(cjroneLivingAllowanceEntity.getApplicationDate ().toString())? cjroneLivingAllowanceEntity.getApplicationDate ().toString():null),"application_date", cjroneLivingAllowanceEntity.getApplicationDate ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceEntity.getAuditPerson ()!=null && !"".equals(cjroneLivingAllowanceEntity.getAuditPerson ().toString())? cjroneLivingAllowanceEntity.getAuditPerson ().toString():null),"audit_person", cjroneLivingAllowanceEntity.getAuditPerson ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceEntity.getAuditDate ()!=null && !"".equals(cjroneLivingAllowanceEntity.getAuditDate ().toString())? cjroneLivingAllowanceEntity.getAuditDate ().toString():null),"audit_date", cjroneLivingAllowanceEntity.getAuditDate ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceEntity.getStatus ()!=null && !"".equals(cjroneLivingAllowanceEntity.getStatus ().toString())? cjroneLivingAllowanceEntity.getStatus ().toString():null),"status", cjroneLivingAllowanceEntity.getStatus ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceEntity.getCreateId ()!=null && !"".equals(cjroneLivingAllowanceEntity.getCreateId ().toString())? cjroneLivingAllowanceEntity.getCreateId ().toString():null),"create_id", cjroneLivingAllowanceEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceEntity.getCreateTime ()!=null && !"".equals(cjroneLivingAllowanceEntity.getCreateTime ().toString())? cjroneLivingAllowanceEntity.getCreateTime ().toString():null),"create_time", cjroneLivingAllowanceEntity.getCreateTime ())
            .orderByDesc("create_time")
        );
        Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key","sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        page.getRecords().forEach( item -> {
            SysDictEntity disabilityCategory_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("cjlb_0000") && iii.getValue().equals(
                        item.getDisabilityCategory ())).findAny().orElse(null);
            if (disabilityCategory_sysDictEntity != null){
                item.setDisabilityCategory (disabilityCategory_sysDictEntity.getLabel());
            }else{
                item.setDisabilityCategory (null);
            }
            SysDictEntity disabilityDegree_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("cjdj_0000") && iii.getValue().equals(
                        item.getDisabilityDegree ())).findAny().orElse(null);
            if (disabilityDegree_sysDictEntity != null){
                item.setDisabilityDegree (disabilityDegree_sysDictEntity.getLabel());
            }else{
                item.setDisabilityDegree (null);
            }
            /*SysDictEntity status_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("zt_0000") && iii.getValue().equals(
                        item.getStatus ())).findAny().orElse(null);
            if (status_sysDictEntity != null){
                item.setStatus (Integer.parseInt(status_sysDictEntity.getLabel()));
            }else{
                item.setStatus (null);
            }*/
            if ("1".equals(item.getStatus())){
                item.setStatus("通过");
            }else if ("2".equals(item.getStatus())){
                item.setStatus("待审核");
            }else if ("0".equals(item.getStatus())){
                item.setStatus("已禁用");
            }else if ("3".equals(item.getStatus())){
                item.setStatus("退回");
            }else if ("5".equals(item.getStatus())){
                item.setStatus("待民政审核");
            }
            if ("1".equals(item.getSignStatus())) {
                item.setSignStatus("申请人待手签");
            } else if ("2".equals(item.getSignStatus())) {
                item.setSignStatus("申请人已手签");
            } else if ("3".equals(item.getSignStatus())) {
                item.setSignStatus("镇街道待手签");
            } else if ("4".equals(item.getSignStatus())) {
                item.setSignStatus("镇街道已手签");
            } else if ("5".equals(item.getSignStatus())) {
                item.setSignStatus("区残联未手签");
            } else if ("6".equals(item.getSignStatus())) {
                item.setSignStatus("区残联已手签");
            }else if ("7".equals(item.getSignStatus())) {
                item.setSignStatus("区残联经办人已手签");
            }  else if ("8".equals(item.getSignStatus())) {
                item.setSignStatus("民政已手签");
            }else if ("9".equals(item.getSignStatus())) {
                item.setSignStatus("民政经办人已手签");
            }else {
                item.setSignStatus("无");
            }
            if ("1".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("镇街道待电子公章");
            } else if ("2".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("区残联待电子公章");
            } else if ("3".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("民政待电子公章");
            } else if ("4".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("已完成");
            } else if ("5".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("镇街道已电子公章");
            } else if ("6".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("区残联已电子公章");
            } else if ("7".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("民政待已电子公章");
            } else {
                item.setSignatureStatus("无");
            }

            //处理户籍地信息
            DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity = disabilityCertificateApplicationService.getByIDCard(item.getIdCard());
            if(disabilityCertificateApplicationEntity!=null){
                item.setNativePlace(disabilityCertificateApplicationEntity.getNativeZhenName()+disabilityCertificateApplicationEntity.getNativeCunName());
            }

        });
        return new PageUtils(page);
    }
    @Override
    public List<CjroneLivingAllowanceEntity> queryExportData(Map<String, Object> params) {
            return cjroneLivingAllowanceDao.queryExportData(params);
    }

    @Override
    public void enable(CjroneLivingAllowanceEntity entity) {
        super.updateById(entity);
    }

    @Override
    public boolean updateById(CjroneLivingAllowanceEntity entity) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",entity.getId());
        map.put("matter_name","生活补贴");
        if ("1".equals(entity.getStatus())){
            map.put("status","3");
        } else if ("3".equals(entity.getStatus())){
            map.put("status","4");
        }else {
            map.put("status",entity.getStatus());
        }
        map.put("verify_time",new Date());
        if (entity.getStatus() != null){
            map.put("signatureStatus",entity.getSignatureStatus());
            cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        }
        return super.updateById(entity);
    }

}