package com.hmit.kernespring.modules.cjrone.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone.entity.CjroneNursingSubsidySyncDataEntity;

import java.util.Map;

import java.util.List;

/**
 * 护理补贴对比数据
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-06-10 09:08:10
 */
public interface CjroneNursingSubsidySyncDataService extends IService<CjroneNursingSubsidySyncDataEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneNursingSubsidySyncDataEntity> queryExportData(Map<String, Object> params);
}

