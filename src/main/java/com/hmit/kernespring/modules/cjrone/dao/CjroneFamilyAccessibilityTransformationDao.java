package com.hmit.kernespring.modules.cjrone.dao;

import com.hmit.kernespring.modules.cjrone.entity.CjroneFamilyAccessibilityTransformationEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 家庭无障碍改造
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-27 16:56:18
 */
@Mapper
public interface CjroneFamilyAccessibilityTransformationDao extends BaseMapper<CjroneFamilyAccessibilityTransformationEntity> {
    List<CjroneFamilyAccessibilityTransformationEntity> queryExportData(Map<String, Object> params);
	
}
