package com.hmit.kernespring.modules.cjrone.dao;

import com.hmit.kernespring.modules.cjrone.entity.CjroneRehabilitationSubsidyEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 康复补助申请
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-10 14:28:57
 */
@Mapper
public interface CjroneRehabilitationSubsidyDao extends BaseMapper<CjroneRehabilitationSubsidyEntity> {
    List<CjroneRehabilitationSubsidyEntity> queryExportData(Map<String, Object> params);
	List<String> queryCategoryList(Map<String, Object> params);

}
