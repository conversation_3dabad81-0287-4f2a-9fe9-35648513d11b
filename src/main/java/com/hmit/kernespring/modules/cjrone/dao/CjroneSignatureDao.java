package com.hmit.kernespring.modules.cjrone.dao;

import com.hmit.kernespring.modules.cjrone.entity.CjroneSignatureEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 电子签章
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-11 14:55:13
 */
@Mapper
public interface CjroneSignatureDao extends BaseMapper<CjroneSignatureEntity> {
    List<CjroneSignatureEntity> queryExportData(Map<String, Object> params);
    List<CjroneSignatureEntity> queryDataByMap(Map<String, Object> params);

}
