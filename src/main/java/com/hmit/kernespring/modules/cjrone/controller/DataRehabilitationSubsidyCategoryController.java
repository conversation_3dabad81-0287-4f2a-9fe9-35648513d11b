package com.hmit.kernespring.modules.cjrone.controller;

import java.io.File;
import java.io.IOException;
import java.util.*;

import java.io.OutputStream;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import org.apache.poi.ss.usermodel.Workbook;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hmit.kernespring.modules.cjrone.entity.DataRehabilitationSubsidyCategoryEntity;
import com.hmit.kernespring.modules.cjrone.service.DataRehabilitationSubsidyCategoryService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 康复补贴类别
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-20 14:41:44
 */
@RestController
@RequestMapping("cjrone/datarehabilitationsubsidycategory")
public class DataRehabilitationSubsidyCategoryController {
    @Autowired
    private DataRehabilitationSubsidyCategoryService dataRehabilitationSubsidyCategoryService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("cjrone:datarehabilitationsubsidycategory:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = dataRehabilitationSubsidyCategoryService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{categoryId}")
    @RequiresPermissions("cjrone:datarehabilitationsubsidycategory:info")
    public R info(@PathVariable("categoryId") Integer categoryId){
		DataRehabilitationSubsidyCategoryEntity dataRehabilitationSubsidyCategory = dataRehabilitationSubsidyCategoryService.getById(categoryId);

        return R.ok().put("dataRehabilitationSubsidyCategory", dataRehabilitationSubsidyCategory);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("cjrone:datarehabilitationsubsidycategory:save")
    public R save(@RequestBody DataRehabilitationSubsidyCategoryEntity dataRehabilitationSubsidyCategory){
		dataRehabilitationSubsidyCategoryService.save(dataRehabilitationSubsidyCategory);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("cjrone:datarehabilitationsubsidycategory:update")
    public R update(@RequestBody DataRehabilitationSubsidyCategoryEntity dataRehabilitationSubsidyCategory){
		dataRehabilitationSubsidyCategoryService.updateById(dataRehabilitationSubsidyCategory);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("cjrone:datarehabilitationsubsidycategory:delete")
    public R delete(@RequestBody Integer[] categoryIds){
		dataRehabilitationSubsidyCategoryService.removeByIds(Arrays.asList(categoryIds));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    // @RequiresPermissions("cjrone:datarehabilitationsubsidycategory:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<DataRehabilitationSubsidyCategoryEntity> dataRehabilitationSubsidyCategoryList = new ArrayList<>();
        list.forEach(item ->{
                    item.put("categoryId",item.get("康复补助类别编号"));
                    item.put("parentId",item.get("父菜单ID，一级菜单为0"));
                    item.put("name",item.get("名称"));
                    item.put("createTime",item.get("创建时间"));
                    item.put("createId",item.get(""));
                    item.put("conditionName",item.get("条件及类别名称"));
                    item.put("serviceStandard",item.get("服务标准"));
                    item.put("memo",item.get("备注"));
                    item.put("leafnode",item.get("0 非叶子节点   1 叶子节点"));
                    dataRehabilitationSubsidyCategoryList.add(new Gson().fromJson(new Gson().toJson(item), DataRehabilitationSubsidyCategoryEntity.class));
        });
        // 保存到数据库
        dataRehabilitationSubsidyCategoryService.saveBatch(dataRehabilitationSubsidyCategoryList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    // @RequiresPermissions("cjrone:datarehabilitationsubsidycategory:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<DataRehabilitationSubsidyCategoryEntity> dataRehabilitationSubsidyCategoryEntityList = dataRehabilitationSubsidyCategoryService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("康复补贴类别", null, "康复补贴类别");
        Workbook workbook = ExcelExportUtil.exportExcel(params, DataRehabilitationSubsidyCategoryEntity.class, dataRehabilitationSubsidyCategoryEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "康复补贴类别" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

}
