package com.hmit.kernespring.modules.cjrone.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone.entity.TempDiffEntity;

import java.util.Map;

import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-04-01 16:28:09
 */
public interface TempDiffService extends IService<TempDiffEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<TempDiffEntity> queryExportData(Map<String, Object> params);
}

