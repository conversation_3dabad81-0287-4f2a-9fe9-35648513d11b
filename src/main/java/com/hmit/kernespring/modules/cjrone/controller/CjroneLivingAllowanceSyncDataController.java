package com.hmit.kernespring.modules.cjrone.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.cjrone.entity.CjroneLivingAllowanceSyncDataEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneLivingAllowanceSyncDataService;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;

/**
 * 生活补贴对比数据
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-06-10 09:08:10
 */
@RestController
@RequestMapping("cjrone/cjronelivingallowancesyncdata")
public class CjroneLivingAllowanceSyncDataController {
    @Autowired
    private CjroneLivingAllowanceSyncDataService cjroneLivingAllowanceSyncDataService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = cjroneLivingAllowanceSyncDataService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("cjrone:cjronelivingallowancesyncdata:info")
    public R info(@PathVariable("id") Integer id){
		CjroneLivingAllowanceSyncDataEntity cjroneLivingAllowanceSyncData = cjroneLivingAllowanceSyncDataService.getById(id);

        return R.ok().put("cjroneLivingAllowanceSyncData", cjroneLivingAllowanceSyncData);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("cjrone:cjronelivingallowancesyncdata:save")
    public R save(@RequestBody CjroneLivingAllowanceSyncDataEntity cjroneLivingAllowanceSyncData){
		cjroneLivingAllowanceSyncDataService.save(cjroneLivingAllowanceSyncData);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("cjrone:cjronelivingallowancesyncdata:update")
    public R update(@RequestBody CjroneLivingAllowanceSyncDataEntity cjroneLivingAllowanceSyncData){
		cjroneLivingAllowanceSyncDataService.updateById(cjroneLivingAllowanceSyncData);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("cjrone:cjronelivingallowancesyncdata:delete")
    public R delete(@RequestBody Integer[] ids){
		cjroneLivingAllowanceSyncDataService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    @RequiresPermissions("cjrone:cjronelivingallowancesyncdata:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<CjroneLivingAllowanceSyncDataEntity> cjroneLivingAllowanceSyncDataList = new ArrayList<>();
        list.forEach(item ->{
                    item.put("id",item.get(""));
                    item.put("name",item.get("残疾人姓名"));
                    item.put("idCard",item.get("身份证号码"));
                    item.put("createTime",item.get("创建时间"));
                    item.put("familyEconoCondition",item.get("家庭经济情况"));
                    item.put("medicalInsurance",item.get("医疗保险情况"));
                    item.put("isDead",item.get("是否死亡"));
                    item.put("isTk",item.get("是否特困"));
                    item.put("isSixty",item.get("是否60岁"));
                    item.put("isWorkinjury",item.get("是否工伤保险"));
                    item.put("isDischild",item.get("是否困境儿童"));
                    item.put("isResident",item.get("是否居民养老保险"));
                    item.put("isClork",item.get("是否职工参保"));
                    item.put("status",item.get("0 异常 1正常"));
                    item.put("isApplyWelfareMatter",item.get("是否申请过福利事项"));
                    item.put("isFx",item.get("是否服刑"));
                    item.put("isHkqy",item.get("是否户口迁移"));
                    item.put("fxInfo",item.get("服刑信息"));
                    item.put("hkqyInfo",item.get("户口迁移信息"));
                    cjroneLivingAllowanceSyncDataList.add(new Gson().fromJson(new Gson().toJson(item), CjroneLivingAllowanceSyncDataEntity.class));
        });
        // 保存到数据库
        cjroneLivingAllowanceSyncDataService.saveBatch(cjroneLivingAllowanceSyncDataList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    //@RequiresPermissions("cjrone:cjronelivingallowancesyncdata:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<CjroneLivingAllowanceSyncDataEntity> cjroneLivingAllowanceSyncDataEntityList = cjroneLivingAllowanceSyncDataService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("生活补贴对比数据", null, "生活补贴对比数据");
        Workbook workbook = ExcelExportUtil.exportExcel(params, CjroneLivingAllowanceSyncDataEntity.class, cjroneLivingAllowanceSyncDataEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "生活补贴对比数据" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

}
