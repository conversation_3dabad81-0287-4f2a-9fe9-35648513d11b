package com.hmit.kernespring.modules.cjrone.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hmit.kernespring.modules.cjrone.entity.CjroneAdministrativeDivisionEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface CjroneStreetDao extends BaseMapper<CjroneAdministrativeDivisionEntity> {

     List<CjroneAdministrativeDivisionEntity> getAdministrativeDivisionList(String code) ;

     // 获得所有的省份信息
     List<CjroneAdministrativeDivisionEntity> getAdministrativeDivisionListShen(String code);

     //根据省份信息获得市的消息
    List<CjroneAdministrativeDivisionEntity> getAdministrativeDivisionListShi(String code);

    //根据市的信息获得区的消息
    List<CjroneAdministrativeDivisionEntity> getAdministrativeDivisionListQu(String code);


     List<Map<String,Object>> getAdministrativeDivisionListForAPP(String code) ;

    CjroneAdministrativeDivisionEntity getStreetById(String code);

    Map<String,Object> queryNames(Map<String, Object> params);

    Map<String,Object> queryNames2(Map<String, Object> params);
}
