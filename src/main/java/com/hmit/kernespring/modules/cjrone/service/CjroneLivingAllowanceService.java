package com.hmit.kernespring.modules.cjrone.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone.entity.CjroneLivingAllowanceEntity;

import java.util.Map;

import java.util.List;

/**
 * 生活补贴申请
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-10 14:28:57
 */
public interface CjroneLivingAllowanceService extends IService<CjroneLivingAllowanceEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneLivingAllowanceEntity> queryExportData(Map<String, Object> params);
    void enable(CjroneLivingAllowanceEntity entity);
}

