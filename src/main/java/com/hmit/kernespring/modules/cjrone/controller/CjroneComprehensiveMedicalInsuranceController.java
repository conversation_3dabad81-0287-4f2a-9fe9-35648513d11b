package com.hmit.kernespring.modules.cjrone.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.cjrone.entity.CjroneComprehensiveMedicalInsuranceEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneComprehensiveMedicalInsuranceService;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;

/**
 * 残疾人综合医疗保险
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-27 16:56:19
 */
@RestController
@RequestMapping("cjrone/cjronecomprehensivemedicalinsurance")
public class CjroneComprehensiveMedicalInsuranceController {
    @Autowired
    private CjroneComprehensiveMedicalInsuranceService cjroneComprehensiveMedicalInsuranceService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("cjrone:cjronecomprehensivemedicalinsurance:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = cjroneComprehensiveMedicalInsuranceService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("cjrone:cjronecomprehensivemedicalinsurance:info")
    public R info(@PathVariable("id") Integer id){
		CjroneComprehensiveMedicalInsuranceEntity cjroneComprehensiveMedicalInsurance = cjroneComprehensiveMedicalInsuranceService.getById(id);

        return R.ok().put("cjroneComprehensiveMedicalInsurance", cjroneComprehensiveMedicalInsurance);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("cjrone:cjronecomprehensivemedicalinsurance:save")
    public R save(@RequestBody CjroneComprehensiveMedicalInsuranceEntity cjroneComprehensiveMedicalInsurance){
		cjroneComprehensiveMedicalInsuranceService.save(cjroneComprehensiveMedicalInsurance);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("cjrone:cjronecomprehensivemedicalinsurance:update")
    public R update(@RequestBody CjroneComprehensiveMedicalInsuranceEntity cjroneComprehensiveMedicalInsurance){
		cjroneComprehensiveMedicalInsuranceService.updateById(cjroneComprehensiveMedicalInsurance);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("cjrone:cjronecomprehensivemedicalinsurance:delete")
    public R delete(@RequestBody Integer[] ids){
		cjroneComprehensiveMedicalInsuranceService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    @RequiresPermissions("cjrone:cjronecomprehensivemedicalinsurance:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<CjroneComprehensiveMedicalInsuranceEntity> cjroneComprehensiveMedicalInsuranceList = new ArrayList<>();
        list.forEach(item ->{
                    item.put("id",item.get(""));
                    item.put("name",item.get("姓名"));
                    item.put("idCard",item.get("身份证号"));
                    cjroneComprehensiveMedicalInsuranceList.add(new Gson().fromJson(new Gson().toJson(item), CjroneComprehensiveMedicalInsuranceEntity.class));
        });
        // 保存到数据库
        cjroneComprehensiveMedicalInsuranceService.saveBatch(cjroneComprehensiveMedicalInsuranceList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    @RequiresPermissions("cjrone:cjronecomprehensivemedicalinsurance:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<CjroneComprehensiveMedicalInsuranceEntity> cjroneComprehensiveMedicalInsuranceEntityList = cjroneComprehensiveMedicalInsuranceService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("残疾人综合医疗保险", null, "残疾人综合医疗保险");
        Workbook workbook = ExcelExportUtil.exportExcel(params, CjroneComprehensiveMedicalInsuranceEntity.class, cjroneComprehensiveMedicalInsuranceEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "残疾人综合医疗保险" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

}
