package com.hmit.kernespring.modules.cjrone.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone.entity.CjroneSignatureEntity;

import java.util.Map;

import java.util.List;

/**
 * 电子签章
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-11 14:55:13
 */
public interface CjroneSignatureService extends IService<CjroneSignatureEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneSignatureEntity> queryExportData(Map<String, Object> params);
    List<CjroneSignatureEntity> queryDataByMap(Map<String, Object> params);
}

