package com.hmit.kernespring.modules.cjrone.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone.entity.CjroneRehabilitationSubsidyEntity;

import java.util.Map;

import java.util.List;

/**
 * 康复补助申请
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-10 14:28:57
 */
public interface CjroneRehabilitationSubsidyService extends IService<CjroneRehabilitationSubsidyEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneRehabilitationSubsidyEntity> queryExportData(Map<String, Object> params);
}

