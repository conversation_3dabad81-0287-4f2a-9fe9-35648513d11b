package com.hmit.kernespring.modules.cjrone.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone.entity.DataRehabilitationSubsidyCategoryEntity;

import java.util.Map;

import java.util.List;

/**
 * 康复补贴类别
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-20 14:41:44
 */
public interface DataRehabilitationSubsidyCategoryService extends IService<DataRehabilitationSubsidyCategoryEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<DataRehabilitationSubsidyCategoryEntity> queryExportData(Map<String, Object> params);

    List<DataRehabilitationSubsidyCategoryEntity> getRehabilitationSubsidyCategoryList(Map<String, Object> params);

}

