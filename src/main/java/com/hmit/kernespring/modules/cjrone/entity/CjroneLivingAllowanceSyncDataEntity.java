package com.hmit.kernespring.modules.cjrone.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 生活补贴对比数据
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-06-10 09:08:10
 */
@Data
@TableName("cjrone_living_allowance_sync_data")
public class CjroneLivingAllowanceSyncDataEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
@TableId
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 残疾人姓名
	 */
	@Excel(name = "残疾人姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 身份证号码
	 */
	@Excel(name = "身份证号码", height = 20, width = 30, isImportField = "true_st")
private String idCard;
	/**
	 * 创建时间
	 */
	@Excel(name = "创建时间", height = 20, width = 30, isImportField = "true_st")
private String createTime;
	/**
	 * 家庭经济情况
	 */
	@Excel(name = "家庭经济情况", height = 20, width = 30, isImportField = "true_st")
private String familyEconoCondition;
	/**
	 * 医疗保险情况
	 */
	@Excel(name = "医疗保险情况", height = 20, width = 30, isImportField = "true_st")
private String medicalInsurance;
	/**
	 * 是否死亡
	 */
	@Excel(name = "是否死亡", height = 20, width = 30, isImportField = "true_st")
private Integer isDead;
	/**
	 * 是否特困
	 */
	@Excel(name = "是否特困", height = 20, width = 30, isImportField = "true_st")
private Integer isTk;
	/**
	 * 是否60岁
	 */
	@Excel(name = "是否60岁", height = 20, width = 30, isImportField = "true_st")
private Integer isSixty;
	/**
	 * 是否工伤保险
	 */
	@Excel(name = "是否工伤保险", height = 20, width = 30, isImportField = "true_st")
private Integer isWorkinjury;
	/**
	 * 是否困境儿童
	 */
	@Excel(name = "是否困境儿童", height = 20, width = 30, isImportField = "true_st")
private Integer isDischild;
	/**
	 * 是否居民养老保险
	 */
	@Excel(name = "是否居民养老保险", height = 20, width = 30, isImportField = "true_st")
private Integer isResident;
	/**
	 * 是否职工参保
	 */
	@Excel(name = "是否职工参保", height = 20, width = 30, isImportField = "true_st")
private Integer isClork;
	/**
	 * 0 异常 1正常
	 */
	@Excel(name = "0 异常 1正常", height = 20, width = 30, isImportField = "true_st")
private Integer status;
	/**
	 * 是否申请过福利事项
	 */
	@Excel(name = "是否申请过福利事项", height = 20, width = 30, isImportField = "true_st")
private String isApplyWelfareMatter;
	/**
	 * 是否服刑
	 */
	@Excel(name = "是否服刑", height = 20, width = 30, isImportField = "true_st")
private Integer isFx;
	/**
	 * 是否户口迁移
	 */
	@Excel(name = "是否户口迁移", height = 20, width = 30, isImportField = "true_st")
private Integer isHkqy;
	/**
	 * 服刑信息
	 */
	@Excel(name = "服刑信息", height = 20, width = 30, isImportField = "true_st")
private String fxInfo;
	/**
	 * 户口迁移信息
	 */
	@Excel(name = "户口迁移信息", height = 20, width = 30, isImportField = "true_st")
private String hkqyInfo;
	@Excel(name = "是否导入一致", height = 20, width = 30, isImportField = "true_st")
	private Integer isImportYz;

	@Excel(name = "异常原因", height = 20, width = 30, isImportField = "true_st")
	private String exceptionReason;

}
