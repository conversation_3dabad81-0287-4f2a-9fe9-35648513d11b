package com.hmit.kernespring.modules.cjrone.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;
import com.hmit.kernespring.modules.cjrone.dao.CjroneNursingSubsidySyncDataDao;
import com.hmit.kernespring.modules.cjrone.entity.CjroneNursingSubsidySyncDataEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneNursingSubsidySyncDataService;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service("cjroneNursingSubsidySyncDataService")
public class CjroneNursingSubsidySyncDataServiceImpl extends ServiceImpl<CjroneNursingSubsidySyncDataDao, CjroneNursingSubsidySyncDataEntity> implements CjroneNursingSubsidySyncDataService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneNursingSubsidySyncDataDao cjroneNursingSubsidySyncDataDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneNursingSubsidySyncDataEntity cjroneNursingSubsidySyncDataEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneNursingSubsidySyncDataEntity.class);
        IPage<CjroneNursingSubsidySyncDataEntity> page = this.page(
                new Query<CjroneNursingSubsidySyncDataEntity>().getPage(params),
                new QueryWrapper<CjroneNursingSubsidySyncDataEntity>()
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidySyncDataEntity.getId ()!=null && !"".equals(cjroneNursingSubsidySyncDataEntity.getId ().toString())? cjroneNursingSubsidySyncDataEntity.getId ().toString():null),"id", cjroneNursingSubsidySyncDataEntity.getId ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidySyncDataEntity.getName ()!=null && !"".equals(cjroneNursingSubsidySyncDataEntity.getName ().toString())? cjroneNursingSubsidySyncDataEntity.getName ().toString():null),"name", cjroneNursingSubsidySyncDataEntity.getName ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidySyncDataEntity.getIdCard ()!=null && !"".equals(cjroneNursingSubsidySyncDataEntity.getIdCard ().toString())? cjroneNursingSubsidySyncDataEntity.getIdCard ().toString():null),"id_card", cjroneNursingSubsidySyncDataEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidySyncDataEntity.getCreateTime ()!=null && !"".equals(cjroneNursingSubsidySyncDataEntity.getCreateTime ().toString())? cjroneNursingSubsidySyncDataEntity.getCreateTime ().toString():null),"create_time", cjroneNursingSubsidySyncDataEntity.getCreateTime ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidySyncDataEntity.getFamilyEconoCondition ()!=null && !"".equals(cjroneNursingSubsidySyncDataEntity.getFamilyEconoCondition ().toString())? cjroneNursingSubsidySyncDataEntity.getFamilyEconoCondition ().toString():null),"family_econo_condition", cjroneNursingSubsidySyncDataEntity.getFamilyEconoCondition ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidySyncDataEntity.getMedicalInsurance ()!=null && !"".equals(cjroneNursingSubsidySyncDataEntity.getMedicalInsurance ().toString())? cjroneNursingSubsidySyncDataEntity.getMedicalInsurance ().toString():null),"medical_insurance", cjroneNursingSubsidySyncDataEntity.getMedicalInsurance ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidySyncDataEntity.getIsDead ()!=null && !"".equals(cjroneNursingSubsidySyncDataEntity.getIsDead ().toString())? cjroneNursingSubsidySyncDataEntity.getIsDead ().toString():null),"is_dead", cjroneNursingSubsidySyncDataEntity.getIsDead ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidySyncDataEntity.getIsTk ()!=null && !"".equals(cjroneNursingSubsidySyncDataEntity.getIsTk ().toString())? cjroneNursingSubsidySyncDataEntity.getIsTk ().toString():null),"is_tk", cjroneNursingSubsidySyncDataEntity.getIsTk ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidySyncDataEntity.getIsSixty ()!=null && !"".equals(cjroneNursingSubsidySyncDataEntity.getIsSixty ().toString())? cjroneNursingSubsidySyncDataEntity.getIsSixty ().toString():null),"is_sixty", cjroneNursingSubsidySyncDataEntity.getIsSixty ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidySyncDataEntity.getIsWorkinjury ()!=null && !"".equals(cjroneNursingSubsidySyncDataEntity.getIsWorkinjury ().toString())? cjroneNursingSubsidySyncDataEntity.getIsWorkinjury ().toString():null),"is_workinjury", cjroneNursingSubsidySyncDataEntity.getIsWorkinjury ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidySyncDataEntity.getIsDischild ()!=null && !"".equals(cjroneNursingSubsidySyncDataEntity.getIsDischild ().toString())? cjroneNursingSubsidySyncDataEntity.getIsDischild ().toString():null),"is_dischild", cjroneNursingSubsidySyncDataEntity.getIsDischild ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidySyncDataEntity.getIsResident ()!=null && !"".equals(cjroneNursingSubsidySyncDataEntity.getIsResident ().toString())? cjroneNursingSubsidySyncDataEntity.getIsResident ().toString():null),"is_resident", cjroneNursingSubsidySyncDataEntity.getIsResident ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidySyncDataEntity.getIsClork ()!=null && !"".equals(cjroneNursingSubsidySyncDataEntity.getIsClork ().toString())? cjroneNursingSubsidySyncDataEntity.getIsClork ().toString():null),"is_clork", cjroneNursingSubsidySyncDataEntity.getIsClork ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidySyncDataEntity.getStatus ()!=null && !"".equals(cjroneNursingSubsidySyncDataEntity.getStatus ().toString())? cjroneNursingSubsidySyncDataEntity.getStatus ().toString():null),"status", cjroneNursingSubsidySyncDataEntity.getStatus ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidySyncDataEntity.getIsApplyWelfareMatter ()!=null && !"".equals(cjroneNursingSubsidySyncDataEntity.getIsApplyWelfareMatter ().toString())? cjroneNursingSubsidySyncDataEntity.getIsApplyWelfareMatter ().toString():null),"is_apply_welfare_matter", cjroneNursingSubsidySyncDataEntity.getIsApplyWelfareMatter ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidySyncDataEntity.getIsFx ()!=null && !"".equals(cjroneNursingSubsidySyncDataEntity.getIsFx ().toString())? cjroneNursingSubsidySyncDataEntity.getIsFx ().toString():null),"is_fx", cjroneNursingSubsidySyncDataEntity.getIsFx ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidySyncDataEntity.getIsHkqy ()!=null && !"".equals(cjroneNursingSubsidySyncDataEntity.getIsHkqy ().toString())? cjroneNursingSubsidySyncDataEntity.getIsHkqy ().toString():null),"is_hkqy", cjroneNursingSubsidySyncDataEntity.getIsHkqy ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidySyncDataEntity.getFxInfo ()!=null && !"".equals(cjroneNursingSubsidySyncDataEntity.getFxInfo ().toString())? cjroneNursingSubsidySyncDataEntity.getFxInfo ().toString():null),"fx_info", cjroneNursingSubsidySyncDataEntity.getFxInfo ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidySyncDataEntity.getHkqyInfo ()!=null && !"".equals(cjroneNursingSubsidySyncDataEntity.getHkqyInfo ().toString())? cjroneNursingSubsidySyncDataEntity.getHkqyInfo ().toString():null),"hkqy_info", cjroneNursingSubsidySyncDataEntity.getHkqyInfo ())
        );
        Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key","sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        page.getRecords().forEach( item -> {
        });
        return new PageUtils(page);
    }
    @Override
    public List<CjroneNursingSubsidySyncDataEntity> queryExportData(Map<String, Object> params) {
            return cjroneNursingSubsidySyncDataDao.queryExportData(params);
    }

}