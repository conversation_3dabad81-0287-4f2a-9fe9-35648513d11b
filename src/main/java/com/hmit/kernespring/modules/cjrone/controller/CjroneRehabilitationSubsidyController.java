package com.hmit.kernespring.modules.cjrone.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.common.utils.DateUtils;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.cjrone.entity.CjroneRehabilitationSubsidyEntity;
import com.hmit.kernespring.modules.cjrone.entity.CjroneSignatureEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneRehabilitationSubsidyService;
import com.hmit.kernespring.modules.cjrone.service.CjroneSignatureService;
import com.hmit.kernespring.modules.data_management.entity.ApiCardIdEntity;
import com.hmit.kernespring.modules.data_management.service.ApiCardIdService;
import com.hmit.kernespring.modules.matter_application.controller.CjroneWelfareMatterApplicationController;
import com.hmit.kernespring.modules.matter_application.entity.CjroneWelfareMatterApplicationEntity;
import com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity;
import com.hmit.kernespring.modules.matter_application.service.CjroneWelfareMatterApplicationService;
import com.hmit.kernespring.modules.matter_application.service.DisabilityCertificateApplicationService;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import com.hmit.kernespring.modules.sys.controller.AbstractController;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.*;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.Files;
import java.util.*;

/**
 * 康复补助申请
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-10 14:28:57
 */
@RestController
@RequestMapping("cjrone/cjronerehabilitationsubsidy")
public class CjroneRehabilitationSubsidyController extends AbstractController {
    @Autowired
    private CjroneRehabilitationSubsidyService cjroneRehabilitationSubsidyService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;
    @Autowired
    private CjroneSignatureService cjroneSignatureService;
    @Autowired
    private CjroneWelfareMatterApplicationController cjroneWelfareMatterApplicationController;
    @Autowired
    private DisabilityCertificateApplicationService disabilityCertificateApplicationService;
    @Autowired
    private ApiCardIdService apiCardIdService;
    @Autowired
    private CjroneWelfareMatterApplicationService cjroneWelfareMatterApplicationService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("cjrone:cjronerehabilitationsubsidy:list")
    public R list(@RequestParam Map<String, Object> params){
        System.out.println(new Gson().toJson(getUser()));
        if (getUser().getRoleName() != null && getUser().getRoleName().indexOf("康复部-康复部") != -1){
            params.put("roleName","kangfu");
        }else if (getUser().getRoleName() != null && getUser().getRoleName().indexOf("康复部-康复中心") != -1){
            params.put("roleName","kangfu-center");
        }

        System.out.println(params);
        PageUtils page = cjroneRehabilitationSubsidyService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{name}")
    @RequiresPermissions("cjrone:cjronerehabilitationsubsidy:info")
    public R info(@PathVariable("name") String name){
		CjroneRehabilitationSubsidyEntity cjroneRehabilitationSubsidy = cjroneRehabilitationSubsidyService.getById(name);

        return R.ok().put("cjroneRehabilitationSubsidy", cjroneRehabilitationSubsidy);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    //@RequiresPermissions("cjrone:cjronerehabilitationsubsidy:save")
    public R save(@RequestBody CjroneRehabilitationSubsidyEntity cjroneRehabilitationSubsidy){
        cjroneRehabilitationSubsidy.setCreateTime(new Date());
        cjroneRehabilitationSubsidy.setCreateId(getUserId());
        cjroneRehabilitationSubsidy.setStatus("2");
		cjroneRehabilitationSubsidyService.save(cjroneRehabilitationSubsidy);

        return R.ok();
    }

    /**
     *  专门针对于儿童的保存接口（无需残疾证，但是需要基本信息）
     */
    @RequestMapping("/childSave")
    public R childSave(@RequestBody CjroneRehabilitationSubsidyEntity cjroneRehabilitationSubsidy){

        cjroneRehabilitationSubsidy.setCreateTime(new Date());
        cjroneRehabilitationSubsidy.setCreateId(getUserId());
        cjroneRehabilitationSubsidy.setStatus("1");  //状态为申请中
        cjroneRehabilitationSubsidy.setSignStatus("1");
        cjroneRehabilitationSubsidy.setSignatureStatus("1");
        //cjroneRehabilitationSubsidy.setFamilyEconoCondition();
        //cjroneRehabilitationSubsidy.setMedicalInsurance("无");

        //保存到康复补助主表
        cjroneRehabilitationSubsidyService.save(cjroneRehabilitationSubsidy);

        //保存到申请主表
        CjroneWelfareMatterApplicationEntity entity=new CjroneWelfareMatterApplicationEntity();
        entity.setCreateId(getUserId());
        entity.setCreateTime(DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN));
        entity.setApplicationTime(DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN));
        if (getUser().getRoleName() != null && getUser().getRoleName().indexOf("村代办") != -1){
            entity.setStatus("1");
        }else {
            entity.setStatus("1");
        }
        entity.setMatterName("康复补助");
        entity.setMatterId(cjroneRehabilitationSubsidy.getId());
        entity.setName(cjroneRehabilitationSubsidy.getName());
        entity.setIdCard(cjroneRehabilitationSubsidy.getIdCard());
        entity.setMobilePhone(cjroneRehabilitationSubsidy.getMobilePhone());
        entity.setIsKfbzApply("2");
        entity.setNativeZhen(cjroneRehabilitationSubsidy.getNativeZhen());
        entity.setNativeCun(cjroneRehabilitationSubsidy.getNativeCun());
        cjroneWelfareMatterApplicationService.save(entity);

        //保存到残疾证申请表
        DisabilityCertificateApplicationEntity disableentity=new DisabilityCertificateApplicationEntity();
        disableentity.setName(cjroneRehabilitationSubsidy.getName());
        disableentity.setSex(cjroneRehabilitationSubsidy.getSex().toString());
        disableentity.setNationality(cjroneRehabilitationSubsidy.getNationality());
        disableentity.setBirthday(cjroneRehabilitationSubsidy.getBirthday());
        disableentity.setNativePlace(cjroneRehabilitationSubsidy.getNativePlace());
        disableentity.setIdCard(cjroneRehabilitationSubsidy.getIdCard());
        disableentity.setPresentAddress(cjroneRehabilitationSubsidy.getPresentAddress());
        disableentity.setPostcode(cjroneRehabilitationSubsidy.getPostcode());
        disableentity.setMobilePhone(cjroneRehabilitationSubsidy.getMobilePhone());
        disableentity.setGuardianIdcard(cjroneRehabilitationSubsidy.getGuardianIdcard());
        disableentity.setGuardianName(cjroneRehabilitationSubsidy.getGuardianName());
        disableentity.setGuardianPhone(cjroneRehabilitationSubsidy.getGuardianPhone());
        disableentity.setGuardianRelation(cjroneRehabilitationSubsidy.getGuardianRelation());
        disableentity.setDisabilityType(cjroneRehabilitationSubsidy.getDisabilityCategory());
        disableentity.setBankAccount(cjroneRehabilitationSubsidy.getBankAccount());
        disableentity.setBankName(cjroneRehabilitationSubsidy.getBankName());
        disabilityCertificateApplicationService.save(disableentity);

        //保存到api表
        ApiCardIdEntity apientity=new ApiCardIdEntity();
        apientity.setName(cjroneRehabilitationSubsidy.getName());
        apientity.setIdCard(cjroneRehabilitationSubsidy.getIdCard());
        apiCardIdService.save(apientity);

        return R.ok();
    }


    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("cjrone:cjronerehabilitationsubsidy:update")
    public R update(@RequestBody CjroneRehabilitationSubsidyEntity cjroneRehabilitationSubsidy){
        CjroneRehabilitationSubsidyEntity entity = cjroneRehabilitationSubsidyService.getById(cjroneRehabilitationSubsidy.getId());
        System.out.println(new Gson().toJson(entity));
        if (entity != null){
            if (getUser().getRoleName() != null && getUser().getRoleName().indexOf("区残联") != -1){
                if ("6".equals(entity.getSignStatus())){
                    if (!"3".equals(entity.getSignatureStatus())){
                        return R.error().put("code",100).put("msg","审核未通过，区残联未电子公章！").put("applyId",cjroneRehabilitationSubsidy.getId());
                    }else {
                        System.out.println("aaaaaaa");
                    }
                }else {
                    return R.error().put("code",100).put("msg","审核未通过，区残联未手签！").put("applyId",cjroneRehabilitationSubsidy.getId());
                }
            }else if (getUser().getRoleName() != null && getUser().getRoleName().indexOf("民政") != -1){
                if ("8".equals(entity.getSignStatus())){
                    if (!"4".equals(entity.getSignatureStatus())){
                        return R.error().put("code",100).put("msg","审核未通过，民政未电子公章！").put("applyId",cjroneRehabilitationSubsidy.getId());
                    }else {
                        System.out.println("aaaaaaa");
                    }
                }else {
                    return R.error().put("code",100).put("msg","审核未通过，民政未手签！").put("applyId",cjroneRehabilitationSubsidy.getId());
                }
            }
        }
		cjroneRehabilitationSubsidyService.updateById(cjroneRehabilitationSubsidy);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("cjrone:cjronerehabilitationsubsidy:delete")
    public R delete(@RequestBody String[] names){
		cjroneRehabilitationSubsidyService.removeByIds(Arrays.asList(names));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    // @RequiresPermissions("cjrone:cjronerehabilitationsubsidy:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<CjroneRehabilitationSubsidyEntity> cjroneRehabilitationSubsidyList = new ArrayList<>();
        list.forEach(item ->{
                    item.put("name",item.get("残疾人姓名"));
                    item.put("sex",item.get("性别  1 男  2女"));
                    item.put("nationality",item.get("民族"));
                    item.put("birthday",item.get("出生日期"));
                    item.put("idCard",item.get("身份证号码"));
                    item.put("disableId",item.get("残疾证号"));
                    item.put("disabilityCategory",item.get("残疾类别"));
                    item.put("disabilityDegree",item.get("残疾等级"));
                    item.put("presentZhen",item.get("现住址 镇"));
                    item.put("presentCun",item.get("现地址  村"));
                    item.put("presentAddress",item.get("现地址"));
                    item.put("contactPhone",item.get("联系电话"));
                    item.put("mobilePhone",item.get("手机号"));
                    item.put("familyEconoCondition",item.get("家庭经济情况"));
                    item.put("medicalInsurance",item.get("医疗保险情况"));
                    item.put("rehabilitationProject",item.get("康复需求项目"));
                    item.put("applicationDate",item.get("申请日期"));
                    item.put("auditPerson",item.get("审核人"));
                    item.put("auditDate",item.get("审核日期"));
                    item.put("status",item.get("状态"));
                    item.put("createId",item.get("创建人编号"));
                    item.put("createTime",item.get("创建时间"));
                    cjroneRehabilitationSubsidyList.add(new Gson().fromJson(new Gson().toJson(item), CjroneRehabilitationSubsidyEntity.class));
        });
        // 保存到数据库
        cjroneRehabilitationSubsidyService.saveBatch(cjroneRehabilitationSubsidyList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    // @RequiresPermissions("cjrone:cjronerehabilitationsubsidy:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<CjroneRehabilitationSubsidyEntity> cjroneRehabilitationSubsidyEntityList = cjroneRehabilitationSubsidyService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("康复补助申请", null, "康复补助申请");
        Workbook workbook = ExcelExportUtil.exportExcel(params, CjroneRehabilitationSubsidyEntity.class, cjroneRehabilitationSubsidyEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "康复补助申请" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }


    /**
     * 生成电子签章 pdf
     */
    @RequestMapping("/printPDF/{id}")
    public R printPDF(@PathVariable("id") Integer id,@RequestParam("type") String type) throws IOException {
        Map<String, Object> tmp_params = new HashMap<>();
        tmp_params.put("type","惠残事项康复补助");
        tmp_params.put("status","1");
        tmp_params.put("type_id",id);
        List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
        if (alive_list.size()>0) {
            CjroneSignatureEntity cjroneSignatureEntity = alive_list.get(0);
            if ("电子公章".equals(type)){
                String tmp_file_name = System.currentTimeMillis()+"_"+cjroneSignatureEntity.getFileName();
                Files.copy(new File(cjroneSignatureEntity.getFileActUrl()).toPath(),new File(cjroneProperties.getTempFilePath()+tmp_file_name).toPath());

                return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", tmp_file_name).put("signId",cjroneSignatureEntity.getId());

            }else {
                return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", cjroneSignatureEntity.getFileName()).put("signId",cjroneSignatureEntity.getId());
            }
        }else {
            //根据编号获得详细信息
            CjroneRehabilitationSubsidyEntity cjroneRehabilitationSubsidyEntity = cjroneRehabilitationSubsidyService.getById(id);

            Calendar now = Calendar.getInstance();
            String nums[] = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};

            // 原pdf模板路径
            String templatePath = cjroneProperties.getTempletePath()+"康复补助模板.pdf";
            // 生成的新文件路径
            String newPDFPath = cjroneProperties.getSignaturePath()+"rehabilitation_subsidy_"+cjroneRehabilitationSubsidyEntity.getIdCard()+".pdf";

            // 获得待生成的实体文件
            Map<String, String> map =new HashMap<String, String>();
            map.put("name",cjroneRehabilitationSubsidyEntity.getName()==null?"":cjroneRehabilitationSubsidyEntity.getName());
            if (cjroneRehabilitationSubsidyEntity.getSex() == 1)
                map.put("sex","男");
            else
                map.put("sex","女");

            map.put("guardianName", cjroneRehabilitationSubsidyEntity.getGuardianName()==null?"":cjroneRehabilitationSubsidyEntity.getGuardianName());
            map.put("guardianPhone", cjroneRehabilitationSubsidyEntity.getGuardianPhone()==null?"":cjroneRehabilitationSubsidyEntity.getGuardianPhone());
            map.put("nationality",cjroneRehabilitationSubsidyEntity.getNationality()==null?"":cjroneRehabilitationSubsidyEntity.getNationality());
            map.put("birthday",cjroneRehabilitationSubsidyEntity.getBirthday()==null?"":cjroneRehabilitationSubsidyEntity.getBirthday());
            map.put("idCard",cjroneRehabilitationSubsidyEntity.getIdCard()==null?"":cjroneRehabilitationSubsidyEntity.getIdCard());
            map.put("disableId",cjroneRehabilitationSubsidyEntity.getDisableId()==null?"":cjroneRehabilitationSubsidyEntity.getDisableId());
            if(cjroneRehabilitationSubsidyEntity.getDisabilityCategory()==null){
                map.put("disabilityType", "");
            }
            else{
                if("1".equals(cjroneRehabilitationSubsidyEntity.getDisabilityCategory())){
                    map.put("disabilityType", "视力残疾");
                }
                else if("2".equals(cjroneRehabilitationSubsidyEntity.getDisabilityCategory())){
                    map.put("disabilityType", "听力残疾");
                }
                else if("3".equals(cjroneRehabilitationSubsidyEntity.getDisabilityCategory())){
                    map.put("disabilityType", "智力残疾");
                }
                else if("4".equals(cjroneRehabilitationSubsidyEntity.getDisabilityCategory())){
                    map.put("disabilityType", "精神残疾");
                }
                else if("5".equals(cjroneRehabilitationSubsidyEntity.getDisabilityCategory())){
                    map.put("disabilityType", "肢体");
                }
                else if("6".equals(cjroneRehabilitationSubsidyEntity.getDisabilityCategory())){
                    map.put("disabilityType", "言语残疾");
                }
                else if("7".equals(cjroneRehabilitationSubsidyEntity.getDisabilityCategory())){
                    map.put("disabilityType", "肢体");
                }
                else{
                    //map.put("disabilityType", "多重残疾");
                    // 多重
                    map.put("disabilityType", cjroneRehabilitationSubsidyEntity.getDisabilityCategory());
                }
            }
            map.put("disabilityDegree",cjroneRehabilitationSubsidyEntity.getDisabilityDegree()==null?"未评":nums[Integer.parseInt(cjroneRehabilitationSubsidyEntity.getDisabilityDegree())] + "级");
            map.put("presentAddress",cjroneRehabilitationSubsidyEntity.getPresentAddress()==null?"":cjroneRehabilitationSubsidyEntity.getPresentAddress());
            map.put("mobilePhone",cjroneRehabilitationSubsidyEntity.getMobilePhone()==null?"":cjroneRehabilitationSubsidyEntity.getMobilePhone());
            map.put("economicSituation",cjroneRehabilitationSubsidyEntity.getFamilyEconoCondition()==null?"":cjroneRehabilitationSubsidyEntity.getFamilyEconoCondition());
            map.put("medicalInsurance",cjroneRehabilitationSubsidyEntity.getMedicalInsurance()==null?"":cjroneRehabilitationSubsidyEntity.getMedicalInsurance());
            map.put("rehabilitationNeedsProject",cjroneRehabilitationSubsidyEntity.getRehabilitationProjectName()==null?"":cjroneRehabilitationSubsidyEntity.getRehabilitationProjectName());
            map.put("applicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
            map.put("year",now.get(Calendar.YEAR)+"");
            // rsEntity.setZhenApplicationDate(now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
            //rsEntity.setShiApplicationDate(now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
            //rsEntity.setServiceOrganizationRecord("根据康复需求评估得到项目实施，康复专项补贴：￥_____ 元");
            // rsEntity.setServiceOrganizationRecordDate(now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");

            FileOutputStream out;
            int num = 1;//页数
            ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
            try {
                out = new FileOutputStream(newPDFPath);// 输出流
                Document doc = new Document();   //新建一个文档
                PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                doc.open();

                for (int i = 0; i < num; i++) {
                    bos[i] = new ByteArrayOutputStream();
                    PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                    PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                    AcroFields form = stamper.getAcroFields(); //获取文本域
                    // BaseFont
                    // 1、使用iTextAsian.jar中的字体
                    // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                    // 2、使用Windows系统字体(TrueType)
                    // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                    // 3、使用资源字体(ClassPath)
                    // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                    BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                    java.util.Iterator<String> it = form.getFields().keySet().iterator();
                    while (it.hasNext()) {
                        String name = it.next();
                        System.out.println(name + ":"+map.get(name));
                        form.setFieldProperty(name,"textfont",font,null);
                        // form.addSubstitutionFont(font);
                        form.setField(name, map.get(name));
                    }

                    stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                    stamper.close();

                }
                PdfImportedPage page = null;
                for (int i = 0; i < num; i++) {
                    page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                    copy.addPage(page);
                }
                doc.close();
                out.close();
            } catch (IOException e) {
                System.out.println("导出异常");
            } catch (DocumentException e) {
                System.out.println("文档异常");
            }


            // 生成电子签章pdf 并保存
            //String filePath = pdfUtils.pdfRehabilitationSubsidyApplyToSignature(cjroneProperties.getTempletePath(), cjroneProperties.getSignaturePath(), rsEntity);
            CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
            cjroneSignature.setUrl("/rehabilitation_subsidy_" + cjroneRehabilitationSubsidyEntity.getIdCard()+".pdf");
            cjroneSignature.setFileActUrl(newPDFPath);
            cjroneSignature.setCreateDate(new Date());
            cjroneSignature.setCreateId(getUserId());
            cjroneSignature.setType("惠残事项康复补助");
            cjroneSignature.setTypeId(cjroneRehabilitationSubsidyEntity.getId());
            cjroneSignature.setFileName("rehabilitation_subsidy_"+cjroneRehabilitationSubsidyEntity.getIdCard()+".pdf");
            cjroneSignature.setAccountId(getUserId().toString());
            cjroneSignature.setAccountName(getUser().getUsername());
            cjroneSignature.setStatus("1");

            cjroneSignatureService.save(cjroneSignature);
            // return R.ok().put("fileName", "signature_"+ PinYinUtil.getFullSpell(cjroneDisabilityHospital.getName())+".pdf").put("signId",cjroneSignature.getId());
            return R.ok().put("fileUrl", cjroneSignature.getUrl()).put("fileName", cjroneSignature.getFileName()).put("signId",cjroneSignature.getId());


        }

    }


}
