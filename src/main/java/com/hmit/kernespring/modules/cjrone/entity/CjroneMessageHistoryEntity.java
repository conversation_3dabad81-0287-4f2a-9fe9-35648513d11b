package com.hmit.kernespring.modules.cjrone.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-06-28 14:53:50
 */
@Data
@TableName("cjrone_message_history")
public class CjroneMessageHistoryEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
@TableId
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 接收人姓名
	 */
	@Excel(name = "接收人姓名", height = 20, width = 30, isImportField = "true_st")
private String receiveName;
	/**
	 * 接收人身份证
	 */
	@Excel(name = "接收人身份证", height = 20, width = 30, isImportField = "true_st")
private String receiveIdCard;
	/**
	 * 短信内容
	 */
	@Excel(name = "短信内容", height = 20, width = 30, isImportField = "true_st")
private String messageContent;
	/**
	 * 事项类型
	 */
	@Excel(name = "事项类型", height = 20, width = 30, isImportField = "true_st")
private String matterType;
	/**
	 * 发送时间
	 */
	@Excel(name = "发送时间", height = 20, width = 30, isImportField = "true_st")
private String sendTime;

	/**
	 * 联系电话
	 */
	@Excel(name = "联系电话", height = 20, width = 30, isImportField = "true_st")
	private String mobilePhone;

	/**
	 * 发送类型
	 */
	@Excel(name = "发送类型", height = 20, width = 30, isImportField = "true_st")
	private String sendType;

	/**
	 * 申请记录
	 */
	private Integer applyId;
}
