package com.hmit.kernespring.modules.cjrone.controller;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

import java.io.OutputStream;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.cjrone.constants.CjroneConstants;
import com.hmit.kernespring.modules.cjrone.entity.CjroneAdministrativeDivisionEntity;
import com.hmit.kernespring.modules.cjrone.entity.CjroneChildrenRehabilitationSubsidyEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneChildrenRehabilitationSubsidyService;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import com.hmit.kernespring.modules.sys.entity.SysUserEntity;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;
import com.hmit.kernespring.modules.sys.controller.AbstractController;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 残疾少年儿童康复训练补助
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-10 11:18:02
 */
@RestController
@RequestMapping("matter_application/cjronechildrenrehabilitationsubsidy")
public class CjroneChildrenRehabilitationSubsidyController extends AbstractController {
    @Autowired
    private CjroneChildrenRehabilitationSubsidyService cjroneChildrenRehabilitationSubsidyService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;
    @Autowired
    private com.hmit.kernespring.modules.cjrone.service.CjroneStreetService cjroneStreetService;

    /**
     * 列表
     */
    @RequestMapping("/list")
//    @RequiresPermissions("matter_application:cjronechildrenrehabilitationsubsidy:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = cjroneChildrenRehabilitationSubsidyService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
//    @RequiresPermissions("matter_application:cjronechildrenrehabilitationsubsidy:info")
    public R info(@PathVariable("id") Long id){
		CjroneChildrenRehabilitationSubsidyEntity cjroneChildrenRehabilitationSubsidy = cjroneChildrenRehabilitationSubsidyService.getById(id);

        return R.ok().put("cjroneChildrenRehabilitationSubsidy", cjroneChildrenRehabilitationSubsidy);
    }

    /**
     * 根据身份证查询历史记录（按时间倒序）
     */
    @RequestMapping("/historyByIdCard")
//    @RequiresPermissions("matter_application:cjronechildrenrehabilitationsubsidy:list")
    public R historyByIdCard(@RequestParam String idCard, @RequestParam(required = false) Integer status, @RequestParam(required = false) String subsidyYear) {
        if (StringUtils.isBlank(idCard)) {
            return R.error("idCard不能为空");
        }
        List<CjroneChildrenRehabilitationSubsidyEntity> historyList =
                cjroneChildrenRehabilitationSubsidyService.queryHistoryByIdCard(idCard, status, subsidyYear);
        return R.ok().put("historyList", historyList);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
//    @RequiresPermissions("matter_application:cjronechildrenrehabilitationsubsidy:save")
    public R save(@RequestBody CjroneChildrenRehabilitationSubsidyEntity cjroneChildrenRehabilitationSubsidy){
		cjroneChildrenRehabilitationSubsidyService.save(cjroneChildrenRehabilitationSubsidy);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
//    @RequiresPermissions("matter_application:cjronechildrenrehabilitationsubsidy:update")
    public R update(@RequestBody CjroneChildrenRehabilitationSubsidyEntity cjroneChildrenRehabilitationSubsidy) {
        CjroneChildrenRehabilitationSubsidyEntity entity = cjroneChildrenRehabilitationSubsidyService.getById(cjroneChildrenRehabilitationSubsidy.getId());
        if (entity == null) return R.error("数据不存在");

        SysUserEntity user = getUser();
        if (user.getRoleName() != null && user.getRoleName().contains("区残联")) {
//            if(!Objects.equals(user.getZhen(),entity.getNativeZhen())){
//                return R.error(String.format("街道不匹配，当前用户街道是%s,审核的数据街道是%s",user.getZhen(),entity.getNativeZhen()));
//            }

            String frontendStatus = cjroneChildrenRehabilitationSubsidy.getStatus();
            if ("8".equals(frontendStatus)) {
                cjroneChildrenRehabilitationSubsidy.setStatus("8");
            } else {
                cjroneChildrenRehabilitationSubsidy.setStatus("12");
            }
        } else return R.error("当前用户角色不是区残联，无法进行审核");

        try {
            cjroneChildrenRehabilitationSubsidyService.updateAudioById(cjroneChildrenRehabilitationSubsidy);
        } catch (UnsupportedOperationException e) {
            return R.error(e.getMessage());
        }
        return R.ok();
       
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
//    @RequiresPermissions("matter_application:cjronechildrenrehabilitationsubsidy:delete")
    public R delete(@RequestBody Long[] ids){
		cjroneChildrenRehabilitationSubsidyService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
//    @RequiresPermissions("matter_application:cjronechildrenrehabilitationsubsidy:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<CjroneChildrenRehabilitationSubsidyEntity> cjroneChildrenRehabilitationSubsidyList = new ArrayList<>();
        list.forEach(item ->{
                    item.put("id",item.get("ID, 自增主键"));
                    item.put("welfareMatterApplicationId",item.get("惠残事项申请ID"));
                    item.put("attach",item.get("附件"));
                    item.put("yearTotalAmount",item.get("年度累计金额"));
                    item.put("no",item.get("序号"));
                    item.put("date",item.get("日期"));
                    item.put("rehabilitationCenterName",item.get("康复机构名称"));
                    item.put("designatedInstitution",item.get("定点机构名称"));
                    item.put("rehabilitationType",item.get("康复类型"));
                    item.put("subsidyStandard",item.get("补助标准"));
                    item.put("subsidyYear",item.get("补助年份"));
                    item.put("subsidyMonth",item.get("补助月份"));
                    item.put("actualInvoiceAmount",item.get("实际发票金额"));
                    item.put("districtDisabilityAuditAmount",item.get("区残联审核金额"));
                    item.put("operatorId",item.get("经办人ID"));
                    item.put("operatorName",item.get("经办人姓名"));
                    item.put("fundsSource",item.get("经费来源"));
                    item.put("applyRemark",item.get("申请备注"));
                    item.put("approvalRemark",item.get("审批备注"));
                    item.put("createTime",item.get("创建时间"));
                    item.put("auditorId",item.get("审核人ID"));
                    item.put("auditorName",item.get("审核人姓名"));
                    cjroneChildrenRehabilitationSubsidyList.add(new Gson().fromJson(new Gson().toJson(item), CjroneChildrenRehabilitationSubsidyEntity.class));
        });
        // 保存到数据库
        cjroneChildrenRehabilitationSubsidyService.saveBatch(cjroneChildrenRehabilitationSubsidyList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
//    @RequiresPermissions("matter_application:cjronechildrenrehabilitationsubsidy:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<CjroneChildrenRehabilitationSubsidyEntity> cjroneChildrenRehabilitationSubsidyEntityList = cjroneChildrenRehabilitationSubsidyService.queryExportData(mapArgs);

        cjroneChildrenRehabilitationSubsidyEntityList.forEach(item ->{
            //开始处理性别
            if("1".equals(item.getSex())){
                item.setSexName("男");
            }else if("0".equals(item.getSex())){
                item.setSexName("女");
            }

            //处理出生日期格式转换
            if(item.getBirthday() != null && item.getBirthday().contains("T")) {
                try {
                    // 如果是ISO 8601格式，转换为yyyy-MM-dd格式
                    String[] parts = item.getBirthday().split("T");
                    if(parts.length > 0) {
                        item.setBirthday(parts[0]);  // 只取日期部分
                    }
                } catch (Exception e) {
                    // 如果转换失败，保持原值
                }
            }

            //处理Boolean字段，直接复制值，null值由@Excel注解的replace处理
            item.setDibaoFlagDisplay(item.getDibaoFlag());
            item.setIsOutOfProvinceDisplay(item.getIsOutOfProvince());

            //处理街道编号到名称的映射
            if(item.getNativeZhen() != null && item.getNativeZhen().length() >= 6) {
                String areaCode = item.getNativeZhen().substring(0, 6);
                List<CjroneAdministrativeDivisionEntity> streetList =
                    cjroneStreetService.getAdministrativeDivisionList(areaCode);
                streetList.stream()
                    .filter(s -> item.getNativeZhen().equals(s.getValue()))
                    .findFirst()
                    .ifPresent(street -> item.setNativeZhen(street.getLabel()));
            }
        });

        ExportParams params = new ExportParams("残疾少年儿童康复训练补助", null, "残疾少年儿童康复训练补助");
        Workbook workbook = ExcelExportUtil.exportExcel(params, CjroneChildrenRehabilitationSubsidyEntity.class, cjroneChildrenRehabilitationSubsidyEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "残疾少年儿童康复训练补助" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

    @GetMapping("/statistics")
    public R statistics(@RequestParam (required = false)String approvalYear){
      Map<String, Object> map= cjroneChildrenRehabilitationSubsidyService.statistics(approvalYear);
      return R.ok().put("statistics",map);
    }
}
