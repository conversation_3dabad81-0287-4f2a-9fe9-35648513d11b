package com.hmit.kernespring.modules.cjrone.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone.entity.CjroneFamilyAccessibilityTransformationEntity;

import java.util.Map;

import java.util.List;

/**
 * 家庭无障碍改造
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-27 16:56:18
 */
public interface CjroneFamilyAccessibilityTransformationService extends IService<CjroneFamilyAccessibilityTransformationEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneFamilyAccessibilityTransformationEntity> queryExportData(Map<String, Object> params);
}

