package com.hmit.kernespring.modules.cjrone.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.cjrone.dao.CjroneWelfareApplyDocDao;
import com.hmit.kernespring.modules.cjrone.entity.CjroneWelfareApplyDocEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneWelfareApplyDocService;


@Service("cjroneWelfareApplyDocService")
public class CjroneWelfareApplyDocServiceImpl extends ServiceImpl<CjroneWelfareApplyDocDao, CjroneWelfareApplyDocEntity> implements CjroneWelfareApplyDocService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneWelfareApplyDocDao cjroneWelfareApplyDocDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneWelfareApplyDocEntity cjroneWelfareApplyDocEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneWelfareApplyDocEntity.class);
        IPage<CjroneWelfareApplyDocEntity> page = this.page(
                new Query<CjroneWelfareApplyDocEntity>().getPage(params),
                new QueryWrapper<CjroneWelfareApplyDocEntity>()
            .eq(StringUtils.isNotBlank(cjroneWelfareApplyDocEntity.getId ()!=null && !"".equals(cjroneWelfareApplyDocEntity.getId ().toString())? cjroneWelfareApplyDocEntity.getId ().toString():null),"id", cjroneWelfareApplyDocEntity.getId ())
            .eq(StringUtils.isNotBlank(cjroneWelfareApplyDocEntity.getWelfareMatterApplicationId ()!=null && !"".equals(cjroneWelfareApplyDocEntity.getWelfareMatterApplicationId ().toString())? cjroneWelfareApplyDocEntity.getWelfareMatterApplicationId ().toString():null),"welfare_matter_application_id", cjroneWelfareApplyDocEntity.getWelfareMatterApplicationId ())
            .eq(StringUtils.isNotBlank(cjroneWelfareApplyDocEntity.getDocumentId ()!=null && !"".equals(cjroneWelfareApplyDocEntity.getDocumentId ().toString())? cjroneWelfareApplyDocEntity.getDocumentId ().toString():null),"document_id", cjroneWelfareApplyDocEntity.getDocumentId ())
        );
        Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key","sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        page.getRecords().forEach( item -> {
        });
        return new PageUtils(page);
    }
    @Override
    public List<CjroneWelfareApplyDocEntity> queryExportData(Map<String, Object> params) {
            return cjroneWelfareApplyDocDao.queryExportData(params);
    }

}