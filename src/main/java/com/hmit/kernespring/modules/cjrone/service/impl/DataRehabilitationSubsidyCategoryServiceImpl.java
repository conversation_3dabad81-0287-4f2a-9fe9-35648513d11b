package com.hmit.kernespring.modules.cjrone.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.common.utils.Constant;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.entity.SysMenuEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.cjrone.dao.DataRehabilitationSubsidyCategoryDao;
import com.hmit.kernespring.modules.cjrone.entity.DataRehabilitationSubsidyCategoryEntity;
import com.hmit.kernespring.modules.cjrone.service.DataRehabilitationSubsidyCategoryService;


@Service("dataRehabilitationSubsidyCategoryService")
public class DataRehabilitationSubsidyCategoryServiceImpl extends ServiceImpl<DataRehabilitationSubsidyCategoryDao, DataRehabilitationSubsidyCategoryEntity> implements DataRehabilitationSubsidyCategoryService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private DataRehabilitationSubsidyCategoryDao dataRehabilitationSubsidyCategoryDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        DataRehabilitationSubsidyCategoryEntity dataRehabilitationSubsidyCategoryEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, DataRehabilitationSubsidyCategoryEntity.class);
        IPage<DataRehabilitationSubsidyCategoryEntity> page = this.page(
                new Query<DataRehabilitationSubsidyCategoryEntity>().getPage(params),
                new QueryWrapper<DataRehabilitationSubsidyCategoryEntity>()
            .eq(StringUtils.isNotBlank(dataRehabilitationSubsidyCategoryEntity.getCategoryId ()!=null && !"".equals(dataRehabilitationSubsidyCategoryEntity.getCategoryId ().toString())? dataRehabilitationSubsidyCategoryEntity.getCategoryId ().toString():null),"category_id", dataRehabilitationSubsidyCategoryEntity.getCategoryId ())
            .eq(StringUtils.isNotBlank(dataRehabilitationSubsidyCategoryEntity.getParentId ()!=null && !"".equals(dataRehabilitationSubsidyCategoryEntity.getParentId ().toString())? dataRehabilitationSubsidyCategoryEntity.getParentId ().toString():null),"parent_id", dataRehabilitationSubsidyCategoryEntity.getParentId ())
            .eq(StringUtils.isNotBlank(dataRehabilitationSubsidyCategoryEntity.getName ()!=null && !"".equals(dataRehabilitationSubsidyCategoryEntity.getName ().toString())? dataRehabilitationSubsidyCategoryEntity.getName ().toString():null),"name", dataRehabilitationSubsidyCategoryEntity.getName ())
            .eq(StringUtils.isNotBlank(dataRehabilitationSubsidyCategoryEntity.getCreateTime ()!=null && !"".equals(dataRehabilitationSubsidyCategoryEntity.getCreateTime ().toString())? dataRehabilitationSubsidyCategoryEntity.getCreateTime ().toString():null),"create_time", dataRehabilitationSubsidyCategoryEntity.getCreateTime ())
            .eq(StringUtils.isNotBlank(dataRehabilitationSubsidyCategoryEntity.getCreateId ()!=null && !"".equals(dataRehabilitationSubsidyCategoryEntity.getCreateId ().toString())? dataRehabilitationSubsidyCategoryEntity.getCreateId ().toString():null),"create_id", dataRehabilitationSubsidyCategoryEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(dataRehabilitationSubsidyCategoryEntity.getConditionName ()!=null && !"".equals(dataRehabilitationSubsidyCategoryEntity.getConditionName ().toString())? dataRehabilitationSubsidyCategoryEntity.getConditionName ().toString():null),"condition_name", dataRehabilitationSubsidyCategoryEntity.getConditionName ())
            .eq(StringUtils.isNotBlank(dataRehabilitationSubsidyCategoryEntity.getServiceStandard ()!=null && !"".equals(dataRehabilitationSubsidyCategoryEntity.getServiceStandard ().toString())? dataRehabilitationSubsidyCategoryEntity.getServiceStandard ().toString():null),"service_standard", dataRehabilitationSubsidyCategoryEntity.getServiceStandard ())
            .eq(StringUtils.isNotBlank(dataRehabilitationSubsidyCategoryEntity.getMemo ()!=null && !"".equals(dataRehabilitationSubsidyCategoryEntity.getMemo ().toString())? dataRehabilitationSubsidyCategoryEntity.getMemo ().toString():null),"memo", dataRehabilitationSubsidyCategoryEntity.getMemo ())
            .eq(StringUtils.isNotBlank(dataRehabilitationSubsidyCategoryEntity.getLeafnode ()!=null && !"".equals(dataRehabilitationSubsidyCategoryEntity.getLeafnode ().toString())? dataRehabilitationSubsidyCategoryEntity.getLeafnode ().toString():null),"leafnode", dataRehabilitationSubsidyCategoryEntity.getLeafnode ())
        );
        Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key","sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        page.getRecords().forEach( item -> {
            SysDictEntity categoryId_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("kfbzlbbh_0000") && iii.getValue().equals(
                        item.getCategoryId ())).findAny().orElse(null);
            if (categoryId_sysDictEntity != null){
                item.setCategoryId (Integer.parseInt(categoryId_sysDictEntity.getLabel().toString()));
            }else{
                item.setCategoryId (null);
            }
            SysDictEntity conditionName_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("tjjlbmc_0000") && iii.getValue().equals(
                        item.getConditionName ())).findAny().orElse(null);
            if (conditionName_sysDictEntity != null){
                item.setConditionName (conditionName_sysDictEntity.getLabel());
            }else{
                item.setConditionName (null);
            }
        });
        return new PageUtils(page);
    }
    @Override
    public List<DataRehabilitationSubsidyCategoryEntity> queryExportData(Map<String, Object> params) {
            return dataRehabilitationSubsidyCategoryDao.queryExportData(params);
    }

    @Override
    public List<DataRehabilitationSubsidyCategoryEntity> getRehabilitationSubsidyCategoryList(Map<String, Object> params) {
        //查询根类别列表
        List<DataRehabilitationSubsidyCategoryEntity> rsList = dataRehabilitationSubsidyCategoryDao.queryListByParentId(0);

        //获得两级下拉
        for(DataRehabilitationSubsidyCategoryEntity entity : rsList){
            List<DataRehabilitationSubsidyCategoryEntity> sonrsList = dataRehabilitationSubsidyCategoryDao.queryListByParentId(entity.getValue());
            for(DataRehabilitationSubsidyCategoryEntity sonentity:sonrsList){
                List<DataRehabilitationSubsidyCategoryEntity> grandsonrsList= dataRehabilitationSubsidyCategoryDao.queryListByParentId(sonentity.getValue());
                sonentity.setChildren(grandsonrsList);
            }
            entity.setChildren(sonrsList);
        }

        return rsList;
    }





}