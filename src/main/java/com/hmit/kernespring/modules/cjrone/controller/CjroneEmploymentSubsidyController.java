package com.hmit.kernespring.modules.cjrone.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.cjrone.entity.CjroneEmploymentSubsidyEntity;
import com.hmit.kernespring.modules.cjrone.entity.CjroneSignatureEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneEmploymentSubsidyService;
import com.hmit.kernespring.modules.cjrone.service.CjroneSignatureService;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import com.hmit.kernespring.modules.sys.controller.AbstractController;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.*;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 就业创业补助
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-06-24 09:40:30
 */
@RestController
@RequestMapping("cjrone/cjroneemploymentsubsidy")
public class CjroneEmploymentSubsidyController extends AbstractController {
    @Autowired
    private CjroneEmploymentSubsidyService cjroneEmploymentSubsidyService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;
    @Autowired
    private CjroneSignatureService cjroneSignatureService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("cjrone:cjroneemploymentsubsidy:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = cjroneEmploymentSubsidyService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("cjrone:cjroneemploymentsubsidy:info")
    public R info(@PathVariable("id") Integer id){
		CjroneEmploymentSubsidyEntity cjroneEmploymentSubsidy = cjroneEmploymentSubsidyService.getById(id);

        return R.ok().put("cjroneEmploymentSubsidy", cjroneEmploymentSubsidy);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("cjrone:cjroneemploymentsubsidy:save")
    public R save(@RequestBody CjroneEmploymentSubsidyEntity cjroneEmploymentSubsidy){
		cjroneEmploymentSubsidyService.save(cjroneEmploymentSubsidy);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("cjrone:cjroneemploymentsubsidy:update")
    public R update(@RequestBody CjroneEmploymentSubsidyEntity cjroneEmploymentSubsidy){
		cjroneEmploymentSubsidyService.updateById(cjroneEmploymentSubsidy);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("cjrone:cjroneemploymentsubsidy:delete")
    public R delete(@RequestBody Integer[] ids){
		cjroneEmploymentSubsidyService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    @RequiresPermissions("cjrone:cjroneemploymentsubsidy:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<CjroneEmploymentSubsidyEntity> cjroneEmploymentSubsidyList = new ArrayList<>();
        list.forEach(item ->{
                    item.put("id",item.get(""));
                    item.put("name",item.get("姓名"));
                    item.put("idCard",item.get("身份证号"));
                    item.put("familyEconoCondition",item.get("家庭经济情况"));
                    item.put("sex",item.get("性别"));
                    item.put("educationDegree",item.get("文化程度"));
                    item.put("disabilityType",item.get("残疾类别"));
                    item.put("telephone",item.get("联系电话"));
                    item.put("presentAddress",item.get("现地址"));
                    item.put("managementType",item.get("经营类型  "));
                    item.put("managementAddress",item.get("经营地址"));
                    item.put("subsidyMoney",item.get("申请补助金额"));
                    cjroneEmploymentSubsidyList.add(new Gson().fromJson(new Gson().toJson(item), CjroneEmploymentSubsidyEntity.class));
        });
        // 保存到数据库
        cjroneEmploymentSubsidyService.saveBatch(cjroneEmploymentSubsidyList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    @RequiresPermissions("cjrone:cjroneemploymentsubsidy:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<CjroneEmploymentSubsidyEntity> cjroneEmploymentSubsidyEntityList = cjroneEmploymentSubsidyService.queryExportData(mapArgs);
        //对残疾类别进行处理
        cjroneEmploymentSubsidyEntityList.stream().forEach(item -> {
            if(item.getDisabilityType()==1){
                item.setDisabilityTypeName("视力");
            }else if(item.getDisabilityType()==2){
                item.setDisabilityTypeName("听力");
            }else if(item.getDisabilityType()==3){
                item.setDisabilityTypeName("智力");
            }else if(item.getDisabilityType()==4){
                item.setDisabilityTypeName("精神");
            }else if(item.getDisabilityType()==5){
                item.setDisabilityTypeName("肢体");
            }else if(item.getDisabilityType()==6){
                item.setDisabilityTypeName("言语");
            }else if(item.getDisabilityType()==7){
                item.setDisabilityTypeName("肢体");
            }

            if(item.getSex()==1){
                item.setSexName("男");
            }else{
                item.setSexName("女");
            }

        });


        ExportParams params = new ExportParams("就业创业补助", null, "就业创业补助");
        Workbook workbook = ExcelExportUtil.exportExcel(params, CjroneEmploymentSubsidyEntity.class, cjroneEmploymentSubsidyEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "就业创业补助" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }


    //计算年龄
    public static int IdNOToAge(String IdNO){
        int leh = IdNO.length();
        String dates="";
        if (leh == 18) {
            dates = IdNO.substring(6, 10);
            SimpleDateFormat df = new SimpleDateFormat("yyyy");
            String year=df.format(new Date());
            int u=Integer.parseInt(year)-Integer.parseInt(dates);
            return u;
        }else{
            dates = IdNO.substring(6, 8);
            return Integer.parseInt(dates);
        }

    }


    /**
      *  打印pdf
     */
    @RequestMapping("/printPDF/{id}")
    public R printPDF(@PathVariable("id") Integer id,@RequestParam("type") String type) throws IOException {
        Map<String, Object> tmp_params = new HashMap<>();
        tmp_params.put("type","惠残事项就业创业补助");
        tmp_params.put("status","1");
        tmp_params.put("type_id",id);
        List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
        if (alive_list.size()>0) {
            CjroneSignatureEntity cjroneSignatureEntity = alive_list.get(0);
            if ("电子公章".equals(type)){
                String tmp_file_name = System.currentTimeMillis()+"_"+cjroneSignatureEntity.getFileName();
                Files.copy(new File(cjroneSignatureEntity.getFileActUrl()).toPath(),new File(cjroneProperties.getTempFilePath()+tmp_file_name).toPath());

                return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", tmp_file_name).put("signId",cjroneSignatureEntity.getId());

            }else {
                return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", cjroneSignatureEntity.getFileName()).put("signId",cjroneSignatureEntity.getId());
            }
        }else {
            //根据编号获得详细信息
            CjroneEmploymentSubsidyEntity cjroneEmploymentSubsidyEntity = cjroneEmploymentSubsidyService.getById(id);

            Calendar now = Calendar.getInstance();
            String nums[] = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};

            // 原pdf模板路径
            String templatePath = cjroneProperties.getTempletePath()+"就业创业补助模板.pdf";
            // 生成的新文件路径
            String newPDFPath = cjroneProperties.getSignaturePath()+"employment_subsidy_"+cjroneEmploymentSubsidyEntity.getIdCard()+".pdf";

            // 获得待生成的实体文件
            Map<String, String> map =new HashMap<String, String>();
            map.put("name",cjroneEmploymentSubsidyEntity.getName()==null?"":cjroneEmploymentSubsidyEntity.getName());
            if (cjroneEmploymentSubsidyEntity.getSex() == 1)
                map.put("sexName","男");
            else
                map.put("sexName","女");

            //计算年龄
            Integer age=IdNOToAge(cjroneEmploymentSubsidyEntity.getIdCard());
            map.put("age",age.toString());

            map.put("educationDegree",cjroneEmploymentSubsidyEntity.getEducationDegree());
            map.put("disableId",cjroneEmploymentSubsidyEntity.getDisableId()==null?"":cjroneEmploymentSubsidyEntity.getDisableId());


            if(cjroneEmploymentSubsidyEntity.getDisabilityType()==null){
                map.put("disabilityTypeName", "");
            }
            else{
                if("1".equals(cjroneEmploymentSubsidyEntity.getDisabilityType().toString())){
                    map.put("disabilityTypeName", "视力残疾");
                }
                else if("2".equals(cjroneEmploymentSubsidyEntity.getDisabilityType().toString())){
                    map.put("disabilityTypeName", "听力残疾");
                }
                else if("3".equals(cjroneEmploymentSubsidyEntity.getDisabilityType().toString())){
                    map.put("disabilityTypeName", "智力残疾");
                }
                else if("4".equals(cjroneEmploymentSubsidyEntity.getDisabilityType().toString())){
                    map.put("disabilityTypeName", "精神残疾");
                }
                else if("5".equals(cjroneEmploymentSubsidyEntity.getDisabilityType().toString())){
                    map.put("disabilityTypeName", "肢体（神经系统疾病致残）");
                }
                else if("6".equals(cjroneEmploymentSubsidyEntity.getDisabilityType().toString())){
                    map.put("disabilityTypeName", "言语残疾");
                }
                else if("7".equals(cjroneEmploymentSubsidyEntity.getDisabilityType().toString())){
                    map.put("disabilityTypeName", "肢体（非神经系统疾病致残）");
                }
                else{
                    map.put("disabilityTypeName", "多重残疾");
                }
            }

            map.put("telephone",cjroneEmploymentSubsidyEntity.getTelephone()==null?"":cjroneEmploymentSubsidyEntity.getTelephone());
            map.put("presentAddress",cjroneEmploymentSubsidyEntity.getPresentAddress()==null?"":cjroneEmploymentSubsidyEntity.getPresentAddress());
            String employeeType = cjroneEmploymentSubsidyEntity.getEmployeeType() != null ? " -- " + cjroneEmploymentSubsidyEntity.getEmployeeType() : "";
            map.put("managementType",cjroneEmploymentSubsidyEntity.getManagementType()==null?"":cjroneEmploymentSubsidyEntity.getManagementType() + employeeType);
            map.put("managementAddress",cjroneEmploymentSubsidyEntity.getManagementAddress()==null?"":cjroneEmploymentSubsidyEntity.getManagementAddress());
            map.put("subsidyMoney",cjroneEmploymentSubsidyEntity.getSubsidyMoney()==null?"":cjroneEmploymentSubsidyEntity.getSubsidyMoney());
            map.put("guimo",cjroneEmploymentSubsidyEntity.getGuimo()==null?"":cjroneEmploymentSubsidyEntity.getGuimo());
            map.put("applyReason",cjroneEmploymentSubsidyEntity.getApplyReason()==null?"":cjroneEmploymentSubsidyEntity.getApplyReason());
            map.put("applicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
            map.put("zhenApplicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
            map.put("quApplicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
            // rsEntity.setZhenApplicationDate(now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
            //rsEntity.setShiApplicationDate(now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
            //rsEntity.setServiceOrganizationRecord("根据康复需求评估得到项目实施，康复专项补贴：￥_____ 元");
            // rsEntity.setServiceOrganizationRecordDate(now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");

            FileOutputStream out;
            int num = 1;//页数
            ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
            try {
                out = new FileOutputStream(newPDFPath);// 输出流
                Document doc = new Document();   //新建一个文档
                PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                doc.open();

                for (int i = 0; i < num; i++) {
                    bos[i] = new ByteArrayOutputStream();
                    PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                    PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                    AcroFields form = stamper.getAcroFields(); //获取文本域
                    // BaseFont
                    // 1、使用iTextAsian.jar中的字体
                    // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                    // 2、使用Windows系统字体(TrueType)
                    // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                    // 3、使用资源字体(ClassPath)
                    // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                    BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                    java.util.Iterator<String> it = form.getFields().keySet().iterator();
                    while (it.hasNext()) {
                        String name = it.next();
                        System.out.println(name + ":"+map.get(name));
                        form.setFieldProperty(name,"textfont",font,null);
                        // form.addSubstitutionFont(font);
                        form.setField(name, map.get(name));
                    }

                    stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                    stamper.close();

                }
                PdfImportedPage page = null;
                for (int i = 0; i < num; i++) {
                    page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                    copy.addPage(page);
                }
                doc.close();
                out.close();
            } catch (IOException e) {
                e.printStackTrace();
                System.out.println("导出异常");
            } catch (DocumentException e) {
                System.out.println("文档异常");
            }


            // 生成电子签章pdf 并保存
            //String filePath = pdfUtils.pdfRehabilitationSubsidyApplyToSignature(cjroneProperties.getTempletePath(), cjroneProperties.getSignaturePath(), rsEntity);
            CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
            cjroneSignature.setUrl("/employment_subsidy_" + cjroneEmploymentSubsidyEntity.getIdCard()+".pdf");
            cjroneSignature.setFileActUrl(newPDFPath);
            cjroneSignature.setCreateDate(new Date());
            cjroneSignature.setCreateId(getUserId());
            cjroneSignature.setType("就业创业补助");
            cjroneSignature.setTypeId(cjroneEmploymentSubsidyEntity.getId());
            cjroneSignature.setFileName("employment_subsidy_"+cjroneEmploymentSubsidyEntity.getIdCard()+".pdf");
            cjroneSignature.setAccountId(getUserId().toString());
            cjroneSignature.setAccountName(getUser().getUsername());
            cjroneSignature.setStatus("1");

            cjroneSignatureService.save(cjroneSignature);
            // return R.ok().put("fileName", "signature_"+ PinYinUtil.getFullSpell(cjroneDisabilityHospital.getName())+".pdf").put("signId",cjroneSignature.getId());
            return R.ok().put("fileUrl", cjroneSignature.getUrl()).put("fileName", cjroneSignature.getFileName()).put("signId",cjroneSignature.getId());


        }

    }


    /*
     手签
     */




}
