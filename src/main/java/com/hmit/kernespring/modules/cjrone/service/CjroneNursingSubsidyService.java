package com.hmit.kernespring.modules.cjrone.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone.entity.CjroneNursingSubsidyEntity;

import java.util.Collection;
import java.util.Map;

import java.util.List;

/**
 * 护理补贴申请
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-10 14:28:57
 */
public interface CjroneNursingSubsidyService extends IService<CjroneNursingSubsidyEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneNursingSubsidyEntity> queryExportData(Map<String, Object> params);

    @Override
    boolean saveBatch(Collection<CjroneNursingSubsidyEntity> entityList);
    void enable(CjroneNursingSubsidyEntity entity);
}

