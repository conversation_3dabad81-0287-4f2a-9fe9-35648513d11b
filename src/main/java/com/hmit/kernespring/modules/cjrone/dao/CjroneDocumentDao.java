package com.hmit.kernespring.modules.cjrone.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hmit.kernespring.modules.cjrone.entity.CjroneCertificateApplyDocEntity;
import com.hmit.kernespring.modules.cjrone.entity.CjroneDocumentEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-09 14:03:30
 */
@Mapper
public interface CjroneDocumentDao extends BaseMapper<CjroneDocumentEntity> {
    List<CjroneDocumentEntity> queryExportData(Map<String, Object> params);
	List<CjroneDocumentEntity> queryDocumentDataByMap(Map<String, Object> params);
    List<CjroneDocumentEntity> queryDocumentDataByMapN(Map<String, Object> params);
    List<CjroneCertificateApplyDocEntity> queryDocumentDocDataByMap(Map<String, Object> params);
}
