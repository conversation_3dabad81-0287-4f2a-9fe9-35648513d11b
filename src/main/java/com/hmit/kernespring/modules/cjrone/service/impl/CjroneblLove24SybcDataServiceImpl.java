package com.hmit.kernespring.modules.cjrone.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.cjrone_bl.entity.Love24Entity;
import com.hmit.kernespring.modules.cjrone_bl.service.Love24Service;
import com.hmit.kernespring.modules.matter_application.entity.CjroneWelfareMatterApplicationEntity;
import com.hmit.kernespring.modules.matter_application.service.CjroneWelfareMatterApplicationService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.lang.reflect.Array;
import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.cjrone.dao.CjroneblLove24SybcDataDao;
import com.hmit.kernespring.modules.cjrone.entity.CjroneblLove24SybcDataEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneblLove24SybcDataService;
import org.springframework.transaction.annotation.Transactional;


@Service("cjroneblLove24SybcDataService")
public class CjroneblLove24SybcDataServiceImpl extends ServiceImpl<CjroneblLove24SybcDataDao, CjroneblLove24SybcDataEntity> implements CjroneblLove24SybcDataService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneblLove24SybcDataDao cjroneblLove24SybcDataDao;
    @Autowired
    private Love24Service love24Service;
    @Autowired
    private CjroneWelfareMatterApplicationService cjroneWelfareMatterApplicationService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        System.out.println("PARAMS: "+params);
        CjroneblLove24SybcDataEntity cjroneblLove24SybcDataEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneblLove24SybcDataEntity.class);
        String dead = "";
        String hjqy = "";
        if("1".equals(cjroneblLove24SybcDataEntity.getErrorStatus())){
            dead = "1";
        }

        if("2".equals(cjroneblLove24SybcDataEntity.getErrorStatus())){
            hjqy = "1";
        }
        String startDay = cjroneblLove24SybcDataEntity.getStartDay ()!=null && !"".equals(cjroneblLove24SybcDataEntity.getStartDay ().toString())? cjroneblLove24SybcDataEntity.getStartDay ().toString():null;
        String endDay = cjroneblLove24SybcDataEntity.getEndDay ()!=null && !"".equals(cjroneblLove24SybcDataEntity.getEndDay ().toString())? cjroneblLove24SybcDataEntity.getEndDay ().toString():null;

        IPage<CjroneblLove24SybcDataEntity> page = this.page(
                new Query<CjroneblLove24SybcDataEntity>().getPage(params),
                new QueryWrapper<CjroneblLove24SybcDataEntity>()
            .eq(StringUtils.isNotBlank(cjroneblLove24SybcDataEntity.getId ()!=null && !"".equals(cjroneblLove24SybcDataEntity.getId ().toString())? cjroneblLove24SybcDataEntity.getId ().toString():null),"id", cjroneblLove24SybcDataEntity.getId ())
            .eq(StringUtils.isNotBlank(cjroneblLove24SybcDataEntity.getName ()!=null && !"".equals(cjroneblLove24SybcDataEntity.getName ().toString())? cjroneblLove24SybcDataEntity.getName ().toString():null),"name", cjroneblLove24SybcDataEntity.getName ())
            .eq(StringUtils.isNotBlank(cjroneblLove24SybcDataEntity.getIdCard ()!=null && !"".equals(cjroneblLove24SybcDataEntity.getIdCard ().toString())? cjroneblLove24SybcDataEntity.getIdCard ().toString():null),"id_card", cjroneblLove24SybcDataEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(cjroneblLove24SybcDataEntity.getCreateTime ()!=null && !"".equals(cjroneblLove24SybcDataEntity.getCreateTime ().toString())? cjroneblLove24SybcDataEntity.getCreateTime ().toString():null),"create_time", cjroneblLove24SybcDataEntity.getCreateTime ())
            .eq(StringUtils.isNotBlank(cjroneblLove24SybcDataEntity.getIsDead ()!=null && !"".equals(cjroneblLove24SybcDataEntity.getIsDead ().toString())? cjroneblLove24SybcDataEntity.getIsDead ().toString():null),"is_dead", cjroneblLove24SybcDataEntity.getIsDead ())
            .eq(StringUtils.isNotBlank(cjroneblLove24SybcDataEntity.getStatus ()!=null && !"".equals(cjroneblLove24SybcDataEntity.getStatus ().toString())? cjroneblLove24SybcDataEntity.getStatus ().toString():null),"status", cjroneblLove24SybcDataEntity.getStatus ())
            .eq(StringUtils.isNotBlank(cjroneblLove24SybcDataEntity.getIsDisable ()!=null && !"".equals(cjroneblLove24SybcDataEntity.getIsDisable ().toString())? cjroneblLove24SybcDataEntity.getIsDisable ().toString():null),"is_disable", cjroneblLove24SybcDataEntity.getIsDisable ())
            .eq(StringUtils.isNotBlank(cjroneblLove24SybcDataEntity.getIsDisable ()!=null && !"".equals(cjroneblLove24SybcDataEntity.getIsDisable ().toString())? cjroneblLove24SybcDataEntity.getIsDisable ().toString():null),"is_disable", cjroneblLove24SybcDataEntity.getIsDisable ())
            .eq(StringUtils.isNotBlank(dead),"is_dead",dead)
            .eq(StringUtils.isNotBlank(hjqy),"is_hkqy",hjqy)
            .between(StringUtils.isNotBlank(startDay) && StringUtils.isNotBlank(endDay),"create_time",startDay,endDay)
            .like(StringUtils.isNotBlank(cjroneblLove24SybcDataEntity.getApplicationType ()!=null && !"".equals(cjroneblLove24SybcDataEntity.getApplicationType ().toString())? cjroneblLove24SybcDataEntity.getApplicationType ().toString():null),"application_type", cjroneblLove24SybcDataEntity.getApplicationType ())
            .orderByAsc("create_time","application_type","is_disable")
        );
        return new PageUtils(page);
    }
    @Override
    public List<CjroneblLove24SybcDataEntity> queryExportData(Map<String, Object> params) {
        CjroneblLove24SybcDataEntity cjroneblLove24SybcDataEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneblLove24SybcDataEntity.class);
        String applicationType = params.get("applicationType")!=null ? params.get("applicationType").toString():"";
        String dead = "";
        String hjqy = "";
        if("1".equals(cjroneblLove24SybcDataEntity.getErrorStatus())){
            dead = "1";
        }

        if("2".equals(cjroneblLove24SybcDataEntity.getErrorStatus())){
            hjqy = "1";
        }

        String startDay = cjroneblLove24SybcDataEntity.getStartDay ()!=null && !"".equals(cjroneblLove24SybcDataEntity.getStartDay ().toString())? cjroneblLove24SybcDataEntity.getStartDay ().toString():null;
        String endDay = cjroneblLove24SybcDataEntity.getEndDay ()!=null && !"".equals(cjroneblLove24SybcDataEntity.getEndDay ().toString())? cjroneblLove24SybcDataEntity.getEndDay ().toString():null;

        List<CjroneblLove24SybcDataEntity> exportLists = list(new QueryWrapper<CjroneblLove24SybcDataEntity>()
                .eq(StringUtils.isNotBlank(cjroneblLove24SybcDataEntity.getName ()!=null && !"".equals(cjroneblLove24SybcDataEntity.getName ().toString())? cjroneblLove24SybcDataEntity.getName ().toString():null),"name", cjroneblLove24SybcDataEntity.getName ())
                .eq(StringUtils.isNotBlank(cjroneblLove24SybcDataEntity.getIdCard ()!=null && !"".equals(cjroneblLove24SybcDataEntity.getIdCard ().toString())? cjroneblLove24SybcDataEntity.getIdCard ().toString():null),"id_card", cjroneblLove24SybcDataEntity.getIdCard ())
                .eq(StringUtils.isNotBlank(cjroneblLove24SybcDataEntity.getIsDisable ()!=null && !"".equals(cjroneblLove24SybcDataEntity.getIsDisable ().toString())? cjroneblLove24SybcDataEntity.getIsDisable ().toString():null),"is_disable", cjroneblLove24SybcDataEntity.getIsDisable ())
                .eq(StringUtils.isNotBlank(dead),"is_dead",dead)
                .eq(StringUtils.isNotBlank(hjqy),"is_hkqy",hjqy)
                .eq("status","1")
                .like(StringUtils.isNotBlank(cjroneblLove24SybcDataEntity.getApplicationType ()!=null && !"".equals(cjroneblLove24SybcDataEntity.getApplicationType ().toString())? cjroneblLove24SybcDataEntity.getApplicationType ().toString():null),"application_type", cjroneblLove24SybcDataEntity.getApplicationType ())
                .like(StringUtils.isNotBlank(applicationType),"application_type",applicationType)
                .between(StringUtils.isNotBlank(startDay) && StringUtils.isNotBlank(endDay),"create_time",startDay,endDay)
                .orderByAsc("create_time","application_type","is_disable")
        );
            return exportLists;
    }

    @Override
    @Transactional(rollbackFor=Exception.class)
    public boolean disable(Integer[] ids) {
        for (Integer id : ids) {
            CjroneblLove24SybcDataEntity entity = getById(id);
            entity.setIsDisable("1");
            updateById(entity);
            Integer love24Id = love24Service.getOne(new QueryWrapper<Love24Entity>().eq("id_card",entity.getIdCard())).getId();
            love24Service.removeById(love24Id);
            cjroneWelfareMatterApplicationService.remove(new QueryWrapper<CjroneWelfareMatterApplicationEntity>()
                    .eq("matter_id",love24Id));
        }
        return true;
    }

}
