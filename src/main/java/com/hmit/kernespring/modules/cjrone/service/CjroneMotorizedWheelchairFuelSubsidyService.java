package com.hmit.kernespring.modules.cjrone.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone.entity.CjroneMotorizedWheelchairFuelSubsidyEntity;

import java.util.Map;

import java.util.List;

/**
 * 机动轮椅车燃油补贴
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-27 16:56:18
 */
public interface CjroneMotorizedWheelchairFuelSubsidyService extends IService<CjroneMotorizedWheelchairFuelSubsidyEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneMotorizedWheelchairFuelSubsidyEntity> queryExportData(Map<String, Object> params);
}

