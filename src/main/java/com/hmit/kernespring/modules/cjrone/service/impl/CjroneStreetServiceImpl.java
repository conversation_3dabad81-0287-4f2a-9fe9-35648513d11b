package com.hmit.kernespring.modules.cjrone.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.google.gson.reflect.TypeToken;
import com.hmit.kernespring.common.utils.RedisUtils;
import com.hmit.kernespring.modules.cjrone.dao.CjroneStreetDao;
import com.hmit.kernespring.modules.cjrone.entity.CjroneAdministrativeDivisionEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneStreetService;
import com.hmit.kernespring.modules.cjrone.service.CjroneVillageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;

@Service("cjroneStreetService")
public class CjroneStreetServiceImpl extends ServiceImpl<CjroneStreetDao, CjroneAdministrativeDivisionEntity> implements CjroneStreetService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kerne<PERSON>ring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
            .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

                @Override
                public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                    if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                        //定义为int类型,如果后台返回""或者null,则返回0
                        return null;
                    }
                    return json.getAsInt();
                }
            })
            .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

                @Override
                public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                    if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                        //定义为int类型,如果后台返回""或者null,则返回0
                        return null;
                    }
                    return json.getAsInt();
                }
            })
            .create();
    @Autowired
    CjroneStreetDao cjroneStreetDao;
    @Autowired
    CjroneVillageService cjroneVillageService;
    @Autowired
    private RedisUtils redisUtils;

    @Override
    public List<CjroneAdministrativeDivisionEntity> getAdministrativeDivisionList(String code) {
        if (redisUtils.get(code) != null){
            System.out.println("缓存中获取镇街道数据key--------> "+code);
            return gson.fromJson(redisUtils.get(code),new TypeToken<List<CjroneAdministrativeDivisionEntity>>() {
            }.getType());
        }else{
            List<CjroneAdministrativeDivisionEntity> adList=cjroneStreetDao.getAdministrativeDivisionList(code);
            for (CjroneAdministrativeDivisionEntity entity:adList) {
                List<CjroneAdministrativeDivisionEntity> adChildrenList=cjroneVillageService.getAdministrativeDivisionList(entity.getValue());
                entity.setChildren(adChildrenList);
            }
            redisUtils.set(code,gson.toJson(adList));
            return adList;
        }
    }

    @Override
    public  List<CjroneAdministrativeDivisionEntity>  getAdministrativeDivisionListAll(String code){
        if (redisUtils.get("getAdministrativeDivisionListAll") != null){
            System.out.println("缓存中获取现地址镇街道数据key--------> "+"getAdministrativeDivisionListAll");
            return gson.fromJson(redisUtils.get("getAdministrativeDivisionListAll"),new TypeToken<List<CjroneAdministrativeDivisionEntity>>() {
            }.getType());
        }else{
            //首先获得省份信息
            List<CjroneAdministrativeDivisionEntity> shengList=cjroneStreetDao.getAdministrativeDivisionListShen(code);
            //遍历获得市的信息
            for (CjroneAdministrativeDivisionEntity entity:shengList) {
                List<CjroneAdministrativeDivisionEntity> shiList=cjroneStreetDao.getAdministrativeDivisionListShi(entity.getValue());
                //遍历获得区的信息
                for (CjroneAdministrativeDivisionEntity entity1:shiList) {
                    List<CjroneAdministrativeDivisionEntity> quList=cjroneStreetDao.getAdministrativeDivisionListQu(entity1.getValue());
                    //遍历获得街道的信息
                    for (CjroneAdministrativeDivisionEntity entity2:quList) {
                        List<CjroneAdministrativeDivisionEntity> adList=cjroneStreetDao.getAdministrativeDivisionList(entity2.getValue());
                        //获得村社区的数据
                        for (CjroneAdministrativeDivisionEntity entity3:adList) {
                            List<CjroneAdministrativeDivisionEntity> adChildrenList=cjroneVillageService.getAdministrativeDivisionList(entity3.getValue());
                            entity3.setChildren(adChildrenList);
                        }
                        entity2.setChildren(adList);
                    }
                    entity1.setChildren(quList);
                }
                entity.setChildren(shiList);
            }

            redisUtils.set("getAdministrativeDivisionListAll",gson.toJson(shengList));
            return shengList;
        }
    }

    @Override
    public  List<CjroneAdministrativeDivisionEntity>  getAdministrativeDivisionListStep(String code, Integer step){
        if (step == 1){
            if (redisUtils.get("province:"+code) != null){
                System.out.println("缓存中获取省--------> " + redisUtils.get("province:"+code));
                return gson.fromJson(redisUtils.get("province:"+code),new TypeToken<List<CjroneAdministrativeDivisionEntity>>() {
                }.getType());
            }else {
                //首先获得省份信息
                List<CjroneAdministrativeDivisionEntity> shengList=cjroneStreetDao.getAdministrativeDivisionListShen(code);
                redisUtils.set("province:"+code,gson.toJson(shengList));
                return shengList;
            }
        }else if (step == 2){
            if (redisUtils.get("city:"+code) != null){
                System.out.println("缓存中获取市--------> " + redisUtils.get("city:"+code));
                return gson.fromJson(redisUtils.get("city:"+code),new TypeToken<List<CjroneAdministrativeDivisionEntity>>() {
                }.getType());
            }else {
                //首先获得市信息
                List<CjroneAdministrativeDivisionEntity> shiList=cjroneStreetDao.getAdministrativeDivisionListShi(code);
                redisUtils.set("city:"+code,gson.toJson(shiList));
                return shiList;
            }
        }else if (step == 3){
            if (redisUtils.get("area:"+code) != null){
                System.out.println("缓存中获取区--------> " + redisUtils.get("area:"+code));
                return gson.fromJson(redisUtils.get("area:"+code),new TypeToken<List<CjroneAdministrativeDivisionEntity>>() {
                }.getType());
            }else {
                //首先获得区信息
                List<CjroneAdministrativeDivisionEntity> quList=cjroneStreetDao.getAdministrativeDivisionListQu(code);
                redisUtils.set("area:"+code,gson.toJson(quList));
                return quList;
            }
        }else if (step == 4){
            if (redisUtils.get("zhen:"+code) != null){
                System.out.println("缓存中获取镇街道--------> " + redisUtils.get("zhen:"+code));
                return gson.fromJson(redisUtils.get("zhen:"+code),new TypeToken<List<CjroneAdministrativeDivisionEntity>>() {
                }.getType());
            }else {
                //首先获得镇街道信息
                List<CjroneAdministrativeDivisionEntity> adList=cjroneStreetDao.getAdministrativeDivisionList(code);
                redisUtils.set("zhen:"+code,gson.toJson(adList));
                return adList;
            }
        }else if (step == 5){
            if (redisUtils.get("csq:"+code) != null){
                System.out.println("缓存中获取村社区--------> " + redisUtils.get("csq:"+code));
                return gson.fromJson(redisUtils.get("csq:"+code),new TypeToken<List<CjroneAdministrativeDivisionEntity>>() {
                }.getType());
            }else {
                //首先获得村社区
                List<CjroneAdministrativeDivisionEntity> adChildrenList=cjroneVillageService.getAdministrativeDivisionList(code);
                redisUtils.set("csq:"+code,gson.toJson(adChildrenList));
                return adChildrenList;
            }
        }else {
            return null;
        }
    }


    @Override
    public List<Map<String,Object>>  getAdministrativeDivisionListForAPP(String code){
        if (redisUtils.get("app"+code) != null){
            System.out.println("缓存中获取镇街道数据key--------> "+"app"+code);
            return gson.fromJson(redisUtils.get("app"+code),new TypeToken<List<Map<String,Object>>>() {
            }.getType());
        }else{
            List<Map<String,Object>> adList=cjroneStreetDao.getAdministrativeDivisionListForAPP(code);
            for (Map<String,Object> entity:adList) {
                List<Map<String,Object>> adChildrenList=cjroneVillageService.getAdministrativeDivisionListForAPP(entity.get("id").toString());
                entity.put("children",adChildrenList);
            }
            redisUtils.set("app"+code,gson.toJson(adList));
            return adList;
        }
    }

    @Override
    public CjroneAdministrativeDivisionEntity getStreetById(String code) {
        CjroneAdministrativeDivisionEntity cjroneAdministrativeDivisionEntity=cjroneStreetDao.getStreetById(code);
        return cjroneAdministrativeDivisionEntity;
    }

    @Override
    public  Map<String, Object> queryNames(Map<String, Object> params){
        return cjroneStreetDao.queryNames(params);
    }

    @Override
    public  Map<String, Object> queryNames2(Map<String, Object> params){
        return cjroneStreetDao.queryNames2(params);
    }


}
