package com.hmit.kernespring.modules.cjrone.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;
import com.hmit.kernespring.modules.cjrone.dao.CjroneComprehensiveMedicalInsuranceDao;
import com.hmit.kernespring.modules.cjrone.entity.CjroneComprehensiveMedicalInsuranceEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneComprehensiveMedicalInsuranceService;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service("cjroneComprehensiveMedicalInsuranceService")
public class CjroneComprehensiveMedicalInsuranceServiceImpl extends ServiceImpl<CjroneComprehensiveMedicalInsuranceDao, CjroneComprehensiveMedicalInsuranceEntity> implements CjroneComprehensiveMedicalInsuranceService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneComprehensiveMedicalInsuranceDao cjroneComprehensiveMedicalInsuranceDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneComprehensiveMedicalInsuranceEntity cjroneComprehensiveMedicalInsuranceEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneComprehensiveMedicalInsuranceEntity.class);
        IPage<CjroneComprehensiveMedicalInsuranceEntity> page = this.page(
                new Query<CjroneComprehensiveMedicalInsuranceEntity>().getPage(params),
                new QueryWrapper<CjroneComprehensiveMedicalInsuranceEntity>()
            .eq(StringUtils.isNotBlank(cjroneComprehensiveMedicalInsuranceEntity.getId ()!=null && !"".equals(cjroneComprehensiveMedicalInsuranceEntity.getId ().toString())? cjroneComprehensiveMedicalInsuranceEntity.getId ().toString():null),"id", cjroneComprehensiveMedicalInsuranceEntity.getId ())
            .eq(StringUtils.isNotBlank(cjroneComprehensiveMedicalInsuranceEntity.getName ()!=null && !"".equals(cjroneComprehensiveMedicalInsuranceEntity.getName ().toString())? cjroneComprehensiveMedicalInsuranceEntity.getName ().toString():null),"name", cjroneComprehensiveMedicalInsuranceEntity.getName ())
            .eq(StringUtils.isNotBlank(cjroneComprehensiveMedicalInsuranceEntity.getIdCard ()!=null && !"".equals(cjroneComprehensiveMedicalInsuranceEntity.getIdCard ().toString())? cjroneComprehensiveMedicalInsuranceEntity.getIdCard ().toString():null),"id_card", cjroneComprehensiveMedicalInsuranceEntity.getIdCard ())
        );
        Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key","sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        page.getRecords().forEach( item -> {
        });
        return new PageUtils(page);
    }
    @Override
    public List<CjroneComprehensiveMedicalInsuranceEntity> queryExportData(Map<String, Object> params) {
            return cjroneComprehensiveMedicalInsuranceDao.queryExportData(params);
    }

}