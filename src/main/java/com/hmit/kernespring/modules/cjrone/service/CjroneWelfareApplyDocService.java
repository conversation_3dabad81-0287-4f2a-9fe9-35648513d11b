package com.hmit.kernespring.modules.cjrone.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone.entity.CjroneWelfareApplyDocEntity;

import java.util.Map;

import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-29 15:54:11
 */
public interface CjroneWelfareApplyDocService extends IService<CjroneWelfareApplyDocEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneWelfareApplyDocEntity> queryExportData(Map<String, Object> params);
}

