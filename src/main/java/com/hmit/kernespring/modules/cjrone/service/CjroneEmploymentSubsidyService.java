package com.hmit.kernespring.modules.cjrone.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone.entity.CjroneEmploymentSubsidyEntity;

import java.util.Map;

import java.util.List;

/**
 * 就业创业补助
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-06-24 09:40:30
 */
public interface CjroneEmploymentSubsidyService extends IService<CjroneEmploymentSubsidyEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneEmploymentSubsidyEntity> queryExportData(Map<String, Object> params);
}

