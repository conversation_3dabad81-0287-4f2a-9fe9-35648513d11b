package com.hmit.kernespring.modules.cjrone.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 就业创业补助
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-06-24 09:40:30
 */
@Data
@TableName("cjrone_employment_subsidy")
public class CjroneEmploymentSubsidyEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
@TableId
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 姓名
	 */
	@Excel(name = "姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 身份证号
	 */
	@Excel(name = "身份证号", height = 20, width = 30, isImportField = "true_st")
private String idCard;
	/**
	 * 家庭经济情况
	 */
	@Excel(name = "家庭经济情况", height = 20, width = 30, isImportField = "true_st")
private String familyEconoCondition;
	/**
	 * 性别
	 */

private Integer sex;

	@Excel(name = "性别", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String sexName;
	/**
	 * 文化程度
	 */
	@Excel(name = "文化程度", height = 20, width = 30, isImportField = "true_st")
private String educationDegree;
	/**
	 * 残疾类别
	 */

private Integer disabilityType;

	@Excel(name = "残疾类别", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist = false)
	private String disabilityTypeName;

	/**
	 * 联系电话
	 */
	@Excel(name = "联系电话", height = 20, width = 30, isImportField = "true_st")
private String telephone;
	/**
	 * 现地址
	 */
	@Excel(name = "现地址", height = 20, width = 30, isImportField = "true_st")
private String presentAddress;
	/**
	 * 经营类型  
	 */
	@Excel(name = "经营类型", height = 20, width = 30, isImportField = "true_st")
private String managementType;

	@Excel(name = "创业类别", height = 20, width = 30, isImportField = "true_st")
	private String employeeType;

	@Excel(name = "创业规模", height = 20, width = 30, isImportField = "true_st")
	private String guimo;
	/**
	 * 经营地址
	 */
	@Excel(name = "经营地址", height = 20, width = 30, isImportField = "true_st")
private String managementAddress;
	/**
	 * 申请补助金额
	 */
	@Excel(name = "申请补助金额", height = 20, width = 30, isImportField = "true_st")
private String subsidyMoney;


	/**I
	 * 残疾证号
	 */
	@Excel(name = "残疾证号", height = 20, width = 30, isImportField = "true_st")
	private String disableId;

	/**
	 * 出生日期
	 */
	@Excel(name = "出生日期", height = 20, width = 30, isImportField = "true_st")
	private String birthday;

	private String status;

	private String signStatus;

	private String signatureStatus;

	/**
	 * 申请理由
	 */
	private String applyReason;

}
