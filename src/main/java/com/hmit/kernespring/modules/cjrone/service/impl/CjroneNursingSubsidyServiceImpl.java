package com.hmit.kernespring.modules.cjrone.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;
import com.hmit.kernespring.modules.cjrone.dao.CjroneNursingSubsidyDao;
import com.hmit.kernespring.modules.cjrone.entity.CjroneNursingSubsidyEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneNursingSubsidyService;
import com.hmit.kernespring.modules.matter_application.dao.CjroneWelfareMatterApplicationDao;
import com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity;
import com.hmit.kernespring.modules.matter_application.service.DisabilityCertificateApplicationService;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.*;


@Service("cjroneNursingSubsidyService")
public class CjroneNursingSubsidyServiceImpl extends ServiceImpl<CjroneNursingSubsidyDao, CjroneNursingSubsidyEntity> implements CjroneNursingSubsidyService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneNursingSubsidyDao cjroneNursingSubsidyDao;
    @Autowired
    private CjroneWelfareMatterApplicationDao cjroneWelfareMatterApplicationDao;

    @Autowired
    private SysDictService sysDictService;
    @Autowired
    private DisabilityCertificateApplicationService disabilityCertificateApplicationService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneNursingSubsidyEntity cjroneNursingSubsidyEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneNursingSubsidyEntity.class);
        IPage<CjroneNursingSubsidyEntity> page = this.page(
                new Query<CjroneNursingSubsidyEntity>().getPage(params),
                new QueryWrapper<CjroneNursingSubsidyEntity>()
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidyEntity.getName ()!=null && !"".equals(cjroneNursingSubsidyEntity.getName ().toString())? cjroneNursingSubsidyEntity.getName ().toString():null),"name", cjroneNursingSubsidyEntity.getName ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidyEntity.getSex ()!=null && !"".equals(cjroneNursingSubsidyEntity.getSex ().toString())? cjroneNursingSubsidyEntity.getSex ().toString():null),"sex", cjroneNursingSubsidyEntity.getSex ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidyEntity.getBirthday ()!=null && !"".equals(cjroneNursingSubsidyEntity.getBirthday ().toString())? cjroneNursingSubsidyEntity.getBirthday ().toString():null),"birthday", cjroneNursingSubsidyEntity.getBirthday ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidyEntity.getNativePlace ()!=null && !"".equals(cjroneNursingSubsidyEntity.getNativePlace ().toString())? cjroneNursingSubsidyEntity.getNativePlace ().toString():null),"native_place", cjroneNursingSubsidyEntity.getNativePlace ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidyEntity.getIdCard ()!=null && !"".equals(cjroneNursingSubsidyEntity.getIdCard ().toString())? cjroneNursingSubsidyEntity.getIdCard ().toString():null),"id_card", cjroneNursingSubsidyEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidyEntity.getMobilePhone ()!=null && !"".equals(cjroneNursingSubsidyEntity.getMobilePhone ().toString())? cjroneNursingSubsidyEntity.getMobilePhone ().toString():null),"mobile_phone", cjroneNursingSubsidyEntity.getMobilePhone ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidyEntity.getGuardianName ()!=null && !"".equals(cjroneNursingSubsidyEntity.getGuardianName ().toString())? cjroneNursingSubsidyEntity.getGuardianName ().toString():null),"guardian_name", cjroneNursingSubsidyEntity.getGuardianName ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidyEntity.getGuardianPhone ()!=null && !"".equals(cjroneNursingSubsidyEntity.getGuardianPhone ().toString())? cjroneNursingSubsidyEntity.getGuardianPhone ().toString():null),"guardian_phone", cjroneNursingSubsidyEntity.getGuardianPhone ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidyEntity.getDisabilityCategory ()!=null && !"".equals(cjroneNursingSubsidyEntity.getDisabilityCategory ().toString())? cjroneNursingSubsidyEntity.getDisabilityCategory ().toString():null),"disability_category", cjroneNursingSubsidyEntity.getDisabilityCategory ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidyEntity.getDisabilityDegree ()!=null && !"".equals(cjroneNursingSubsidyEntity.getDisabilityDegree ().toString())? cjroneNursingSubsidyEntity.getDisabilityDegree ().toString():null),"disability_degree", cjroneNursingSubsidyEntity.getDisabilityDegree ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidyEntity.getDisableId ()!=null && !"".equals(cjroneNursingSubsidyEntity.getDisableId ().toString())? cjroneNursingSubsidyEntity.getDisableId ().toString():null),"disable_id", cjroneNursingSubsidyEntity.getDisableId ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidyEntity.getBankAccount ()!=null && !"".equals(cjroneNursingSubsidyEntity.getBankAccount ().toString())? cjroneNursingSubsidyEntity.getBankAccount ().toString():null),"bank_account", cjroneNursingSubsidyEntity.getBankAccount ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidyEntity.getBankName ()!=null && !"".equals(cjroneNursingSubsidyEntity.getBankName ().toString())? cjroneNursingSubsidyEntity.getBankName ().toString():null),"bank_name", cjroneNursingSubsidyEntity.getBankName ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidyEntity.getCareType ()!=null && !"".equals(cjroneNursingSubsidyEntity.getCareType ().toString())? cjroneNursingSubsidyEntity.getCareType ().toString():null),"care_type", cjroneNursingSubsidyEntity.getCareType ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidyEntity.getApplicationDate ()!=null && !"".equals(cjroneNursingSubsidyEntity.getApplicationDate ().toString())? cjroneNursingSubsidyEntity.getApplicationDate ().toString():null),"application_date", cjroneNursingSubsidyEntity.getApplicationDate ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidyEntity.getAuditPerson ()!=null && !"".equals(cjroneNursingSubsidyEntity.getAuditPerson ().toString())? cjroneNursingSubsidyEntity.getAuditPerson ().toString():null),"audit_person", cjroneNursingSubsidyEntity.getAuditPerson ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidyEntity.getAuditDate ()!=null && !"".equals(cjroneNursingSubsidyEntity.getAuditDate ().toString())? cjroneNursingSubsidyEntity.getAuditDate ().toString():null),"audit_date", cjroneNursingSubsidyEntity.getAuditDate ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidyEntity.getStatus ()!=null && !"".equals(cjroneNursingSubsidyEntity.getStatus ().toString())? cjroneNursingSubsidyEntity.getStatus ().toString():null),"status", cjroneNursingSubsidyEntity.getStatus ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidyEntity.getCreateId ()!=null && !"".equals(cjroneNursingSubsidyEntity.getCreateId ().toString())? cjroneNursingSubsidyEntity.getCreateId ().toString():null),"create_id", cjroneNursingSubsidyEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(cjroneNursingSubsidyEntity.getCreateTime ()!=null && !"".equals(cjroneNursingSubsidyEntity.getCreateTime ().toString())? cjroneNursingSubsidyEntity.getCreateTime ().toString():null),"create_time", cjroneNursingSubsidyEntity.getCreateTime ())
            .orderByDesc("create_time")
        );
        Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key","sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        page.getRecords().forEach( item -> {
            SysDictEntity disabilityCategory_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("cjlb_0000") && iii.getValue().equals(
                        item.getDisabilityCategory ())).findAny().orElse(null);
            if (disabilityCategory_sysDictEntity != null){
                item.setDisabilityCategory (disabilityCategory_sysDictEntity.getLabel());
            }else{
                item.setDisabilityCategory (null);
            }
            SysDictEntity disabilityDegree_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("cjdj_0000") && iii.getValue().equals(
                        item.getDisabilityDegree ())).findAny().orElse(null);
            if (disabilityDegree_sysDictEntity != null){
                item.setDisabilityDegree (disabilityDegree_sysDictEntity.getLabel());
            }else{
                item.setDisabilityDegree (null);
            }
            /*SysDictEntity status_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("zt_0000") && iii.getValue().equals(
                        item.getStatus ())).findAny().orElse(null);
            if (status_sysDictEntity != null){
                item.setStatus (Integer.parseInt(status_sysDictEntity.getLabel()));
            }else{
                item.setStatus (null);
            }*/
            String careTypeName = null ;
            String aaa[]={};
            if(item.getCareType()!=null)
                aaa = item.getCareType().substring(1,item.getCareType().length()-1).split(",");
            for (String s : aaa) {
                if (s.replace("\"", "").equals("1")){
                    if (careTypeName == null){
                        careTypeName =  "居家安养 ";
                    }else {
                        careTypeName = careTypeName + "居家安养 ";
                    }
                }else if (s.replace("\"", "").equals("2")){
                    if (careTypeName == null){
                        careTypeName =  "日间照料 ";
                    }else {
                        careTypeName = careTypeName + "日间照料 ";
                    }
                }else if (s.replace("\"", "").equals("3")){
                    if (careTypeName == null){
                        careTypeName =  "集中托养 ";
                    }else {
                        careTypeName = careTypeName + "集中托养 ";
                    }
                }else if (s.replace("\"", "").equals("4")){
                    if (careTypeName == null){
                        careTypeName =  "项目服务 ";
                    }else {
                        careTypeName = careTypeName + "项目服务 ";
                    }
                }
            }
            item.setCareType(careTypeName != null?careTypeName:item.getCareType());
            if ("1".equals(item.getStatus())){
                item.setStatus("已通过");
            }else if ("2".equals(item.getStatus())){
                item.setStatus("待审核");
            }else if ("0".equals(item.getStatus())){
                item.setStatus("已禁用");
            }else if ("3".equals(item.getStatus())){
                item.setStatus("退回");
            }else if ("5".equals(item.getStatus())){
                item.setStatus("待民政审核");
            }
            if ("1".equals(item.getSignStatus())) {
                item.setSignStatus("申请人待手签");
            } else if ("2".equals(item.getSignStatus())) {
                item.setSignStatus("申请人已手签");
            } else if ("3".equals(item.getSignStatus())) {
                item.setSignStatus("镇街道待手签");
            } else if ("4".equals(item.getSignStatus())) {
                item.setSignStatus("镇街道已手签");
            } else if ("5".equals(item.getSignStatus())) {
                item.setSignStatus("区残联未手签");
            } else if ("6".equals(item.getSignStatus())) {
                item.setSignStatus("区残联已手签");
            }else if ("7".equals(item.getSignStatus())) {
                item.setSignStatus("区残联经办人已手签");
            }  else if ("8".equals(item.getSignStatus())) {
                item.setSignStatus("民政已手签");
            }else if ("9".equals(item.getSignStatus())) {
                item.setSignStatus("民政经办人已手签");
            }else {
                item.setSignStatus("无");
            }

            if ("1".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("镇街道待电子公章");
            } else if ("2".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("区残联待电子公章");
            } else if ("3".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("民政待电子公章");
            } else if ("4".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("已完成");
            } else if ("5".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("镇街道已电子公章");
            } else if ("6".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("区残联已电子公章");
            } else if ("7".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("民政已电子公章");
            } else {
                item.setSignatureStatus("无");
            }

            //开始处理籍贯数据
            DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity = disabilityCertificateApplicationService.getByIDCard(item.getIdCard());
            if(disabilityCertificateApplicationEntity!=null){
                item.setNativePlace(disabilityCertificateApplicationEntity.getNativeZhenName()+disabilityCertificateApplicationEntity.getNativeCunName());
            }
        });
        return new PageUtils(page);
    }
    @Override
    public List<CjroneNursingSubsidyEntity> queryExportData(Map<String, Object> params) {
            return cjroneNursingSubsidyDao.queryExportData(params);
    }

    @Override
    public boolean saveBatch(Collection<CjroneNursingSubsidyEntity> entityList) {

        cjroneNursingSubsidyDao.deleteAllData(null);

        return super.saveBatch(entityList);
    }

    @Override
    public void enable(CjroneNursingSubsidyEntity entity) {
        super.updateById(entity);
    }

    @Override
    public boolean updateById(CjroneNursingSubsidyEntity entity) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",entity.getId());
        map.put("matter_name","护理补贴");
        if ("1".equals(entity.getStatus())){
            map.put("status","3");
        } else if ("3".equals(entity.getStatus())){
            map.put("status","4");
        }else {
            map.put("status",entity.getStatus());
        }
        map.put("verify_time",new Date());
        if (entity.getStatus() != null){
            map.put("signatureStatus",entity.getSignatureStatus());
            cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        }
        return super.updateById(entity);
    }
}