package com.hmit.kernespring.modules.cjrone.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;
import com.hmit.kernespring.modules.cjrone.dao.CjroneMotorizedWheelchairFuelSubsidyDao;
import com.hmit.kernespring.modules.cjrone.entity.CjroneMotorizedWheelchairFuelSubsidyEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneMotorizedWheelchairFuelSubsidyService;
import com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity;
import com.hmit.kernespring.modules.matter_application.service.DisabilityCertificateApplicationService;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service("cjroneMotorizedWheelchairFuelSubsidyService")
public class CjroneMotorizedWheelchairFuelSubsidyServiceImpl extends ServiceImpl<CjroneMotorizedWheelchairFuelSubsidyDao, CjroneMotorizedWheelchairFuelSubsidyEntity> implements CjroneMotorizedWheelchairFuelSubsidyService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneMotorizedWheelchairFuelSubsidyDao cjroneMotorizedWheelchairFuelSubsidyDao;
    @Autowired
    private SysDictService sysDictService;
    @Autowired
    private DisabilityCertificateApplicationService disabilityCertificateApplicationService;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneMotorizedWheelchairFuelSubsidyEntity cjroneMotorizedWheelchairFuelSubsidyEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneMotorizedWheelchairFuelSubsidyEntity.class);
        IPage<CjroneMotorizedWheelchairFuelSubsidyEntity> page = this.page(
                new Query<CjroneMotorizedWheelchairFuelSubsidyEntity>().getPage(params),
                new QueryWrapper<CjroneMotorizedWheelchairFuelSubsidyEntity>()
            .eq(StringUtils.isNotBlank(cjroneMotorizedWheelchairFuelSubsidyEntity.getId ()!=null && !"".equals(cjroneMotorizedWheelchairFuelSubsidyEntity.getId ().toString())? cjroneMotorizedWheelchairFuelSubsidyEntity.getId ().toString():null),"id", cjroneMotorizedWheelchairFuelSubsidyEntity.getId ())
            .eq(StringUtils.isNotBlank(cjroneMotorizedWheelchairFuelSubsidyEntity.getName ()!=null && !"".equals(cjroneMotorizedWheelchairFuelSubsidyEntity.getName ().toString())? cjroneMotorizedWheelchairFuelSubsidyEntity.getName ().toString():null),"name", cjroneMotorizedWheelchairFuelSubsidyEntity.getName ())
            .eq(StringUtils.isNotBlank(cjroneMotorizedWheelchairFuelSubsidyEntity.getIdCard ()!=null && !"".equals(cjroneMotorizedWheelchairFuelSubsidyEntity.getIdCard ().toString())? cjroneMotorizedWheelchairFuelSubsidyEntity.getIdCard ().toString():null),"id_card", cjroneMotorizedWheelchairFuelSubsidyEntity.getIdCard ())
        );
        Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key","sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        page.getRecords().forEach( item -> {

            //遍历查询相关字段
            DisabilityCertificateApplicationEntity disabilityCertificateApplication = disabilityCertificateApplicationService.getByIDCard(item.getIdCard());
            if(disabilityCertificateApplication!=null){
                item.setDisableId(disabilityCertificateApplication.getDisableId());
                if("0".equals(disabilityCertificateApplication.getIsDead()))
                    item.setIsFx("否");
                else
                    item.setIsFx("是");
                if("0".equals(disabilityCertificateApplication.getIsDead()))
                    item.setIsDead("否");
                else
                    item.setIsDead("是");
            }
           else{
                item.setDisableId("");
                item.setIsFx("");
                item.setIsDead("");
            }

        });
        return new PageUtils(page);
    }
    @Override
    public List<CjroneMotorizedWheelchairFuelSubsidyEntity> queryExportData(Map<String, Object> params) {
            return cjroneMotorizedWheelchairFuelSubsidyDao.queryExportData(params);
    }

}