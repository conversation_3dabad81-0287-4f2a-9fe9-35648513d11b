package com.hmit.kernespring.modules.cjrone.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;
import com.hmit.kernespring.modules.cjrone.dao.CjroneSignatureDao;
import com.hmit.kernespring.modules.cjrone.entity.CjroneSignatureEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneSignatureService;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service("cjroneSignatureService")
public class CjroneSignatureServiceImpl extends ServiceImpl<CjroneSignatureDao, CjroneSignatureEntity> implements CjroneSignatureService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneSignatureDao cjroneSignatureDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneSignatureEntity cjroneSignatureEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneSignatureEntity.class);
        IPage<CjroneSignatureEntity> page = this.page(
                new Query<CjroneSignatureEntity>().getPage(params),
                new QueryWrapper<CjroneSignatureEntity>()
            .eq(StringUtils.isNotBlank(cjroneSignatureEntity.getId ()!=null && !"".equals(cjroneSignatureEntity.getId ().toString())? cjroneSignatureEntity.getId ().toString():null),"id", cjroneSignatureEntity.getId ())
            .eq(StringUtils.isNotBlank(cjroneSignatureEntity.getCreateDate ()!=null && !"".equals(cjroneSignatureEntity.getCreateDate ().toString())? cjroneSignatureEntity.getCreateDate ().toString():null),"create_date", cjroneSignatureEntity.getCreateDate ())
            .eq(StringUtils.isNotBlank(cjroneSignatureEntity.getCreateId ()!=null && !"".equals(cjroneSignatureEntity.getCreateId ().toString())? cjroneSignatureEntity.getCreateId ().toString():null),"create_id", cjroneSignatureEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(cjroneSignatureEntity.getTypeId ()!=null && !"".equals(cjroneSignatureEntity.getTypeId ().toString())? cjroneSignatureEntity.getTypeId ().toString():null),"type_id", cjroneSignatureEntity.getTypeId ())
            .eq(StringUtils.isNotBlank(cjroneSignatureEntity.getType ()!=null && !"".equals(cjroneSignatureEntity.getType ().toString())? cjroneSignatureEntity.getType ().toString():null),"type", cjroneSignatureEntity.getType ())
            .eq(StringUtils.isNotBlank(cjroneSignatureEntity.getUrl ()!=null && !"".equals(cjroneSignatureEntity.getUrl ().toString())? cjroneSignatureEntity.getUrl ().toString():null),"url", cjroneSignatureEntity.getUrl ())
            .orderByDesc("create_time")
        );
        Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key","sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        page.getRecords().forEach( item -> {
            SysDictEntity type_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("qzlx_0000") && iii.getValue().equals(
                        item.getType ())).findAny().orElse(null);
            if (type_sysDictEntity != null){
                item.setType (type_sysDictEntity.getLabel());
            }else{
                item.setType (null);
            }
        });
        return new PageUtils(page);
    }
    @Override
    public List<CjroneSignatureEntity> queryExportData(Map<String, Object> params) {
            return cjroneSignatureDao.queryExportData(params);
    }
    @Override
    public List<CjroneSignatureEntity> queryDataByMap(Map<String, Object> params) {
            return cjroneSignatureDao.queryDataByMap(params);
    }

}