package com.hmit.kernespring.modules.cjrone.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hmit.kernespring.modules.cjrone.entity.CjroneChildrenRehabilitationSubsidyEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 残疾少年儿童康复训练补助
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-10 11:18:02
 */
@Mapper
public interface CjroneChildrenRehabilitationSubsidyDao extends BaseMapper<CjroneChildrenRehabilitationSubsidyEntity> {
    List<CjroneChildrenRehabilitationSubsidyEntity> queryExportData(Map<String, Object> params);
	Map<String, Object>queryStatistics(@Param("approvalYear") String approvalYear);
	
	/**
	 * 计算年度累计金额
	 * @param idCard 身份证号
	 * @param subsidyYear 补助年份
	 * @return 年度累计金额
	 */
	BigDecimal calculateYearTotalAmount(@Param("idCard") String idCard, @Param("subsidyYear") Integer subsidyYear);
}
