package com.hmit.kernespring.modules.cjrone.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone.entity.CjroneComprehensiveMedicalInsuranceEntity;

import java.util.Map;

import java.util.List;

/**
 * 残疾人综合医疗保险
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-27 16:56:19
 */
public interface CjroneComprehensiveMedicalInsuranceService extends IService<CjroneComprehensiveMedicalInsuranceEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneComprehensiveMedicalInsuranceEntity> queryExportData(Map<String, Object> params);
}

