package com.hmit.kernespring.modules.cjrone.dao;

import com.hmit.kernespring.modules.cjrone.entity.CjroneComprehensiveMedicalInsuranceEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 残疾人综合医疗保险
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-27 16:56:19
 */
@Mapper
public interface CjroneComprehensiveMedicalInsuranceDao extends BaseMapper<CjroneComprehensiveMedicalInsuranceEntity> {
    List<CjroneComprehensiveMedicalInsuranceEntity> queryExportData(Map<String, Object> params);
	
}
