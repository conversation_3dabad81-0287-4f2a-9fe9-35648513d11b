package com.hmit.kernespring.modules.cjrone.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 电子签章
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-11 14:55:13
 */
@Data
@TableName("cjrone_signature")
public class CjroneSignatureEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
@TableId
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 创建时间
	 */
	@Excel(name = "创建时间", height = 20, width = 30, isImportField = "true_st")
private Date createDate;
	/**
	 * 创建者
	 */
	@Excel(name = "创建者", height = 20, width = 30, isImportField = "true_st")
private Long createId;
	/**
	 * 签章来源ID
	 */
	@Excel(name = "签章来源ID", height = 20, width = 30, isImportField = "true_st")
private Integer typeId;
	/**
	 * 签章类型
	 */
	@Excel(name = "签章类型", height = 20, width = 30, isImportField = "true_st")
private String type;
	/**
	 * url
	 */
	@Excel(name = "url", height = 20, width = 30, isImportField = "true_st")
private String url;
	/**
	 * fileName
	 */
	@Excel(name = "url", height = 20, width = 30, isImportField = "true_st")
private String fileName;
	/**
	 * fileActUrl
	 */
	@Excel(name = "url", height = 20, width = 30, isImportField = "true_st")
private String fileActUrl;
	/**
	 * accountId
	 */
	@Excel(name = "url", height = 20, width = 30, isImportField = "true_st")
private String accountId;
	/**
	 * accountName
	 */
	@Excel(name = "url", height = 20, width = 30, isImportField = "true_st")
	private String accountName;
	/**
	 * status
	 */
	@Excel(name = "url", height = 20, width = 30, isImportField = "true_st")
	private String status;

}
