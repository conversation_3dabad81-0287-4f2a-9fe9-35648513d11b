package com.hmit.kernespring.modules.cjrone.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("cjrone_children_rehabilitation_subsidy")
public class CjroneChildrenRehabilitationSubsidyEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    @Excel(name = "残疾人姓名", height = 20, width = 30, isImportField = "true_st")
    private String name;
    /**
     * 性别  1 男  0女
     */
    private String sex;

    @Excel(name = "性别", height = 20, width = 30, isImportField = "true_st")
    @TableField(exist = false)
    private String sexName;

    /**
     * 出生日期
     */
    @Excel(name = "出生日期", height = 20, width = 30, isImportField = "true_st")
    private String birthday;
    /**
     * 民族
     */
    @Excel(name = "民族", height = 20, width = 30, isImportField = "true_st")
    private String nationality;
    /**
     * 身份证号码
     */
    @Excel(name = "身份证号码", height = 20, width = 30, isImportField = "true_st")
    private String idCard;
    /**
     * 手机号码
     */
    @Excel(name = "手机号码", height = 20, width = 30, isImportField = "true_st")
    private String mobilePhone;

    //惠残事项申请ID
    private Integer welfareMatterApplicationId;

    //附件
    private String attach;

    //年度累计金额
//    @Excel(name = "年度累计金额", height = 20, width = 30, isImportField = "true_st")
    private BigDecimal yearTotalAmount;

    //序号
    private String no;

    //日期
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date date;

    //康复机构名称
    @Excel(name = "康复机构名称", height = 20, width = 30, isImportField = "true_st")
    private String rehabilitationCenterName;

    //定点机构名称
//    @Excel(name = "定点机构名称", height = 20, width = 30, isImportField = "true_st")
    private String designatedInstitution;

    //康复类型
    @Excel(name = "康复类型", height = 20, width = 30, isImportField = "true_st")
    private String rehabilitationType;

    //补助标准
    @Excel(name = "补助标准", height = 20, width = 30, isImportField = "true_st")
    private BigDecimal subsidyStandard;

    //补助年份
    @Excel(name = "补助年份", height = 20, width = 30, isImportField = "true_st")
    private Integer subsidyYear;

    //补助月份
    @Excel(name = "补助月份", height = 20, width = 30, isImportField = "true_st")
    private Integer subsidyMonth;

    //实际发票金额
    @Excel(name = "实际发票金额", height = 20, width = 30, isImportField = "true_st")
    private BigDecimal actualInvoiceAmount;

    //区残联审核金额
    @Excel(name = "区残联审核金额", height = 20, width = 30, isImportField = "true_st")
    private BigDecimal districtDisabilityAuditAmount;

    //经办人ID
    private Long operatorId;

    //经办人姓名
    @Excel(name = "经办人姓名", height = 20, width = 30, isImportField = "true_st")
    private String operatorName;

    //审核人ID
    private Long auditorId;

    //审核人姓名
    @Excel(name = "审核人姓名", height = 20, width = 30, isImportField = "true_st")
    private String auditorName;

    //经费来源
    @Excel(name = "经费来源", height = 20, width = 30, isImportField = "true_st")
    private String fundsSource;

    //申请备注
    @Excel(name = "申请备注", height = 20, width = 30, isImportField = "true_st")
    private String applyRemark;

    //审批备注
    private String approvalRemark;

    //创建时间
    @Excel(name = "创建时间", height = 20, width = 30, isImportField = "true_st", format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    //是否低保低边特困孤儿(true：是  false：不是）
    private Boolean dibaoFlag;

    @Excel(name = "是否低保低边特困孤儿", height = 20, width = 30, isImportField = "true_st", replace = {"是_true", "否_false", "否_null"})
    @TableField(exist = false)
    private Boolean dibaoFlagDisplay;

    //休学证明附件
    private String absenceCertAttachment;

    /**
     * 状态说明：
     * "0" - 禁用
     * "1" - 申请人待手签
     * "2" - 街道待审核
     * "4" - 民政局经办人待审核
     * "5" - 民政负责人待审核
     * "6" - 区残联经办人待审核
     * "7" - 区残联负责人待审核
     * "8" - 通过
     * "9" - 街道退回-社区
     * "10" - 民政退回-街道
     * "11" - 区残联-民政
     * "12" - 区残联退回-街道
     */
    private String status;


    //诊断证明照片URL
    private String diagnosisCertificateImage;

    //诊断证明时间 起始时间
    @Excel(name = "诊断证明起始时间", height = 20, width = 30, isImportField = "true_st", format = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date diagnosisCertificateStartTime;

    //诊断证明时间 结束时间
    @Excel(name = "诊断证明结束时间", height = 20, width = 30, isImportField = "true_st", format = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date diagnosisCertificateEndTime;

    //是否省外
    private Boolean isOutOfProvince;

    @Excel(name = "是否省外", height = 20, width = 30, isImportField = "true_st", replace = {"是_true", "否_false", "否_null"})
    @TableField(exist = false)
    private Boolean isOutOfProvinceDisplay;

    //审批时间
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date approvalTime;

    @Excel(name = "街道", height = 20, width = 30, isImportField = "true_st")
    private String nativeZhen;
}
