package com.hmit.kernespring.modules.cjrone.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.cjrone.entity.*;
import com.hmit.kernespring.modules.cjrone.service.*;
import com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity;
import com.hmit.kernespring.modules.matter_application.service.DisabilityCertificateApplicationService;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import com.hmit.kernespring.modules.sys.controller.AbstractController;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.*;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.Files;
import java.util.*;

/**
 * 护理补贴申请
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-10 14:28:57
 */
@RestController
@RequestMapping("cjrone/cjronenursingsubsidy")
public class CjroneNursingSubsidyController extends AbstractController {
    @Autowired
    private CjroneNursingSubsidyService cjroneNursingSubsidyService;
    @Autowired
    private CjroneDocumentService cjroneDocumentService;
    @Autowired
    private CjroneWelfareApplyDocService cjroneWelfareApplyDocService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;
    @Autowired
    private CjroneSignatureService cjroneSignatureService;
    @Autowired
    private CjroneTwoSubsidyStandardsService cjroneTwoSubsidyStandardsService;
    @Autowired
    private DisabilityCertificateApplicationService disabilityCertificateApplicationService;


    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("cjrone:cjronenursingsubsidy:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = cjroneNursingSubsidyService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{name}")
    @RequiresPermissions("cjrone:cjronenursingsubsidy:info")
    public R info(@PathVariable("name") String name){
		CjroneNursingSubsidyEntity cjroneNursingSubsidy = cjroneNursingSubsidyService.getById(name);

        return R.ok().put("cjroneNursingSubsidy", cjroneNursingSubsidy);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("cjrone:cjronenursingsubsidy:save")
    public R save(@RequestBody CjroneNursingSubsidyEntity cjroneNursingSubsidy){
        cjroneNursingSubsidy.setCreateTime(new Date());
        cjroneNursingSubsidy.setCreateId(getUserId());
        cjroneNursingSubsidy.setStatus("2");
		cjroneNursingSubsidyService.save(cjroneNursingSubsidy);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("cjrone:cjronenursingsubsidy:update")
    public R update(@RequestBody CjroneNursingSubsidyEntity cjroneNursingSubsidy){
        CjroneNursingSubsidyEntity entity = cjroneNursingSubsidyService.getById(cjroneNursingSubsidy.getId());
        System.out.println(new Gson().toJson(entity));
        if (entity != null){
            if (getUser().getRoleName() != null && getUser().getRoleName().indexOf("区残联") != -1){
                if ("6".equals(entity.getSignStatus())){
                    if (!"3".equals(entity.getSignatureStatus())){
                        return R.error().put("code",100).put("msg","审核未通过，区残联未电子公章！").put("applyId",cjroneNursingSubsidy.getId());
                    }else {
                        System.out.println("aaaaaaa");
                    }
                }else {
                    return R.error().put("code",100).put("msg","审核未通过，区残联未手签！").put("applyId",cjroneNursingSubsidy.getId());
                }
            }else if (getUser().getRoleName() != null && getUser().getRoleName().indexOf("民政") != -1){
                if ("8".equals(entity.getSignStatus())){
                    if (!"4".equals(entity.getSignatureStatus())){
                        return R.error().put("code",100).put("msg","审核未通过，民政未电子公章！").put("applyId",cjroneNursingSubsidy.getId());
                    }else {
                        System.out.println("aaaaaaa");
                    }
                }else {
                    return R.error().put("code",100).put("msg","审核未通过，民政未手签！").put("applyId",cjroneNursingSubsidy.getId());
                }
            }
        }
		cjroneNursingSubsidyService.updateById(cjroneNursingSubsidy);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("cjrone:cjronenursingsubsidy:delete")
    public R delete(@RequestBody String[] names){
		cjroneNursingSubsidyService.removeByIds(Arrays.asList(names));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    // @RequiresPermissions("cjrone:cjronenursingsubsidy:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<CjroneNursingSubsidyEntity> cjroneNursingSubsidyList = new ArrayList<>();
        list.forEach(item ->{
            if (null != item.get("身份证") && !"".equals(item.get("身份证").toString()) || (null != item.get("身份证号") && !"".equals(item.get("身份证号").toString()))) {
                item.put("name", item.get("姓名"));
                item.put("sex","男".equals(item.get("性别"))?1:0);
                item.put("birthday", item.get("出生年月"));
                item.put("nativePlace", item.get("籍贯"));
                item.put("idCard", item.get("身份证号"));
                item.put("mobilePhone", item.get("家庭电话"));
                item.put("guardianName", item.get("监护人姓名"));
                item.put("guardianPhone", item.get("监护人手机"));
                item.put("disabilityCategory", item.get("残疾类别"));
                item.put("disabilityDegree", item.get("残疾等级"));
                item.put("disableId", item.get("残疾证号"));
                item.put("bankAccount", item.get("银行账号"));
                item.put("bankName", item.get("开户银行"));
                item.put("careType", item.get("照料服务方式"));
                item.put("applicationDate", item.get("申请时间（年/月）"));
                item.put("auditPerson", item.get("审核人"));
                item.put("auditDate", item.get("审核时间"));
                item.put("status", item.get("状态"));
                item.put("createId", item.get("创建人编号"));
                item.put("createTime", item.get("创建时间"));
                cjroneNursingSubsidyList.add(new Gson().fromJson(new Gson().toJson(item), CjroneNursingSubsidyEntity.class));
            }
        });
        // 保存到数据库
        cjroneNursingSubsidyService.saveBatch(cjroneNursingSubsidyList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    //@RequiresPermissions("cjrone:cjronenursingsubsidy:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<CjroneNursingSubsidyEntity> cjroneNursingSubsidyEntityList = cjroneNursingSubsidyService.queryExportData(mapArgs);
        cjroneNursingSubsidyEntityList.forEach(item ->{
            if ("1".equals(item.getDisabilityCategory())){
                item.setDisabilityCategory("视力");
            }else  if ("2".equals(item.getDisabilityCategory())){
                item.setDisabilityCategory("听力");
            }else  if ("3".equals(item.getDisabilityCategory())){
                item.setDisabilityCategory("智力");
            }else  if ("4".equals(item.getDisabilityCategory())){
                item.setDisabilityCategory("精神");
            }else  if ("5".equals(item.getDisabilityCategory())){
                item.setDisabilityCategory("肢体");
            }else  if ("6".equals(item.getDisabilityCategory())){
                item.setDisabilityCategory("言语");
            }else  if ("7".equals(item.getDisabilityCategory())){
                item.setDisabilityCategory("肢体");
            }
            if ("1".equals(item.getDisabilityDegree())){
                item.setDisabilityDegree("一级");
            }else  if ("2".equals(item.getDisabilityDegree())){
                item.setDisabilityDegree("二级");
            }else  if ("3".equals(item.getDisabilityDegree())){
                item.setDisabilityDegree("三级");
            }else  if ("4".equals(item.getDisabilityDegree())){
                item.setDisabilityDegree("四级");
            }

            //开始处理性别
            if(item.getSex()==1){
                item.setSexName("男");
            }else{
                item.setSexName("女");
            }

            if(item.getCareType()!=null){
                String aaa[] = item.getCareType().substring(1, item.getCareType().length() - 1).split(",");
                String careTypeName = null;
                for (String s : aaa) {
                    if (s.replace("\"", "").equals("1")) {
                        if (careTypeName == null) {
                            careTypeName = "居家安养 ";
                        } else {
                            careTypeName = careTypeName + "居家安养 ";
                        }
                    } else if (s.replace("\"", "").equals("2")) {
                        if (careTypeName == null) {
                            careTypeName = "日间照料 ";
                        } else {
                            careTypeName = careTypeName + "日间照料 ";
                        }
                    } else if (s.replace("\"", "").equals("3")) {
                        if (careTypeName == null) {
                            careTypeName = "集中托养 ";
                        } else {
                            careTypeName = careTypeName + "集中托养 ";
                        }
                    } else if (s.replace("\"", "").equals("4")) {
                        if (careTypeName == null) {
                            careTypeName = "项目服务 ";
                        } else {
                            careTypeName = careTypeName + "项目服务 ";
                        }
                    }
                }
                item.setCareType(careTypeName);
            }
            else{
                item.setCareType("无");
            }


            //开始处理籍贯数据
           /* DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity = disabilityCertificateApplicationService.getByIDCard(item.getIdCard());
            if(disabilityCertificateApplicationEntity!=null){
                item.setNativePlace(disabilityCertificateApplicationEntity.getNativeZhenName()+disabilityCertificateApplicationEntity.getNativeCunName());
            }*/
        });

        ExportParams params = new ExportParams("护理补贴申请", null, "护理补贴申请");
        Workbook workbook = ExcelExportUtil.exportExcel(params, CjroneNursingSubsidyEntity.class, cjroneNursingSubsidyEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "护理补贴申请" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }
    /**
     * 下载附件
     */
    @RequestMapping("/downloadFJ/{id}")
    public R downloadFJ(@PathVariable("id") Integer id,@RequestParam("type") String type) throws IOException {
        Map<String, Object> params = new HashMap<>();
        params.put("welfare_matter_application_id",id);
        List<CjroneWelfareApplyDocEntity> docEntityList = (List<CjroneWelfareApplyDocEntity>) cjroneWelfareApplyDocService.listByMap(params);
        CjroneDocumentEntity documentEntity = cjroneDocumentService.getById(docEntityList.get(0).getDocumentId());
        return R.ok().put("fileUrl", documentEntity.getFilePath()).put("fileName", documentEntity.getFileName());
    }
        /**
         * 生成电子签章 pdf
         */
    @RequestMapping("/printPDF/{id}")
    public R printPDF(@PathVariable("id") Integer id,@RequestParam("type") String type) throws IOException {
        Map<String, Object> tmp_params = new HashMap<>();
        tmp_params.put("type","惠残事项护理补贴");
        tmp_params.put("status","1");
        tmp_params.put("type_id",id);
        List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
        if (alive_list.size()>0) {
            CjroneSignatureEntity cjroneSignatureEntity = alive_list.get(0);
            if ("电子公章".equals(type)){
                String tmp_file_name = System.currentTimeMillis()+"_"+cjroneSignatureEntity.getFileName();
                Files.copy(new File(cjroneSignatureEntity.getFileActUrl()).toPath(),new File(cjroneProperties.getTempFilePath()+tmp_file_name).toPath());
                return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", tmp_file_name).put("signId",cjroneSignatureEntity.getId());
            }else {
                return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", cjroneSignatureEntity.getFileName()).put("signId",cjroneSignatureEntity.getId());
            }
        }else {

            //根据编号获得详细信息
            CjroneNursingSubsidyEntity cjroneNursingSubsidyEntity = cjroneNursingSubsidyService.getById(id);
            //获得残疾证信息
            DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity = disabilityCertificateApplicationService.getByIDCard(cjroneNursingSubsidyEntity.getIdCard());


            // 原pdf模板路径
            String templatePath = cjroneProperties.getTempletePath()+"护理补助模板.pdf";
            // 生成的新文件路径
            String newPDFPath = cjroneProperties.getSignaturePath()+"nursing_subsidy_"+cjroneNursingSubsidyEntity.getIdCard()+".pdf";

            // 获得待生成的实体文件
            Map<String, String> map =new HashMap<String, String>();
            Map<String, Object> mapparams =new HashMap<String, Object>();

            if(cjroneNursingSubsidyEntity.getCareType()!=null){
                mapparams.put("isConcentratedCare",0);
                String aaa[] = cjroneNursingSubsidyEntity.getCareType().substring(1, cjroneNursingSubsidyEntity.getCareType().length() - 1).split(",");
                String careTypeName = null;
                for (String s : aaa) {
                    if (s.replace("\"", "").equals("1")) {
                        if (careTypeName == null) {
                            careTypeName = "居家安养 ";
                        } else {
                            careTypeName = careTypeName + "居家安养 ";
                        }
                    } else if (s.replace("\"", "").equals("2")) {
                        if (careTypeName == null) {
                            careTypeName = "日间照料 ";
                        } else {
                            careTypeName = careTypeName + "日间照料 ";
                        }
                    } else if (s.replace("\"", "").equals("3")) {
                        if (careTypeName == null) {
                            careTypeName = "集中托养 ";
                        } else {
                            careTypeName = careTypeName + "集中托养 ";
                        }
                        mapparams.put("isConcentratedCare",1);
                    } else if (s.replace("\"", "").equals("4")) {
                        if (careTypeName == null) {
                            careTypeName = "项目服务 ";
                        } else {
                            careTypeName = careTypeName + "项目服务 ";
                        }
                    }
                }
                cjroneNursingSubsidyEntity.setCareType(careTypeName);
            }
            else{
                cjroneNursingSubsidyEntity.setCareType("无");
            }

            //获取年月日数据
            Calendar now = Calendar.getInstance();
            String nums[] = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};

            // 获得待生成的实体文件
            map.put("name", cjroneNursingSubsidyEntity.getName());
            if (cjroneNursingSubsidyEntity.getSex() == 1)
                map.put("sex", "男");
            else
                map.put("sex", "女");
            map.put("birthday", cjroneNursingSubsidyEntity.getBirthday()==null?"":cjroneNursingSubsidyEntity.getBirthday());
            map.put("mobilePhone", cjroneNursingSubsidyEntity.getMobilePhone()==null?"":cjroneNursingSubsidyEntity.getMobilePhone());
            map.put("idCard", cjroneNursingSubsidyEntity.getIdCard()==null?"":cjroneNursingSubsidyEntity.getIdCard());
            if(cjroneNursingSubsidyEntity.getDisabilityCategory()==null){
                map.put("disabilityType", "");
                mapparams.put("disabilityType","");
            }
            else{
                if("1".equals(cjroneNursingSubsidyEntity.getDisabilityCategory())){
                    map.put("disabilityType", "视力残疾");
                    mapparams.put("disabilityType","视力");
                }
                else if("2".equals(cjroneNursingSubsidyEntity.getDisabilityCategory())){
                    map.put("disabilityType", "听力残疾");
                    mapparams.put("disabilityType","听力");
                }
                else if("3".equals(cjroneNursingSubsidyEntity.getDisabilityCategory())){
                    map.put("disabilityType", "智力残疾");
                    mapparams.put("disabilityType","智力");
                }
                else if("4".equals(cjroneNursingSubsidyEntity.getDisabilityCategory())){
                    map.put("disabilityType", "精神残疾");
                    mapparams.put("disabilityType","精神");
                }
                else if("5".equals(cjroneNursingSubsidyEntity.getDisabilityCategory())){
                    map.put("disabilityType", "肢体");
                    mapparams.put("disabilityType","肢体");
                }
                else if("6".equals(cjroneNursingSubsidyEntity.getDisabilityCategory())){
                    map.put("disabilityType", "言语残疾");
                    mapparams.put("disabilityType","言语");
                }
                else if("7".equals(cjroneNursingSubsidyEntity.getDisabilityCategory())){
                    map.put("disabilityType", "肢体");
                    mapparams.put("disabilityType","肢体");
                }
                else{
                    map.put("disabilityType", cjroneNursingSubsidyEntity.getDisabilityCategory());
                }
            }
            map.put("disabilityDegree", cjroneNursingSubsidyEntity.getDisabilityDegree()==null?"无等":nums[Integer.parseInt(cjroneNursingSubsidyEntity.getDisabilityDegree())] + "级");
            map.put("disableId", cjroneNursingSubsidyEntity.getDisableId()==null?"":cjroneNursingSubsidyEntity.getDisableId());
            // 从残疾证表中获得详细的镇街道，村社区地址
            if(disabilityCertificateApplicationEntity!=null)
                map.put("nativeAddress", disabilityCertificateApplicationEntity.getNativeZhenName()==null?"":disabilityCertificateApplicationEntity.getNativeZhenName()+disabilityCertificateApplicationEntity.getNativeCunName());
            else
                map.put("nativeAddress", cjroneNursingSubsidyEntity.getNativePlace()==null?"":cjroneNursingSubsidyEntity.getNativePlace());
            map.put("bankName", cjroneNursingSubsidyEntity.getBankName()==null?"":cjroneNursingSubsidyEntity.getBankName());
            map.put("bankAccount", cjroneNursingSubsidyEntity.getBankAccount()==null?"":cjroneNursingSubsidyEntity.getBankAccount());

            map.put("guardianName", cjroneNursingSubsidyEntity.getGuardianName()==null?"":cjroneNursingSubsidyEntity.getGuardianName());
            map.put("guardianPhone", cjroneNursingSubsidyEntity.getGuardianPhone()==null?"":cjroneNursingSubsidyEntity.getGuardianPhone());

            map.put("careServiceType",cjroneNursingSubsidyEntity.getCareType()==null?"":cjroneNursingSubsidyEntity.getCareType());
            map.put("degree",cjroneNursingSubsidyEntity.getDisabilityDegree()==null?"":nums[Integer.parseInt(cjroneNursingSubsidyEntity.getDisabilityDegree())]);

            map.put("applicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");

            //开始关联补助金额
            mapparams.put("type","护理补贴");
            mapparams.put("disabilityDegree",cjroneNursingSubsidyEntity.getDisabilityDegree());
            /*List<CjroneTwoSubsidyStandardsEntity> cjroneTwoSubsidyStandardsEntityList=cjroneTwoSubsidyStandardsService.queryByMap(mapparams);
            if(cjroneTwoSubsidyStandardsEntityList!=null&&cjroneTwoSubsidyStandardsEntityList.size()>0){
                map.put("subsidymoney",cjroneTwoSubsidyStandardsEntityList.get(0).getMoney().toString());
            }
            else{
                map.put("subsidymoney"," ");
            }*/
            map.put("subsidymoney",cjroneNursingSubsidyEntity.getStandardSubsidy());

            FileOutputStream out;
            int num = 1;//页数
            ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
            try {
                out = new FileOutputStream(newPDFPath);// 输出流
                Document doc = new Document();   //新建一个文档
                PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                doc.open();

                for (int i = 0; i < num; i++) {
                    bos[i] = new ByteArrayOutputStream();
                    PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                    PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                    AcroFields form = stamper.getAcroFields(); //获取文本域
                    // BaseFont
                    // 1、使用iTextAsian.jar中的字体
                    // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                    // 2、使用Windows系统字体(TrueType)
                    // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                    // 3、使用资源字体(ClassPath)
                    // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                    BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                    java.util.Iterator<String> it = form.getFields().keySet().iterator();
                    while (it.hasNext()) {
                        String name = it.next();
                        System.out.println(name + ":"+map.get(name));
                        form.setFieldProperty(name,"textfont",font,null);
                        // form.addSubstitutionFont(font);
                        form.setField(name, map.get(name));
                    }

                    stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                    stamper.close();

                }
                PdfImportedPage page = null;
                for (int i = 0; i < num; i++) {
                    page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                    copy.addPage(page);
                }
                doc.close();
                out.close();
            } catch (IOException e) {
                System.out.println("导出异常");
            } catch (DocumentException e) {
                System.out.println("文档异常");
            }



            // 生成电子签章pdf 并保存
            //String filePath = pdfUtils.pdfNursingSubsidyApplyToSignature(cjroneProperties.getTempletePath(), cjroneProperties.getSignaturePath(), nsEntity);
            CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
            cjroneSignature.setUrl("/nursing_subsidy_" + cjroneNursingSubsidyEntity.getIdCard()+".pdf");
            cjroneSignature.setFileActUrl(newPDFPath);
            cjroneSignature.setCreateDate(new Date());
            cjroneSignature.setCreateId(getUserId());
            cjroneSignature.setType("惠残事项护理补贴");
            cjroneSignature.setTypeId(cjroneNursingSubsidyEntity.getId());
            cjroneSignature.setFileName("nursing_subsidy_"+cjroneNursingSubsidyEntity.getIdCard()+".pdf");
            cjroneSignature.setAccountId(getUserId().toString());
            cjroneSignature.setAccountName(getUser().getUsername());
            cjroneSignature.setStatus("1");

            cjroneSignatureService.save(cjroneSignature);
            // return R.ok().put("fileName", "signature_"+ PinYinUtil.getFullSpell(cjroneDisabilityHospital.getName())+".pdf").put("signId",cjroneSignature.getId());
            return R.ok().put("fileUrl", cjroneSignature.getUrl()).put("fileName", cjroneSignature.getFileName()).put("signId",cjroneSignature.getId());

        }

    }

}
