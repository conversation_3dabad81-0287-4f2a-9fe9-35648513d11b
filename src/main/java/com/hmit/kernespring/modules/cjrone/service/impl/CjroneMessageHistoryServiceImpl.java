package com.hmit.kernespring.modules.cjrone.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;
import com.hmit.kernespring.modules.cjrone.dao.CjroneMessageHistoryDao;
import com.hmit.kernespring.modules.cjrone.entity.CjroneMessageHistoryEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneMessageHistoryService;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service("cjroneMessageHistoryService")
public class CjroneMessageHistoryServiceImpl extends ServiceImpl<CjroneMessageHistoryDao, CjroneMessageHistoryEntity> implements CjroneMessageHistoryService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneMessageHistoryDao cjroneMessageHistoryDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneMessageHistoryEntity cjroneMessageHistoryEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneMessageHistoryEntity.class);
        IPage<CjroneMessageHistoryEntity> page = this.page(
                new Query<CjroneMessageHistoryEntity>().getPage(params),
                new QueryWrapper<CjroneMessageHistoryEntity>()
            .eq(StringUtils.isNotBlank(cjroneMessageHistoryEntity.getId ()!=null && !"".equals(cjroneMessageHistoryEntity.getId ().toString())? cjroneMessageHistoryEntity.getId ().toString():null),"id", cjroneMessageHistoryEntity.getId ())
            .eq(StringUtils.isNotBlank(cjroneMessageHistoryEntity.getReceiveName ()!=null && !"".equals(cjroneMessageHistoryEntity.getReceiveName ().toString())? cjroneMessageHistoryEntity.getReceiveName ().toString():null),"receive_name", cjroneMessageHistoryEntity.getReceiveName ())
            .eq(StringUtils.isNotBlank(cjroneMessageHistoryEntity.getReceiveIdCard ()!=null && !"".equals(cjroneMessageHistoryEntity.getReceiveIdCard ().toString())? cjroneMessageHistoryEntity.getReceiveIdCard ().toString():null),"receive_id_card", cjroneMessageHistoryEntity.getReceiveIdCard ())
            .eq(StringUtils.isNotBlank(cjroneMessageHistoryEntity.getMessageContent ()!=null && !"".equals(cjroneMessageHistoryEntity.getMessageContent ().toString())? cjroneMessageHistoryEntity.getMessageContent ().toString():null),"message_content", cjroneMessageHistoryEntity.getMessageContent ())
            .eq(StringUtils.isNotBlank(cjroneMessageHistoryEntity.getMatterType ()!=null && !"".equals(cjroneMessageHistoryEntity.getMatterType ().toString())? cjroneMessageHistoryEntity.getMatterType ().toString():null),"matter_type", cjroneMessageHistoryEntity.getMatterType ())
            .eq(StringUtils.isNotBlank(cjroneMessageHistoryEntity.getApplyId ()!=null && !"".equals(cjroneMessageHistoryEntity.getApplyId ().toString())? cjroneMessageHistoryEntity.getApplyId ().toString():null),"apply_id", cjroneMessageHistoryEntity.getApplyId ())
            .eq(StringUtils.isNotBlank(cjroneMessageHistoryEntity.getSendTime ()!=null && !"".equals(cjroneMessageHistoryEntity.getSendTime ().toString())? cjroneMessageHistoryEntity.getSendTime ().toString():null),"send_time", cjroneMessageHistoryEntity.getSendTime ())
                .orderByDesc("send_time")
        );
        Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key","sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        page.getRecords().forEach( item -> {
            SysDictEntity matterType_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("sxlx_0000") && iii.getValue().equals(
                        item.getMatterType ())).findAny().orElse(null);
            if (matterType_sysDictEntity != null){
                item.setMatterType (matterType_sysDictEntity.getLabel());
            }else{
                item.setMatterType (null);
            }
        });
        return new PageUtils(page);
    }
    @Override
    public List<CjroneMessageHistoryEntity> queryExportData(Map<String, Object> params) {
            return cjroneMessageHistoryDao.queryExportData(params);
    }

}