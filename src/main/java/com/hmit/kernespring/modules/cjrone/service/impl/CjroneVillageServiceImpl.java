package com.hmit.kernespring.modules.cjrone.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.modules.cjrone.dao.CjroneStreetDao;
import com.hmit.kernespring.modules.cjrone.dao.CjroneVillageDao;
import com.hmit.kernespring.modules.cjrone.entity.CjroneAdministrativeDivisionEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneVillageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Map;

@Service("cjroneVillageServiceImpl")
public class CjroneVillageServiceImpl extends ServiceImpl<CjroneVillageDao, CjroneAdministrativeDivisionEntity> implements CjroneVillageService {

    @Autowired
    CjroneVillageDao cjroneVillageDao;

    @Override
    public List<CjroneAdministrativeDivisionEntity> getAdministrativeDivisionList(String code) {
        return cjroneVillageDao.getAdministrativeDivisionList(code);
    }

    @Override
    public List<Map<String,Object>> getAdministrativeDivisionListForAPP(String code){
        return cjroneVillageDao.getAdministrativeDivisionListForAPP(code);
    }

    @Override
    public CjroneAdministrativeDivisionEntity getVillageById(String code) {
        return cjroneVillageDao.getVillageById(code);
    }
}
