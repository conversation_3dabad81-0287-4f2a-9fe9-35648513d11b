package com.hmit.kernespring.modules.cjrone.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;
import com.hmit.kernespring.modules.cjrone.dao.CjroneCertificateApplyDocDao;
import com.hmit.kernespring.modules.cjrone.entity.CjroneCertificateApplyDocEntity;
import com.hmit.kernespring.modules.cjrone.entity.CjroneDocumentEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneCertificateApplyDocService;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;


@Service("cjroneCertificateApplyDocService")
public class CjroneCertificateApplyDocServiceImpl extends ServiceImpl<CjroneCertificateApplyDocDao, CjroneCertificateApplyDocEntity> implements CjroneCertificateApplyDocService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneCertificateApplyDocDao cjroneCertificateApplyDocDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneCertificateApplyDocEntity cjroneCertificateApplyDocEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneCertificateApplyDocEntity.class);
        IPage<CjroneCertificateApplyDocEntity> page = this.page(
                new Query<CjroneCertificateApplyDocEntity>().getPage(params),
                new QueryWrapper<CjroneCertificateApplyDocEntity>()
            .eq(StringUtils.isNotBlank(cjroneCertificateApplyDocEntity.getId ()!=null && !"".equals(cjroneCertificateApplyDocEntity.getId ().toString())? cjroneCertificateApplyDocEntity.getId ().toString():null),"id", cjroneCertificateApplyDocEntity.getId ())
            .eq(StringUtils.isNotBlank(cjroneCertificateApplyDocEntity.getDisabilityCertificateApplicationId ()!=null && !"".equals(cjroneCertificateApplyDocEntity.getDisabilityCertificateApplicationId ().toString())? cjroneCertificateApplyDocEntity.getDisabilityCertificateApplicationId ().toString():null),"disability_certificate_application_id", cjroneCertificateApplyDocEntity.getDisabilityCertificateApplicationId ())
            .eq(StringUtils.isNotBlank(cjroneCertificateApplyDocEntity.getDisabilityAssessmentId ()!=null && !"".equals(cjroneCertificateApplyDocEntity.getDisabilityAssessmentId ().toString())? cjroneCertificateApplyDocEntity.getDisabilityAssessmentId ().toString():null),"disability_assessment_id", cjroneCertificateApplyDocEntity.getDisabilityAssessmentId ())
            .eq(StringUtils.isNotBlank(cjroneCertificateApplyDocEntity.getDisabilityAssessmentDetailName ()!=null && !"".equals(cjroneCertificateApplyDocEntity.getDisabilityAssessmentDetailName ().toString())? cjroneCertificateApplyDocEntity.getDisabilityAssessmentDetailName ().toString():null),"disability_assessment_detail_name", cjroneCertificateApplyDocEntity.getDisabilityAssessmentDetailName ())
            .eq(StringUtils.isNotBlank(cjroneCertificateApplyDocEntity.getDocumentId ()!=null && !"".equals(cjroneCertificateApplyDocEntity.getDocumentId ().toString())? cjroneCertificateApplyDocEntity.getDocumentId ().toString():null),"document_id", cjroneCertificateApplyDocEntity.getDocumentId ())
        );
        return new PageUtils(page);
    }
    @Override
    public List<CjroneCertificateApplyDocEntity> queryExportData(Map<String, Object> params) {
            return cjroneCertificateApplyDocDao.queryExportData(params);
    }
    @Override
    public List<CjroneCertificateApplyDocEntity> queryApplyDocByMap(Map<String, Object> params) {
            return cjroneCertificateApplyDocDao.queryApplyDocByMap(params);
    }

     @Override
    public List<CjroneDocumentEntity> listDocumentByMap(Map<String, Object> params) {
            return cjroneCertificateApplyDocDao.listDocumentByMap(params);
    }

    @Override
    public void deleteByApplicationId(Map<String, Object> params) {
        cjroneCertificateApplyDocDao.deleteByApplicationId(params);
    }

}