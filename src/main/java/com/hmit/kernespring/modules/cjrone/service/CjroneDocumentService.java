package com.hmit.kernespring.modules.cjrone.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone.entity.CjroneCertificateApplyDocEntity;
import com.hmit.kernespring.modules.cjrone.entity.CjroneDocumentEntity;

import java.io.File;
import java.io.FileNotFoundException;
import java.util.List;
import java.util.Map;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-09 14:03:30
 */
public interface CjroneDocumentService extends IService<CjroneDocumentEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneDocumentEntity> queryExportData(Map<String, Object> params);
    List<CjroneDocumentEntity> queryDocumentDataByMap(Map<String, Object> params);
    List<CjroneDocumentEntity> queryDocumentDataByMapN(Map<String, Object> params);
    List<CjroneCertificateApplyDocEntity> queryDocumentDocDataByMap(Map<String, Object> params);
    String downloadFj(List<File> fileList,Map<String, Object> params) throws FileNotFoundException;
}

