package com.hmit.kernespring.modules.cjrone.controller;

import java.io.File;
import java.io.IOException;
import java.util.*;

import java.io.OutputStream;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import org.apache.poi.ss.usermodel.Workbook;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hmit.kernespring.modules.cjrone.entity.CjroneMotorizedWheelchairFuelSubsidyEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneMotorizedWheelchairFuelSubsidyService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 机动轮椅车燃油补贴
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-27 16:56:18
 */
@RestController
@RequestMapping("cjrone/cjronemotorizedwheelchairfuelsubsidy")
public class CjroneMotorizedWheelchairFuelSubsidyController {
    @Autowired
    private CjroneMotorizedWheelchairFuelSubsidyService cjroneMotorizedWheelchairFuelSubsidyService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("cjrone:cjronemotorizedwheelchairfuelsubsidy:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = cjroneMotorizedWheelchairFuelSubsidyService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("cjrone:cjronemotorizedwheelchairfuelsubsidy:info")
    public R info(@PathVariable("id") Integer id){
		CjroneMotorizedWheelchairFuelSubsidyEntity cjroneMotorizedWheelchairFuelSubsidy = cjroneMotorizedWheelchairFuelSubsidyService.getById(id);

        return R.ok().put("cjroneMotorizedWheelchairFuelSubsidy", cjroneMotorizedWheelchairFuelSubsidy);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("cjrone:cjronemotorizedwheelchairfuelsubsidy:save")
    public R save(@RequestBody CjroneMotorizedWheelchairFuelSubsidyEntity cjroneMotorizedWheelchairFuelSubsidy){
		cjroneMotorizedWheelchairFuelSubsidyService.save(cjroneMotorizedWheelchairFuelSubsidy);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("cjrone:cjronemotorizedwheelchairfuelsubsidy:update")
    public R update(@RequestBody CjroneMotorizedWheelchairFuelSubsidyEntity cjroneMotorizedWheelchairFuelSubsidy){
		cjroneMotorizedWheelchairFuelSubsidyService.updateById(cjroneMotorizedWheelchairFuelSubsidy);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("cjrone:cjronemotorizedwheelchairfuelsubsidy:delete")
    public R delete(@RequestBody Integer[] ids){
		cjroneMotorizedWheelchairFuelSubsidyService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    @RequiresPermissions("cjrone:cjronemotorizedwheelchairfuelsubsidy:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        params_import.setHeadRows(1);
        params_import.setTitleRows(0);

        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<CjroneMotorizedWheelchairFuelSubsidyEntity> cjroneMotorizedWheelchairFuelSubsidyList = new ArrayList<>();
        list.forEach(item ->{
                    item.put("id",item.get(""));
                    item.put("name",item.get("姓名"));
                    item.put("idCard",item.get("身份证号"));
                    cjroneMotorizedWheelchairFuelSubsidyList.add(new Gson().fromJson(new Gson().toJson(item), CjroneMotorizedWheelchairFuelSubsidyEntity.class));
        });
        // 保存到数据库
        cjroneMotorizedWheelchairFuelSubsidyService.saveBatch(cjroneMotorizedWheelchairFuelSubsidyList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    @RequiresPermissions("cjrone:cjronemotorizedwheelchairfuelsubsidy:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<CjroneMotorizedWheelchairFuelSubsidyEntity> cjroneMotorizedWheelchairFuelSubsidyEntityList = cjroneMotorizedWheelchairFuelSubsidyService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("机动轮椅车燃油补贴", null, "机动轮椅车燃油补贴");
        Workbook workbook = ExcelExportUtil.exportExcel(params, CjroneMotorizedWheelchairFuelSubsidyEntity.class, cjroneMotorizedWheelchairFuelSubsidyEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "机动轮椅车燃油补贴" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

}
