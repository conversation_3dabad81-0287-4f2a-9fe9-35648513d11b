package com.hmit.kernespring.modules.cjrone.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 家庭无障碍改造
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-27 16:56:18
 */
@Data
@TableName("cjrone_family_accessibility_transformation")
public class CjroneFamilyAccessibilityTransformationEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
@TableId
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 姓名
	 */
	@Excel(name = "姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 身份证号
	 */
	@Excel(name = "身份证号", height = 20, width = 30, isImportField = "true_st")
private String idCard;


	@TableField(exist = false)
	private String disableId;

	@TableField(exist = false)
	private String mobilePhone;

	@TableField(exist = false)
	private String presentAddress;

	@TableField(exist = false)
	private String familyEconoCondition;



}
