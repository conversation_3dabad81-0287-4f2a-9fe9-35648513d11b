package com.hmit.kernespring.modules.cjrone.service.impl;


import com.google.gson.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;

import com.hmit.kernespring.modules.cjrone.dao.TempDiffDao;
import com.hmit.kernespring.modules.cjrone.entity.TempDiffEntity;
import com.hmit.kernespring.modules.cjrone.service.TempDiffService;


@Service("tempDiffService")
public class TempDiffServiceImpl extends ServiceImpl<TempDiffDao, TempDiffEntity> implements TempDiffService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private TempDiffDao tempDiffDao;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        TempDiffEntity tempDiffEntity = gson.fromJson(params.get("key")!=null ? gson.toJson(params.get("key")):null, TempDiffEntity.class);
        IPage<TempDiffEntity> page = this.page(
                new Query<TempDiffEntity>().getPage(params),
                new QueryWrapper<TempDiffEntity>()
            .eq(StringUtils.isNotBlank(tempDiffEntity.getId ()!=null && !"".equals(tempDiffEntity.getId ().toString())? tempDiffEntity.getId ().toString():null),"id", tempDiffEntity.getId ())
            .eq(StringUtils.isNotBlank(tempDiffEntity.getName ()!=null && !"".equals(tempDiffEntity.getName ().toString())? tempDiffEntity.getName ().toString():null),"name", tempDiffEntity.getName ())
            .eq(StringUtils.isNotBlank(tempDiffEntity.getIdCard ()!=null && !"".equals(tempDiffEntity.getIdCard ().toString())? tempDiffEntity.getIdCard ().toString():null),"id_card", tempDiffEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(tempDiffEntity.getDisableId ()!=null && !"".equals(tempDiffEntity.getDisableId ().toString())? tempDiffEntity.getDisableId ().toString():null),"disable_id", tempDiffEntity.getDisableId ())
            .eq(StringUtils.isNotBlank(tempDiffEntity.getError ()!=null && !"".equals(tempDiffEntity.getError ().toString())? tempDiffEntity.getError ().toString():null),"error", tempDiffEntity.getError ())
        );
        return new PageUtils(page);
    }
    @Override
    public List<TempDiffEntity> queryExportData(Map<String, Object> params) {
            return tempDiffDao.queryExportData(params);
    }

}
