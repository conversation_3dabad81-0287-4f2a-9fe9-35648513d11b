package com.hmit.kernespring.modules.cjrone.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.common.utils.*;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.cjrone.entity.*;
import com.hmit.kernespring.modules.cjrone.service.*;
import com.hmit.kernespring.modules.cjrone_bl.entity.*;
import com.hmit.kernespring.modules.cjrone_bl.service.*;
import com.hmit.kernespring.modules.matter_application.entity.CjroneWelfareMatterApplicationEntity;
import com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity;
import com.hmit.kernespring.modules.matter_application.service.CjroneWelfareMatterApplicationService;
import com.hmit.kernespring.modules.matter_application.service.DisabilityCertificateApplicationService;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import com.hmit.kernespring.modules.sys.controller.AbstractController;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.*;

/**
 *
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-09 14:03:30
 */
@RestController
@RequestMapping("cjrone/cjronedocument")
public class CjroneDocumentController extends AbstractController {
    @Autowired
    private DisabilityCertificateApplicationService disabilityCertificateApplicationService;
    @Autowired
    private CjroneEmploymentSubsidyService cjroneEmploymentSubsidyService;
    @Autowired
    private CjroneNursingSubsidyService cjroneNursingSubsidyService;
    @Autowired
    private CjroneRehabilitationSubsidyService cjroneRehabilitationSubsidyService;
    @Autowired
    private CjroneLivingAllowanceService cjroneLivingAllowanceService;
    @Autowired
    private CjroneWelfareMatterApplicationService cjroneWelfareMatterApplicationService;
    @Autowired
    private CjroneSignatureService cjroneSignatureService;
    @Autowired
    private CjroneDocumentService cjroneDocumentService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;
    @Autowired
    private CjroneblLivingSubsidyService cjroneblLivingSubsidyService;
    @Autowired
    private CjroneblNursingSubsidyService cjroneblNursingSubsidyService;
    @Autowired
    private CjroneblLivingAllowanceService cjroneblLivingAllowanceService;
    @Autowired
    private CjroneblZgjbyanglaoService cjroneblZgjbyanglaoService;
    @Autowired
    private CjroneblZgjbyiliaoService cjroneblZgjbyiliaoService;
    @Autowired
    private CjroneblCxjmyanglaoService cjroneblCxjmyanglaoService;
    @Autowired
    private CjroneblCxjmyiliaoService cjroneblCxjmyiliaoService;
    @Autowired
    private CjroneblTemporaryAssistanceService cjroneblTemporaryAssistanceService;
    @Autowired
    private CjroneblRehabilitationSubsidyService cjroneblRehabilitationSubsidyService;
    @Autowired
    private CjroneblBusinessGrantService cjroneblBusinessGrantService;
    @Autowired
    private CjroneblCollegeeduService cjroneblCollegeeduService;
    @Autowired
    private CjroneblChildeduService cjroneblChildeduService;
    @Autowired
    private CjroneblMedicalSupportService cjroneblMedicalSupportService;
    @Autowired
    private CjroneblHospitalizationAllowanceService cjroneblHospitalizationAllowanceService;
    @Autowired
    private Love24Service love24Service;

    //=================================

    /**
     * 上传文件  电子签章的回调函数
     */
    @PostMapping("/uploadNeSignature")
    @Transactional(rollbackFor=Exception.class)
    public R uploadNe(@RequestParam("file") MultipartFile file, @RequestParam("extraParam") String extraParam) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }
        String file_name = "" ;
        String signId = "" ;
        String actionType = "" ; // 1 镇街道 2 区残联 3 民政
        String userName = "" ; // 1 镇街道 2 区残联 3 民政
        System.out.println("--------------extraParam------------------");
        System.out.println(extraParam);
        String[] params = extraParam.split(",");
        List<String> params_list = Arrays.asList(params);
        for(int i=0;i<params_list.size();i++){
            String[] params_tmp = params_list.get(i).split("=");
            if (params_tmp != null && "fileName".equals(params_tmp[0])){
                file_name = params_tmp[1];
            }else if (params_tmp != null && "signId".equals(params_tmp[0])){
                signId = params_tmp[1];
            }else if (params_tmp != null && "actionType".equals(params_tmp[0])){
                actionType = params_tmp[1];
            }else if (params_tmp != null && "userName".equals(params_tmp[0])){
                userName = params_tmp[1];
            }
        }
        System.out.println(file_name+" "+signId);
        // 根据签署文章的id获得实体
        CjroneSignatureEntity cjroneSignatureEntity = cjroneSignatureService.getById(signId);
        if (cjroneSignatureEntity != null){
            String save_path = null;
            String url_path = null;
            if ("惠残事项护理补贴".equals(cjroneSignatureEntity.getType())){
                Map<String, Object> tmp_params = new HashMap<>();
                tmp_params.put("type","惠残事项护理补贴");
                tmp_params.put("status","1");
                tmp_params.put("type_id",cjroneSignatureEntity.getTypeId());
                List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
                alive_list.forEach(item->{
                    item.setStatus("0");
                });
                if (alive_list.size()>0) {
                    cjroneSignatureService.updateBatchById(alive_list);
                }
                // 获得护理补贴的实体
                CjroneblNursingSubsidyEntity cjroneblNursingSubsidyEntity=cjroneblNursingSubsidyService.getById(cjroneSignatureEntity.getTypeId());
                if (cjroneblNursingSubsidyEntity!=null) {
                    save_path = cjroneProperties.getSignaturePath() + cjroneblNursingSubsidyEntity.getIdCard() + "/惠残事项护理补贴/";
                    url_path = "/"+cjroneblNursingSubsidyEntity.getIdCard() + "/"+ URLEncoder.encode("惠残事项护理补贴","UTF-8") +"/";
                    if ("1".equals(actionType)){
                        // 镇街道电子签章  必须镇街道手签才可审核 (6表示区残联待手签)
                        if ("6".equals(cjroneblNursingSubsidyEntity.getSignStatus())) {
                            cjroneblNursingSubsidyEntity.setStatus("2"); //镇街道待审核
                            cjroneblNursingSubsidyEntity.setSignatureStatus("5");  //区残联待电子签章
                            cjroneblNursingSubsidyService.updateById(cjroneblNursingSubsidyEntity);
                        }
                    }else  if ("2".equals(actionType)){
                        // 区残联电子签章  必须区残联手签才可审核（8表示手签完成）
                        if ("8".equals(cjroneblNursingSubsidyEntity.getSignStatus())) {
                            cjroneblNursingSubsidyEntity.setStatus("7"); //区残联负责人待审核
                            cjroneblNursingSubsidyEntity.setSignatureStatus("6");  //电子签章完成
                            cjroneblNursingSubsidyService.updateById(cjroneblNursingSubsidyEntity);
                        }
                    }
                }else {
                    save_path = cjroneProperties.getSignaturePath()+cjroneSignatureEntity.getTypeId()+"/hand/";
                    url_path = "/"+cjroneSignatureEntity.getTypeId()+"/hand/";
                }
            }
            else if ("惠残事项生活补贴".equals(cjroneSignatureEntity.getType())){
                Map<String, Object> tmp_params = new HashMap<>();
                tmp_params.put("type","惠残事项生活补贴");
                tmp_params.put("status","1");
                tmp_params.put("type_id",cjroneSignatureEntity.getTypeId());
                List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
                alive_list.forEach(item->{
                    item.setStatus("0");
                });
                if (alive_list.size()>0) {
                    cjroneSignatureService.updateBatchById(alive_list);
                }
                // 获得生活补贴的实体信息
                CjroneblLivingSubsidyEntity cjroneblLivingSubsidyEntity= cjroneblLivingSubsidyService.getById(cjroneSignatureEntity.getTypeId());
                if (cjroneblLivingSubsidyEntity!=null) {
                    save_path = cjroneProperties.getSignaturePath() + cjroneblLivingSubsidyEntity.getIdCard() + "/惠残事项生活补贴/";
                    url_path = "/"+cjroneblLivingSubsidyEntity.getIdCard() + "/"+ URLEncoder.encode("惠残事项生活补贴","UTF-8") +"/";
                    //cjroneblLivingSubsidyEntity.setAuditPerson(userName);
                    //cjroneblLivingSubsidyEntity.setAuditDate(DateUtils.getNowDate());
                    if ("1".equals(actionType)){
                        // 镇街道电子签章  必须镇街道手签才可审核 (4 表示民政待手签 )
                        if ("4".equals(cjroneblLivingSubsidyEntity.getSignStatus())) {
                            cjroneblLivingSubsidyEntity.setStatus("2");
                            cjroneblLivingSubsidyEntity.setSignatureStatus("4");
                            //cjroneblLivingSubsidyEntity.setSignStatus("4");
                            cjroneblLivingSubsidyService.updateById(cjroneblLivingSubsidyEntity);
                        }
                    }else  if ("2".equals(actionType)){
                        // 区残联电子签章  必须区残联手签才可审核
                        if ("8".equals(cjroneblLivingSubsidyEntity.getSignStatus())) {
                            cjroneblLivingSubsidyEntity.setStatus("7"); //区残联负责人待审核
                            cjroneblLivingSubsidyEntity.setSignatureStatus("6");  //电子签章完成
                            cjroneblLivingSubsidyService.updateById(cjroneblLivingSubsidyEntity);
                        }
                    }else  if ("3".equals(actionType)){
                       // 民政电子签章  必须民政手签才可审核 （6表示区残联经办人待手签）
                        if ("6".equals(cjroneblLivingSubsidyEntity.getSignStatus())) {
                            cjroneblLivingSubsidyEntity.setStatus("5"); //民政负责人待审核
                            cjroneblLivingSubsidyEntity.setSignatureStatus("5");  //区负责人待电子签章
                            cjroneblLivingSubsidyService.updateById(cjroneblLivingSubsidyEntity);
                        }
                    }
                }else {
                    save_path = cjroneProperties.getSignaturePath()+cjroneSignatureEntity.getTypeId()+"/hand/";
                    url_path = "/"+cjroneSignatureEntity.getTypeId()+"/hand/";
                }
            }
            else if("惠残事项生活补助金".equals(cjroneSignatureEntity.getType())){
                Map<String, Object> tmp_params = new HashMap<>();
                tmp_params.put("type","惠残事项生活补助金");
                tmp_params.put("status","1");
                tmp_params.put("type_id",cjroneSignatureEntity.getTypeId());
                List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
                alive_list.forEach(item->{
                    item.setStatus("0");
                });
                if (alive_list.size()>0) {
                    cjroneSignatureService.updateBatchById(alive_list);
                }

                // 获得生活补助金的实体
                CjroneblLivingAllowanceEntity cjroneblLivingAllowanceEntity=cjroneblLivingAllowanceService.getById(cjroneSignatureEntity.getTypeId());
                if (cjroneblLivingAllowanceEntity!=null) {
                    save_path = cjroneProperties.getSignaturePath() + cjroneblLivingAllowanceEntity.getIdCard() + "/惠残事项生活补助金/";
                    url_path = "/"+cjroneblLivingAllowanceEntity.getIdCard() + "/"+ URLEncoder.encode("惠残事项生活补助金","UTF-8") +"/";
                    if ("1".equals(actionType)){
                        // 镇街道电子签章  必须镇街道手签才可审核 (6表示区残联待手签)
                        if ("6".equals(cjroneblLivingAllowanceEntity.getSignStatus())) {
                            cjroneblLivingAllowanceEntity.setStatus("2"); //镇街道待审核
                            cjroneblLivingAllowanceEntity.setSignatureStatus("5");  //区残联待电子签章
                            cjroneblLivingAllowanceService.updateById(cjroneblLivingAllowanceEntity);
                        }
                    }else  if ("2".equals(actionType)){
                        // 区残联电子签章  必须区残联手签才可审核（8表示手签完成）
                        if ("8".equals(cjroneblLivingAllowanceEntity.getSignStatus())) {
                            cjroneblLivingAllowanceEntity.setStatus("7"); //区残联负责人待审核
                            cjroneblLivingAllowanceEntity.setSignatureStatus("6");  //电子签章完成
                            cjroneblLivingAllowanceService.updateById(cjroneblLivingAllowanceEntity);
                        }
                    }
                }else {
                    save_path = cjroneProperties.getSignaturePath()+cjroneSignatureEntity.getTypeId()+"/hand/";
                    url_path = "/"+cjroneSignatureEntity.getTypeId()+"/hand/";
                }
            }
            else if("惠残事项职工基本养老保险补助".equals(cjroneSignatureEntity.getType())){
                Map<String, Object> tmp_params = new HashMap<>();
                tmp_params.put("type","惠残事项职工基本养老保险补助");
                tmp_params.put("status","1");
                tmp_params.put("type_id",cjroneSignatureEntity.getTypeId());
                List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
                alive_list.forEach(item->{
                    item.setStatus("0");
                });
                if (alive_list.size()>0) {
                    cjroneSignatureService.updateBatchById(alive_list);
                }

                // 获得职工基本养老保险的实体
                CjroneblZgjbyanglaoEntity cjroneblZgjbyanglaoEntity = cjroneblZgjbyanglaoService.getById(cjroneSignatureEntity.getTypeId());
                if (cjroneblZgjbyanglaoEntity!=null) {
                    save_path = cjroneProperties.getSignaturePath() + cjroneblZgjbyanglaoEntity.getDisableId() + "/惠残事项职工基本养老保险补助/";
                    url_path = "/"+cjroneblZgjbyanglaoEntity.getDisableId() + "/"+ URLEncoder.encode("惠残事项职工基本养老保险补助","UTF-8") +"/";
                    if ("1".equals(actionType)){
                        // 镇街道电子签章  必须镇街道手签才可审核 (6表示区残联待手签)
                        if ("6".equals(cjroneblZgjbyanglaoEntity.getSignStatus())) {
                            cjroneblZgjbyanglaoEntity.setStatus("2"); //镇街道待审核
                            cjroneblZgjbyanglaoEntity.setSignatureStatus("5");  //区残联待电子签章
                            cjroneblZgjbyanglaoService.updateById(cjroneblZgjbyanglaoEntity);
                        }
                    }else  if ("2".equals(actionType)){
                        // 区残联电子签章  必须区残联手签才可审核（8表示手签完成）
                        if ("8".equals(cjroneblZgjbyanglaoEntity.getSignStatus())) {
                            cjroneblZgjbyanglaoEntity.setStatus("7"); //区残联负责人待审核
                            cjroneblZgjbyanglaoEntity.setSignatureStatus("6");  //电子签章完成
                            cjroneblZgjbyanglaoService.updateById(cjroneblZgjbyanglaoEntity);
                        }
                    }
                }else {
                    save_path = cjroneProperties.getSignaturePath()+cjroneSignatureEntity.getTypeId()+"/hand/";
                    url_path = "/"+cjroneSignatureEntity.getTypeId()+"/hand/";
                }
            }
            else if("惠残事项职工基本医疗保险补助".equals(cjroneSignatureEntity.getType())){
                Map<String, Object> tmp_params = new HashMap<>();
                tmp_params.put("type","惠残事项职工基本医疗保险补助");
                tmp_params.put("status","1");
                tmp_params.put("type_id",cjroneSignatureEntity.getTypeId());
                List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
                alive_list.forEach(item->{
                    item.setStatus("0");
                });
                if (alive_list.size()>0) {
                    cjroneSignatureService.updateBatchById(alive_list);
                }

                // 获得职工基本医疗保险的实体
                CjroneblZgjbyiliaoEntity cjroneblZgjbyiliaoEntity = cjroneblZgjbyiliaoService.getById(cjroneSignatureEntity.getTypeId());
                if (cjroneblZgjbyiliaoEntity!=null) {
                    save_path = cjroneProperties.getSignaturePath() + cjroneblZgjbyiliaoEntity.getDisableId() + "/惠残事项职工基本医疗保险补助/";
                    url_path = "/"+cjroneblZgjbyiliaoEntity.getDisableId() + "/"+ URLEncoder.encode("惠残事项职工基本医疗保险补助","UTF-8") +"/";
                    if ("1".equals(actionType)){
                        // 镇街道电子签章  必须镇街道手签才可审核 (6表示区残联待手签)
                        if ("6".equals(cjroneblZgjbyiliaoEntity.getSignStatus())) {
                            cjroneblZgjbyiliaoEntity.setStatus("2"); //镇街道待审核
                            cjroneblZgjbyiliaoEntity.setSignatureStatus("5");  //区残联待电子签章
                            cjroneblZgjbyiliaoService.updateById(cjroneblZgjbyiliaoEntity);
                        }
                    }else  if ("2".equals(actionType)){
                        // 区残联电子签章  必须区残联手签才可审核（8表示手签完成）
                        if ("8".equals(cjroneblZgjbyiliaoEntity.getSignStatus())) {
                            cjroneblZgjbyiliaoEntity.setStatus("7"); //区残联负责人待审核
                            cjroneblZgjbyiliaoEntity.setSignatureStatus("6");  //电子签章完成
                            cjroneblZgjbyiliaoService.updateById(cjroneblZgjbyiliaoEntity);
                        }
                    }
                }else {
                    save_path = cjroneProperties.getSignaturePath()+cjroneSignatureEntity.getTypeId()+"/hand/";
                    url_path = "/"+cjroneSignatureEntity.getTypeId()+"/hand/";
                }
            }
            else if("惠残事项城乡居民养老保险补助".equals(cjroneSignatureEntity.getType())){
                Map<String, Object> tmp_params = new HashMap<>();
                tmp_params.put("type","惠残事项城乡居民养老保险补助");
                tmp_params.put("status","1");
                tmp_params.put("type_id",cjroneSignatureEntity.getTypeId());
                List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
                alive_list.forEach(item->{
                    item.setStatus("0");
                });
                if (alive_list.size()>0) {
                    cjroneSignatureService.updateBatchById(alive_list);
                }

                // 获得城乡居民养老保险的实体
                CjroneblCxjmyanglaoEntity cjroneblCxjmyanglaoEntity =cjroneblCxjmyanglaoService.getById(cjroneSignatureEntity.getTypeId());
                if (cjroneblCxjmyanglaoEntity!=null) {
                    save_path = cjroneProperties.getSignaturePath() + cjroneblCxjmyanglaoEntity.getDisableId() + "/惠残事项城乡居民养老保险补助/";
                    url_path = "/"+cjroneblCxjmyanglaoEntity.getDisableId() + "/"+ URLEncoder.encode("惠残事项城乡居民养老保险补助","UTF-8") +"/";
                    if ("1".equals(actionType)){
                        // 镇街道电子签章  必须镇街道手签才可审核 (6表示区残联待手签)
                        if ("6".equals(cjroneblCxjmyanglaoEntity.getSignStatus())) {
                            cjroneblCxjmyanglaoEntity.setStatus("2"); //镇街道待审核
                            cjroneblCxjmyanglaoEntity.setSignatureStatus("5");  //区残联待电子签章
                            cjroneblCxjmyanglaoService.updateById(cjroneblCxjmyanglaoEntity);
                        }
                    }else  if ("2".equals(actionType)){
                        // 区残联电子签章  必须区残联手签才可审核（8表示手签完成）
                        if ("8".equals(cjroneblCxjmyanglaoEntity.getSignStatus())) {
                            cjroneblCxjmyanglaoEntity.setStatus("7"); //区残联负责人待审核
                            cjroneblCxjmyanglaoEntity.setSignatureStatus("6");  //电子签章完成
                            cjroneblCxjmyanglaoService.updateById(cjroneblCxjmyanglaoEntity);
                        }
                    }
                }else {
                    save_path = cjroneProperties.getSignaturePath()+cjroneSignatureEntity.getTypeId()+"/hand/";
                    url_path = "/"+cjroneSignatureEntity.getTypeId()+"/hand/";
                }
            }
            else if("惠残事项城乡基本医疗保险补助".equals(cjroneSignatureEntity.getType())){
                Map<String, Object> tmp_params = new HashMap<>();
                tmp_params.put("type","惠残事项城乡基本医疗保险补助");
                tmp_params.put("status","1");
                tmp_params.put("type_id",cjroneSignatureEntity.getTypeId());
                List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
                alive_list.forEach(item->{
                    item.setStatus("0");
                });
                if (alive_list.size()>0) {
                    cjroneSignatureService.updateBatchById(alive_list);
                }

                // 获得城乡居民养老保险的实体
                CjroneblCxjmyiliaoEntity cjroneblCxjmyiliaoEntity =cjroneblCxjmyiliaoService.getById(cjroneSignatureEntity.getTypeId());
                if (cjroneblCxjmyiliaoEntity!=null) {
                    save_path = cjroneProperties.getSignaturePath() + cjroneblCxjmyiliaoEntity.getDisableId() + "/惠残事项城乡基本医疗保险补助/";
                    url_path = "/"+cjroneblCxjmyiliaoEntity.getDisableId() + "/"+ URLEncoder.encode("惠残事项城乡基本医疗保险补助","UTF-8") +"/";
                    if ("1".equals(actionType)){
                        // 镇街道电子签章  必须镇街道手签才可审核 (6表示区残联待手签)
                        if ("6".equals(cjroneblCxjmyiliaoEntity.getSignStatus())) {
                            cjroneblCxjmyiliaoEntity.setStatus("2"); //镇街道待审核
                            cjroneblCxjmyiliaoEntity.setSignatureStatus("5");  //区残联待电子签章
                            cjroneblCxjmyiliaoService.updateById(cjroneblCxjmyiliaoEntity);
                        }
                    }else  if ("2".equals(actionType)){
                        // 区残联电子签章  必须区残联手签才可审核（8表示手签完成）
                        if ("8".equals(cjroneblCxjmyiliaoEntity.getSignStatus())) {
                            cjroneblCxjmyiliaoEntity.setStatus("7"); //区残联负责人待审核
                            cjroneblCxjmyiliaoEntity.setSignatureStatus("6");  //电子签章完成
                            cjroneblCxjmyiliaoService.updateById(cjroneblCxjmyiliaoEntity);
                        }
                    }
                }else {
                    save_path = cjroneProperties.getSignaturePath()+cjroneSignatureEntity.getTypeId()+"/hand/";
                    url_path = "/"+cjroneSignatureEntity.getTypeId()+"/hand/";
                }
            }
            else if("惠残事项残疾人临时救助".equals(cjroneSignatureEntity.getType())){
                Map<String, Object> tmp_params = new HashMap<>();
                tmp_params.put("type","惠残事项残疾人临时救助");
                tmp_params.put("status","1");
                tmp_params.put("type_id",cjroneSignatureEntity.getTypeId());
                List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
                alive_list.forEach(item->{
                    item.setStatus("0");
                });
                if (alive_list.size()>0) {
                    cjroneSignatureService.updateBatchById(alive_list);
                }

                // 获得残疾人临时救助的实体
                CjroneblTemporaryAssistanceEntity cjroneblTemporaryAssistanceEntity =cjroneblTemporaryAssistanceService.getById(cjroneSignatureEntity.getTypeId());
                if (cjroneblTemporaryAssistanceEntity!=null) {
                    save_path = cjroneProperties.getSignaturePath() + cjroneblTemporaryAssistanceEntity.getIdCard() + "/惠残事项残疾人临时救助/";
                    url_path = "/"+cjroneblTemporaryAssistanceEntity.getIdCard() + "/"+ URLEncoder.encode("惠残事项残疾人临时救助","UTF-8") +"/";
                    if ("1".equals(actionType)){
                        // 镇街道电子签章  必须镇街道手签才可审核 (6表示区残联待手签)
                        if ("6".equals(cjroneblTemporaryAssistanceEntity.getSignStatus())) {
                            cjroneblTemporaryAssistanceEntity.setStatus("2"); //镇街道待审核
                            cjroneblTemporaryAssistanceEntity.setSignatureStatus("5");  //区残联待电子签章
                            cjroneblTemporaryAssistanceService.updateById(cjroneblTemporaryAssistanceEntity);
                        }
                    }else  if ("2".equals(actionType)){
                        // 区残联电子签章  必须区残联手签才可审核（8表示手签完成）
                        if ("8".equals(cjroneblTemporaryAssistanceEntity.getSignStatus())) {
                            cjroneblTemporaryAssistanceEntity.setStatus("7"); //区残联负责人待审核
                            cjroneblTemporaryAssistanceEntity.setSignatureStatus("6");  //电子签章完成
                            cjroneblTemporaryAssistanceService.updateById(cjroneblTemporaryAssistanceEntity);
                        }
                    }
                }else {
                    save_path = cjroneProperties.getSignaturePath()+cjroneSignatureEntity.getTypeId()+"/hand/";
                    url_path = "/"+cjroneSignatureEntity.getTypeId()+"/hand/";
                }
            }
            else if("惠残事项康复补助".equals(cjroneSignatureEntity.getType())){
                Map<String, Object> tmp_params = new HashMap<>();
                tmp_params.put("type","惠残事项康复补助");
                tmp_params.put("status","1");
                tmp_params.put("type_id",cjroneSignatureEntity.getTypeId());
                List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
                alive_list.forEach(item->{
                    item.setStatus("0");
                });
                if (alive_list.size()>0) {
                    cjroneSignatureService.updateBatchById(alive_list);
                }

                // 获得残疾人临时救助的实体
                CjroneblRehabilitationSubsidyEntity cjroneblRehabilitationSubsidyEntity =cjroneblRehabilitationSubsidyService.getById(cjroneSignatureEntity.getTypeId());
                if (cjroneblRehabilitationSubsidyEntity!=null) {
                    save_path = cjroneProperties.getSignaturePath() + cjroneblRehabilitationSubsidyEntity.getIdCard() + "/惠残事项康复补助/";
                    url_path = "/"+cjroneblRehabilitationSubsidyEntity.getIdCard() + "/"+ URLEncoder.encode("惠残事项康复补助","UTF-8") +"/";
                    if ("1".equals(actionType)){
                        // 镇街道电子签章  必须镇街道手签才可审核 (6表示区残联待手签)
                        if ("6".equals(cjroneblRehabilitationSubsidyEntity.getSignStatus())) {
                            cjroneblRehabilitationSubsidyEntity.setStatus("2"); //镇街道待审核
                            cjroneblRehabilitationSubsidyEntity.setSignatureStatus("5");  //区残联待电子签章
                            cjroneblRehabilitationSubsidyService.updateById(cjroneblRehabilitationSubsidyEntity);
                        }
                    }else  if ("2".equals(actionType)){
                        // 区残联电子签章  必须区残联手签才可审核（8表示手签完成）
                        if ("8".equals(cjroneblRehabilitationSubsidyEntity.getSignStatus())) {
                            cjroneblRehabilitationSubsidyEntity.setStatus("7"); //区残联负责人待审核
                            cjroneblRehabilitationSubsidyEntity.setSignatureStatus("6");  //电子签章完成
                            cjroneblRehabilitationSubsidyService.updateById(cjroneblRehabilitationSubsidyEntity);
                        }
                    }
                }else {
                    save_path = cjroneProperties.getSignaturePath()+cjroneSignatureEntity.getTypeId()+"/hand/";
                    url_path = "/"+cjroneSignatureEntity.getTypeId()+"/hand/";
                }
            }
            else if("惠残事项创业补助".equals(cjroneSignatureEntity.getType())){
                Map<String, Object> tmp_params = new HashMap<>();
                tmp_params.put("type","惠残事项创业补助");
                tmp_params.put("status","1");
                tmp_params.put("type_id",cjroneSignatureEntity.getTypeId());
                List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
                alive_list.forEach(item->{
                    item.setStatus("0");
                });
                if (alive_list.size()>0) {
                    cjroneSignatureService.updateBatchById(alive_list);
                }

                // 获得残疾人创业补助的实体
                CjroneblBusinessGrantEntity cjroneblBusinessGrantEntity =cjroneblBusinessGrantService.getById(cjroneSignatureEntity.getTypeId());
                if (cjroneblBusinessGrantEntity!=null) {
                    save_path = cjroneProperties.getSignaturePath() + cjroneblBusinessGrantEntity.getDisableId() + "/惠残事项创业补助/";
                    url_path = "/"+cjroneblBusinessGrantEntity.getDisableId() + "/"+ URLEncoder.encode("惠残事项创业补助","UTF-8") +"/";
                    if ("1".equals(actionType)){
                        // 镇街道电子签章  必须镇街道手签才可审核 (6表示区残联待手签)
                        if ("6".equals(cjroneblBusinessGrantEntity.getSignStatus())) {
                            cjroneblBusinessGrantEntity.setStatus("2"); //镇街道待审核
                            cjroneblBusinessGrantEntity.setSignatureStatus("5");  //区残联待电子签章
                            cjroneblBusinessGrantService.updateById(cjroneblBusinessGrantEntity);
                        }
                    }else  if ("2".equals(actionType)){
                        // 区残联电子签章  必须区残联手签才可审核（8表示手签完成）
                        if ("8".equals(cjroneblBusinessGrantEntity.getSignStatus())) {
                            cjroneblBusinessGrantEntity.setStatus("7"); //区残联负责人待审核
                            cjroneblBusinessGrantEntity.setSignatureStatus("6");  //电子签章完成
                            cjroneblBusinessGrantService.updateById(cjroneblBusinessGrantEntity);
                        }
                    }
                }else {
                    save_path = cjroneProperties.getSignaturePath()+cjroneSignatureEntity.getTypeId()+"/hand/";
                    url_path = "/"+cjroneSignatureEntity.getTypeId()+"/hand/";
                }
            }
            else if("惠残事项大学生补助".equals(cjroneSignatureEntity.getType())){
                Map<String, Object> tmp_params = new HashMap<>();
                tmp_params.put("type","惠残事项大学生补助");
                tmp_params.put("status","1");
                tmp_params.put("type_id",cjroneSignatureEntity.getTypeId());
                List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
                alive_list.forEach(item->{
                    item.setStatus("0");
                });
                if (alive_list.size()>0) {
                    cjroneSignatureService.updateBatchById(alive_list);
                }

                // 获得残疾人大学生补助的实体
                CjroneblCollegeeduEntity cjroneblCollegeeduEntity =cjroneblCollegeeduService.getById(cjroneSignatureEntity.getTypeId());
                if (cjroneblCollegeeduEntity!=null) {
                    save_path = cjroneProperties.getSignaturePath() + cjroneblCollegeeduEntity.getDisableId() + "/惠残事项大学生补助/";
                    url_path = "/"+cjroneblCollegeeduEntity.getDisableId() + "/"+ URLEncoder.encode("惠残事项大学生补助","UTF-8") +"/";
                    if ("2".equals(actionType)){
                        // 区残联电子签章  必须区残联手签才可审核（8表示手签完成）
                        if ("8".equals(cjroneblCollegeeduEntity.getSignStatus())) {
                            cjroneblCollegeeduEntity.setStatus("7"); //区残联负责人待审核
                            cjroneblCollegeeduEntity.setSignatureStatus("7");  //教育局待电子签章
                            cjroneblCollegeeduService.updateById(cjroneblCollegeeduEntity);
                        }
                    } else if ("3".equals(actionType)){
                        // 教育局电子签章
                        if ("8".equals(cjroneblCollegeeduEntity.getSignStatus())) {
                            cjroneblCollegeeduEntity.setStatus("98"); //教育局待审核
                            cjroneblCollegeeduEntity.setSignatureStatus("8");  //财政局待电子签章
                            cjroneblCollegeeduService.updateById(cjroneblCollegeeduEntity);
                        }
                    } else if ("4".equals(actionType)){
                        // 财政局电子签章
                        if ("8".equals(cjroneblCollegeeduEntity.getSignStatus())) {
                            cjroneblCollegeeduEntity.setStatus("97"); //财政局待审核
                            cjroneblCollegeeduEntity.setSignatureStatus("6");  //电子签章完成
                            cjroneblCollegeeduService.updateById(cjroneblCollegeeduEntity);
                        }
                    }
                }else {
                    save_path = cjroneProperties.getSignaturePath()+cjroneSignatureEntity.getTypeId()+"/hand/";
                    url_path = "/"+cjroneSignatureEntity.getTypeId()+"/hand/";
                }
            }
            else if("惠残事项残疾人子女教育补贴".equals(cjroneSignatureEntity.getType())){
                Map<String, Object> tmp_params = new HashMap<>();
                tmp_params.put("type","惠残事项残疾人子女教育补贴");
                tmp_params.put("status","1");
                tmp_params.put("type_id",cjroneSignatureEntity.getTypeId());
                List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
                alive_list.forEach(item->{
                    item.setStatus("0");
                });
                if (alive_list.size()>0) {
                    cjroneSignatureService.updateBatchById(alive_list);
                }

                // 获得补助的实体
                CjroneblChildeduEntity cjroneblChildeduEntity = cjroneblChildeduService.getById(cjroneSignatureEntity.getTypeId());
                if (cjroneblChildeduEntity!=null) {
                    save_path = cjroneProperties.getSignaturePath() + cjroneblChildeduEntity.getIdCard() + "/惠残事项残疾人子女教育补贴/";
                    url_path = "/"+cjroneblChildeduEntity.getIdCard() + "/"+ URLEncoder.encode("惠残事项残疾人子女教育补贴","UTF-8") +"/";
                    if ("1".equals(actionType)){
                        // 镇街道电子签章  必须镇街道手签才可审核 (6表示区残联待手签)
                        if ("6".equals(cjroneblChildeduEntity.getSignStatus())) {
                            cjroneblChildeduEntity.setStatus("2"); //镇街道待审核
                            cjroneblChildeduEntity.setSignatureStatus("5");  //区残联待电子签章
                            cjroneblChildeduService.updateById(cjroneblChildeduEntity);
                        }
                    }else  if ("2".equals(actionType)){
                        // 区残联电子签章  必须区残联手签才可审核（8表示手签完成）
                        if ("8".equals(cjroneblChildeduEntity.getSignStatus())) {
                            cjroneblChildeduEntity.setStatus("7"); //区残联负责人待审核
                            cjroneblChildeduEntity.setSignatureStatus("6");  //电子签章完成
                            cjroneblChildeduService.updateById(cjroneblChildeduEntity);
                        }
                    }
                }else {
                    save_path = cjroneProperties.getSignaturePath()+cjroneSignatureEntity.getTypeId()+"/hand/";
                    url_path = "/"+cjroneSignatureEntity.getTypeId()+"/hand/";
                }
            }
            else if("惠残事项医疗救助".equals(cjroneSignatureEntity.getType())){
                Map<String, Object> tmp_params = new HashMap<>();
                tmp_params.put("type","惠残事项医疗救助");
                tmp_params.put("status","1");
                tmp_params.put("type_id",cjroneSignatureEntity.getTypeId());
                List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
                alive_list.forEach(item->{
                    item.setStatus("0");
                });
                if (alive_list.size()>0) {
                    cjroneSignatureService.updateBatchById(alive_list);
                }

                // 获得残疾人的实体
                CjroneblMedicalSupportEntity cjroneblMedicalSupportEntity =cjroneblMedicalSupportService.getById(cjroneSignatureEntity.getTypeId());
                if (cjroneblMedicalSupportEntity!=null) {
                    save_path = cjroneProperties.getSignaturePath() + cjroneblMedicalSupportEntity.getDisableId() + "/惠残事项医疗救助/";
                    url_path = "/"+cjroneblMedicalSupportEntity.getDisableId() + "/"+ URLEncoder.encode("惠残事项医疗救助","UTF-8") +"/";
                    if ("1".equals(actionType)){
                        // 镇街道电子签章  必须镇街道手签才可审核 (6表示区残联待手签)
                        if ("6".equals(cjroneblMedicalSupportEntity.getSignStatus())) {
                            cjroneblMedicalSupportEntity.setStatus("2"); //镇街道待审核
                            cjroneblMedicalSupportEntity.setSignatureStatus("5");  //区残联待电子签章
                            cjroneblMedicalSupportService.updateById(cjroneblMedicalSupportEntity);
                        }
                    }else  if ("2".equals(actionType)){
                        // 区残联电子签章  必须区残联手签才可审核（8表示手签完成）
                        if ("8".equals(cjroneblMedicalSupportEntity.getSignStatus())) {
                            cjroneblMedicalSupportEntity.setStatus("7"); //区残联负责人待审核
                            cjroneblMedicalSupportEntity.setSignatureStatus("6");  //电子签章完成
                            cjroneblMedicalSupportService.updateById(cjroneblMedicalSupportEntity);
                        }
                    }
                }else {
                    save_path = cjroneProperties.getSignaturePath()+cjroneSignatureEntity.getTypeId()+"/hand/";
                    url_path = "/"+cjroneSignatureEntity.getTypeId()+"/hand/";
                }
            }
            else if("惠残事项住院补助".equals(cjroneSignatureEntity.getType())){
                Map<String, Object> tmp_params = new HashMap<>();
                tmp_params.put("type","惠残事项住院补助");
                tmp_params.put("status","1");
                tmp_params.put("type_id",cjroneSignatureEntity.getTypeId());
                List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
                alive_list.forEach(item->{
                    item.setStatus("0");
                });
                if (alive_list.size()>0) {
                    cjroneSignatureService.updateBatchById(alive_list);
                }

                // 获得残疾人的实体
                CjroneblHospitalizationAllowanceEntity cjroneblHospitalizationAllowanceEntity =cjroneblHospitalizationAllowanceService.getById(cjroneSignatureEntity.getTypeId());
                if (cjroneblHospitalizationAllowanceEntity!=null) {
                    save_path = cjroneProperties.getSignaturePath() + cjroneblHospitalizationAllowanceEntity.getDisableId() + "/惠残事项住院补助/";
                    url_path = "/"+cjroneblHospitalizationAllowanceEntity.getDisableId() + "/"+ URLEncoder.encode("惠残事项住院补助","UTF-8") +"/";
                    if ("1".equals(actionType)){
                        // 镇街道电子签章  必须镇街道手签才可审核 (6表示区残联待手签)
                        if ("6".equals(cjroneblHospitalizationAllowanceEntity.getSignStatus())) {
                            cjroneblHospitalizationAllowanceEntity.setStatus("2"); //镇街道待审核
                            cjroneblHospitalizationAllowanceEntity.setSignatureStatus("5");  //区残联待电子签章
                            cjroneblHospitalizationAllowanceService.updateById(cjroneblHospitalizationAllowanceEntity);
                        }
                    }else  if ("2".equals(actionType)){
                        // 区残联电子签章  必须区残联手签才可审核（8表示手签完成）
                        if ("8".equals(cjroneblHospitalizationAllowanceEntity.getSignStatus())) {
                            cjroneblHospitalizationAllowanceEntity.setStatus("7"); //区残联负责人待审核
                            cjroneblHospitalizationAllowanceEntity.setSignatureStatus("6");  //电子签章完成
                            cjroneblHospitalizationAllowanceService.updateById(cjroneblHospitalizationAllowanceEntity);
                        }
                    }
                }else {
                    save_path = cjroneProperties.getSignaturePath()+cjroneSignatureEntity.getTypeId()+"/hand/";
                    url_path = "/"+cjroneSignatureEntity.getTypeId()+"/hand/";
                }
            }
            else if("惠残事项智慧爱心24小时".equals(cjroneSignatureEntity.getType())){
                Map<String, Object> tmp_params = new HashMap<>();
                tmp_params.put("type","惠残事项智慧爱心24小时");
                tmp_params.put("status","1");
                tmp_params.put("type_id",cjroneSignatureEntity.getTypeId());
                List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
                alive_list.forEach(item->{
                    item.setStatus("0");
                });
                if (alive_list.size()>0) {
                    cjroneSignatureService.updateBatchById(alive_list);
                }

                // 获得补助的实体
                Love24Entity love24Entity = love24Service.getById(cjroneSignatureEntity.getTypeId());
                if (love24Entity!=null) {
                    save_path = cjroneProperties.getSignaturePath() + love24Entity.getIdCard() + "/惠残事项智慧爱心24小时/";
                    url_path = "/"+love24Entity.getIdCard() + "/"+ URLEncoder.encode("惠残事项智慧爱心24小时","UTF-8") +"/";
                    if ("1".equals(actionType)){
                        // 镇街道电子签章  必须镇街道手签才可审核 (6表示区残联待手签)
                        if ("6".equals(love24Entity.getSignStatus())) {
                            love24Entity.setStatus("2"); //镇街道待审核
                            love24Entity.setSignatureStatus("5");  //区残联待电子签章
                            love24Service.updateById(love24Entity);
                        }
                    }else  if ("2".equals(actionType)){
                        // 区残联电子签章  必须区残联手签才可审核（8表示手签完成）
                        if ("8".equals(love24Entity.getSignStatus())) {
                            love24Entity.setStatus("7"); //区残联负责人待审核
                            love24Entity.setSignatureStatus("6");  //电子签章完成
                            love24Service.updateById(love24Entity);
                        }
                    }
                }else {
                    save_path = cjroneProperties.getSignaturePath()+cjroneSignatureEntity.getTypeId()+"/hand/";
                    url_path = "/"+cjroneSignatureEntity.getTypeId()+"/hand/";
                }
            }


            // 保存文件
            String file_path = save_path+ file_name;
            System.out.println(file_path);
            File filePath = new File(file_path);
            if(!filePath.getParentFile().exists()){
                filePath.mkdirs();
            }
            try {
                file.transferTo(filePath);
            } catch (IllegalStateException | IOException e) {
                e.printStackTrace();
            }
            cjroneSignatureEntity.setFileActUrl(file_path);
            cjroneSignatureEntity.setFileName(file_name);
            String url = cjroneSignatureEntity.getUrl().substring(0,cjroneSignatureEntity.getUrl().lastIndexOf("/"))+"/"+file_name;
            cjroneSignatureEntity.setUrl(url);
            cjroneSignatureEntity.setAccountName("admin");
            cjroneSignatureEntity.setAccountId("1");
            cjroneSignatureEntity.setCreateId(Long.parseLong("1"));
            cjroneSignatureEntity.setCreateDate(new Date());
            cjroneSignatureEntity.setStatus("1");
            cjroneSignatureService.save(cjroneSignatureEntity);
        }
        return R.ok();
    }


    /**
     * 上传文件
     */
    @PostMapping("/uploadNe")
    // @RequiresPermissions("sys:oss:all")
    public R uploadNe(@RequestParam("file") MultipartFile file, @RequestParam("disabilityType") Integer disabilityType, @RequestParam("cardId") String cardId, @RequestParam("disabilityTypeName") String disabilityTypeName) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }
        System.out.println("cardId:" + cardId);
        if (disabilityTypeName !=null && "photo".equals(disabilityTypeName)){
            //上传文件
            String file_path = "/static/" + cardId + "/" + PinYinUtil.getFullSpell(disabilityTypeName) + "/" + "/" + file.getOriginalFilename();
            String file_path_act = cjroneProperties.getDownloadPath() + "/" + cardId + ".jpg";
            System.out.println(file_path_act);
            File fileAct = new File(file_path_act);
            if (!fileAct.getParentFile().exists()) {
                fileAct.getParentFile().mkdirs();
            }
            try {
                file.transferTo(fileAct);
            } catch (IllegalStateException | IOException e) {
                e.printStackTrace();
            }

            //保存文件信息
            CjroneDocumentEntity cjroneDocumentEntity = new CjroneDocumentEntity();
            cjroneDocumentEntity.setCreateId(getUserId());
            cjroneDocumentEntity.setCreateTime(DateUtils.getNowDate());
            cjroneDocumentEntity.setDisabilityAssessmentDetailName("电子照片");
            cjroneDocumentEntity.setDisabilityAssessmentId(disabilityType);
            cjroneDocumentEntity.setFileName(fileAct.getName());
            cjroneDocumentEntity.setFilePath(file_path);
            cjroneDocumentEntity.setFilePathAct(fileAct.getPath());
            cjroneDocumentEntity.setFileSize(Double.parseDouble(String.valueOf(file.getSize())));
            cjroneDocumentEntity.setFileType(file.getContentType());
            cjroneDocumentEntity.setRemark("");
            cjroneDocumentEntity.setStatus("1");
            cjroneDocumentService.save(cjroneDocumentEntity);

            return R.ok().put("idCard",cardId).put("url", file_path_act).put("docId", cjroneDocumentEntity.getId()).put("detailValue", disabilityTypeName);
        }else {
            //上传文件
            String file_path = "/static/" + cardId + "/" + PinYinUtil.getFullSpell(disabilityTypeName) + "/" + "/" + file.getOriginalFilename();
            String file_path_act = cjroneProperties.getUploadPath() + cardId + "/" + PinYinUtil.getFullSpell(disabilityTypeName) + "/" + file.getOriginalFilename();
            System.out.println(file_path_act);
            File fileAct = new File(file_path_act);
            if (!fileAct.getParentFile().exists()) {
                fileAct.getParentFile().mkdirs();
            }
            try {
                file.transferTo(fileAct);
            } catch (IllegalStateException | IOException e) {
                e.printStackTrace();
            }

            //保存文件信息
            CjroneDocumentEntity cjroneDocumentEntity = new CjroneDocumentEntity();
            cjroneDocumentEntity.setCreateId(getUserId());
            cjroneDocumentEntity.setCreateTime(DateUtils.getNowDate());
            cjroneDocumentEntity.setDisabilityAssessmentDetailName(disabilityTypeName);
            cjroneDocumentEntity.setDisabilityAssessmentId(disabilityType);
            cjroneDocumentEntity.setFileName(fileAct.getName());
            cjroneDocumentEntity.setFilePath(file_path);
            cjroneDocumentEntity.setFilePathAct(fileAct.getPath());
            cjroneDocumentEntity.setFileSize(Double.parseDouble(String.valueOf(file.getSize())));
            cjroneDocumentEntity.setFileType(file.getContentType());
            cjroneDocumentEntity.setRemark("");
            cjroneDocumentEntity.setStatus("1");
            cjroneDocumentService.save(cjroneDocumentEntity);

            return R.ok().put("url", file_path).put("docId", cjroneDocumentEntity.getId()).put("detailValue", disabilityTypeName);
        }
    }





    //================================

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("cjrone:cjronedocument:list")
    public R list(@RequestParam Map<String, Object> params) {
        PageUtils page = cjroneDocumentService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("cjrone:cjronedocument:info")
    public R info(@PathVariable("id") Integer id) {
        CjroneDocumentEntity cjroneDocument = cjroneDocumentService.getById(id);

        return R.ok().put("cjroneDocument", cjroneDocument);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("cjrone:cjronedocument:save")
    public R save(@RequestBody CjroneDocumentEntity cjroneDocument) {
        cjroneDocumentService.save(cjroneDocument);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("cjrone:cjronedocument:update")
    public R update(@RequestBody CjroneDocumentEntity cjroneDocument) {
        cjroneDocumentService.updateById(cjroneDocument);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("cjrone:cjronedocument:delete")
    public R delete(@RequestBody Integer[] ids) {
        cjroneDocumentService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }

    /**
     * 导入数据
     */
    @RequestMapping("/importData")
    // @RequiresPermissions("cjrone:cjronedocument:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath() + file.getOriginalFilename();
        File filePath = new File(file_path);
        if (!filePath.getParentFile().exists()) {
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<CjroneDocumentEntity> cjroneDocumentList = new ArrayList<>();
        list.forEach(item -> {
            item.put("id", item.get(""));
            item.put("disabilityCertificateApplicationId", item.get(""));
            item.put("fileName", item.get(""));
            item.put("fileSize", item.get(""));
            item.put("createId", item.get(""));
            item.put("createTime", item.get(""));
            item.put("filePath", item.get(""));
            item.put("filePathAct", item.get(""));
            item.put("fileType", item.get(""));
            item.put("remark", item.get(""));
            item.put("status", item.get(""));
            item.put("disabilityAssessmentId", item.get(""));
            item.put("disabilityAssessmentDetailName", item.get(""));
            cjroneDocumentList.add(new Gson().fromJson(new Gson().toJson(item), CjroneDocumentEntity.class));
        });
        // 保存到数据库
        cjroneDocumentService.saveBatch(cjroneDocumentList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }

    /**
     * 导出数据
     */
    @RequestMapping("/exportData")
    // @RequiresPermissions("cjrone:cjronedocument:export")
    public void exportData(@RequestParam Map<String, Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<CjroneDocumentEntity> cjroneDocumentEntityList = cjroneDocumentService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("", null, "");
        Workbook workbook = ExcelExportUtil.exportExcel(params, CjroneDocumentEntity.class, cjroneDocumentEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "";
        response.setHeader("Content-disposition", "attachment;filename=" + new String(fileName.getBytes(), "iso-8859-1") + ".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }


    /**
     * 下载附件 公交卡 zip
     */
    @RequestMapping("/downloadFj/buscard/{file_name}")
    public HttpServletResponse downloadFj(@PathVariable("file_name") String file_name, HttpServletResponse response) throws IOException {
        response.reset();
        String file_download_path = cjroneProperties.getDownloadZipPath()+"buscard/"+file_name;
        System.out.println(file_download_path);
        return HttpRequestUtil.downloadZip(new File(file_download_path), response);
    }

    /**
     * 下载附件 公交卡 zip
     */
    @RequestMapping("/downloadFj/application/{file_name}")
    public HttpServletResponse downloadApplyFj(@PathVariable("file_name") String file_name, HttpServletResponse response) throws IOException {
        response.reset();
        String file_download_path = cjroneProperties.getDownloadZipPath()+"application/"+file_name;
        System.out.println(file_download_path);
        return HttpRequestUtil.downloadZip(new File(file_download_path), response);
    }


}
