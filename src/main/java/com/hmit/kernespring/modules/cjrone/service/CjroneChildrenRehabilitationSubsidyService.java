package com.hmit.kernespring.modules.cjrone.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone.entity.CjroneChildrenRehabilitationSubsidyEntity;

import java.math.BigDecimal;
import java.util.Map;

import java.util.List;

/**
 * 残疾少年儿童康复训练补助
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-10 11:18:02
 */
public interface CjroneChildrenRehabilitationSubsidyService extends IService<CjroneChildrenRehabilitationSubsidyEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneChildrenRehabilitationSubsidyEntity> queryExportData(Map<String, Object> params);
    void updateAudioById(CjroneChildrenRehabilitationSubsidyEntity cjroneChildrenRehabilitationSubsidy);
    List<CjroneChildrenRehabilitationSubsidyEntity> queryHistoryByIdCard(String idCard, Integer status, String subsidyYear);

    Map<String, Object> statistics(String approvalYear);
    
    /**
     * 计算年度累计金额
     * @param idCard 身份证号
     * @param subsidyYear 补助年份
     * @return 年度累计金额
     */
    BigDecimal calculateYearTotalAmount(String idCard, Integer subsidyYear);
    
    /**
     * 验证年度康复补贴上限，超出限制时抛出异常
     * @param idCard 身份证号
     * @param subsidyYear 补助年份
     * @param newAmount 新申请金额
     * @param rehabilitationType 康复类型
     * @throws RuntimeException 当累计金额超过限制时抛出
     */
    void validateAnnualSubsidyLimit(String idCard, Integer subsidyYear, BigDecimal newAmount, String rehabilitationType);
}
