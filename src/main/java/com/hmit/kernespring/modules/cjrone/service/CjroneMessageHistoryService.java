package com.hmit.kernespring.modules.cjrone.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone.entity.CjroneMessageHistoryEntity;

import java.util.Map;

import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-06-28 14:53:50
 */
public interface CjroneMessageHistoryService extends IService<CjroneMessageHistoryEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneMessageHistoryEntity> queryExportData(Map<String, Object> params);
}

