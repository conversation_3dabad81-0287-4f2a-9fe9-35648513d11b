package com.hmit.kernespring.modules.cjrone.dao;

import com.hmit.kernespring.modules.cjrone.entity.CjroneMessageHistoryEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-06-28 14:53:50
 */
@Mapper
public interface CjroneMessageHistoryDao extends BaseMapper<CjroneMessageHistoryEntity> {
    List<CjroneMessageHistoryEntity> queryExportData(Map<String, Object> params);
	
}
