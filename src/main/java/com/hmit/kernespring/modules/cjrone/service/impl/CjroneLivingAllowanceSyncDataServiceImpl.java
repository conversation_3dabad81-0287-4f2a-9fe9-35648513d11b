package com.hmit.kernespring.modules.cjrone.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;
import com.hmit.kernespring.modules.cjrone.dao.CjroneLivingAllowanceSyncDataDao;
import com.hmit.kernespring.modules.cjrone.entity.CjroneLivingAllowanceSyncDataEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneLivingAllowanceSyncDataService;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service("cjroneLivingAllowanceSyncDataService")
public class CjroneLivingAllowanceSyncDataServiceImpl extends ServiceImpl<CjroneLivingAllowanceSyncDataDao, CjroneLivingAllowanceSyncDataEntity> implements CjroneLivingAllowanceSyncDataService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneLivingAllowanceSyncDataDao cjroneLivingAllowanceSyncDataDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneLivingAllowanceSyncDataEntity cjroneLivingAllowanceSyncDataEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneLivingAllowanceSyncDataEntity.class);
        IPage<CjroneLivingAllowanceSyncDataEntity> page = this.page(
                new Query<CjroneLivingAllowanceSyncDataEntity>().getPage(params),
                new QueryWrapper<CjroneLivingAllowanceSyncDataEntity>()
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceSyncDataEntity.getId ()!=null && !"".equals(cjroneLivingAllowanceSyncDataEntity.getId ().toString())? cjroneLivingAllowanceSyncDataEntity.getId ().toString():null),"id", cjroneLivingAllowanceSyncDataEntity.getId ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceSyncDataEntity.getName ()!=null && !"".equals(cjroneLivingAllowanceSyncDataEntity.getName ().toString())? cjroneLivingAllowanceSyncDataEntity.getName ().toString():null),"name", cjroneLivingAllowanceSyncDataEntity.getName ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceSyncDataEntity.getIdCard ()!=null && !"".equals(cjroneLivingAllowanceSyncDataEntity.getIdCard ().toString())? cjroneLivingAllowanceSyncDataEntity.getIdCard ().toString():null),"id_card", cjroneLivingAllowanceSyncDataEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceSyncDataEntity.getCreateTime ()!=null && !"".equals(cjroneLivingAllowanceSyncDataEntity.getCreateTime ().toString())? cjroneLivingAllowanceSyncDataEntity.getCreateTime ().toString():null),"create_time", cjroneLivingAllowanceSyncDataEntity.getCreateTime ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceSyncDataEntity.getFamilyEconoCondition ()!=null && !"".equals(cjroneLivingAllowanceSyncDataEntity.getFamilyEconoCondition ().toString())? cjroneLivingAllowanceSyncDataEntity.getFamilyEconoCondition ().toString():null),"family_econo_condition", cjroneLivingAllowanceSyncDataEntity.getFamilyEconoCondition ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceSyncDataEntity.getMedicalInsurance ()!=null && !"".equals(cjroneLivingAllowanceSyncDataEntity.getMedicalInsurance ().toString())? cjroneLivingAllowanceSyncDataEntity.getMedicalInsurance ().toString():null),"medical_insurance", cjroneLivingAllowanceSyncDataEntity.getMedicalInsurance ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceSyncDataEntity.getIsDead ()!=null && !"".equals(cjroneLivingAllowanceSyncDataEntity.getIsDead ().toString())? cjroneLivingAllowanceSyncDataEntity.getIsDead ().toString():null),"is_dead", cjroneLivingAllowanceSyncDataEntity.getIsDead ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceSyncDataEntity.getIsTk ()!=null && !"".equals(cjroneLivingAllowanceSyncDataEntity.getIsTk ().toString())? cjroneLivingAllowanceSyncDataEntity.getIsTk ().toString():null),"is_tk", cjroneLivingAllowanceSyncDataEntity.getIsTk ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceSyncDataEntity.getIsSixty ()!=null && !"".equals(cjroneLivingAllowanceSyncDataEntity.getIsSixty ().toString())? cjroneLivingAllowanceSyncDataEntity.getIsSixty ().toString():null),"is_sixty", cjroneLivingAllowanceSyncDataEntity.getIsSixty ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceSyncDataEntity.getIsWorkinjury ()!=null && !"".equals(cjroneLivingAllowanceSyncDataEntity.getIsWorkinjury ().toString())? cjroneLivingAllowanceSyncDataEntity.getIsWorkinjury ().toString():null),"is_workinjury", cjroneLivingAllowanceSyncDataEntity.getIsWorkinjury ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceSyncDataEntity.getIsDischild ()!=null && !"".equals(cjroneLivingAllowanceSyncDataEntity.getIsDischild ().toString())? cjroneLivingAllowanceSyncDataEntity.getIsDischild ().toString():null),"is_dischild", cjroneLivingAllowanceSyncDataEntity.getIsDischild ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceSyncDataEntity.getIsResident ()!=null && !"".equals(cjroneLivingAllowanceSyncDataEntity.getIsResident ().toString())? cjroneLivingAllowanceSyncDataEntity.getIsResident ().toString():null),"is_resident", cjroneLivingAllowanceSyncDataEntity.getIsResident ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceSyncDataEntity.getIsClork ()!=null && !"".equals(cjroneLivingAllowanceSyncDataEntity.getIsClork ().toString())? cjroneLivingAllowanceSyncDataEntity.getIsClork ().toString():null),"is_clork", cjroneLivingAllowanceSyncDataEntity.getIsClork ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceSyncDataEntity.getStatus ()!=null && !"".equals(cjroneLivingAllowanceSyncDataEntity.getStatus ().toString())? cjroneLivingAllowanceSyncDataEntity.getStatus ().toString():null),"status", cjroneLivingAllowanceSyncDataEntity.getStatus ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceSyncDataEntity.getIsApplyWelfareMatter ()!=null && !"".equals(cjroneLivingAllowanceSyncDataEntity.getIsApplyWelfareMatter ().toString())? cjroneLivingAllowanceSyncDataEntity.getIsApplyWelfareMatter ().toString():null),"is_apply_welfare_matter", cjroneLivingAllowanceSyncDataEntity.getIsApplyWelfareMatter ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceSyncDataEntity.getIsFx ()!=null && !"".equals(cjroneLivingAllowanceSyncDataEntity.getIsFx ().toString())? cjroneLivingAllowanceSyncDataEntity.getIsFx ().toString():null),"is_fx", cjroneLivingAllowanceSyncDataEntity.getIsFx ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceSyncDataEntity.getIsHkqy ()!=null && !"".equals(cjroneLivingAllowanceSyncDataEntity.getIsHkqy ().toString())? cjroneLivingAllowanceSyncDataEntity.getIsHkqy ().toString():null),"is_hkqy", cjroneLivingAllowanceSyncDataEntity.getIsHkqy ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceSyncDataEntity.getFxInfo ()!=null && !"".equals(cjroneLivingAllowanceSyncDataEntity.getFxInfo ().toString())? cjroneLivingAllowanceSyncDataEntity.getFxInfo ().toString():null),"fx_info", cjroneLivingAllowanceSyncDataEntity.getFxInfo ())
            .eq(StringUtils.isNotBlank(cjroneLivingAllowanceSyncDataEntity.getHkqyInfo ()!=null && !"".equals(cjroneLivingAllowanceSyncDataEntity.getHkqyInfo ().toString())? cjroneLivingAllowanceSyncDataEntity.getHkqyInfo ().toString():null),"hkqy_info", cjroneLivingAllowanceSyncDataEntity.getHkqyInfo ())
        );
        Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key","sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        page.getRecords().forEach( item -> {
        });
        return new PageUtils(page);
    }
    @Override
    public List<CjroneLivingAllowanceSyncDataEntity> queryExportData(Map<String, Object> params) {
            return cjroneLivingAllowanceSyncDataDao.queryExportData(params);
    }

}