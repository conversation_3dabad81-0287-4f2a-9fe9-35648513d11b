package com.hmit.kernespring.modules.cjrone.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-29 15:54:11
 */
@Data
@TableName("cjrone_welfare_apply_doc")
public class CjroneWelfareApplyDocEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	
@TableId
	@Excel(name = "主键ID", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 
	 */
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer welfareMatterApplicationId;
	/**
	 * 
	 */
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer documentId;

}
