package com.hmit.kernespring.modules.cjrone.service.impl;


import com.google.gson.*;
import com.hmit.kernespring.modules.cjrone.dao.CjroneChildrenRehabilitationSubsidyDao;
import com.hmit.kernespring.modules.cjrone.entity.CjroneChildrenRehabilitationSubsidyEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneChildrenRehabilitationSubsidyService;
import com.hmit.kernespring.modules.cjrone.constants.CjroneConstants;
import com.hmit.kernespring.modules.sys.entity.SysUserEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import com.hmit.kernespring.modules.matter_application.dao.CjroneWelfareMatterApplicationDao;

import java.math.BigDecimal;
import java.util.*;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;
import org.springframework.transaction.annotation.Transactional;

import static com.hmit.kernespring.common.utils.ShiroUtils.getSubject;


@Service("cjroneChildrenRehabilitationSubsidyService")
public class CjroneChildrenRehabilitationSubsidyServiceImpl extends ServiceImpl<CjroneChildrenRehabilitationSubsidyDao, CjroneChildrenRehabilitationSubsidyEntity> implements CjroneChildrenRehabilitationSubsidyService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneChildrenRehabilitationSubsidyDao cjroneChildrenRehabilitationSubsidyDao;
    @Autowired
    private SysDictService sysDictService;
    @Autowired
    private CjroneWelfareMatterApplicationDao cjroneWelfareMatterApplicationDao;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneChildrenRehabilitationSubsidyEntity entity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneChildrenRehabilitationSubsidyEntity.class);
        IPage<CjroneChildrenRehabilitationSubsidyEntity> page = this.page(
                new Query<CjroneChildrenRehabilitationSubsidyEntity>().getPage(params),
                getWrapper(entity)
        );
//        Map<String, Object> params_map = new HashMap<>();
//        params_map.put("redis_key","sys_dict:all");
//        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
//        page.getRecords().forEach( item -> {
//            SysDictEntity rehabilitationType_sysDictEntity = sys_dict_all_list.stream().filter(
//                iii->iii.getCode().equals("kflx_0000") && iii.getValue().equals(
//                        item.getRehabilitationType ())).findAny().orElse(null);
//            if (rehabilitationType_sysDictEntity != null){
//                item.setRehabilitationType (rehabilitationType_sysDictEntity.getLabel());
//            }else{
//                item.setRehabilitationType (null);
//            }
//        });
        return new PageUtils(page);
    }
    @Override
    public List<CjroneChildrenRehabilitationSubsidyEntity> queryExportData(Map<String, Object> params) {
        // 直接从URL参数构建实体对象，不依赖JSON解析
        CjroneChildrenRehabilitationSubsidyEntity entity = new CjroneChildrenRehabilitationSubsidyEntity();
        
        // 从URL参数中设置查询条件
        if (params.get("name") != null) entity.setName(params.get("name").toString());
        if (params.get("sex") != null) entity.setSex(params.get("sex").toString());
        if (params.get("birthday") != null) entity.setBirthday(params.get("birthday").toString());
        if (params.get("nationality") != null) entity.setNationality(params.get("nationality").toString());
        if (params.get("idCard") != null) entity.setIdCard(params.get("idCard").toString());
        if (params.get("mobilePhone") != null) entity.setMobilePhone(params.get("mobilePhone").toString());
        if (params.get("status") != null) entity.setStatus(params.get("status").toString());
        if (params.get("subsidyYear") != null) entity.setSubsidyYear(Integer.valueOf(params.get("subsidyYear").toString()));
        if (params.get("subsidyMonth") != null) entity.setSubsidyMonth(Integer.valueOf(params.get("subsidyMonth").toString()));
        if (params.get("rehabilitationType") != null) entity.setRehabilitationType(params.get("rehabilitationType").toString());
        if (params.get("rehabilitationCenterName") != null) entity.setRehabilitationCenterName(params.get("rehabilitationCenterName").toString());
        if (params.get("designatedInstitution") != null) entity.setDesignatedInstitution(params.get("designatedInstitution").toString());
        if (params.get("operatorName") != null) entity.setOperatorName(params.get("operatorName").toString());
        if (params.get("auditorName") != null) entity.setAuditorName(params.get("auditorName").toString());
        if (params.get("fundsSource") != null) entity.setFundsSource(params.get("fundsSource").toString());
        if (params.get("dibaoFlag") != null) entity.setDibaoFlag(Boolean.valueOf(params.get("dibaoFlag").toString()));
        if (params.get("isOutOfProvince") != null) entity.setIsOutOfProvince(Boolean.valueOf(params.get("isOutOfProvince").toString()));
        if (params.get("nativeZhen") != null) entity.setNativeZhen((String) params.get("nativeZhen"));
        
        return this.list(getWrapper(entity));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAudioById(CjroneChildrenRehabilitationSubsidyEntity cjroneChildrenRehabilitationSubsidy) {
        // 检查年度累计金额是否超过限制
        if (cjroneChildrenRehabilitationSubsidy.getSubsidyYear() != null &&
            cjroneChildrenRehabilitationSubsidy.getDistrictDisabilityAuditAmount() != null &&
            "8".equals(cjroneChildrenRehabilitationSubsidy.getStatus())) {
            validateAnnualSubsidyLimit(
                    cjroneChildrenRehabilitationSubsidy.getIdCard(),
                    cjroneChildrenRehabilitationSubsidy.getSubsidyYear(),
                    cjroneChildrenRehabilitationSubsidy.getDistrictDisabilityAuditAmount(),
                    cjroneChildrenRehabilitationSubsidy.getRehabilitationType() // 传递康复类型
            );
        }
        
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id", cjroneChildrenRehabilitationSubsidy.getId());
        map.put("matter_name", "残疾少年儿童康复训练补助");
        map.put("verify_time", new Date());
        map.put("status", cjroneChildrenRehabilitationSubsidy.getStatus());
        if (cjroneChildrenRehabilitationSubsidy.getStatus() != null) {
            cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        }
        cjroneChildrenRehabilitationSubsidy.setApprovalTime(new Date());
        super.updateById(cjroneChildrenRehabilitationSubsidy);
    }

    @Override
    public List<CjroneChildrenRehabilitationSubsidyEntity> queryHistoryByIdCard(String idCard, Integer status, String subsidyYear) {
        QueryWrapper<CjroneChildrenRehabilitationSubsidyEntity> queryWrapper = new QueryWrapper<CjroneChildrenRehabilitationSubsidyEntity>()
            .eq(StringUtils.isNotBlank(idCard), "id_card", idCard);
        
        if (status != null) {
            queryWrapper.eq("status", status);
        }
        
        if (StringUtils.isNotBlank(subsidyYear)) {
            queryWrapper.eq("subsidy_year", subsidyYear);
        }
        
        return this.list(queryWrapper.orderByDesc("create_time"));
    }

    @Override
    public Map<String, Object> statistics(String approvalYear) {
       return cjroneChildrenRehabilitationSubsidyDao.queryStatistics(approvalYear);
    }
    
    @Override
    public BigDecimal calculateYearTotalAmount(String idCard, Integer subsidyYear) {
        // 使用DAO层的方法直接计算年度累计金额
        return cjroneChildrenRehabilitationSubsidyDao.calculateYearTotalAmount(idCard, subsidyYear);
    }
    
    @Override
    public void validateAnnualSubsidyLimit(String idCard, Integer subsidyYear, BigDecimal newAmount, String rehabilitationType) {
        // 如果新金额为null，则认为没有限制
        if (newAmount == null) {
            return;
        }

        // 计算当前年度已累计的金额
        BigDecimal currentYearTotal = calculateYearTotalAmount(idCard, subsidyYear);

        // 根据康复类型确定年度累计金额限制
        BigDecimal limitAmount;
        if ("视力".equals(rehabilitationType)) {
            limitAmount = CjroneConstants.CHILDREN_REHABILITATION_SUBSIDY_VISUAL_YEAR_LIMIT;
        } else if ("听力".equals(rehabilitationType) || "言语".equals(rehabilitationType) || "智力".equals(rehabilitationType)) {
            limitAmount = CjroneConstants.CHILDREN_REHABILITATION_SUBSIDY_HEARING_SPEECH_INTELLECTUAL_YEAR_LIMIT;
        } else if ("肢体".equals(rehabilitationType) || "孤独症".equals(rehabilitationType)) {
            limitAmount = CjroneConstants.CHILDREN_REHABILITATION_SUBSIDY_LIMB_AUTISM_YEAR_LIMIT;
        } else {
            // 对于未明确指定的康复类型，抛出运行时异常
            throw new RuntimeException("未知的康复类型: " + rehabilitationType + "，无法进行年度金额上限检查。");
        }

        // 检查加上新金额后是否超过限制
        BigDecimal totalWithNewAmount = currentYearTotal.add(newAmount);
        boolean isOverLimit = totalWithNewAmount.compareTo(limitAmount) > 0;

        // 如果超过限制，将限制金额附加到异常消息中
        if (isOverLimit) {
            throw new UnsupportedOperationException(
                String.format("该身份证号在%d年度的累计报销金额已超过限制。康复类型: %s, 年度上限: %.2f元, 当前累计: %.2f元, 本次申请: %.2f元, 申请后总计: %.2f元",
                    subsidyYear,
                    rehabilitationType,
                    limitAmount,
                    currentYearTotal,
                    newAmount,
                    totalWithNewAmount)
                );
        }
    }

    /**
     * 构建查询条件Wrapper
     * @param entity 查询条件实体
     * @return QueryWrapper
     */
    private QueryWrapper<CjroneChildrenRehabilitationSubsidyEntity> getWrapper(CjroneChildrenRehabilitationSubsidyEntity entity) {
        QueryWrapper<CjroneChildrenRehabilitationSubsidyEntity> wrapper= new QueryWrapper<CjroneChildrenRehabilitationSubsidyEntity>()
            .eq(StringUtils.isNotBlank(entity.getId ()!=null && !"".equals(entity.getId ().toString())? entity.getId ().toString():null),"id", entity.getId ())
            .eq(StringUtils.isNotBlank(entity.getName ()!=null && !"".equals(entity.getName ().toString())? entity.getName ().toString():null),"name", entity.getName ())
            .eq(StringUtils.isNotBlank(entity.getSex ()!=null && !"".equals(entity.getSex ().toString())? entity.getSex ().toString():null),"sex", entity.getSex ())
            .eq(StringUtils.isNotBlank(entity.getBirthday ()!=null && !"".equals(entity.getBirthday ().toString())? entity.getBirthday ().toString():null),"birthday", entity.getBirthday ())
            .eq(StringUtils.isNotBlank(entity.getNationality ()!=null && !"".equals(entity.getNationality ().toString())? entity.getNationality ().toString():null),"nationality", entity.getNationality ())
            .eq(StringUtils.isNotBlank(entity.getIdCard ()!=null && !"".equals(entity.getIdCard ().toString())? entity.getIdCard ().toString():null),"id_card", entity.getIdCard ())
            .eq(StringUtils.isNotBlank(entity.getMobilePhone ()!=null && !"".equals(entity.getMobilePhone ().toString())? entity.getMobilePhone ().toString():null),"mobile_phone", entity.getMobilePhone ())
            .eq(StringUtils.isNotBlank(entity.getWelfareMatterApplicationId ()!=null && !"".equals(entity.getWelfareMatterApplicationId ().toString())? entity.getWelfareMatterApplicationId ().toString():null),"welfare_matter_application_id", entity.getWelfareMatterApplicationId ())
            .eq(StringUtils.isNotBlank(entity.getAttach ()!=null && !"".equals(entity.getAttach ().toString())? entity.getAttach ().toString():null),"attach", entity.getAttach ())
            .eq(StringUtils.isNotBlank(entity.getYearTotalAmount ()!=null && !"".equals(entity.getYearTotalAmount ().toString())? entity.getYearTotalAmount ().toString():null),"year_total_amount", entity.getYearTotalAmount ())
            .eq(StringUtils.isNotBlank(entity.getNo ()!=null && !"".equals(entity.getNo ().toString())? entity.getNo ().toString():null),"no", entity.getNo ())
            .eq(StringUtils.isNotBlank(entity.getDate ()!=null && !"".equals(entity.getDate ().toString())? entity.getDate ().toString():null),"date", entity.getDate ())
            .eq(StringUtils.isNotBlank(entity.getRehabilitationCenterName ()!=null && !"".equals(entity.getRehabilitationCenterName ().toString())? entity.getRehabilitationCenterName ().toString():null),"rehabilitation_center_name", entity.getRehabilitationCenterName ())
            .eq(StringUtils.isNotBlank(entity.getDesignatedInstitution ()!=null && !"".equals(entity.getDesignatedInstitution ().toString())? entity.getDesignatedInstitution ().toString():null),"designated_institution", entity.getDesignatedInstitution ())
            .eq(StringUtils.isNotBlank(entity.getRehabilitationType ()!=null && !"".equals(entity.getRehabilitationType ().toString())? entity.getRehabilitationType ().toString():null),"rehabilitation_type", entity.getRehabilitationType ())
            .eq(StringUtils.isNotBlank(entity.getSubsidyStandard ()!=null && !"".equals(entity.getSubsidyStandard ().toString())? entity.getSubsidyStandard ().toString():null),"subsidy_standard", entity.getSubsidyStandard ())
            .eq(StringUtils.isNotBlank(entity.getSubsidyYear ()!=null && !"".equals(entity.getSubsidyYear ().toString())? entity.getSubsidyYear ().toString():null),"subsidy_year", entity.getSubsidyYear ())
            .eq(StringUtils.isNotBlank(entity.getSubsidyMonth ()!=null && !"".equals(entity.getSubsidyMonth ().toString())? entity.getSubsidyMonth ().toString():null),"subsidy_month", entity.getSubsidyMonth ())
            .eq(StringUtils.isNotBlank(entity.getActualInvoiceAmount ()!=null && !"".equals(entity.getActualInvoiceAmount ().toString())? entity.getActualInvoiceAmount ().toString():null),"actual_invoice_amount", entity.getActualInvoiceAmount ())
            .eq(StringUtils.isNotBlank(entity.getDistrictDisabilityAuditAmount ()!=null && !"".equals(entity.getDistrictDisabilityAuditAmount ().toString())? entity.getDistrictDisabilityAuditAmount ().toString():null),"district_disability_audit_amount", entity.getDistrictDisabilityAuditAmount ())
            .eq(StringUtils.isNotBlank(entity.getOperatorId ()!=null && !"".equals(entity.getOperatorId ().toString())? entity.getOperatorId ().toString():null),"operator_id", entity.getOperatorId ())
            .eq(StringUtils.isNotBlank(entity.getOperatorName ()!=null && !"".equals(entity.getOperatorName ().toString())? entity.getOperatorName ().toString():null),"operator_name", entity.getOperatorName ())
            .eq(StringUtils.isNotBlank(entity.getAuditorId ()!=null && !"".equals(entity.getAuditorId ().toString())? entity.getAuditorId ().toString():null),"auditor_id", entity.getAuditorId ())
            .eq(StringUtils.isNotBlank(entity.getAuditorName ()!=null && !"".equals(entity.getAuditorName ().toString())? entity.getAuditorName ().toString():null),"auditor_name", entity.getAuditorName ())
            .eq(StringUtils.isNotBlank(entity.getFundsSource ()!=null && !"".equals(entity.getFundsSource ().toString())? entity.getFundsSource ().toString():null),"funds_source", entity.getFundsSource ())
            .eq(StringUtils.isNotBlank(entity.getApplyRemark ()!=null && !"".equals(entity.getApplyRemark ().toString())? entity.getApplyRemark ().toString():null),"apply_remark", entity.getApplyRemark ())
            .eq(StringUtils.isNotBlank(entity.getApprovalRemark ()!=null && !"".equals(entity.getApprovalRemark ().toString())? entity.getApprovalRemark ().toString():null),"approval_remark", entity.getApprovalRemark ())
            .eq(StringUtils.isNotBlank(entity.getCreateTime ()!=null && !"".equals(entity.getCreateTime ().toString())? entity.getCreateTime ().toString():null),"create_time", entity.getCreateTime ())
            .eq(StringUtils.isNotBlank(entity.getDibaoFlag ()!=null && !"".equals(entity.getDibaoFlag ().toString())? entity.getDibaoFlag ().toString():null),"dibao_flag", entity.getDibaoFlag ())
            .eq(StringUtils.isNotBlank(entity.getAbsenceCertAttachment ()!=null && !"".equals(entity.getAbsenceCertAttachment ().toString())? entity.getAbsenceCertAttachment ().toString():null),"absence_cert_attachment", entity.getAbsenceCertAttachment ())
            .eq(StringUtils.isNotBlank(entity.getStatus ()!=null && !"".equals(entity.getStatus ().toString())? entity.getStatus ().toString():null),"status", entity.getStatus ())
            .eq(StringUtils.isNotBlank(entity.getDiagnosisCertificateImage ()!=null && !"".equals(entity.getDiagnosisCertificateImage ().toString())? entity.getDiagnosisCertificateImage ().toString():null),"diagnosis_certificate_image", entity.getDiagnosisCertificateImage ())
            .eq(StringUtils.isNotBlank(entity.getDiagnosisCertificateStartTime ()!=null && !"".equals(entity.getDiagnosisCertificateStartTime ().toString())? entity.getDiagnosisCertificateStartTime ().toString():null),"diagnosis_certificate_start_time", entity.getDiagnosisCertificateStartTime ())
            .eq(StringUtils.isNotBlank(entity.getDiagnosisCertificateEndTime ()!=null && !"".equals(entity.getDiagnosisCertificateEndTime ().toString())? entity.getDiagnosisCertificateEndTime ().toString():null),"diagnosis_certificate_end_time", entity.getDiagnosisCertificateEndTime ())
            .eq(StringUtils.isNotBlank(entity.getIsOutOfProvince ()!=null && !"".equals(entity.getIsOutOfProvince ().toString())? entity.getIsOutOfProvince ().toString():null),"is_out_of_province", entity.getIsOutOfProvince ())
            .eq(StringUtils.isNotBlank(entity.getApprovalTime ()!=null && !"".equals(entity.getApprovalTime ().toString())? entity.getApprovalTime ().toString():null),"approval_time", entity.getApprovalTime ())
            .eq(StringUtils.isNotBlank(entity.getNativeZhen()), "native_zhen", entity.getNativeZhen());

        SysUserEntity user = (SysUserEntity) getSubject().getPrincipal();
        String roleName = user.getRoleName();
        if (StringUtils.isNotBlank(roleName) && roleName.contains("街道")) {
            wrapper.eq("native_zhen", user.getZhen());
        }

        return wrapper;
    }

}