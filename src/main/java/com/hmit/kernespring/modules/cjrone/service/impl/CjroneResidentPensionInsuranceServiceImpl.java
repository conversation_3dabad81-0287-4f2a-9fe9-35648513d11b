package com.hmit.kernespring.modules.cjrone.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;
import com.hmit.kernespring.modules.cjrone.dao.CjroneResidentPensionInsuranceDao;
import com.hmit.kernespring.modules.cjrone.entity.CjroneResidentPensionInsuranceEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneResidentPensionInsuranceService;
import com.hmit.kernespring.modules.matter_application.dao.CjroneWelfareMatterApplicationDao;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service("cjroneResidentPensionInsuranceService")
public class CjroneResidentPensionInsuranceServiceImpl extends ServiceImpl<CjroneResidentPensionInsuranceDao, CjroneResidentPensionInsuranceEntity> implements CjroneResidentPensionInsuranceService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneResidentPensionInsuranceDao cjroneResidentPensionInsuranceDao;
    @Autowired
    private SysDictService sysDictService;
    @Autowired
    private CjroneWelfareMatterApplicationDao cjroneWelfareMatterApplicationDao;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneResidentPensionInsuranceEntity cjroneResidentPensionInsuranceEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneResidentPensionInsuranceEntity.class);
        IPage<CjroneResidentPensionInsuranceEntity> page = this.page(
                new Query<CjroneResidentPensionInsuranceEntity>().getPage(params),
                new QueryWrapper<CjroneResidentPensionInsuranceEntity>()
            .eq(StringUtils.isNotBlank(cjroneResidentPensionInsuranceEntity.getId ()!=null && !"".equals(cjroneResidentPensionInsuranceEntity.getId ().toString())? cjroneResidentPensionInsuranceEntity.getId ().toString():null),"id", cjroneResidentPensionInsuranceEntity.getId ())
            .eq(StringUtils.isNotBlank(cjroneResidentPensionInsuranceEntity.getName ()!=null && !"".equals(cjroneResidentPensionInsuranceEntity.getName ().toString())? cjroneResidentPensionInsuranceEntity.getName ().toString():null),"name", cjroneResidentPensionInsuranceEntity.getName ())
            .eq(StringUtils.isNotBlank(cjroneResidentPensionInsuranceEntity.getIdCard ()!=null && !"".equals(cjroneResidentPensionInsuranceEntity.getIdCard ().toString())? cjroneResidentPensionInsuranceEntity.getIdCard ().toString():null),"id_card", cjroneResidentPensionInsuranceEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(cjroneResidentPensionInsuranceEntity.getProjectName ()!=null && !"".equals(cjroneResidentPensionInsuranceEntity.getProjectName ().toString())? cjroneResidentPensionInsuranceEntity.getProjectName ().toString():null),"project_name", cjroneResidentPensionInsuranceEntity.getProjectName ())
            .eq(StringUtils.isNotBlank(cjroneResidentPensionInsuranceEntity.getSubsidy ()!=null && !"".equals(cjroneResidentPensionInsuranceEntity.getSubsidy ().toString())? cjroneResidentPensionInsuranceEntity.getSubsidy ().toString():null),"subsidy", cjroneResidentPensionInsuranceEntity.getSubsidy ())
            .eq(StringUtils.isNotBlank(cjroneResidentPensionInsuranceEntity.getMobilePhone ()!=null && !"".equals(cjroneResidentPensionInsuranceEntity.getMobilePhone ().toString())? cjroneResidentPensionInsuranceEntity.getMobilePhone ().toString():null),"mobile_phone", cjroneResidentPensionInsuranceEntity.getMobilePhone ())
            .eq(StringUtils.isNotBlank(cjroneResidentPensionInsuranceEntity.getAddress ()!=null && !"".equals(cjroneResidentPensionInsuranceEntity.getAddress ().toString())? cjroneResidentPensionInsuranceEntity.getAddress ().toString():null),"address", cjroneResidentPensionInsuranceEntity.getAddress ())
            .eq(StringUtils.isNotBlank(cjroneResidentPensionInsuranceEntity.getStatus ()!=null && !"".equals(cjroneResidentPensionInsuranceEntity.getStatus ().toString())? cjroneResidentPensionInsuranceEntity.getStatus ().toString():null),"status", cjroneResidentPensionInsuranceEntity.getStatus ())
            .eq(StringUtils.isNotBlank(cjroneResidentPensionInsuranceEntity.getCreateId ()!=null && !"".equals(cjroneResidentPensionInsuranceEntity.getCreateId ().toString())? cjroneResidentPensionInsuranceEntity.getCreateId ().toString():null),"create_id", cjroneResidentPensionInsuranceEntity.getCreateId ())
            .eq(StringUtils.isNotBlank(cjroneResidentPensionInsuranceEntity.getCreateTime ()!=null && !"".equals(cjroneResidentPensionInsuranceEntity.getCreateTime ().toString())? cjroneResidentPensionInsuranceEntity.getCreateTime ().toString():null),"create_time", cjroneResidentPensionInsuranceEntity.getCreateTime ())
            .eq(StringUtils.isNotBlank(cjroneResidentPensionInsuranceEntity.getInsureMonth()!=null && !"".equals(cjroneResidentPensionInsuranceEntity.getInsureMonth().toString())?cjroneResidentPensionInsuranceEntity.getInsureMonth().toString():null),"insure_month",cjroneResidentPensionInsuranceEntity.getInsureMonth())
            .eq(StringUtils.isNotBlank(cjroneResidentPensionInsuranceEntity.getIsResident()!=null && !"".equals(cjroneResidentPensionInsuranceEntity.getIsResident().toString())?cjroneResidentPensionInsuranceEntity.getIsResident().toString():null),"is_resident",cjroneResidentPensionInsuranceEntity.getIsResident())
            .orderByDesc("create_time")
        );
        Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key","sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        page.getRecords().forEach( item -> {
            /*SysDictEntity status_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("zt_0000") && iii.getValue().equals(
                        item.getStatus ())).findAny().orElse(null);
            if (status_sysDictEntity != null){
                item.setStatus (Integer.parseInt(status_sysDictEntity.getLabel()));
            }else{
                item.setStatus (null);
            }*/
            if ("1".equals(item.getStatus())){
                item.setStatus("已通过");
            }else if ("2".equals(item.getStatus())){
                item.setStatus("待审核");
            }else if ("0".equals(item.getStatus())){
                item.setStatus("已禁用");
            }else if ("3".equals(item.getStatus())){
                item.setStatus("退回");
            }
        });
        return new PageUtils(page);
    }
    @Override
    public List<CjroneResidentPensionInsuranceEntity> queryExportData(Map<String, Object> params) {
            return cjroneResidentPensionInsuranceDao.queryExportData(params);
    }
    @Override
    public boolean updateById(CjroneResidentPensionInsuranceEntity entity) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",entity.getId());
        map.put("matter_name","养老保险补贴");
        if ("1".equals(entity.getStatus())){
            map.put("status","3");
        } else if ("3".equals(entity.getStatus())){
            map.put("status","4");
        }else {
            map.put("status",entity.getStatus());
        }
        map.put("verify_time",new Date());
        cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        return super.updateById(entity);
    }

}