package com.hmit.kernespring.modules.cjrone.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-09 14:42:18
 */
@Data
@TableName("cjrone_certificate_apply_doc")
public class CjroneCertificateApplyDocEntity implements Serializable {
		private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	
@TableId
	@Excel(name = "主键ID", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 
	 */
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String disabilityCertificateApplicationId;
	/**
	 * 
	 */
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String disabilityAssessmentId;
	/**
	 * 
	 */
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private String disabilityAssessmentDetailName;
	/**
	 * 
	 */
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer documentId;

	@TableField(exist = false)
	private String fileName;
	@TableField(exist = false)
	private String filePath;


}
