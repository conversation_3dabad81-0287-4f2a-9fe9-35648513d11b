package com.hmit.kernespring.modules.cjrone.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 康复补助申请
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-10 14:28:57
 */
@Data
@TableName("cjrone_rehabilitation_subsidy")
public class CjroneRehabilitationSubsidyEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 残疾人姓名
	 */

	@TableId
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
	private Integer id;
	@Excel(name = "残疾人姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 性别  1 男  2女
	 */
	@Excel(name = "性别  1 男  2女", height = 20, width = 30, isImportField = "true_st")
private Integer sex;
	/**
	 * 民族
	 */
	@Excel(name = "民族", height = 20, width = 30, isImportField = "true_st")
private String nationality;
	/**
	 * 出生日期
	 */
	@Excel(name = "出生日期", height = 20, width = 30, isImportField = "true_st")
private String birthday;
	/**
	 * 身份证号码
	 */
	@Excel(name = "身份证号码", height = 20, width = 30, isImportField = "true_st")
private String idCard;
	/**
	 * 残疾证号
	 */
	@Excel(name = "残疾证号", height = 20, width = 30, isImportField = "true_st")
private String disableId;
	/**
	 * 残疾类别
	 */
	@Excel(name = "残疾类别", height = 20, width = 30, isImportField = "true_st")
private String disabilityCategory;
	/**
	 * 残疾等级
	 */
	@Excel(name = "残疾等级", height = 20, width = 30, isImportField = "true_st")
private String disabilityDegree;
	/**
	 * 现住址 镇
	 */
	@Excel(name = "现住址 镇", height = 20, width = 30, isImportField = "true_st")
private String presentZhen;
	/**
	 * 现地址  村
	 */
	@Excel(name = "现地址  村", height = 20, width = 30, isImportField = "true_st")
private String presentCun;
	/**
	 * 现地址
	 */
	@Excel(name = "现地址", height = 20, width = 30, isImportField = "true_st")
private String presentAddress;
	/**
	 * 联系电话
	 */
	@Excel(name = "联系电话", height = 20, width = 30, isImportField = "true_st")
private String contactPhone;
	/**
	 * 手机号
	 */
	@Excel(name = "手机号", height = 20, width = 30, isImportField = "true_st")
private String mobilePhone;
	/**
	 * 家庭经济情况
	 */
	@Excel(name = "家庭经济情况", height = 20, width = 30, isImportField = "true_st")
private String familyEconoCondition;
	/**
	 * 医疗保险情况
	 */
	@Excel(name = "医疗保险情况", height = 20, width = 30, isImportField = "true_st")
private String medicalInsurance;
	/**
	 * 康复需求项目
	 */
	@Excel(name = "康复需求项目", height = 20, width = 30, isImportField = "true_st")
private String rehabilitationProject;
	/**
	 * 康复需求项目
	 */
	@Excel(name = "康复需求项目", height = 20, width = 30, isImportField = "true_st")
private String rehabilitationProjectName;
	/**
	 * 申请日期
	 */
	@Excel(name = "申请日期", height = 20, width = 30, isImportField = "true_st")
private Date applicationDate;
	/**
	 * 审核人
	 */
	@Excel(name = "审核人", height = 20, width = 30, isImportField = "true_st")
private String auditPerson;
	/**
	 * 审核日期
	 */
	@Excel(name = "审核日期", height = 20, width = 30, isImportField = "true_st")
private String auditDate;
	/**
	 * 状态
	 */
	@Excel(name = "状态", height = 20, width = 30, isImportField = "true_st")
private String status;
	/**
	 * 创建人编号
	 */
	@Excel(name = "创建人编号", height = 20, width = 30, isImportField = "true_st")
private Long createId;
	/**
	 * 创建时间
	 */
	@Excel(name = "创建时间", height = 20, width = 30, isImportField = "true_st")
private Date createTime;
	private String signStatus;


	// 下面是针对于儿童护理补贴输入的特殊字段

	//婚姻状况
	@TableField(exist = false)
	private String maritalStatus;

	//籍贯
	@TableField(exist = false)
	private String nativePlace;

	//文化程度
	@TableField(exist = false)
	private String educationDegree;

	/**
	 * 户籍地址  镇
	 */
	@TableField(exist = false)
	private String nativeZhen;

	/**
	 * 户籍地址 村
	 */
	@TableField(exist = false)
	private String nativeCun;

	/**
	 * 户籍地址
	 */
	@TableField(exist = false)
	private String nativeAddress;

	//邮编
	@TableField(exist = false)
	private String postcode;

	/**
	 * 联系人姓名
	 */
	@TableField(exist = true)
	private String guardianName;
	/**
	 * 联系人手机
	 */
	@TableField(exist = true)
	private String guardianPhone;
	/**
	 * 联系人身份证号
	 */
	@TableField(exist = true)
	private String guardianIdcard;
	/**
	 * 与申请人关系
	 */
	@TableField(exist = true)
	private String guardianRelation;

	/**
	 * 申请类型
	 */
	@TableField(exist = false)
	private String applicationType;


	@TableField(exist = false)
	private String bankAccount;

	@TableField(exist = false)
	private String bankName;

	/**
	 * 电子签章状态
	 */
	private String signatureStatus;

}
