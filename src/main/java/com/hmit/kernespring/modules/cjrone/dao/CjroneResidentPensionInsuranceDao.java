package com.hmit.kernespring.modules.cjrone.dao;

import com.hmit.kernespring.modules.cjrone.entity.CjroneResidentPensionInsuranceEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 城乡居民养老保险补贴汇总表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-10 14:28:57
 */
@Mapper
public interface CjroneResidentPensionInsuranceDao extends BaseMapper<CjroneResidentPensionInsuranceEntity> {
    List<CjroneResidentPensionInsuranceEntity> queryExportData(Map<String, Object> params);
	
}
