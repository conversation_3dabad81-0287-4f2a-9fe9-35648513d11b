package com.hmit.kernespring.modules.cjrone.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone.entity.CjroneblLove24SybcDataEntity;

import java.util.Map;

import java.util.List;

/**
 * 爱心24小时异常数据
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-03-22 16:57:32
 */
public interface CjroneblLove24SybcDataService extends IService<CjroneblLove24SybcDataEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneblLove24SybcDataEntity> queryExportData(Map<String, Object> params);
    boolean disable(Integer[] ids);
}

