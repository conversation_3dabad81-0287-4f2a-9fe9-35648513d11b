package com.hmit.kernespring.modules.cjrone.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.cjrone.entity.CjroneNursingSubsidySyncDataEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneNursingSubsidySyncDataService;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;

/**
 * 护理补贴对比数据
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-06-10 09:08:10
 */
@RestController
@RequestMapping("cjrone/cjronenursingsubsidysyncdata")
public class CjroneNursingSubsidySyncDataController {
    @Autowired
    private CjroneNursingSubsidySyncDataService cjroneNursingSubsidySyncDataService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = cjroneNursingSubsidySyncDataService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("cjrone:cjronenursingsubsidysyncdata:info")
    public R info(@PathVariable("id") Integer id){
		CjroneNursingSubsidySyncDataEntity cjroneNursingSubsidySyncData = cjroneNursingSubsidySyncDataService.getById(id);

        return R.ok().put("cjroneNursingSubsidySyncData", cjroneNursingSubsidySyncData);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("cjrone:cjronenursingsubsidysyncdata:save")
    public R save(@RequestBody CjroneNursingSubsidySyncDataEntity cjroneNursingSubsidySyncData){
		cjroneNursingSubsidySyncDataService.save(cjroneNursingSubsidySyncData);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("cjrone:cjronenursingsubsidysyncdata:update")
    public R update(@RequestBody CjroneNursingSubsidySyncDataEntity cjroneNursingSubsidySyncData){
		cjroneNursingSubsidySyncDataService.updateById(cjroneNursingSubsidySyncData);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("cjrone:cjronenursingsubsidysyncdata:delete")
    public R delete(@RequestBody Integer[] ids){
		cjroneNursingSubsidySyncDataService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    @RequiresPermissions("cjrone:cjronenursingsubsidysyncdata:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<CjroneNursingSubsidySyncDataEntity> cjroneNursingSubsidySyncDataList = new ArrayList<>();
        list.forEach(item ->{
                    item.put("id",item.get(""));
                    item.put("name",item.get("残疾人姓名"));
                    item.put("idCard",item.get("身份证号码"));
                    item.put("createTime",item.get("创建时间"));
                    item.put("familyEconoCondition",item.get("家庭经济情况"));
                    item.put("medicalInsurance",item.get("医疗保险情况"));
                    item.put("isDead",item.get("是否死亡"));
                    item.put("isTk",item.get("是否特困"));
                    item.put("isSixty",item.get("是否60岁"));
                    item.put("isWorkinjury",item.get("是否工伤保险"));
                    item.put("isDischild",item.get("是否困境儿童"));
                    item.put("isResident",item.get("是否居民养老保险"));
                    item.put("isClork",item.get("是否职工参保"));
                    item.put("status",item.get("0 异常 1正常"));
                    item.put("isApplyWelfareMatter",item.get("是否申请过福利事项"));
                    item.put("isFx",item.get("是否服刑"));
                    item.put("isHkqy",item.get("是否户口迁移"));
                    item.put("fxInfo",item.get("服刑信息"));
                    item.put("hkqyInfo",item.get("户口迁移信息"));
                    cjroneNursingSubsidySyncDataList.add(new Gson().fromJson(new Gson().toJson(item), CjroneNursingSubsidySyncDataEntity.class));
        });
        // 保存到数据库
        cjroneNursingSubsidySyncDataService.saveBatch(cjroneNursingSubsidySyncDataList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    //@RequiresPermissions("cjrone:cjronenursingsubsidysyncdata:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<CjroneNursingSubsidySyncDataEntity> cjroneNursingSubsidySyncDataEntityList = cjroneNursingSubsidySyncDataService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("护理补贴对比数据", null, "护理补贴对比数据");
        Workbook workbook = ExcelExportUtil.exportExcel(params, CjroneNursingSubsidySyncDataEntity.class, cjroneNursingSubsidySyncDataEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "护理补贴对比数据" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

}
