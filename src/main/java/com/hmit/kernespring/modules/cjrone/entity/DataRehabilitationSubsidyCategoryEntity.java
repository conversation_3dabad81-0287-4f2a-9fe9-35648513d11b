package com.hmit.kernespring.modules.cjrone.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 康复补贴类别
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-20 14:41:44
 */
@Data
@TableName("data_rehabilitation_subsidy_category")
public class DataRehabilitationSubsidyCategoryEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 康复补助类别编号
	 */
	
@TableId
	@Excel(name = "康复补助类别编号", height = 20, width = 30, isImportField = "true_st")
private Integer categoryId;
	/**
	 * 父菜单ID，一级菜单为0
	 */
	@Excel(name = "父菜单ID，一级菜单为0", height = 20, width = 30, isImportField = "true_st")
private Integer parentId;
	/**
	 * 名称
	 */
	@Excel(name = "名称", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 创建时间
	 */
	@Excel(name = "创建时间", height = 20, width = 30, isImportField = "true_st")
private Date createTime;
	/**
	 * 
	 */
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer createId;
	/**
	 * 条件及类别名称
	 */
	@Excel(name = "条件及类别名称", height = 20, width = 30, isImportField = "true_st")
private String conditionName;
	/**
	 * 服务标准
	 */
	@Excel(name = "服务标准", height = 20, width = 30, isImportField = "true_st")
private String serviceStandard;
	/**
	 * 备注
	 */
	@Excel(name = "备注", height = 20, width = 30, isImportField = "true_st")
private String memo;
	/**
	 * 0 非叶子节点   1 叶子节点
	 */
	@Excel(name = "0 非叶子节点   1 叶子节点", height = 20, width = 30, isImportField = "true_st")
private Integer leafnode;

	@TableField(exist=false)
	private List<?> children;

	@TableField(exist=false)
	private Integer value;

	@TableField(exist=false)
	private String label;

}
