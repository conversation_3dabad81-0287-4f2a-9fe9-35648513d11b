package com.hmit.kernespring.modules.cjrone.constants;

import java.math.BigDecimal;

/**
 * 残疾人惠残事项相关常量定义
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-14
 */
public class CjroneConstants {
    
    /**
     * 残疾少年儿童康复训练补助年度累计金额限制（元）
     * 视力5000元/年；听力、言语、智力24000元/年；肢体、孤独症36000元/年
     */
    // 视力康复类型年度上限
    public static final BigDecimal CHILDREN_REHABILITATION_SUBSIDY_VISUAL_YEAR_LIMIT = new BigDecimal("5000");
    // 听力、言语、智力康复类型年度上限
    public static final BigDecimal CHILDREN_REHABILITATION_SUBSIDY_HEARING_SPEECH_INTELLECTUAL_YEAR_LIMIT = new BigDecimal("24000");
    // 肢体、孤独症康复类型年度上限
    public static final BigDecimal CHILDREN_REHABILITATION_SUBSIDY_LIMB_AUTISM_YEAR_LIMIT = new BigDecimal("36000");

    /**
     * 精神病住院补贴年度累计金额限制（元）
     */
    public static final BigDecimal MENTAL_ILLNESS_SUBSIDY_YEAR_TOTAL_AMOUNT_LIMIT = new BigDecimal("6000");
}