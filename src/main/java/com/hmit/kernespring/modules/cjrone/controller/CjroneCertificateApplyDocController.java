package com.hmit.kernespring.modules.cjrone.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.cjrone.entity.CjroneCertificateApplyDocEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneCertificateApplyDocService;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;

/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-09 14:42:18
 */
@RestController
@RequestMapping("cjrone/cjronecertificateapplydoc")
public class CjroneCertificateApplyDocController {
    @Autowired
    private CjroneCertificateApplyDocService cjroneCertificateApplyDocService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("cjrone:cjronecertificateapplydoc:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = cjroneCertificateApplyDocService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("cjrone:cjronecertificateapplydoc:info")
    public R info(@PathVariable("id") Integer id){
		CjroneCertificateApplyDocEntity cjroneCertificateApplyDoc = cjroneCertificateApplyDocService.getById(id);

        return R.ok().put("cjroneCertificateApplyDoc", cjroneCertificateApplyDoc);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("cjrone:cjronecertificateapplydoc:save")
    public R save(@RequestBody CjroneCertificateApplyDocEntity cjroneCertificateApplyDoc){
		cjroneCertificateApplyDocService.save(cjroneCertificateApplyDoc);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("cjrone:cjronecertificateapplydoc:update")
    public R update(@RequestBody CjroneCertificateApplyDocEntity cjroneCertificateApplyDoc){
		cjroneCertificateApplyDocService.updateById(cjroneCertificateApplyDoc);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("cjrone:cjronecertificateapplydoc:delete")
    public R delete(@RequestBody Integer[] ids){
		cjroneCertificateApplyDocService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }
    /**
    * 导入数据
    */
    @RequestMapping("/importData")
    // @RequiresPermissions("cjrone:cjronecertificateapplydoc:import")
    public R importData(@RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }

        //上传文件
        String file_path = cjroneProperties.getUploadPath()+file.getOriginalFilename();
        File filePath = new File(file_path);
        if(!filePath.getParentFile().exists()){
            filePath.mkdirs();
        }
        try {
            file.transferTo(filePath);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        //解析excel，
        ImportParams params_import = new ImportParams();
        //params_import.setDataHanlder(new MapImportHanlder());
        List<Map<String, Object>> list = ExcelImportUtil.importExcel(
                filePath, Map.class, params_import);
        List<CjroneCertificateApplyDocEntity> cjroneCertificateApplyDocList = new ArrayList<>();
        list.forEach(item ->{
                    item.put("id",item.get("主键ID"));
                    item.put("disabilityCertificateApplicationId",item.get(""));
                    item.put("disabilityAssessmentId",item.get(""));
                    item.put("disabilityAssessmentDetailName",item.get(""));
                    item.put("documentId",item.get(""));
                    cjroneCertificateApplyDocList.add(new Gson().fromJson(new Gson().toJson(item), CjroneCertificateApplyDocEntity.class));
        });
        // 保存到数据库
        cjroneCertificateApplyDocService.saveBatch(cjroneCertificateApplyDocList);

        //保存文件信息
        SysOssEntity ossEntity = new SysOssEntity();
        ossEntity.setUrl(file_path);
        ossEntity.setCreateDate(new Date());
        sysOssService.save(ossEntity);

        return R.ok().put("url", file_path);
    }
    /**
    * 导出数据
    */
    @RequestMapping("/exportData")
    //@RequiresPermissions("cjrone:cjronecertificateapplydoc:export")
    public void exportData(@RequestParam Map<String,Object> mapArgs, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        List<CjroneCertificateApplyDocEntity> cjroneCertificateApplyDocEntityList = cjroneCertificateApplyDocService.queryExportData(mapArgs);

        ExportParams params = new ExportParams("", null, "");
        Workbook workbook = ExcelExportUtil.exportExcel(params, CjroneCertificateApplyDocEntity.class, cjroneCertificateApplyDocEntityList);

        response.setContentType("application/vnd.ms-excel");
        String fileName = "" ;
        response.setHeader("Content-disposition", "attachment;filename="+new String(fileName.getBytes(),"iso-8859-1")+".xls");
        OutputStream ouputStream = response.getOutputStream();
        workbook.write(ouputStream);
        ouputStream.flush();
        ouputStream.close();
    }

}
