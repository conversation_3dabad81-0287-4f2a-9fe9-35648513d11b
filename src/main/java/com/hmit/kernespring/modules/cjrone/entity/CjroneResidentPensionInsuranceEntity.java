package com.hmit.kernespring.modules.cjrone.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 城乡居民养老保险补贴汇总表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-10 14:28:57
 */
@Data
@TableName("cjrone_resident_pension_insurance")
public class CjroneResidentPensionInsuranceEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	
@TableId
	@Excel(name = "", height = 20, width = 30, isImportField = "true_st")
private Integer id;
	/**
	 * 姓名
	 */
	@Excel(name = "姓名", height = 20, width = 30, isImportField = "true_st")
private String name;
	/**
	 * 身份证号
	 */
	@Excel(name = "身份证号", height = 20, width = 30, isImportField = "true_st")
private String idCard;

	@Excel(name = "残疾证号", height = 20, width = 30, isImportField = "true_st")
	private String disableId;
	/**
	 * 项目名称
	 */

private String projectName;
	@Excel(name = "参保月数", height = 20, width = 30, isImportField = "true_st")
	private String insureMonth;
	/**
	 * 补贴金额
	 */
	@Excel(name = "补贴金额", height = 20, width = 30, isImportField = "true_st")
private Double subsidy;
	/**
	 * 联系电话
	 */
	@Excel(name = "联系电话", height = 20, width = 30, isImportField = "true_st")
private String mobilePhone;
	/**
	 * 地址
	 */
	@Excel(name = "地址", height = 20, width = 30, isImportField = "true_st")
private String address;

	@Excel(name = "工商注册号", height = 20, width = 30, isImportField = "true_st")
	private String gszch;

	@Excel(name = "工商注册名称", height = 20, width = 30, isImportField = "true_st")
	private String gszcmc;
	/**
	 * 状态
	 */

private String status;
	/**
	 * 创建人编号
	 */

private Long createId;
	/**
	 * 创建时间
	 */
	@Excel(name = "创建时间", height = 20, width = 30, isImportField = "true_st")
private String createTime;


	private String isResident;

	@Excel(name = "备注", height = 20, width = 30, isImportField = "true_st")
	@TableField(exist=false)
	private String bz;

}
