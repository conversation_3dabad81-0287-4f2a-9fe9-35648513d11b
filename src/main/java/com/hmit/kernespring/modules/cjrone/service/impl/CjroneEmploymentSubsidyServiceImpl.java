package com.hmit.kernespring.modules.cjrone.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;
import com.hmit.kernespring.modules.cjrone.dao.CjroneEmploymentSubsidyDao;
import com.hmit.kernespring.modules.cjrone.entity.CjroneEmploymentSubsidyEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneEmploymentSubsidyService;
import com.hmit.kernespring.modules.matter_application.dao.CjroneWelfareMatterApplicationDao;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service("cjroneEmploymentSubsidyService")
public class CjroneEmploymentSubsidyServiceImpl extends ServiceImpl<CjroneEmploymentSubsidyDao, CjroneEmploymentSubsidyEntity> implements CjroneEmploymentSubsidyService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();
    @Autowired
    private CjroneEmploymentSubsidyDao cjroneEmploymentSubsidyDao;
    @Autowired
    private CjroneWelfareMatterApplicationDao cjroneWelfareMatterApplicationDao;
    @Autowired
    private SysDictService sysDictService;
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneEmploymentSubsidyEntity cjroneEmploymentSubsidyEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneEmploymentSubsidyEntity.class);
        IPage<CjroneEmploymentSubsidyEntity> page = this.page(
                new Query<CjroneEmploymentSubsidyEntity>().getPage(params),
                new QueryWrapper<CjroneEmploymentSubsidyEntity>()
            .eq(StringUtils.isNotBlank(cjroneEmploymentSubsidyEntity.getId ()!=null && !"".equals(cjroneEmploymentSubsidyEntity.getId ().toString())? cjroneEmploymentSubsidyEntity.getId ().toString():null),"id", cjroneEmploymentSubsidyEntity.getId ())
            .eq(StringUtils.isNotBlank(cjroneEmploymentSubsidyEntity.getName ()!=null && !"".equals(cjroneEmploymentSubsidyEntity.getName ().toString())? cjroneEmploymentSubsidyEntity.getName ().toString():null),"name", cjroneEmploymentSubsidyEntity.getName ())
            .eq(StringUtils.isNotBlank(cjroneEmploymentSubsidyEntity.getIdCard ()!=null && !"".equals(cjroneEmploymentSubsidyEntity.getIdCard ().toString())? cjroneEmploymentSubsidyEntity.getIdCard ().toString():null),"id_card", cjroneEmploymentSubsidyEntity.getIdCard ())
            .eq(StringUtils.isNotBlank(cjroneEmploymentSubsidyEntity.getFamilyEconoCondition ()!=null && !"".equals(cjroneEmploymentSubsidyEntity.getFamilyEconoCondition ().toString())? cjroneEmploymentSubsidyEntity.getFamilyEconoCondition ().toString():null),"family_econo_condition", cjroneEmploymentSubsidyEntity.getFamilyEconoCondition ())
            .eq(StringUtils.isNotBlank(cjroneEmploymentSubsidyEntity.getSex ()!=null && !"".equals(cjroneEmploymentSubsidyEntity.getSex ().toString())? cjroneEmploymentSubsidyEntity.getSex ().toString():null),"sex", cjroneEmploymentSubsidyEntity.getSex ())
            .eq(StringUtils.isNotBlank(cjroneEmploymentSubsidyEntity.getEducationDegree ()!=null && !"".equals(cjroneEmploymentSubsidyEntity.getEducationDegree ().toString())? cjroneEmploymentSubsidyEntity.getEducationDegree ().toString():null),"education_degree", cjroneEmploymentSubsidyEntity.getEducationDegree ())
            .eq(StringUtils.isNotBlank(cjroneEmploymentSubsidyEntity.getDisabilityType ()!=null && !"".equals(cjroneEmploymentSubsidyEntity.getDisabilityType ().toString())? cjroneEmploymentSubsidyEntity.getDisabilityType ().toString():null),"disability_type", cjroneEmploymentSubsidyEntity.getDisabilityType ())
            .eq(StringUtils.isNotBlank(cjroneEmploymentSubsidyEntity.getTelephone ()!=null && !"".equals(cjroneEmploymentSubsidyEntity.getTelephone ().toString())? cjroneEmploymentSubsidyEntity.getTelephone ().toString():null),"telephone", cjroneEmploymentSubsidyEntity.getTelephone ())
            .eq(StringUtils.isNotBlank(cjroneEmploymentSubsidyEntity.getPresentAddress ()!=null && !"".equals(cjroneEmploymentSubsidyEntity.getPresentAddress ().toString())? cjroneEmploymentSubsidyEntity.getPresentAddress ().toString():null),"present_address", cjroneEmploymentSubsidyEntity.getPresentAddress ())
            .eq(StringUtils.isNotBlank(cjroneEmploymentSubsidyEntity.getManagementType ()!=null && !"".equals(cjroneEmploymentSubsidyEntity.getManagementType ().toString())? cjroneEmploymentSubsidyEntity.getManagementType ().toString():null),"management_type", cjroneEmploymentSubsidyEntity.getManagementType ())
            .eq(StringUtils.isNotBlank(cjroneEmploymentSubsidyEntity.getManagementAddress ()!=null && !"".equals(cjroneEmploymentSubsidyEntity.getManagementAddress ().toString())? cjroneEmploymentSubsidyEntity.getManagementAddress ().toString():null),"management_address", cjroneEmploymentSubsidyEntity.getManagementAddress ())
            .eq(StringUtils.isNotBlank(cjroneEmploymentSubsidyEntity.getSubsidyMoney ()!=null && !"".equals(cjroneEmploymentSubsidyEntity.getSubsidyMoney ().toString())? cjroneEmploymentSubsidyEntity.getSubsidyMoney ().toString():null),"subsidy_money", cjroneEmploymentSubsidyEntity.getSubsidyMoney ())
        );
        Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key","sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        page.getRecords().forEach( item -> {
            SysDictEntity familyEconoCondition_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("jtjjqk_0000") && iii.getValue().equals(
                        item.getFamilyEconoCondition ())).findAny().orElse(null);
            if (familyEconoCondition_sysDictEntity != null){
                item.setFamilyEconoCondition (familyEconoCondition_sysDictEntity.getLabel());
            }else{
                item.setFamilyEconoCondition (null);
            }
            SysDictEntity disabilityType_sysDictEntity = sys_dict_all_list.stream().filter(
                iii->iii.getCode().equals("cjlb_0000") && iii.getValue().equals(
                        item.getDisabilityType ())).findAny().orElse(null);
            if (disabilityType_sysDictEntity != null){
                //item.setDisabilityType (disabilityType_sysDictEntity.getLabel().toString());
            }else{
                item.setDisabilityType (null);
            }
            if ("1".equals(item.getStatus())){
                item.setStatus("已通过");
            }else if ("2".equals(item.getStatus())){
                item.setStatus("待审核");
            }else if ("0".equals(item.getStatus())){
                item.setStatus("已禁用");
            }else if ("3".equals(item.getStatus())){
                item.setStatus("退回");
            }else if ("5".equals(item.getStatus())){
                item.setStatus("待民政审核");
            }
            if ("1".equals(item.getSignStatus())) {
                item.setSignStatus("申请人待手签");
            } else if ("2".equals(item.getSignStatus())) {
                item.setSignStatus("申请人已手签");
            } else if ("3".equals(item.getSignStatus())) {
                item.setSignStatus("镇街道待手签");
            } else if ("4".equals(item.getSignStatus())) {
                item.setSignStatus("镇街道已手签");
            } else if ("5".equals(item.getSignStatus())) {
                item.setSignStatus("区残联未手签");
            } else if ("6".equals(item.getSignStatus())) {
                item.setSignStatus("区残联已手签");
            }else if ("7".equals(item.getSignStatus())) {
                item.setSignStatus("区残联经办人已手签");
            }  else if ("8".equals(item.getSignStatus())) {
                item.setSignStatus("民政已手签");
            }else if ("9".equals(item.getSignStatus())) {
                item.setSignStatus("民政经办人已手签");
            }else {
                item.setSignStatus("无");
            }

            if ("1".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("镇街道待电子公章");
            } else if ("2".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("区残联待电子公章");
            } else if ("3".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("民政待电子公章");
            } else if ("4".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("已完成");
            } else if ("5".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("镇街道已电子公章");
            } else if ("6".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("区残联已电子公章");
            } else if ("7".equals(item.getSignatureStatus())) {
                item.setSignatureStatus("民政待已电子公章");
            } else {
                item.setSignatureStatus("无");
            }
        });
        return new PageUtils(page);
    }
    @Override
    public List<CjroneEmploymentSubsidyEntity> queryExportData(Map<String, Object> params) {
            return cjroneEmploymentSubsidyDao.queryExportData(params);
    }

    @Override
    public boolean updateById(CjroneEmploymentSubsidyEntity entity) {
        Map<String,Object> map = new HashMap<>();
        map.put("matter_id",entity.getId());
        map.put("matter_name","就业创业补助");
        if ("1".equals(entity.getStatus())){
            map.put("status","3");
        } else if ("3".equals(entity.getStatus())){
            map.put("status","4");
        }else {
            map.put("status",entity.getStatus());
        }
        map.put("verify_time",new Date());
        if (entity.getStatus() != null){
            map.put("signatureStatus",entity.getSignatureStatus());
            cjroneWelfareMatterApplicationDao.updateStatusByMap(map);
        }
        return super.updateById(entity);
    }
}