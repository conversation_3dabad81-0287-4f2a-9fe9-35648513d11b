package com.hmit.kernespring.modules.cjrone.dao;

import com.hmit.kernespring.modules.cjrone.entity.CjroneNursingSubsidySyncDataEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 护理补贴对比数据
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-06-10 09:08:10
 */
@Mapper
public interface CjroneNursingSubsidySyncDataDao extends BaseMapper<CjroneNursingSubsidySyncDataEntity> {
    List<CjroneNursingSubsidySyncDataEntity> queryExportData(Map<String, Object> params);
	
}
