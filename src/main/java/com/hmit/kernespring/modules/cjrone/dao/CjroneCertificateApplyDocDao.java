package com.hmit.kernespring.modules.cjrone.dao;

import com.hmit.kernespring.modules.cjrone.entity.CjroneCertificateApplyDocEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hmit.kernespring.modules.cjrone.entity.CjroneDocumentEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-09 14:42:18
 */
@Mapper
public interface CjroneCertificateApplyDocDao extends BaseMapper<CjroneCertificateApplyDocEntity> {
    List<CjroneDocumentEntity> listDocumentByMap(Map<String, Object> params);
	List<CjroneCertificateApplyDocEntity> queryExportData(Map<String, Object> params);
	List<CjroneCertificateApplyDocEntity> queryApplyDocByMap(Map<String, Object> params);
    void deleteByApplicationId(Map<String, Object> params);
}
