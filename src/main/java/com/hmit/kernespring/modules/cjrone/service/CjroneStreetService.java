package com.hmit.kernespring.modules.cjrone.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.modules.cjrone.entity.CjroneAdministrativeDivisionEntity;

import java.util.List;
import java.util.Map;

public interface CjroneStreetService extends IService<CjroneAdministrativeDivisionEntity> {

     List<CjroneAdministrativeDivisionEntity>  getAdministrativeDivisionList(String code);

     List<CjroneAdministrativeDivisionEntity>  getAdministrativeDivisionListAll(String code);

     List<CjroneAdministrativeDivisionEntity>  getAdministrativeDivisionListStep(String code, Integer step);

     List<Map<String,Object>>  getAdministrativeDivisionListForAPP(String code);

     CjroneAdministrativeDivisionEntity getStreetById(String code);

     Map<String, Object> queryNames(Map<String, Object> params);

     Map<String, Object> queryNames2(Map<String, Object> params);

}
