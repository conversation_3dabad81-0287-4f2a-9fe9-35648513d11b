package com.hmit.kernespring.modules.cjrone.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 
 *
 * @<NAME_EMAIL>
 * @since ${version} ${date}
 */
@Data
@ApiModel(value = "")
public class TempDiffDTO implements Serializable {
    private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "")
	private Integer id;

	@ApiModelProperty(value = "残疾人姓名")
	private String name;

	@ApiModelProperty(value = "身份证号码")
	private String idCard;

	@ApiModelProperty(value = "残疾证号")
	private String disableId;

	@ApiModelProperty(value = "异常原因")
	private String error;


}