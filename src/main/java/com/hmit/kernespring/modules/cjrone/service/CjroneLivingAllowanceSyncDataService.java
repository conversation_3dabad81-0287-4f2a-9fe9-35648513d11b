package com.hmit.kernespring.modules.cjrone.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.cjrone.entity.CjroneLivingAllowanceSyncDataEntity;

import java.util.Map;

import java.util.List;

/**
 * 生活补贴对比数据
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-06-10 09:08:10
 */
public interface CjroneLivingAllowanceSyncDataService extends IService<CjroneLivingAllowanceSyncDataEntity> {

    PageUtils queryPage(Map<String, Object> params);
    List<CjroneLivingAllowanceSyncDataEntity> queryExportData(Map<String, Object> params);
}

