package com.hmit.kernespring.modules.cjrone.dao;

import com.hmit.kernespring.modules.cjrone.entity.DataRehabilitationSubsidyCategoryEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hmit.kernespring.modules.sys.entity.SysMenuEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 康复补贴类别
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-04-20 14:41:44
 */
@Mapper
public interface DataRehabilitationSubsidyCategoryDao extends BaseMapper<DataRehabilitationSubsidyCategoryEntity> {
    List<DataRehabilitationSubsidyCategoryEntity> queryExportData(Map<String, Object> params);

    List<DataRehabilitationSubsidyCategoryEntity> queryListByParentId(Integer parentId);
}
