package com.hmit.kernespring.modules.cjrone.dao;

import com.hmit.kernespring.modules.cjrone.entity.CjroneblLove24SybcDataEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 爱心24小时异常数据
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-03-22 16:57:32
 */
@Mapper
public interface CjroneblLove24SybcDataDao extends BaseMapper<CjroneblLove24SybcDataEntity> {
    List<CjroneblLove24SybcDataEntity> queryExportData(Map<String, Object> params);
	
}
