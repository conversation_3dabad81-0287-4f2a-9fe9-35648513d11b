package com.hmit.kernespring.modules.cjrone.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.Query;
import com.hmit.kernespring.modules.cjrone.dao.CjroneFamilyAccessibilityTransformationDao;
import com.hmit.kernespring.modules.cjrone.entity.CjroneFamilyAccessibilityTransformationEntity;
import com.hmit.kernespring.modules.cjrone.service.CjroneFamilyAccessibilityTransformationService;
import com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity;
import com.hmit.kernespring.modules.matter_application.service.DisabilityCertificateApplicationService;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service("cjroneFamilyAccessibilityTransformationService")
public class CjroneFamilyAccessibilityTransformationServiceImpl extends ServiceImpl<CjroneFamilyAccessibilityTransformationDao, CjroneFamilyAccessibilityTransformationEntity> implements CjroneFamilyAccessibilityTransformationService {
    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
        .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

            @Override
            public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                    //定义为int类型,如果后台返回""或者null,则返回0
                    return null;
                }
                return json.getAsInt();
            }
        })
        .create();

    @Autowired
    private CjroneFamilyAccessibilityTransformationDao cjroneFamilyAccessibilityTransformationDao;
    @Autowired
    private SysDictService sysDictService;
    @Autowired
    private DisabilityCertificateApplicationService disabilityCertificateApplicationService;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        CjroneFamilyAccessibilityTransformationEntity cjroneFamilyAccessibilityTransformationEntity = gson.fromJson(params.get("key")!=null ? params.get("key").toString():null, CjroneFamilyAccessibilityTransformationEntity.class);
        IPage<CjroneFamilyAccessibilityTransformationEntity> page = this.page(
                new Query<CjroneFamilyAccessibilityTransformationEntity>().getPage(params),
                new QueryWrapper<CjroneFamilyAccessibilityTransformationEntity>()
            .eq(StringUtils.isNotBlank(cjroneFamilyAccessibilityTransformationEntity.getId ()!=null && !"".equals(cjroneFamilyAccessibilityTransformationEntity.getId ().toString())? cjroneFamilyAccessibilityTransformationEntity.getId ().toString():null),"id", cjroneFamilyAccessibilityTransformationEntity.getId ())
            .eq(StringUtils.isNotBlank(cjroneFamilyAccessibilityTransformationEntity.getName ()!=null && !"".equals(cjroneFamilyAccessibilityTransformationEntity.getName ().toString())? cjroneFamilyAccessibilityTransformationEntity.getName ().toString():null),"name", cjroneFamilyAccessibilityTransformationEntity.getName ())
            .eq(StringUtils.isNotBlank(cjroneFamilyAccessibilityTransformationEntity.getIdCard ()!=null && !"".equals(cjroneFamilyAccessibilityTransformationEntity.getIdCard ().toString())? cjroneFamilyAccessibilityTransformationEntity.getIdCard ().toString():null),"id_card", cjroneFamilyAccessibilityTransformationEntity.getIdCard ())
        );
        Map<String, Object> params_map = new HashMap<>();
        params_map.put("redis_key","sys_dict:all");
        List<SysDictEntity> sys_dict_all_list = sysDictService.queryDataByMap(params_map);
        page.getRecords().forEach( item -> {

            //遍历查询相关字段
            DisabilityCertificateApplicationEntity disabilityCertificateApplication = disabilityCertificateApplicationService.getByIDCard(item.getIdCard());
            if(disabilityCertificateApplication!=null){
                item.setDisableId(disabilityCertificateApplication.getDisableId());
                item.setMobilePhone(disabilityCertificateApplication.getMobilePhone());
                item.setPresentAddress(disabilityCertificateApplication.getPresentAddress());
                item.setFamilyEconoCondition(disabilityCertificateApplication.getFamilyEconoCondition());
            }
            else{
                item.setDisableId("");
                item.setMobilePhone("");
                item.setPresentAddress("");
                item.setFamilyEconoCondition("");
            }
        });
        return new PageUtils(page);
    }
    @Override
    public List<CjroneFamilyAccessibilityTransformationEntity> queryExportData(Map<String, Object> params) {
            return cjroneFamilyAccessibilityTransformationDao.queryExportData(params);
    }

}