package com.hmit.kernespring.modules.app.controller;

import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.common.utils.DateUtils;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.PinYinUtil;
import com.hmit.kernespring.common.utils.R;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.app.annotation.Login;
import com.hmit.kernespring.modules.app.annotation.LoginUser;
import com.hmit.kernespring.modules.app.entity.UserEntity;
import com.hmit.kernespring.modules.app.service.AppDisabilityCretificateService;
import com.hmit.kernespring.modules.cjrone.entity.CjroneAdministrativeDivisionEntity;
import com.hmit.kernespring.modules.cjrone.entity.CjroneCertificateApplyDocEntity;
import com.hmit.kernespring.modules.cjrone.entity.CjroneDocumentEntity;
import com.hmit.kernespring.modules.cjrone.entity.CjroneSignatureEntity;
import com.hmit.kernespring.modules.cjrone.service.*;
import com.hmit.kernespring.modules.cjrone_bl.entity.CjroneblDisabilityCertificateRedemptionEntity;
import com.hmit.kernespring.modules.data_management.entity.ApiCardIdEntity;
import com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity;
import com.hmit.kernespring.modules.data_management.service.ApiCardIdService;
import com.hmit.kernespring.modules.data_management.service.DataDisabilityCertificateService;
import com.hmit.kernespring.modules.cjrone_bl.service.CjroneblDisabilityCertificateRedemptionService;
import com.hmit.kernespring.modules.matter_application.entity.CjroneWelfareMatterApplicationEntity;
import com.hmit.kernespring.modules.matter_application.entity.DisabilityAssessmentCategoryEntity;
import com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity;
import com.hmit.kernespring.modules.matter_application.service.CjroneWelfareMatterApplicationService;
import com.hmit.kernespring.modules.matter_application.service.DisabilityAssessmentCategoryService;
import com.hmit.kernespring.modules.matter_application.service.DisabilityCertificateApplicationService;
import com.hmit.kernespring.modules.sign.DB.PersonDb;
import com.hmit.kernespring.modules.sign.bean.*;
import com.hmit.kernespring.modules.sign.config.ProjectConfig;
import com.hmit.kernespring.modules.sign.service.Account;
import com.hmit.kernespring.modules.sign.service.PdfManage;
import com.hmit.kernespring.modules.sign.service.Sign;
import com.hmit.kernespring.modules.sys.controller.AbstractController;
import com.itextpdf.text.BadElementException;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Image;
import com.itextpdf.text.pdf.*;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.*;

@RestController
@RequestMapping("/app")
@Api(value="AppDisabilityCretificateController",description = "APP残疾证申请相关接口")
public class AppDisabilityCretificateController extends AbstractController {

    @Autowired
    private DisabilityAssessmentCategoryService disabilityAssessmentCategoryService;
    @Autowired
    private CjroneDocumentService cjroneDocumentService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private DisabilityCertificateApplicationService disabilityCertificateApplicationService;

    @Autowired
    private ApiCardIdService apiCardIdService;
    @Autowired
    private CjroneCertificateApplyDocService cjroneCertificateApplyDocService;
    @Autowired
    private CjroneStreetService cjroneStreetService;
    @Autowired
    private CjroneVillageService cjroneVillageService;
    @Autowired
    private AppDisabilityCretificateService appDisabilityCretificateService;
    @Autowired
    private CjroneSignatureService cjroneSignatureService;
    @Autowired
    private CjroneWelfareMatterApplicationService cjroneWelfareMatterApplicationService;
    @Autowired
    private DataDisabilityCertificateService dataDisabilityCertificateService;
    @Autowired
    private CjroneblDisabilityCertificateRedemptionService cjroneblDisabilityCertificateRedemptionService;

    /**
     * 获得残疾人基本信息
     * @param user
     * @return
     */
    @Login
    @GetMapping("getDisabledPersonBaseInfo")
    @ApiOperation("获得残疾人基本信息")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "token", value = "登录token", required = false, paramType = "header")
    })
    public R getDisabledPersonBaseInfo(@LoginUser UserEntity user){
        //从 api-card 接口中获取
        System.out.println("------ user ---------------");
        System.out.println(new Gson().toJson(user));
        Map<String, Object> mp_tmp = new HashMap<>();
        Map<String, Object> tmp_params_names = new HashMap<>();
        mp_tmp.put("id_card", user.getPassword());
        List<ApiCardIdEntity> is_alive_klist = (List<ApiCardIdEntity>) apiCardIdService.listByMap(mp_tmp);
        if(is_alive_klist != null && is_alive_klist.size()>0){
            //获得省市区的数据
            tmp_params_names.put("code",is_alive_klist.get(0).getPresentCun());
            Map<String, Object> names=cjroneStreetService.queryNames2(tmp_params_names);
            if(names!=null){
                is_alive_klist.get(0).setShengName(names.get("provincename").toString());
                is_alive_klist.get(0).setShiName(names.get("cityname").toString());
                is_alive_klist.get(0).setQuName(names.get("areaname").toString());
                is_alive_klist.get(0).setZhenName(names.get("streetname").toString());
                is_alive_klist.get(0).setCunName(names.get("villagename").toString());
            }

            //更改性别
            if(is_alive_klist.get(0).getSex().equals("男"))
                is_alive_klist.get(0).setSex("1");
            else
                is_alive_klist.get(0).setSex("0");
            return R.ok().put("cjroneDisabledPerson", is_alive_klist.get(0));
        }else{
            return R.error(201,"暂无残疾人个人信息").put("cjroneDisabledPerson", null);
        }

    }

    /**
     * 修改残疾人基本信息
     */
    @Login
    @PostMapping("/updateDisabledPersonBaseInfo")
    @ApiOperation("修改残疾人基本信息")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "token", value = "登录token", required = false, paramType = "header")
    })
    public R updateDisabledPersonBaseInfo(@RequestBody ApiCardIdEntity apiCardIdEntity){
        if(apiCardIdEntity.getSex()!=null){
            if(apiCardIdEntity.getSex().equals("1"))
                apiCardIdEntity.setSex("男");
            else
                apiCardIdEntity.setSex("女");
        }
        apiCardIdService.updateById(apiCardIdEntity);
        return R.ok();
    }

    /**
     * 获得残疾人银行卡基本信息
     * @param user
     * @return
     */
    @Login
    @GetMapping("getBankInfo")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "token", value = "登录token", required = false, paramType = "header"),
            @ApiImplicitParam(name = "bankName", value = "银行卡姓名", required = true, paramType = "query"),
            @ApiImplicitParam(name = "bankAccount", value = "银行卡号", required = true, paramType = "query")
    })
//    @ApiResponses({@ApiResponse(code = 0, message = "返回残疾证信息", response = ApiCardIdEntity.class)})
    @ApiOperation("获得残疾人银行卡信息")
    public R getBankInfo(@LoginUser UserEntity user){
        //从 api-card 接口中获取
        System.out.println("------ user ---------------");
        System.out.println(new Gson().toJson(user));
        Map<String, Object> mp_tmp = new HashMap<>();
        ApiCardIdEntity apiCardIdEntity = apiCardIdService.queryByIdnum(user.getPassword());
        if(apiCardIdEntity != null){
            //获得银行卡信息
            mp_tmp.put("bankName",apiCardIdEntity.getBankName());
            mp_tmp.put("bankAccount",apiCardIdEntity.getBankAccount());
            return R.ok().put("cjroneBankInfo", mp_tmp);
        }else{
            return R.error(201,"此残疾人暂无银行卡信息").put("cjroneBankInfo", null);
        }

    }

    /**
     * 修改残疾人银行卡信息
     */
    @Login
    @PostMapping("/updateBankInfo")
    @ApiOperation("修改残疾人银行卡信息")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "token", value = "登录token", required = false, paramType = "header"),
            @ApiImplicitParam(name = "bankName", value = "银行卡姓名", required = true, paramType = "query"),
            @ApiImplicitParam(name = "bankAccount", value = "银行卡号", required = true, paramType = "query")
    })
    public R updateBankInfo(@LoginUser UserEntity user,@RequestParam String bankName,@RequestParam String bankAccount){
        ApiCardIdEntity apiCardIdEntity = apiCardIdService.queryByIdnum(user.getPassword());
        if(apiCardIdEntity != null){
            if(!"".equals(bankName)){
                apiCardIdEntity.setBankName(bankName);
            }
            if(!"".equals(bankAccount)){
                apiCardIdEntity.setBankAccount(bankAccount);
            }
            apiCardIdService.updateById(apiCardIdEntity);
            return R.ok();
        }else{
            return R.error(201,"此残疾人暂无银行卡信息！");
        }

    }

    /**
     * 获取原来的残疾证信息
     * @param user
     * @return
     */
    @Login
    @GetMapping("getOldDisabilityApplyInfo")
    @ApiOperation("获取原来的残疾证信息")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "token", value = "登录token", required = false, paramType = "header")
    })
    @ApiResponses({@ApiResponse(code = 0, message = "返回残疾证信息", response = DisabilityCertificateApplicationEntity.class)})
    public R getOldDisabilityApplyInfo(@LoginUser UserEntity user){
        //从 api-card 接口中获取
        System.out.println("------ user ---------------");
        System.out.println(new Gson().toJson(user));
        Map<String, Object> mp_tmp = new HashMap<>();
        mp_tmp.put("id_card", user.getPassword());
        List<DisabilityCertificateApplicationEntity> applyLists = (List<DisabilityCertificateApplicationEntity>) disabilityCertificateApplicationService.listByMap(mp_tmp);
        if(applyLists != null && applyLists.size()>0){
            return R.ok().put("cjroneOldCertificateApplication", applyLists.get(0));
        }else{
            return R.error(201,"暂无此人的残疾证信息").put("cjroneOldCertificateApplication", null);
        }
    }

    @Login
    @GetMapping("getApplyChangeList")
    @ApiOperation("获得残疾证换领记录")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "token", value = "登录token", required = false, paramType = "header")
    })
    public R getApplyChangeList(@LoginUser UserEntity user){
        System.out.println(new Gson().toJson(user));
        Map<String, Object> params=new HashMap<>();
        Map<String, Object> key=new HashMap<>();
        key.put("idCard",user.getPassword());
        params.put("key",key);
        PageUtils page = cjroneblDisabilityCertificateRedemptionService.queryPage(params);
        return R.ok().put("applyChangeList", page.getList());
    }

    /**
     * 残疾证换领
     */
    @Login
    @PostMapping("/saveDisabilityCertificateRedemption")
    @ApiOperation("保存残疾证换领")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "token", value = "登录token", required = false, paramType = "header")
    })
    public R saveDisabilityCertificateRedemption(@LoginUser UserEntity user){
        Map<String, Object> mp_tmp = new HashMap<>();
        mp_tmp.put("id_card", user.getPassword());
        CjroneWelfareMatterApplicationEntity entityCJZHL = new CjroneWelfareMatterApplicationEntity();
        DataDisabilityCertificateEntity dataDisabilityCertificateEntity=dataDisabilityCertificateService.getByIDCard(user.getPassword());
        //保存新增人
        entityCJZHL.setCreateId(user.getUserId());
        entityCJZHL.setCreateName(user.getUsername());
        //保存创建时间
        entityCJZHL.setCreateTime(DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN));
        entityCJZHL.setApplicationTime(DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN));
        // 保存申请类型为移动端
        entityCJZHL.setApplyType(0);
        // 保存申请状态  1 申请人待手签
        entityCJZHL.setStatus("1");
        // 根据身份证ID 去api_card表查询信息
        ApiCardIdEntity apiCardIdEntity = apiCardIdService.queryByIdnum(user.getPassword());

        if(apiCardIdEntity.getNativeCun() != null  &&  !"".equals(apiCardIdEntity.getNativeCun())){
            entityCJZHL.setNativeCun(apiCardIdEntity.getNativeCun());
            entityCJZHL.setNativeZhen(apiCardIdEntity.getNativeZhen());
        }else if(apiCardIdEntity.getNativeZhen() != null  && !"".equals(apiCardIdEntity.getNativeZhen())){
            entityCJZHL.setNativeZhen(apiCardIdEntity.getNativeZhen());
        }

        entityCJZHL.setName(user.getUsername());
        entityCJZHL.setIsCjzhlApply("1");
        entityCJZHL.setDisableId(dataDisabilityCertificateEntity.getDisableId());
        entityCJZHL.setIdCard(user.getPassword());
        entityCJZHL.setDisabilityCategory(dataDisabilityCertificateEntity.getDisabilityCategory());
        entityCJZHL.setDisabilityDegree(dataDisabilityCertificateEntity.getDisabilityDegree());

        cjroneWelfareMatterApplicationService.save(entityCJZHL);

        return R.ok();
    }

    @Login
    @GetMapping("getAdministrativeDivisionListStep")
    @ApiOperation(value = "获得所有省份的行政划分", notes = "获得所有省份的行政划分带code和step")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "token", value = "登录token", required = false, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "step", value = "步骤(1、省，2、市，3、区，4、街道)",  required = true,paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "code", value = "编码（传上级编码）",  required = true,dataType = "int", paramType = "query")
    })
    public R getAdministrativeDivisionListStep(@RequestParam Map<String, Object> params){
        System.out.println("aaaaa"+params);
        System.out.println(params.get("code"));
        System.out.println("0".equals(params.get("code").toString()));
        if (params.get("code") != null && "0".equals(params.get("code").toString())){
            List<CjroneAdministrativeDivisionEntity> adList=cjroneStreetService.getAdministrativeDivisionListStep(null,Integer.parseInt(params.get("step").toString()));
            return R.ok().put("list", adList);
        } else {
            List<CjroneAdministrativeDivisionEntity> adList=cjroneStreetService.getAdministrativeDivisionListStep(params.get("code").toString(),Integer.parseInt(params.get("step").toString()));
            return R.ok().put("list", adList);
        }
    }






//===========================以下代码是老代码==================================================

    /**
     *  获得评残排队人数
     */
/*    @Login
    @GetMapping("/getQueueNum")
    @ApiOperation("获得评残排队人数")*/
    public R getQueueNum(@RequestParam Integer disabilityType,@RequestParam Integer typeId){

        Map<String, Object> params = new HashMap<>();
        params.put("disabilitytype",disabilityType);

        Integer queueNum=0;

        //获得该残疾类别的医院信息
        DisabilityAssessmentCategoryEntity disabilityAssessmentCategoryEntity=disabilityAssessmentCategoryService.getById(disabilityType);

        //1、获得评残的时间段
        String ratingTime=disabilityAssessmentCategoryEntity.getRatingTime();

        if(typeId==1){
            // 新申请残疾证
            // 获取当前的日期(周几)
            String[] weeks = {"日","一","二","三","四","五","六"};
            Calendar cal = Calendar.getInstance();
            cal.setTime(new Date());
            int week_index = cal.get(Calendar.DAY_OF_WEEK) - 1;
            if(week_index<0){
                week_index = 0;
            }
            String todayOfWeeks=weeks[week_index];

            queueNum=appDisabilityCretificateService.getQueueNum(params);
        }
        else{
            //残疾证详情

            params.put("idcard",getUser().getPassword());
            queueNum=appDisabilityCretificateService.getQueueNum(params);
        }
        return R.ok().put("queueNum",queueNum).put("ratingLocation",disabilityAssessmentCategoryEntity.getRatingLocation()).put("ratingTime",disabilityAssessmentCategoryEntity.getRatingTime()).put("typeName",disabilityAssessmentCategoryEntity.getDisabilityType());


    }


    /**
     * 保存残疾人个人信息接口
     */
 /*   @Login
    @PostMapping("/saveDisabledPerson")
    @ApiOperation("保存残疾人个人信息接口")
    public R saveDisabledPerson(@RequestBody CjroneDisabledPersonEntity cjroneDisabledPerson){
        cjroneDisabledPersonService.save(cjroneDisabledPerson);

        return R.ok();
    }
*/


    /**
     * 获得残疾人的个人信息
     * @param user
     * @return
     */
/*    @Login
    @GetMapping("getDisabledPersonInfo")
    @ApiOperation("获得残疾人的个人信息")*/
    public R getDisabledPersonInfo(@LoginUser UserEntity user){
      /*  System.out.println(new Gson().toJson(user));
        CjroneDisabledPersonEntity cjroneDisabledPerson=cjroneDisabledPersonService.getById(1);

        return R.ok().put("cjroneDisabledPerson", cjroneDisabledPerson);*/

        //从 api-card 接口中获取
        System.out.println("------ user ---------------");
        System.out.println(new Gson().toJson(user));
        Map<String, Object> mp_tmp = new HashMap<>();
        Map<String, Object> tmp_params_names = new HashMap<>();
        mp_tmp.put("id_card", user.getPassword());
        List<ApiCardIdEntity> is_alive_klist = (List<ApiCardIdEntity>) apiCardIdService.listByMap(mp_tmp);
        if(is_alive_klist != null && is_alive_klist.size()>0){
            //获得省市区的数据
            tmp_params_names.put("code",is_alive_klist.get(0).getPresentCun());
            Map<String, Object> names=cjroneStreetService.queryNames2(tmp_params_names);
            if(names!=null){
                is_alive_klist.get(0).setShengName(names.get("provincename").toString());
                is_alive_klist.get(0).setShiName(names.get("cityname").toString());
                is_alive_klist.get(0).setQuName(names.get("areaname").toString());
                is_alive_klist.get(0).setZhenName(names.get("streetname").toString());
                is_alive_klist.get(0).setCunName(names.get("villagename").toString());
            }

            //更改性别
            if(is_alive_klist.get(0).getSex().equals("男"))
                is_alive_klist.get(0).setSex("1");
            else
                is_alive_klist.get(0).setSex("0");
            return R.ok().put("cjroneDisabledPerson", is_alive_klist.get(0));
        }else{
            return R.error(201,"暂无残疾人个人信息").put("cjroneDisabledPerson", null);
        }

    }

    /**
     * 修改残疾人基本信息
     */
/*
    @Login
    @PostMapping("/updateDisabledPerson")
    @ApiOperation("修改残疾人基本信息")
*/
    public R updateDisabledPerson(@RequestBody ApiCardIdEntity apiCardIdEntity){

        Map<String, Object> mp_tmp = new HashMap<>();
        mp_tmp.put("id_card", apiCardIdEntity.getIdCard());
        List<ApiCardIdEntity> is_alive_klist = (List<ApiCardIdEntity>) apiCardIdService.listByMap(mp_tmp);
        if(is_alive_klist != null && is_alive_klist.size()>0){
            System.out.println("------ updateuser ---------------");
            System.out.println(new Gson().toJson(apiCardIdEntity));

            if(apiCardIdEntity.getSex()!=null){
                if(apiCardIdEntity.getSex().equals("1"))
                    apiCardIdEntity.setSex("男");
                else
                    apiCardIdEntity.setSex("女");
            }
            apiCardIdService.updateById(apiCardIdEntity);
        } else{
            System.out.println("------ insertuser ---------------");
            System.out.println(new Gson().toJson(apiCardIdEntity));

            apiCardIdService.save(apiCardIdEntity);
        }


        return R.ok();
    }


    /**
     * 列表
     */
/*
    @Login
    @GetMapping("getAssessmentCategoryList")
    @ApiOperation("获得评定类别列表接口")
*/
    // @RequiresPermissions("matter_application:disabilityassessmentcategory:list")
    public R getAssessmentCategoryList(){

        Map<String, Object> params=new HashMap<>();
        Map<String, Object> key=new HashMap<>();
        params.put("key",key);
        PageUtils page = disabilityAssessmentCategoryService.queryPage(params);

        return R.ok().put("list", page.getList());
    }


/*
    @Login
    @GetMapping("getAssessmentCategoryInfo")
    @ApiOperation("获得评定类别详情接口")
*/
    // @RequiresPermissions("matter_application:disabilityassessmentcategory:list")
    public R getAssessmentCategoryInfo(@RequestParam Integer id){

        DisabilityAssessmentCategoryEntity disabilityAssessmentCategory = disabilityAssessmentCategoryService.getById(id);

        return R.ok().put("disabilityAssessmentCategory", disabilityAssessmentCategory);
    }

    /**
     * 上传文件
     */
/*
    @Login
    @PostMapping("/uploadNe")
    @ApiOperation("附件上传接口")
*/
    public R uploadNe(@RequestParam("file") MultipartFile file, @RequestParam("disabilityType") Integer disabilityType, @RequestParam("value") String value,@LoginUser UserEntity user) throws Exception {
        if (file.isEmpty()) {
            throw new RRException("上传文件不能为空");
        }
        System.out.println("cardId:" + user.getPassword());
        //上传文件
        String file_path = "/static/" + user.getPassword() + "/" + PinYinUtil.getFullSpell(value) + "/" + "/" + file.getOriginalFilename();
        String file_path_act = cjroneProperties.getUploadPath() + user.getPassword() + "/" + PinYinUtil.getFullSpell(value) + "/" + file.getOriginalFilename();
        System.out.println(file_path_act);
        File fileAct = new File(file_path_act);
        if (!fileAct.getParentFile().exists()) {
            fileAct.getParentFile().mkdirs();
        }
        try {
            file.transferTo(fileAct);
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }

        //保存文件信息
        CjroneDocumentEntity cjroneDocumentEntity = new CjroneDocumentEntity();
        cjroneDocumentEntity.setCreateId(user.getUserId());
        cjroneDocumentEntity.setCreateTime(DateUtils.getNowDate());
        cjroneDocumentEntity.setDisabilityAssessmentDetailName(value);  //value=0 表示是福利事项附件
        cjroneDocumentEntity.setDisabilityAssessmentId(disabilityType);
        cjroneDocumentEntity.setFileName(fileAct.getName());
        cjroneDocumentEntity.setFilePath(file_path);
        cjroneDocumentEntity.setFilePathAct(fileAct.getPath());
        cjroneDocumentEntity.setFileSize(Double.parseDouble(String.valueOf(file.getSize())));
        cjroneDocumentEntity.setFileType(file.getContentType());
        cjroneDocumentEntity.setRemark("");
        cjroneDocumentEntity.setStatus("1");
        cjroneDocumentService.save(cjroneDocumentEntity);

        return R.ok().put("url", file_path).put("docId", cjroneDocumentEntity.getId()).put("detailValue", value);
    }

/*
    @Login
    @PostMapping("/saveDisabilityApply")
    @ApiOperation("残疾证信息保存接口")
*/
    public R saveDisabilityApply(@RequestBody DisabilityCertificateApplicationEntity disabilityCertificateApplication,@LoginUser UserEntity user){
        System.out.println(new Gson().toJson(disabilityCertificateApplication));
        // 先判断又没有记录
        Map<String, Object> params = new HashMap<>();
        params.put("id_card",disabilityCertificateApplication.getIdCard());
        params.put("disability_type",disabilityCertificateApplication.getDisabilityType());
        List<DisabilityCertificateApplicationEntity> applyLists = (List<DisabilityCertificateApplicationEntity>) disabilityCertificateApplicationService.listByMap(params);
        if (applyLists.size()>0){
            return R.error(201,"该残疾人已申请过"+applyLists.get(0).getDisabilityTypeName()).put("applyId",applyLists.get(0).getDisabilityTypeName());
        }else {
            disabilityCertificateApplication.setApplicationTime(DateUtils.getNowDate());
            disabilityCertificateApplication.setCreateTime(DateUtils.getNowDate());
            disabilityCertificateApplication.setCreateId(user.getUserId());
            disabilityCertificateApplication.setStatus("1");  //先设置初始状态，等到残疾人手签之后，更新为1
            disabilityCertificateApplication.setSignStatus("1");  //未手签的状态
            //设置村镇名称
            CjroneAdministrativeDivisionEntity nativezhen=cjroneStreetService.getStreetById(disabilityCertificateApplication.getNativeZhen());
            if(nativezhen!=null){
                disabilityCertificateApplication.setNativeZhenName(nativezhen.getLabel());
            }else{
                disabilityCertificateApplication.setNativeZhenName("");
            }
            CjroneAdministrativeDivisionEntity presentzhen=cjroneStreetService.getStreetById(disabilityCertificateApplication.getPresentZhen());
            if(presentzhen!=null){
                disabilityCertificateApplication.setPresentZhenName(presentzhen.getLabel());
            }else{
                disabilityCertificateApplication.setPresentZhenName("");
            }
            CjroneAdministrativeDivisionEntity nativecun=cjroneVillageService.getVillageById(disabilityCertificateApplication.getNativeCun());
            if(nativecun!=null){
                disabilityCertificateApplication.setNativeCunName(nativecun.getLabel());
            }else{
                disabilityCertificateApplication.setNativeCunName("");
            }
            CjroneAdministrativeDivisionEntity presentcun=cjroneVillageService.getVillageById(disabilityCertificateApplication.getPresentCun());
            if(presentcun!=null){
                disabilityCertificateApplication.setPresentCunName(presentcun.getLabel());
            }else{
                disabilityCertificateApplication.setPresentCunName("");
            }


            disabilityCertificateApplicationService.save(disabilityCertificateApplication);
            return R.ok().put("applyId",disabilityCertificateApplication.getId());
            /*
             */
        }
    }

    /**
     * 修改
     */
/*
    @Login
    @RequestMapping("/updateDisabilityApply")
    @ApiOperation("残疾证信息修改接口")
*/
    // @RequiresPermissions("matter_application:disabilitycertificateapplication:update")
    public R updateDisabilityApply(@RequestBody DisabilityCertificateApplicationEntity disabilityCertificateApplication){
        disabilityCertificateApplicationService.updateById(disabilityCertificateApplication);
        return R.ok().put("applyId",disabilityCertificateApplication.getId());
    }

    // 删除残疾证信息
/*
    @Login
    @GetMapping("/deleteDisabilityApply")
    @ApiOperation("删除")
*/
    public R delete(@RequestParam Integer id){
        disabilityCertificateApplicationService.removeById(id);

        return R.ok();
    }


/*
    @Login
    @GetMapping("getDisabilityApplyList")
    @ApiOperation("残疾证申请列表接口")
*/
    public R getDisabilityApplyList(@LoginUser UserEntity user){
        System.out.println("================getList===================");
        System.out.println(new Gson().toJson(user));
        Map<String, Object> params=new HashMap<>();
        Map<String, Object> key=new HashMap<>();
        key.put("idCard",user.getPassword());
        params.put("key",key);
        PageUtils page = disabilityCertificateApplicationService.queryPage(params);
        //新增了是否已手签的状态
        return R.ok().put("list", page.getList());
    }

    //获得已经申请成功的残疾证列表
/*
    @Login
    @GetMapping("getDisabilityFinallyApplyList")
    @ApiOperation("残疾证申请成功列表接口")
*/
    public R getDisabilityFinallyApplyList(@LoginUser UserEntity user){

        List<Map<String,String>> resultList=new ArrayList<Map<String,String>>();;
        System.out.println(new Gson().toJson(user));
        Map<String, Object> key=new HashMap<>();
        key.put("idCard",user.getPassword());
        key.put("status","6");  //通过的状态
        List<DisabilityCertificateApplicationEntity> disabilityCertificateApplicationEntities = disabilityCertificateApplicationService.getListByMap(key);

        //新增了是否已手签的状态
        return R.ok().put("list", resultList);
    }


    /**
     * 信息
     */
/*
    @Login
    @GetMapping("getDisabilityInfo")
    @ApiOperation("残疾证申请详情接口")
*/
    public R info(@RequestParam Integer id ){

        /*DisabilityCertificateApplicationEntity disabilityCertificate = disabilityCertificateApplicationService.getById(id);
        //添加附件 docList
        disabilityCertificate
        return R.ok().put("disabilityCertificate", disabilityCertificate);*/

        DisabilityCertificateApplicationEntity disabilityCertificateApplication = disabilityCertificateApplicationService.getById(id);
        Map<String, Object> map = new HashMap<>();
        map.put("apply_id",disabilityCertificateApplication.getId());
        List<CjroneCertificateApplyDocEntity> doc_list = cjroneCertificateApplyDocService.queryApplyDocByMap(map);


        disabilityCertificateApplication.setDocList(doc_list);


        return R.ok().put("disabilityCertificateApplication", disabilityCertificateApplication);

    }

    /*@GetMapping("test")
    @ApiOperation("test接口")
    public R test(){

        return R.ok();
    }*/


/*    @Login
    @GetMapping("getApplyList")
    @ApiOperation("获得所有申请列表接口")*/
    public R getApplyList(@LoginUser UserEntity user){
        System.out.println(new Gson().toJson(user));
        Map<String, Object> params=new HashMap<>();
        Map<String, Object> key=new HashMap<>();
        key.put("createId",user.getUserId());
        params.put("key",key);
        PageUtils page = disabilityCertificateApplicationService.queryPage(params);
        return R.ok().put("list", page.getList());
    }

//    @Login
//    @GetMapping("getAdministrativeDivisionList")
//    @ApiOperation("获得奉化区新政划分")
    public R getAdministrativeDivisionList(){
        //奉化编号330213
        List<CjroneAdministrativeDivisionEntity> adList=cjroneStreetService.getAdministrativeDivisionList("330213");
        return R.ok().put("list", adList);
    }

/*    @Login
    @GetMapping("getAdministrativeDivisionListForAPP")
    @ApiOperation("获得奉化区新政划分-APP专用")*/
    public R getAdministrativeDivisionListForAPP(){
        //奉化编号330213
        List<Map<String,Object>> adList=cjroneStreetService.getAdministrativeDivisionListForAPP("330213");
        return R.ok().put("list", adList);

    }


    /**
     * 移动端 残疾证申请 生成电子签章 pdf
     */
/*    @Login
    @RequestMapping("/printDisPDF/{id}")
    @ApiOperation("生成电子签章")*/
    public R printPDF(@PathVariable("id") Integer id) throws UnsupportedEncodingException {
        Map<String, Object> tmp_params = new HashMap<>();
        Map<String, Object> tmp_params_names = new HashMap<>();
        tmp_params.put("type","残疾证申请残疾人手签");
        tmp_params.put("status","1");
        tmp_params.put("type_id",id);
        List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
        if (alive_list.size()>0){
            CjroneSignatureEntity cjroneSignatureEntity = alive_list.get(0);
            return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", cjroneSignatureEntity.getFileName());
        }else {

            //根据编号获得详细信息
            DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity = disabilityCertificateApplicationService.getById(id);

            String fileName = "残疾证申请表" + '_' + disabilityCertificateApplicationEntity.getName() + "_" + disabilityCertificateApplicationEntity.getDisabilityTypeName() + ".pdf";

            // 原pdf模板路径
            String templatePath = cjroneProperties.getTempletePath()+"残疾人证申请表模板.pdf";
            // 生成的新文件路径
            String newPDFPath = cjroneProperties.getSignaturePath()+fileName;

            System.out.println("文件路径1："+templatePath);
            System.out.println("文件路径2："+newPDFPath);



            // 获得待生成的实体文件
            Map<String, String> map =new HashMap<String, String>();
            map.put("name",disabilityCertificateApplicationEntity.getName()==null?"":disabilityCertificateApplicationEntity.getName());


            if ("1".equals(disabilityCertificateApplicationEntity.getSex()))
                map.put("sex","男");
            else
                map.put("sex","女");
            map.put("nationality",disabilityCertificateApplicationEntity.getNationality()==null?"":disabilityCertificateApplicationEntity.getNationality());

            if("1".equals(disabilityCertificateApplicationEntity.getMaritalStatus()))
                map.put("maritalStatus","已婚");
            else if("2".equals(disabilityCertificateApplicationEntity.getMaritalStatus()))
                map.put("maritalStatus","未婚");
            else if("3".equals(disabilityCertificateApplicationEntity.getMaritalStatus()))
                map.put("maritalStatus","丧偶");
            else
                map.put("maritalStatus","离异");

            if("1".equals(disabilityCertificateApplicationEntity.getApplicationType()))
                map.put("applicationType","新申请（监护人证明材料粘在申请表后面）");
            else if("2".equals(disabilityCertificateApplicationEntity.getApplicationType()))
                map.put("applicationType","换领申请");
            else
                map.put("applicationType","补办申请");

            map.put("birthday",disabilityCertificateApplicationEntity.getBirthday()==null?"":disabilityCertificateApplicationEntity.getBirthday());
            map.put("nativePlace",disabilityCertificateApplicationEntity.getNativePlace()==null?"":disabilityCertificateApplicationEntity.getNativePlace());
            map.put("educationDegree",disabilityCertificateApplicationEntity.getEducationDegree()==null?"":disabilityCertificateApplicationEntity.getEducationDegree());
            map.put("idCard",disabilityCertificateApplicationEntity.getIdCard()==null?"":disabilityCertificateApplicationEntity.getIdCard());
            map.put("nativeZhen",disabilityCertificateApplicationEntity.getNativeZhenName()==null?"":disabilityCertificateApplicationEntity.getNativeZhenName());
            map.put("nativeCun",disabilityCertificateApplicationEntity.getNativeCunName()==null?"":disabilityCertificateApplicationEntity.getNativeCunName());
            map.put("nativeAddress",disabilityCertificateApplicationEntity.getNativeAddress()==null?"":disabilityCertificateApplicationEntity.getNativeAddress());
            map.put("presentZhen",disabilityCertificateApplicationEntity.getPresentZhenName()==null?"":disabilityCertificateApplicationEntity.getPresentZhenName());
            map.put("presentCun",disabilityCertificateApplicationEntity.getPresentCunName()==null?"":disabilityCertificateApplicationEntity.getPresentCunName());
            //获得省市区的数据
            tmp_params_names.put("code",disabilityCertificateApplicationEntity.getPresentZhen());
            Map<String, Object> names=cjroneStreetService.queryNames(tmp_params_names);
            if(names!=null){
                map.put("presentShen",names.get("provincename").toString());
                map.put("presentShi",names.get("cityname").toString());
                map.put("presentQu",names.get("areaname").toString());
            }
            map.put("presentAddress",disabilityCertificateApplicationEntity.getPresentAddress()==null?"":disabilityCertificateApplicationEntity.getPresentAddress());
            map.put("postcode",disabilityCertificateApplicationEntity.getPostcode()==null?"":disabilityCertificateApplicationEntity.getPostcode());
            map.put("mobilePhone",disabilityCertificateApplicationEntity.getMobilePhone()==null?"":disabilityCertificateApplicationEntity.getMobilePhone());
            map.put("guardianName",disabilityCertificateApplicationEntity.getGuardianName()==null?"":disabilityCertificateApplicationEntity.getGuardianName());
            map.put("guardianPhone",disabilityCertificateApplicationEntity.getGuardianPhone()==null?"":disabilityCertificateApplicationEntity.getGuardianPhone());
            map.put("guardianIdcard",disabilityCertificateApplicationEntity.getGuardianIdcard()==null?"":disabilityCertificateApplicationEntity.getGuardianIdcard());
            map.put("guardianRelation",disabilityCertificateApplicationEntity.getGuardianRelation()==null?"":disabilityCertificateApplicationEntity.getGuardianRelation());
            //dcEntity.setApplicationType(disabilityCertificateApplicationEntity.getApplicationType());
            //map.put("applicationTime",disabilityCertificateApplicationEntity.getApplicationTime()==null?"":disabilityCertificateApplicationEntity.getApplicationTime());
            map.put("applicationTime",disabilityCertificateApplicationEntity.getApplicationTime()==null?"":disabilityCertificateApplicationEntity.getApplicationTime().split(" ")[0]);
            Calendar now = Calendar.getInstance();
            map.put("applicationYear",now.get(Calendar.YEAR)+"");
            map.put("applicationMonth",(now.get(Calendar.MONTH) + 1)+"");
            map.put("applicationDay",now.get(Calendar.DAY_OF_MONTH)+"");

            //添加照片
            String imagePath = cjroneProperties.getDownloadPath()+disabilityCertificateApplicationEntity.getIdCard()+".jpg";      // 图片的绝对路径
            Image image = null;     // 声明图片对象
            try {
                image = Image.getInstance(imagePath);       // 取得图片对象
            } catch (BadElementException | IOException e) {
                System.out.println("实例化【图片】 - 失败！");
            }
            if(image!=null){
                image.scaleAbsolute(87,108);
                image.setAbsolutePosition(427, 591);      // （以左下角为原点）设置图片的坐标
            }


            FileOutputStream out;
            int num = 1;//页数
            ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
            try {
                out = new FileOutputStream(newPDFPath);// 输出流
                Document doc = new Document();   //新建一个文档
                PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                doc.open();

                for (int i = 0; i < num; i++) {
                    bos[i] = new ByteArrayOutputStream();
                    PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                    PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                    AcroFields form = stamper.getAcroFields(); //获取文本域
                    // BaseFont
                    // 1、使用iTextAsian.jar中的字体
                    // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                    // 2、使用Windows系统字体(TrueType)
                    // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                    // 3、使用资源字体(ClassPath)
                    // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                    BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                    java.util.Iterator<String> it = form.getFields().keySet().iterator();
                    while (it.hasNext()) {
                        String name = it.next();
                        System.out.println(name + ":"+map.get(name));
                        form.setFieldProperty(name,"textfont",font,null);
                        // form.addSubstitutionFont(font);
                        form.setField(name, map.get(name));
                    }

                    if(image!=null){
                        // 获取操作的页面--添加照片
                        PdfContentByte under = stamper.getOverContent(1);
                        under.addImage(image);
                    }

                    stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                    stamper.close();

                }
                PdfImportedPage page = null;
                for (int i = 0; i < num; i++) {
                    page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                    copy.addPage(page);
                }
                doc.close();
                out.close();
            } catch (IOException e) {
                System.out.println("导出异常");
            } catch (DocumentException e) {
                System.out.println("文档异常");
            }

            // 生成电子签章pdf 并保存
            System.out.println(disabilityCertificateApplicationEntity.getDisabilityTypeName());

            String fileUrl = URLEncoder.encode("残疾证申请表", "UTF-8") + "_" + URLEncoder.encode(disabilityCertificateApplicationEntity.getName(), "UTF-8") + "_" + URLEncoder.encode(disabilityCertificateApplicationEntity.getDisabilityTypeName(), "UTF-8") + ".pdf";
            //String filePath = pdfUtils.pdfDisibilityApplyToSignature(cjroneProperties.getTempletePath(), cjroneProperties.getSignaturePath(), dcEntity, fileName);
            CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
            cjroneSignature.setCreateDate(new Date());
            cjroneSignature.setCreateId(disabilityCertificateApplicationEntity.getCreateId());
            cjroneSignature.setType("残疾证申请");
            cjroneSignature.setTypeId(disabilityCertificateApplicationEntity.getId());
            cjroneSignature.setUrl(newPDFPath);
            cjroneSignatureService.save(cjroneSignature);
            // return R.ok().put("fileName", "signature_"+ PinYinUtil.getFullSpell(cjroneDisabilityHospital.getName())+".pdf").put("signId",cjroneSignature.getId());
            return R.ok().put("fileUrl", fileUrl).put("fileName", fileName);
        }
    }


    //移动端电子签章-单文件--- 已作废

/*    @GetMapping("/signateAppFile")
    @Login*/
    public void signateAppFile(@LoginUser UserEntity user ,@RequestParam("applyId") String applyId, @RequestParam("fileName") String fileName, @RequestParam("type") String type, HttpServletRequest request, HttpServletResponse response) throws Exception{
        System.out.println(new Gson().toJson(user));
        String filePath = cjroneProperties.getSignaturePath()+fileName;

        //创建personBean对象  设置个人参数
        PersonBean personBean = new PersonBean();
        personBean.setThirdId(PersonDb.userid);
        personBean.setName(PersonDb.username);
        personBean.setIdType(19);//demo中默认设置为中国大陆居民身份证，如遇其他类型，请参考对接文档中证件号对应的类型值。
        personBean.setIdNo(PersonDb.idnum);
        personBean.setMobile(PersonDb.mobile);
        personBean.setEmail(PersonDb.email);
        //创建账户
        Account account = new Account();
        String accountId = account.addPerson(personBean);
        Cookie c11 = new Cookie("personAccountId", accountId);
        response.addCookie(c11);
        if(accountId!=null&&!accountId.equals("")){
        }
        System.out.println(accountId);
        //log.error("accountId:{}", accountId);
        //获取accountId
        String personAccountId = accountId;

        Cookie[] cookies = request.getCookies();
        if(cookies!=null){
            for (Cookie cookie : cookies) {
                if (cookie.getName().equals("personAccountId")) {
                    personAccountId = cookie.getValue();
                }
            }
        }

        System.out.println("personAccountId:"+personAccountId);
        if(personAccountId.equals("")){
            response.getWriter().write("请先创建账户");
            response.getWriter().flush();
            response.getWriter().close();
            return ;
        }

        PdfManage pm= new PdfManage();
        //将文件上传至签章服务器
        UploadUrlBean uploadUrlBean = pm.uploadUrl(filePath);
        int uploadFile = pm.uploadFile(filePath, uploadUrlBean.getUploadUrl());
        System.out.println("uploadFile::"+uploadFile);
        if(uploadFile<200||uploadFile>400){//判断是否文件上传至签章服务器是否成功
            response.getWriter().write("文件上传至签章服务器失败");
            response.getWriter().flush();
            response.getWriter().close();
            return ;
        }

        //本地文件创建文档
        String docId = pm.createDocByFileKey(uploadUrlBean.getFilekey());
        if(docId==null||docId.equals("")){//判断本地文件创建文档是否成功
            response.getWriter().write("本地文件创建文档失败");
            response.getWriter().flush();
            response.getWriter().close();
            return ;
        }else {
            // 将电子签章保存到数据库 已备签章完毕数据回调
            CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
            cjroneSignature.setCreateDate(new Date());
            cjroneSignature.setCreateId(Long.parseLong("2"));
            // cjroneSignature.setType("残疾证申请残疾人手签");
            cjroneSignature.setType(type);
            cjroneSignature.setTypeId(Integer.parseInt(applyId));
            cjroneSignature.setFileName(docId+".pdf");
            cjroneSignature.setAccountId(accountId);
            cjroneSignature.setAccountName(personBean.getName());
            cjroneSignature.setStatus("2");
            cjroneSignatureService.save(cjroneSignature);
        }
        /*
         * 单个文档创建签署流程
         */
        //准备数据
        ProcessBean pb= new ProcessBean();
        pb.setBusinessScene("申请材料证明");
        pb.setInitiatorAccountId(personAccountId);
        pb.setSignPlatform("1");
        //pb.setRedirectUrl("http://www.ctools.top");
        pb.setRedirectUrl("http://**************:8090/cjrone/cjrone/cjroneSign/docSigndetail");
        pb.setDocId(docId);
        pb.setMultiple(false);
        pb.setEncryption(false);
        pb.setPayer(ProjectConfig.payer);

        /*
         * 创建签署流程
         */
        Sign sign = new Sign();
        String flowId = sign.signProcess(pb);
        if(flowId==null||flowId.equals("")){
            response.getWriter().write("创建签署流程失败");
            response.getWriter().flush();
            response.getWriter().close();
            return ;
        }

        /*
         * 发起签署
         */
        //设置签署类型和位置
        PosBean pos = new PosBean();
        //签署页码
        pos.setPosPage("1");
        //签署坐标X
        pos.setPosX(200.0f);
        //签署坐标Y
        pos.setPosY(300.0f);
        //印章大小，可为空
        pos.setScale(159.0f);
        //签署定位类型，1-关键字定位，2-坐标定位，3-骑缝章
        pos.setSignType(2);
        ArrayList<PosBean> list= new ArrayList<>();
        list.add(pos);
        String signShortUrl = "";

        //登录的个人用户，使用个人用户签署
        PersonSignBean p = new PersonSignBean();

        p.setAccountId(personAccountId);
        p.setFlowId(flowId);
        p.setSealType("0,1");
        p.setThirdOrderNo("setThirdOrderNosetThirdOrderNo");
        //p.setPosList(list);

        SignResultBean personSign = sign.PersonSign(p,pb.isMultiple(),true);
        signShortUrl =  personSign.getSignShortUrl();    // 获得短链接            //personSign.getSignUrl();
        if(signShortUrl==null||signShortUrl.equals("")){
            response.getWriter().write("获取签署链接失败！");
            response.getWriter().flush();
            response.getWriter().close();
            return ;
        }

        /**
         * 将docId、flowId存放在cookie中，正式使用也可存放在数据库中与accountId进行对应
         */
        Cookie c1 = new Cookie("docId", docId);
        Cookie c2 = new Cookie("flowId", flowId);
        response.addCookie(c1);
        response.addCookie(c2);

        /*
         * 跳转到签署页面
         */

       // logger.info("请求前的response参数："+response.getHeader("Cookie"));


        System.out.println(new Gson().toJson(c1));
         System.out.println(new Gson().toJson(c2));
        response.setHeader("refresh", "1;URL="+signShortUrl);


    }





}
