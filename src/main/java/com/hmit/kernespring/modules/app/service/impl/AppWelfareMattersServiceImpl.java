package com.hmit.kernespring.modules.app.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.*;
import com.google.gson.reflect.TypeToken;
import com.hmit.kernespring.common.utils.DateUtils;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.app.service.APIService;
import com.hmit.kernespring.modules.app.service.AppWelfareMattersService;
import com.hmit.kernespring.modules.cjrone.entity.*;
import com.hmit.kernespring.modules.cjrone.service.*;
import com.hmit.kernespring.modules.data_management.entity.ApiFdDbrEntity;
import com.hmit.kernespring.modules.data_management.entity.DataResidentPensionInsuranceEntity;
import com.hmit.kernespring.modules.data_management.entity.DisabilityCertificateSyncDataEntity;
import com.hmit.kernespring.modules.data_management.service.DataResidentPensionInsuranceService;
import com.hmit.kernespring.modules.data_management.service.DisabilityCertificateSyncDataService;
import com.hmit.kernespring.modules.matter_application.dao.CjroneWelfareMatterApplicationDao;
import com.hmit.kernespring.modules.matter_application.entity.CjroneWelfareMatterApplicationEntity;
import com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity;
import com.hmit.kernespring.modules.matter_application.service.DisabilityCertificateApplicationService;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Type;
import java.util.*;


@Service("appWelfareMattersServiceImpl")
public class AppWelfareMattersServiceImpl extends ServiceImpl<CjroneWelfareMatterApplicationDao, CjroneWelfareMatterApplicationEntity> implements AppWelfareMattersService {

    @Autowired
    private DisabilityCertificateSyncDataService disabilityCertificateSyncDataService;
    @Autowired
    private SysDictService sysDictService;
    @Autowired
    private CjroneWelfareApplyDocService cjroneWelfareApplyDocService;
    @Autowired
    private CjroneLivingAllowanceService cjroneLivingAllowanceService;
    @Autowired
    private CjroneNursingSubsidyService cjroneNursingSubsidyService;
    @Autowired
    private CjroneRehabilitationSubsidyService cjroneRehabilitationSubsidyService;
    @Autowired
    private CjroneResidentPensionInsuranceService cjroneResidentPensionInsuranceService;
    @Autowired
    private DataResidentPensionInsuranceService dataResidentPensionInsuranceService;
    @Autowired
    private CjroneSignatureService  cjroneSignatureService;
    @Autowired
    private CjroneProperties  cjroneProperties;
    @Autowired
    private CjroneEmploymentSubsidyService cjroneEmploymentSubsidyService;
    @Autowired
    private CjroneComprehensiveMedicalInsuranceService cjroneComprehensiveMedicalInsuranceService;
    @Autowired
    private CjroneFamilyAccessibilityTransformationService cjroneFamilyAccessibilityTransformationService;
    @Autowired
    private DisabilityCertificateApplicationService disabilityCertificateApplicationService;
    @Autowired
    private APIService apiService;

    /**
     * 用来解决空字符串无法转为0的问题
     * Created by kernespring
     * on 2019-04-03.
     */
    public static Gson gson = new GsonBuilder()
            .registerTypeAdapter(Integer.class, new JsonDeserializer<Integer>() {

                @Override
                public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                    if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                        //定义为int类型,如果后台返回""或者null,则返回0
                        return null;
                    }
                    return json.getAsInt();
                }
            })
            .registerTypeAdapter(int.class, new JsonDeserializer<Integer>() {

                @Override
                public Integer deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
                    if (json.getAsString().equals("") || json.getAsString().equals("null")) {
                        //定义为int类型,如果后台返回""或者null,则返回0
                        return null;
                    }
                    return json.getAsInt();
                }
            })
            .create();


    @Override
    public Map<String, String> saveWel(CjroneWelfareMatterApplicationEntity entity) {
        return null;
    }
}
