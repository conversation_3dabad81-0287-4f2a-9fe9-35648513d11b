

package com.hmit.kernespring.modules.app.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.hmit.kernespring.modules.app.form.LoginForm;
import com.hmit.kernespring.modules.app.entity.UserEntity;

/**
 * 用户
 *
 * @<NAME_EMAIL>
 */
public interface UserService extends IService<UserEntity> {

	UserEntity queryByMobile(String mobile);

	UserEntity queryByIdnum(String idnum);

	/**
	 * 用户登录
	 * @param form    登录表单
	 * @return        返回用户ID
	 */
	long login(LoginForm form);
}
