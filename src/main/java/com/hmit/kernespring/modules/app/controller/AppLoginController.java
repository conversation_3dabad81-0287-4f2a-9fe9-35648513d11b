

package com.hmit.kernespring.modules.app.controller;


import com.google.gson.Gson;
import com.hmit.kernespring.common.utils.CjroneAppUtil;
import com.hmit.kernespring.common.utils.HttpRequestUtil;
import com.hmit.kernespring.common.utils.R;
import com.hmit.kernespring.common.validator.ValidatorUtils;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.app.annotation.LoginUser;
import com.hmit.kernespring.modules.app.entity.UserEntity;
import com.hmit.kernespring.modules.app.form.LoginForm;
import com.hmit.kernespring.modules.app.service.APIService;
import com.hmit.kernespring.modules.app.service.UserService;
import com.hmit.kernespring.modules.app.utils.JwtUtils;
import com.hmit.kernespring.modules.data_management.entity.ApiCardIdEntity;
import com.hmit.kernespring.modules.data_management.entity.ApiHuJiEntity;
import com.hmit.kernespring.modules.data_management.service.ApiCardIdService;
import com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity;
import com.hmit.kernespring.modules.matter_application.service.DisabilityCertificateApplicationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.hmit.kernespring.modules.matter_application.controller.DisabilityCertificateApplicationController.count;

/**
 * APP登录授权  123
 *
 * @<NAME_EMAIL>
 */
@RestController
@RequestMapping("/app")
@Api(value="APP登录接口",description = "APP登录接口")
public class AppLoginController {
    @Autowired
    private UserService userService;
    @Autowired
    private JwtUtils jwtUtils;
    @Autowired
    private APIService apiService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private ApiCardIdService apiCardIdService;
    @Autowired
    private DisabilityCertificateApplicationService disabilityCertificateApplicationService;

    /**
     * 登录
     */
    @PostMapping("login")
    @ApiOperation("登录")
    public R login(@RequestBody LoginForm form){
        //表单校验
        ValidatorUtils.validateEntity(form);

        //用户登录
        long userId = userService.login(form);

        //生成token
        String token = jwtUtils.generateToken(userId);

        Map<String, Object> map = new HashMap<>();
        map.put("token", token);
        map.put("expire", jwtUtils.getExpire());

        return R.ok(map);
    }

    //@Login
    @GetMapping("ssoSimulation")
    @ApiOperation("返回当前登陆用户数据")
    public R ssoSimulation(@LoginUser UserEntity user){
        System.out.println(new Gson().toJson(user));


        return R.ok();
    }


    //个人登录 - 单点登陆
    @RequestMapping(value="/check_user")
    @ResponseBody
    public void checkUser(@RequestParam Map<String,Object> map , HttpServletRequest request, HttpServletResponse response) throws UnsupportedEncodingException {
        // 获取到票据信息ticket
        System.out.println("开始进入这个checkuser了————————————————————");
        System.out.println(map);
        String redirectUrl = "";
        String Token="";

        if(map.get("ticket") != null){
            System.out.println("Ticket 是"+map.get("ticket")+"——————————————————————");
            Map<String,Object> user_map = CjroneAppUtil.getUserInfo(map);
            System.out.println("UserMap是——————————————————————————————");
            System.out.println(user_map);
            if (user_map.get("idnum") !=null){
                System.out.println(user_map.get("idnum"));
                String params = "idnum="+ URLEncoder.encode(user_map.get("idnum").toString(), "UTF8")+
                        "&mobile="+URLEncoder.encode(user_map.get("mobile").toString(), "UTF8")+
                        "&username="+URLEncoder.encode(user_map.get("username").toString(), "UTF8");
                if (map.get("sp") != null) {
                    redirectUrl = map.get("sp") +"?"+ params;
                }

                //判断是否已经存在了这条信息
                UserEntity oldUser=userService.queryByIdnum(URLEncoder.encode(user_map.get("idnum").toString(), "UTF8"));
                if(oldUser==null){
                    //将获得的信息保存到个人登陆表
                    UserEntity user=new UserEntity();
                    user.setUsername(user_map.get("username").toString());
                    user.setMobile(URLEncoder.encode(user_map.get("mobile").toString(), "UTF8"));
                    user.setPassword(URLEncoder.encode(user_map.get("idnum").toString(), "UTF8"));
                    userService.save(user);

                    //将获得的信息保存到个人信息表
                   /* CjroneDisabledPersonEntity disabledPersonEntity=new CjroneDisabledPersonEntity();
                    disabledPersonEntity.setName(user_map.get("username").toString());
                    //  disabledPersonEntity.setMobilePhone(URLEncoder.encode(user_map.get("mobile").toString(), "UTF8"));
                    disabledPersonEntity.setIdCard(URLEncoder.encode(user_map.get("idnum").toString(), "UTF8"));
                    // disabledPersonEntity.setSex(URLEncoder.encode(user_map.get("sex").toString(), "UTF8"));
                    disabledPersonEntity.setPostcode(URLEncoder.encode(user_map.get("postcode").toString(), "UTF8"));
                    disabledPersonEntity.setBirthday(URLEncoder.encode(user_map.get("birthday").toString(), "UTF8"));
                    disabledPersonEntity.setPresentAddress(user_map.get("homeaddress").toString());
                    cjroneDisabledPersonService.save(disabledPersonEntity);*/
                }
                //判断apiCardId表是否已经存在了
                ApiCardIdEntity apiCardId = apiCardIdService.queryByIdnum(URLEncoder.encode(user_map.get("idnum").toString(), "UTF8"));
                if(apiCardId==null){
                    //将获得的信息保存到个人登陆表
                    ApiCardIdEntity apiCardIdUser=new ApiCardIdEntity();
                    apiCardIdUser.setName(user_map.get("username").toString());
                    apiCardIdUser.setMobilePhone(URLEncoder.encode(user_map.get("mobile").toString(), "UTF8"));
                    apiCardIdUser.setIdCard(URLEncoder.encode(user_map.get("idnum").toString(), "UTF8"));
                    apiCardIdService.save(apiCardIdUser);

                }
                //生成token返回
                UserEntity tbuser=userService.queryByIdnum(URLEncoder.encode(user_map.get("idnum").toString(), "UTF8"));
                Token = jwtUtils.generateToken(tbuser.getUserId());

            }
        }else{
            //return  R.error().put("msg","票据信息不能为空！");
            if (map.get("sp") != null) {
                redirectUrl = map.get("sp").toString()+"?1=1";
            }
        }


        String record_id = UUID.randomUUID().toString();
        System.out.println("Url: "+redirectUrl+"&recordId="+record_id+"&token="+Token);
        response.setHeader("refresh", "1;URL=" + redirectUrl+"&recordId="+record_id+"&token="+Token);
    }



    /**
     * 1、验证是否奉化户籍或死亡 2、是否有残疾证 调用公安身份证和户籍接口，及查询本系统后台数据
     */
    @PostMapping("/checkIDCard")
    @ApiOperation("身份验证")
    // @RequiresPermissions("matter_application:disabilitycertificateapplication:save")
    public R checkIDCard(@RequestBody DisabilityCertificateApplicationEntity disabilityCertificateApplication){
        System.out.println("aaa "+disabilityCertificateApplication.getIdCard());
        boolean is_dead = false;
        boolean is_fh = false;
        boolean is_cjz = false;
        boolean is_exists=true;   // 申请人信息在公安查询不到
        DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity = new DisabilityCertificateApplicationEntity();
        ApiCardIdEntity apiCardId_result = new ApiCardIdEntity();
        // 验证是否奉化户籍 公安身份证接口  优先从系统验证
        Map<String, Object> mp_tmp = new HashMap<>();
        mp_tmp.put("id_card", disabilityCertificateApplication.getIdCard());
        Map<String, Object> aaa_tmp = new HashMap<>();
        aaa_tmp.put("id_card", disabilityCertificateApplication.getIdCard());
        aaa_tmp.put("status", "6");
        List<DisabilityCertificateApplicationEntity> is_apply_list = (List<DisabilityCertificateApplicationEntity>) disabilityCertificateApplicationService.listByMap(aaa_tmp);
        is_apply_list = is_apply_list.stream().sorted((h1,h2) -> h2.getCreateTime().compareTo(h1.getCreateTime())).collect(Collectors.toList());
        if (is_apply_list.size() == 0) {
            List<ApiCardIdEntity> is_alive_klist = (List<ApiCardIdEntity>) apiCardIdService.listByMap(mp_tmp);
            mp_tmp.put("name", disabilityCertificateApplication.getName());
            if (is_alive_klist.size() == 0) {
                Map<String, Object> hh_result = apiService.queryHuJiInfo(disabilityCertificateApplication.getIdCard(), null);
                if ("00".equals(hh_result.get("code").toString()) && hh_result.get("datas") != null) {
                    String data = hh_result.get("datas").toString();
                    data = data.substring(1,data.length()-1);
                    String data_new = data.substring(0,data.indexOf("registrationDate=")+27)+data.substring(data.indexOf("registrationDate=")+36,data.length());
                    System.out.println(data_new);

                    ApiHuJiEntity apiHuJiEntity = new Gson().fromJson(data_new.replaceAll(" ","").replaceAll("=,","=null,"), ApiHuJiEntity.class);
                    if (apiHuJiEntity != null) {
                        ApiCardIdEntity apiCardId = new ApiCardIdEntity();
                        System.out.println(new Gson().toJson(apiHuJiEntity));
                        if (apiHuJiEntity.getRegistrationAuthority() != null && apiHuJiEntity.getRegistrationAuthority().indexOf("奉化") != -1){
                            System.out.println("当前是奉化户籍");
                            is_fh = true;
                            apiCardId.setQfjg("奉化市公安局");
                        }else {
                            apiCardId.setQfjg(apiHuJiEntity.getRegistrationAuthority());
                        }
                        System.out.println("apiHuJiEntity: "+new Gson().toJson(apiHuJiEntity));
                        apiCardId.setName(apiHuJiEntity.getName());
                        apiCardId.setSex(apiHuJiEntity.getSex());
                        apiCardId.setNationality(apiHuJiEntity.getNation());
                        if (apiHuJiEntity.getDateOfBirth().length() == 8){
                            String aabc = apiHuJiEntity.getDateOfBirth();
                            aabc = aabc.substring(0,4)+"-"+aabc.substring(4,6)+"-"+aabc.substring(6,8);
                            apiCardId.setBirthday(aabc);
                        }else {
                            apiCardId.setBirthday(apiHuJiEntity.getDateOfBirth());
                        }
                        apiCardId.setNativePlace("浙江");
                        apiCardId.setIdCard(apiHuJiEntity.getIdcard());
                        apiCardId.setNativeAddress(apiHuJiEntity.getWhereToLocal());
                        apiCardId.setJgbh("长期");
                        System.out.println("dATABean: "+new Gson().toJson(apiHuJiEntity));
                        Map<String, Object> photo_result = apiService.queryCardIdPho(disabilityCertificateApplication.getIdCard(), apiHuJiEntity.getName(), apiHuJiEntity.getNation(), apiHuJiEntity.getDateOfBirth(), null);
                        System.out.println("photo_result: "+photo_result);
                        if (photo_result.get("datas") == null || "null".equals(photo_result.get("datas"))){
                            apiCardId.setPhoto("abc.jpg");
                        }else {
                            System.out.println(photo_result.get("datas").toString());
                        /*
                        // 老代码
                        List<CardInfoPhoEntity> photo_list = new Gson().fromJson(photo_result.get("datas").toString(), new TypeToken<List<CardInfoPhoEntity>>() {
                        }.getType());
                        if (photo_list.size() != 0 && "1".equals(photo_list.get(0).getIsExist())) {
                            String sava_path = HttpRequestUtil.downloadPicture(photo_list.get(0).getURL(), cjroneProperties.getDownloadPath(), apiCardId.getIdCard() + ".jpg");
                            apiCardId.setPhoto(sava_path);
                        }*/

                            // 新代码
                            String pho_datas = photo_result.get("datas").toString();
                            System.out.println("当前电子照片张数：" + count(pho_datas, "uRL"));

                            String is_exist_path = null;
                            if (count(pho_datas, "uRL") > 0) {
                                is_exist_path = pho_datas.substring(StringUtils.ordinalIndexOf(pho_datas, "uRL", count(pho_datas, "uRL")) + 4, pho_datas.length() - 2);
                            }
                            System.out.println("is_exist_path" + is_exist_path);
                            if (is_exist_path != null) {
                                String sava_path = HttpRequestUtil.downloadPicture(is_exist_path, cjroneProperties.getDownloadPath(), apiCardId.getIdCard() + ".jpg");
                                apiCardId.setPhoto(sava_path);
                            }
                        }

                        apiCardId_result = apiCardId;
                        apiCardIdService.save(apiCardId);
                    }
                }
            } else {
                if (is_alive_klist.get(0).getQfjg().indexOf("奉化") != -1) {
                    is_fh = true;
                }
                apiCardId_result = is_alive_klist.get(0);
            }
            /*if (is_fh) {
                // 验证是否死亡
                Map<String, Object> dead_result = apiService.queryHHInfo(disabilityCertificateApplication.getIdCard(), null);
                if ("00".equals(dead_result.get("code").toString()) && dead_result.get("datas") != null) {
                    List<DeadInfoEntity> dead_list = new Gson().fromJson(dead_result.get("datas").toString(), new TypeToken<List<DeadInfoEntity>>() {
                    }.getType());
                    if (dead_list.size() != 0) {
                        if (disabilityCertificateApplication.getIdCard().equals(dead_list.get(0).getDURID()) && disabilityCertificateApplication.getName().equals(dead_list.get(0).getDURNAME())) {
                            is_dead = true;
                        }
                    }
                }
                // 验证是否有残疾证
                List<DataDisabilityCertificateEntity> dataDisabilityCertificate = (List<DataDisabilityCertificateEntity>) dataDisabilityCertificateService.listByMap(mp_tmp);
                if (dataDisabilityCertificate.size() != 0) {
                    is_cjz = true;
                }
            }*/
        }else {
            is_cjz = true;
            List<ApiCardIdEntity> is_alive_klist = (List<ApiCardIdEntity>) apiCardIdService.listByMap(mp_tmp);
            if (is_alive_klist.size() >0) {
                if ("奉化市公安局".equals(is_alive_klist.get(0).getQfjg())) {
                    is_fh = true;
                }
                apiCardId_result = is_alive_klist.get(0);
            }else {
                Map<String, Object> hh_result = apiService.queryHuJiInfo(disabilityCertificateApplication.getIdCard(), null);
                if ("00".equals(hh_result.get("code").toString()) && hh_result.get("datas") != null) {
                    String data = hh_result.get("datas").toString();
                    data = data.substring(1,data.length()-1);
                    String data_new = data.substring(0,data.indexOf("registrationDate=")+27)+data.substring(data.indexOf("registrationDate=")+36,data.length());
                    System.out.println(data_new);

                    ApiHuJiEntity apiHuJiEntity = new Gson().fromJson(data_new.replaceAll(" ","").replaceAll("=,","=null,"), ApiHuJiEntity.class);
                    if (apiHuJiEntity != null) {
                        ApiCardIdEntity apiCardId = new ApiCardIdEntity();
                        System.out.println(new Gson().toJson(apiHuJiEntity));
                        if (apiHuJiEntity.getRegistrationAuthority() != null && apiHuJiEntity.getRegistrationAuthority().indexOf("奉化") != -1){
                            System.out.println("当前是奉化户籍");
                            is_fh = true;
                            apiCardId.setQfjg("奉化市公安局");
                        }else {
                            apiCardId.setQfjg(apiHuJiEntity.getRegistrationAuthority());
                        }
                        System.out.println("apiHuJiEntity: "+new Gson().toJson(apiHuJiEntity));
                        apiCardId.setName(apiHuJiEntity.getName());
                        apiCardId.setSex(apiHuJiEntity.getSex());
                        apiCardId.setNationality(apiHuJiEntity.getNation());
                        if (apiHuJiEntity.getDateOfBirth().length() == 8){
                            String aabc = apiHuJiEntity.getDateOfBirth();
                            aabc = aabc.substring(0,4)+"-"+aabc.substring(4,6)+"-"+aabc.substring(6,8);
                            apiCardId.setBirthday(aabc);
                        }else {
                            apiCardId.setBirthday(apiHuJiEntity.getDateOfBirth());
                        }
                        apiCardId.setNativePlace("浙江");
                        apiCardId.setIdCard(apiHuJiEntity.getIdcard());
                        apiCardId.setNativeAddress(apiHuJiEntity.getWhereToLocal());
                        apiCardId.setJgbh("长期");
                        System.out.println("dATABean: "+new Gson().toJson(apiHuJiEntity));
                        Map<String, Object> photo_result = apiService.queryCardIdPho(disabilityCertificateApplication.getIdCard(), apiHuJiEntity.getName(), apiHuJiEntity.getNation(), apiHuJiEntity.getDateOfBirth(), null);
                        System.out.println("photo_result: "+photo_result);
                        if (photo_result.get("datas") == null || "null".equals(photo_result.get("datas"))){
                            apiCardId.setPhoto(null);
                        }else {
                            System.out.println(photo_result.get("datas").toString());

                        /*
                        // 老代码
                        List<CardInfoPhoEntity> photo_list = new Gson().fromJson(photo_result.get("datas").toString(), new TypeToken<List<CardInfoPhoEntity>>() {
                        }.getType());
                        if (photo_list.size() != 0 && "1".equals(photo_list.get(0).getIsExist())) {
                            String sava_path = HttpRequestUtil.downloadPicture(photo_list.get(0).getURL(), cjroneProperties.getDownloadPath(), apiCardId.getIdCard() + ".jpg");
                            apiCardId.setPhoto(sava_path);
                        }*/

                            // 新代码
                            String pho_datas = photo_result.get("datas").toString();
                            System.out.println("当前电子照片张数：" + count(pho_datas, "uRL"));

                            String is_exist_path = null;
                            if (count(pho_datas, "uRL") > 0) {
                                is_exist_path = pho_datas.substring(StringUtils.ordinalIndexOf(pho_datas, "uRL", count(pho_datas, "uRL")) + 4, pho_datas.length() - 2);
                            }
                            System.out.println("is_exist_path" + is_exist_path);
                            if (is_exist_path != null) {
                                String sava_path = HttpRequestUtil.downloadPicture(is_exist_path, cjroneProperties.getDownloadPath(), apiCardId.getIdCard() + ".jpg");
                                apiCardId.setPhoto(sava_path);
                            }
                        }

                        apiCardId_result = apiCardId;
                        apiCardIdService.save(apiCardId);
                    }
                }
            }
            disabilityCertificateApplicationEntity = is_apply_list.get(0);

        }
        String birthday = disabilityCertificateApplicationEntity.getBirthday();
        if (birthday != null && birthday.indexOf("-") == -1){
            disabilityCertificateApplicationEntity.setBirthday(birthday.substring(0,4)+"-"+birthday.substring(4,6)+"-"+birthday.substring(6,8));
        }
        if (apiCardId_result.getId() == null){
            // 判断电子照片有没有
            boolean isImgUrl = true; // 默认照片存在
            String file_path = cjroneProperties.getDownloadPath()+disabilityCertificateApplication.getIdCard() + ".jpg";
            File filePath = new File(file_path);
            if(!filePath.exists()){
                isImgUrl= false;
            }
            is_exists=false;
            return R.ok().put("is_exists",is_exists).put("is_fh",is_fh).put("is_dead",is_dead).put("is_disability",is_cjz).put("is_cjz",is_cjz).put("apiCardIdEntity",apiCardId_result).put("disabilityCertificateApplicationEntity",disabilityCertificateApplicationEntity).put("isImgUrl",isImgUrl);
            //return R.ok().put("msg","通过公安身份证信息接口，暂未获取到该申请人身份证信息！").put("is_fh",is_fh).put("is_dead",is_dead).put("is_disability",is_cjz).put("is_cjz",is_cjz).put("apiCardIdEntity",apiCardId_result).put("disabilityCertificateApplicationEntity",disabilityCertificateApplicationEntity).put("isImgUrl",isImgUrl);
        }else {
            // 判断电子照片有没有
            boolean isImgUrl = true; // 默认照片存在
            String file_path = cjroneProperties.getDownloadPath()+disabilityCertificateApplication.getIdCard() + ".jpg";
            File filePath = new File(file_path);
            if(!filePath.exists()){
                isImgUrl= false;
            }
            return R.ok().put("is_exists",is_exists).put("is_fh",is_fh).put("is_dead",is_dead).put("is_disability",is_cjz).put("is_cjz",is_cjz).put("apiCardIdEntity",apiCardId_result).put("disabilityCertificateApplicationEntity",disabilityCertificateApplicationEntity).put("isImgUrl",isImgUrl);
        }
    }






}
