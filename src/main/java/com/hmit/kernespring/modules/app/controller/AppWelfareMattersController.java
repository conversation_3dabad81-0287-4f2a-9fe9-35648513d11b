package com.hmit.kernespring.modules.app.controller;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.hmit.kernespring.common.utils.DateUtils;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.app.annotation.Login;
import com.hmit.kernespring.modules.app.annotation.LoginUser;
import com.hmit.kernespring.modules.app.entity.UserEntity;
import com.hmit.kernespring.modules.app.service.APIService;
import com.hmit.kernespring.modules.app.service.AppWelfareMattersService;
import com.hmit.kernespring.modules.cjrone.entity.*;
import com.hmit.kernespring.modules.cjrone.service.*;
import com.hmit.kernespring.modules.cjrone_bl.entity.*;
import com.hmit.kernespring.modules.cjrone_bl.service.*;
import com.hmit.kernespring.modules.data_management.entity.ApiCardIdEntity;
import com.hmit.kernespring.modules.data_management.entity.ApiFdDbrEntity;
import com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity;
import com.hmit.kernespring.modules.data_management.service.ApiCardIdService;
import com.hmit.kernespring.modules.data_management.service.DataDisabilityCertificateService;
import com.hmit.kernespring.modules.matter_application.entity.CjroneWelfareMatterApplicationEntity;
import com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity;
import com.hmit.kernespring.modules.matter_application.service.CjroneWelfareMatterApplicationService;
import com.hmit.kernespring.modules.matter_application.service.DisabilityCertificateApplicationService;
import com.hmit.kernespring.modules.sys.controller.AbstractController;
import com.hmit.kernespring.modules.sys.entity.SysDictEntity;
import com.hmit.kernespring.modules.sys.service.SysDictService;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.*;
import java.nio.file.Files;
import java.util.*;

@RestController
@RequestMapping("/app")
@Api(value="AppWelfareMattersController",description = "APP惠残事项申请相关接口123")
public class AppWelfareMattersController extends AbstractController {

    @Autowired
    private CjroneTwoSubsidyStandardsService cjroneTwoSubsidyStandardsService;
    @Autowired
    private DataDisabilityCertificateService dataDisabilityCertificateService;
    @Autowired
    private CjroneblLivingSubsidyService cjroneblLivingSubsidyService;
    @Autowired
    private CjroneblDisabilityCertificateRedemptionService cjroneblDisabilityCertificateRedemptionService;
    @Autowired
    private CjroneblNursingSubsidyService cjroneblNursingSubsidyService;
    @Autowired
    private CjroneblLivingAllowanceService cjroneblLivingAllowanceService;
    @Autowired
    private CjroneblZgjbyanglaoService cjroneblZgjbyanglaoService;
    @Autowired
    private CjroneblZgjbyiliaoService cjroneblZgjbyiliaoService;
    @Autowired
    private CjroneblCxjmyanglaoService cjroneblCxjmyanglaoService;
    @Autowired
    private CjroneblCxjmyiliaoService cjroneblCxjmyiliaoService;
    @Autowired
    private CjroneblTemporaryAssistanceService cjroneblTemporaryAssistanceService;
    @Autowired
    private CjroneblRehabilitationSubsidyService cjroneblRehabilitationSubsidyService;

    @Autowired
    private CjroneWelfareMatterApplicationService cjroneWelfareMatterApplicationService;

    @Autowired
    private DataRehabilitationSubsidyCategoryService dataRehabilitationSubsidyCategoryService;

    @Autowired
    private AppWelfareMattersService appWelfareMattersService;

    @Autowired
    private CjroneSignatureService cjroneSignatureService;

    @Autowired
    private CjroneProperties cjroneProperties;

    @Autowired
    private CjroneNursingSubsidyService cjroneNursingSubsidyService;

    @Autowired
    private CjroneLivingAllowanceService cjroneLivingAllowanceService;

    @Autowired
    private CjroneRehabilitationSubsidyService cjroneRehabilitationSubsidService;

    @Autowired
    private DisabilityCertificateApplicationService  disabilityCertificateApplicationService;
    @Autowired
    private ApiCardIdService apiCardIdService;
    @Autowired
    private CjroneblBusinessGrantService cjroneblBusinessGrantService;
    @Autowired
    private CjroneblCollegeeduService cjroneblCollegeeduService;
    @Autowired
    private CjroneblChildeduService cjroneblChildeduService;


    @Autowired
    private SysDictService sysDictService;
    @Autowired
    private APIService apiService;

   /**
     *  验证是否符合惠残政策
     */
    @GetMapping("/infoNeD")
    @ApiOperation("验证是否符合惠残政策")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "token", value = "登录token", required = false, paramType = "header")
    })
    //@RequiresPermissions("matter_application:disabilitycertificateapplication:info")
    public R infoNeD(@LoginUser UserEntity user){
        String iDcard = user.getPassword();
        //根据id_card 获得残疾人信息
        DataDisabilityCertificateEntity dataDisabilityCertificateEntity=dataDisabilityCertificateService.getByIDCard(iDcard);
        //获得残疾证信息，用于判断护理补贴
        //1、去除最后一个字符
        String[] disinfo=null;
        if(dataDisabilityCertificateEntity.getDisabilityInfo()!=null){
            String disabilityinfo = dataDisabilityCertificateEntity.getDisabilityInfo().substring(0,dataDisabilityCertificateEntity.getDisabilityInfo().length()-1);
            disinfo=disabilityinfo.split(";");
        }
        //返回给前端的残疾证信息
        List<Map<String,Object>> dcdisable_list=new ArrayList<>();

        //判断是否已经申请过惠残 及 一些基本条件
        Map<String,Object> nmap = new HashMap<>();
        nmap.put("idCard",iDcard);
        Map<String,Object> result_map = cjroneWelfareMatterApplicationService.queryMattersByMap(nmap);
        // 判断是否法定代表人
        Map<String, Object> doFdDbInfo = apiService.queryFDDBRInfo(iDcard, null);
        boolean is_fdDbr = false;
        if ("00".equals(doFdDbInfo.get("code").toString())) {
            System.out.println("doFdDbInfo: "+doFdDbInfo);
            if (doFdDbInfo.get("datas") != null) {
                List<ApiFdDbrEntity> list = new Gson().fromJson(doFdDbInfo.get("datas").toString(), new TypeToken<List<ApiFdDbrEntity>>() {
                }.getType());
                if (list.size() != 0) {
                    System.out.println("当前存在" + list.size() + "条法定代表人信息 第一条信息如下---> " + new Gson().toJson(list.get(0)));
                    is_fdDbr = true;
                }
            }
        }else {
            System.out.println("获取数据异常");
        }
        // 是否个体工商户
        result_map.put("is_fdDbr",is_fdDbr);

        // 根据上面已获得的数据，判断残疾人是否符合各种条件


        // <editor-fold> 生活补助金的判断条件 v1.0
        boolean isShbzjOption = true;  //生活补助金条件
        String shbzjOptions = null;
        // </editor-fold>

        // <editor-fold> 生活补贴的判断条件 v1.0
        boolean isShbtOption = true;
        String shbtOptions = null;

        if ("1".equals(result_map.get("isFx").toString())){
            isShbtOption = false;
            shbtOptions = "当前属于服刑人员！";
        }else{
            if ("1".equals(result_map.get("isTk").toString())){
                isShbtOption = false;
                shbtOptions = "当前属于特困人员！";
            }else{
                if ("1".equals(result_map.get("isGongS").toString())){
                    shbtOptions = "当前属于享受工伤保险生活护理费人员！";
                    isShbtOption = false;
                }else{
                    if ("1".equals(result_map.get("isKjEt").toString())){
                        shbtOptions = "当前属于享受困境儿童基本生活补贴政策！";
                        isShbtOption = false;
                    }else{
                        if ("无".equals(result_map.get("familyEcho").toString())){
                            shbtOptions = "非低保、低边";
                            isShbtOption = false;
                        }
                    }
                }
            }
        }
        if (shbtOptions == null && !"0".equals(result_map.get("isShbt").toString())){
            isShbtOption = false;
            shbtOptions = "当前人员已经申请过生活补贴!";
        }

        result_map.put("isShbtOption",isShbtOption);
        result_map.put("shbtOptions",shbtOptions);
        // </editor-fold>

        // <editor-fold> 护理补贴的判断条件 v1.0
        boolean isHlbtOption = true;
        String hlbtOptions = null;
        if ("1".equals(result_map.get("isFx").toString())){
            isHlbtOption = false;
            hlbtOptions = "当前属于服刑人员！";
        }else{
            if ("1".equals(result_map.get("isTk").toString())){
                isHlbtOption = false;
                hlbtOptions = "当前属于特困人员！";
            }else{
                if ("1".equals(result_map.get("isGongS").toString())){
                    hlbtOptions = "当前属于享受工伤保险生活护理费人员！";
                    isHlbtOption = false;
                }else{
                    String birthday = iDcard.substring(6,10)+"-"+iDcard.substring(10,12)+"-"+iDcard.substring(12,14)+" 00:00:00";
                    Date stringToDate = DateUtils.stringToDate(birthday,DateUtils.DATE_TIME_PATTERN);
                    if (DateUtils.checkAdultSix(stringToDate) && !"0".equals(result_map.get("isYangL").toString())){
                        hlbtOptions = "当前属于60周岁以上且已享受养老服务补贴政策！";
                        isHlbtOption = false;
                    }else {
                        //判断多重残疾下的情况
                        boolean isHlbtOptiondc=true;
                        if(disinfo!=null){
                            Map<String,Object> params=new HashMap<>();
                            for (String itemhos: disinfo) {
                                params.put("disableId",dataDisabilityCertificateEntity.getDisableId());
                                params.put("disabilityCategoryName",itemhos.substring(0,2));
                                params.put("disabilityDegreeName",itemhos.substring(2,4));
                                dcdisable_list.add(params);
                                if (!"智力".equals(itemhos.substring(0,2)) && !"精神".equals(itemhos.substring(0,2))){
                                    if ("三级".equals(itemhos.substring(2,4)) || "四级".equals(itemhos.substring(2,4))){
                                        hlbtOptions = "残疾等级属于三、四级，且残疾类别不属于智力、精神，不能申请！";
                                        isHlbtOptiondc = false;
                                    }
                                    else{
                                        isHlbtOptiondc = true;
                                        break;
                                    }
                                }
                                else{
                                    isHlbtOptiondc = true;
                                    break;
                                }

                            }
                        }else{
                            isHlbtOptiondc=false;
                        }

                        isHlbtOption=(isHlbtOptiondc&&isHlbtOption);
                    }
                }
            }
        }
        if (hlbtOptions == null && !"0".equals(result_map.get("isHlbt").toString())){
            isHlbtOption = false;
            hlbtOptions = "当前人员已经申请过护理补贴!";
        }

        result_map.put("isHlbtOption",isHlbtOption);
        result_map.put("hlbtOptions",hlbtOptions);
        // </editor-fold>

        // <editor-fold>  职工基本养老保险补助判断条件 v1.0
        boolean isZgjbylOptions = true;
        String zgjbylOptions = null ;
        if(!is_fdDbr){
            isZgjbylOptions = false;
            zgjbylOptions = "非个体工商户";
        }else{
            // 判断基本养老保险
        }

        // 判断是否已经申请过

        result_map.put("isZgjbylOptions",isZgjbylOptions);
        result_map.put("zgjbylOptions",zgjbylOptions);
        // </editor-fold>

        // <editor-fold> 职工基本医疗保险补助判断条件 v1.0
        boolean isZgjbylbxOptions = true;
        String zgjbylbxOptions = null;
        if(!is_fdDbr){
            isZgjbylOptions = false;
            zgjbylOptions = "非个体工商户";
        }else{
            // 判断基本医疗保险

        }

        // 判断是否已经申请

        result_map.put("isZgjbylbxOptions",isZgjbylbxOptions);
        result_map.put("zgjbylbxOptions",zgjbylbxOptions);

        // </editor-fold>

        // <editor-fold> 城乡居民养老保险补助判断条件 v1.0
        boolean isCxjmylOptions = true;
        String cxjmylOptions = null;

        // 判断城乡居民养老保险补助

        //判断是否已经申请

        result_map.put("isCxjmylOptions",isCxjmylOptions);
        result_map.put("cxjmylOptions",cxjmylOptions);

        // </editor-fold>

        // <editor-fold> 城乡基本医疗保险补助 v1.0
        boolean isCxjbylbxOptions = true;
        String cxjbylbxOptions = null;

        //判断残疾等级为34级

        // 判断参加了城乡基本医疗保险

        //判断是否已经申请

        result_map.put("isCxjbylbxOptions",isCxjbylbxOptions);
        result_map.put("cxjbylbxOptions",cxjbylbxOptions);

        // </editor-fold>

        // <editor-fold> 高中教育补助

        boolean isChildeduOptions = true;
        String childeduOptions = null;

        //判断本人残疾或父母残疾

        // 判断是否已经申请

        result_map.put("isChildeduOptions",isChildeduOptions);
        result_map.put("childeduOptions",childeduOptions);

        //</editor-fold>

        // <editor-fold> 残疾大学生 学费，住宿费补助

        boolean isCollegeeduOptions = true;
        String collegeeduOptions = null;

        //判断是否持证（上个接口已判断）
        result_map.put("isCollegeeduOptions",isCollegeeduOptions);
        result_map.put("collegeeduOptions",collegeeduOptions);

        // </editor-fold>

        // <editor-fold> 残疾人康复补助
        boolean isKfbzOptions = true;
        String kfbzOptions = null;

        //无判断条件
        result_map.put("isKfbzOptions",isKfbzOptions);
        result_map.put("kfbzOptions",kfbzOptions);
        //</editor-fold>

        // <editor-fold> 残疾人创业补助

        //判断法定年龄段
        boolean isCybzOptions = true;
        String cybzOptions = null;
        String birthday = iDcard.substring(6,10)+"-"+iDcard.substring(10,12)+"-"+iDcard.substring(12,14)+" 00:00:00";
        Date stringToDate = DateUtils.stringToDate(birthday,DateUtils.DATE_TIME_PATTERN);
        if (DateUtils.checkAdultDYSJ(stringToDate,16) && DateUtils.checkAdultXYSix(stringToDate)) {
        }else{
            cybzOptions = "当前人员年龄不属于16周岁-60周岁之间！";
            isCybzOptions = false;
        }

        //判断是否法定代表人
        result_map.put("isCybzOptions",isCybzOptions);
        result_map.put("cybzOptions",cybzOptions);

        // </editor-fold>

        //<editor-fold> 精神残疾人住院补助
        boolean isZybzOptions = true;
        String zybzOptions = null;

        // 判断残疾类别是否为精神

        result_map.put("isZybzOptions",isZybzOptions);
        result_map.put("zybzOptions",zybzOptions);

        //</editor-fold>

        //<editor-fold> 残疾人医疗救助
        boolean isYljzOptions = true;
        String yljzOptions = null;

        //判断是否持证（上个接口已经判断）

        result_map.put("isYljzOptions",isYljzOptions);
        result_map.put("isYljzOptions",isYljzOptions);
        //</editor-fold>

        //<editor-fold> 残疾人临时救助 v1.0
        boolean isLsjzOptions = true;
        String lsjzOptions = null;

        result_map.put("isLsjzOptions",isLsjzOptions);
        result_map.put("lsjzOptions",lsjzOptions);

        //</editor-fold>

        //<editor-fold> 残疾证到期换领 v1.0
        boolean isCjzhlOptions = true;
        String cjzhlOptions = null;

        result_map.put("isCjzhlOptions",isCjzhlOptions);
        result_map.put("cjzhlOptions",cjzhlOptions);


        //</editor-fold>


        //联系人固话和联系人移动电话取其一
        if("".equals(dataDisabilityCertificateEntity.getGuardianMobile())){
            dataDisabilityCertificateEntity.setGuardianMobile(dataDisabilityCertificateEntity.getGuardianTelephone());
        }

        return R.ok().put("disabilityCertificateApplication", dataDisabilityCertificateEntity).put("result_map",result_map).put("dcList",dcdisable_list);


    }


    /**
     * 保存惠残事项申请接口
     */
    @Login
    @PostMapping("/saveWelfareList")
    @ApiOperation("保存惠残事项申请接口")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "token", value = "登录token", required = false, paramType = "header")
    })
    public R saveWelfareList(@LoginUser UserEntity user,@RequestBody CjroneWelfareMatterApplicationEntity cjroneWelfareMatter){
        Map<String, Object> mp_tmp = new HashMap<>();
        mp_tmp.put("id_card", user.getPassword());
        CjroneWelfareMatterApplicationEntity entityCJZHL = cjroneWelfareMatter;
        DataDisabilityCertificateEntity dataDisabilityCertificateEntity=dataDisabilityCertificateService.getByIDCard(user.getPassword());
        //保存新增人
        entityCJZHL.setCreateId(user.getUserId());
        entityCJZHL.setCreateName(user.getUsername());
        //保存创建时间
        entityCJZHL.setCreateTime(DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN));
        entityCJZHL.setApplicationTime(DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN));
        // 保存申请类型为移动端
        entityCJZHL.setApplyType(0);
        // 保存申请状态  1 申请人待手签
        entityCJZHL.setStatus("1");
        // 根据身份证ID 去api_card表查询信息
        ApiCardIdEntity apiCardIdEntity = apiCardIdService.queryByIdnum(user.getPassword());

        if(apiCardIdEntity.getNativeCun() != null  &&  !"".equals(apiCardIdEntity.getNativeCun())){
            entityCJZHL.setNativeCun(apiCardIdEntity.getNativeCun());
            entityCJZHL.setNativeZhen(apiCardIdEntity.getNativeZhen());
        }else if(apiCardIdEntity.getNativeZhen() != null  && !"".equals(apiCardIdEntity.getNativeZhen())){
            entityCJZHL.setNativeZhen(apiCardIdEntity.getNativeZhen());
        }

        entityCJZHL.setName(user.getUsername());
        entityCJZHL.setDisableId(dataDisabilityCertificateEntity.getDisableId());
        entityCJZHL.setIdCard(user.getPassword());
        entityCJZHL.setDisabilityCategory(dataDisabilityCertificateEntity.getDisabilityCategory());
        entityCJZHL.setDisabilityDegree(dataDisabilityCertificateEntity.getDisabilityDegree());

        cjroneWelfareMatterApplicationService.save(entityCJZHL);
        return R.ok();
    }



    /**
     * 我的惠残事项申请列表
     */
    @Login
    @GetMapping("/getWelfareList")
    @ApiOperation("我的惠残事项申请列表")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "token", value = "登录token", required = false, paramType = "header")
    })
    public R getWelfareList(@LoginUser UserEntity user){
        Map<String, Object> params=new HashMap<>();
        Map<String, Object> key=new HashMap<>();
        key.put("idCard",user.getPassword());
        params.put("key",key);
        PageUtils page = cjroneWelfareMatterApplicationService.queryPage(params);

        return R.ok().put("list", page.getList());
    }


    /**
     * 一次性生成多个pdf 并填充内容 用于申请人手签
     */
    @Login
    @GetMapping("/printThreePDF/{idCard}")
    @ApiOperation("一次性生成多个电子pdf")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "token", value = "登录token", required = false, paramType = "header")
    })
    public R printThreePDF(@PathVariable("idCard") String idCard) throws UnsupportedEncodingException {
        Map<String, Object> params = new HashMap<>();
        params.put("id_card",idCard);
        params.put("status",'1');  // 申请人待手签的状态
        // 获得残疾证信息
        DataDisabilityCertificateEntity dd=dataDisabilityCertificateService.getByIDCard(idCard);
        List<CjroneWelfareMatterApplicationEntity> matterApplicationEntities = (List<CjroneWelfareMatterApplicationEntity>) cjroneWelfareMatterApplicationService.listByMap(params);
        List<CjroneWelfareMatterApplicationEntity> result_list = new ArrayList<>();
        matterApplicationEntities.forEach(matterApplicationEntity -> {
            if ("生活补贴".equals(matterApplicationEntity.getMatterName())) {

                //根据编号获得生活补贴信息
                CjroneblLivingSubsidyEntity cjroneblLivingSubsidyEntity = cjroneblLivingSubsidyService.getById(matterApplicationEntity.getMatterId());

                Calendar now = Calendar.getInstance();

                // 原pdf模板路径
                String templatePath = cjroneProperties.getTempletePath()+"生活补助模板.pdf";
                // 生成的新文件路径
                String newPDFPath = cjroneProperties.getSignaturePath()+"living_allowance_"+cjroneblLivingSubsidyEntity.getIdCard()+".pdf";

                // 获得待生成的实体文件
                Map<String, String> map =new HashMap<String, String>();
                map.put("name", cjroneblLivingSubsidyEntity.getName()==null?"":cjroneblLivingSubsidyEntity.getName());
                if(cjroneblLivingSubsidyEntity.getSex()!=null){
                    if("0".equals(cjroneblLivingSubsidyEntity.getSex())){
                        map.put("sex","女");
                    }else{
                        map.put("sex","男");
                    }
                }else{
                    map.put("sex","");
                }
                map.put("birthday",cjroneblLivingSubsidyEntity.getBirthday()==null?"":cjroneblLivingSubsidyEntity.getBirthday());
                map.put("natinality",dd.getNationality()==null?"":dd.getNationality());
                map.put("nativeZhenName",dd.getJiedao()==null?"":dd.getJiedao());
                map.put("nativeCunName",dd.getShequ()==null?"":dd.getShequ());
                map.put("idCard",cjroneblLivingSubsidyEntity.getIdCard()==null?"":cjroneblLivingSubsidyEntity.getIdCard());
                map.put("disabilityType",cjroneblLivingSubsidyEntity.getDisabilityType()==null?"":cjroneblLivingSubsidyEntity.getDisabilityType());
                map.put("disabilityDegree",cjroneblLivingSubsidyEntity.getDisabilityDegree()==null?"":cjroneblLivingSubsidyEntity.getDisabilityDegree());
                map.put("disableId",cjroneblLivingSubsidyEntity.getDisableId()==null?"":cjroneblLivingSubsidyEntity.getDisableId());
                map.put("nativeAddress",cjroneblLivingSubsidyEntity.getNativeAddress()==null?"":cjroneblLivingSubsidyEntity.getNativeAddress());
                map.put("liveAddress",cjroneblLivingSubsidyEntity.getLiveAddress()==null?"":cjroneblLivingSubsidyEntity.getLiveAddress());
                map.put("telephone",cjroneblLivingSubsidyEntity.getTelephone()==null?"":cjroneblLivingSubsidyEntity.getTelephone());
                map.put("bankName",cjroneblLivingSubsidyEntity.getBankName()==null?"":cjroneblLivingSubsidyEntity.getBankName());
                map.put("bankAccount",cjroneblLivingSubsidyEntity.getBankAccount()==null?"":cjroneblLivingSubsidyEntity.getBankAccount());
                map.put("guardianName",cjroneblLivingSubsidyEntity.getGuardianName()==null?"":cjroneblLivingSubsidyEntity.getGuardianName());
                map.put("guardianPhone",cjroneblLivingSubsidyEntity.getGuardianPhone()==null?"":cjroneblLivingSubsidyEntity.getGuardianPhone());
                if("1".equals(cjroneblLivingSubsidyEntity.getFamilyEconomy())){
                    map.put("fe1","√");
                }else if("2".equals(cjroneblLivingSubsidyEntity.getFamilyEconomy())){
                    map.put("fe2","√");
                }else if("3".equals(cjroneblLivingSubsidyEntity.getFamilyEconomy())){
                    map.put("fe3","√");
                }else if("4".equals(cjroneblLivingSubsidyEntity.getFamilyEconomy())){
                    map.put("fe4","√");
                }
                map.put("income",cjroneblLivingSubsidyEntity.getIncome()==null?"":cjroneblLivingSubsidyEntity.getIncome());
                map.put("applyDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
                map.put("zhen1","√");
                map.put("mz2","√");
                //开始关联补助金额
                Map<String, Object> mapparams =new HashMap<String, Object>();
                mapparams.put("type","生活补贴");
                List<CjroneTwoSubsidyStandardsEntity> cjroneTwoSubsidyStandardsEntityList=cjroneTwoSubsidyStandardsService.queryByMap(mapparams);
                if(cjroneTwoSubsidyStandardsEntityList!=null&&cjroneTwoSubsidyStandardsEntityList.size()>0){
                    map.put("subsidymoney",cjroneTwoSubsidyStandardsEntityList.get(0).getMoney().toString());
                }
                else{
                    map.put("subsidymoney","无数据");
                }
                map.put("qcl2","√");


                FileOutputStream out;
                int num = 2;//页数
                ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
                try {
                    out = new FileOutputStream(newPDFPath);// 输出流
                    Document doc = new Document();   //新建一个文档
                    PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                    doc.open();

                    for (int i = 0; i < num; i++) {
                        bos[i] = new ByteArrayOutputStream();
                        System.out.println("templatePath--->"+templatePath);
                        PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                        PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                        AcroFields form = stamper.getAcroFields(); //获取文本域
                        // BaseFont
                        // 1、使用iTextAsian.jar中的字体
                        // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                        // 2、使用Windows系统字体(TrueType)
                        // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                        // 3、使用资源字体(ClassPath)
                        // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                        BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                        java.util.Iterator<String> it = form.getFields().keySet().iterator();
                        while (it.hasNext()) {
                            String name = it.next();
                            System.out.println(name + ":"+map.get(name));
                            form.setFieldProperty(name,"textfont",font,null);
                            // form.addSubstitutionFont(font);
                            form.setField(name, map.get(name));
                        }

                        stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                        stamper.close();

                    }
                    PdfImportedPage page = null;
                    for (int i = 0; i < num; i++) {
                        page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                        copy.addPage(page);
                    }
                    doc.close();
                    out.close();
                } catch (IOException e) {
                    System.out.println("导出异常");
                } catch (DocumentException e) {
                    System.out.println("文档异常");
                }

                CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
                cjroneSignature.setUrl("/living_allowance_" + cjroneblLivingSubsidyEntity.getIdCard() + ".pdf");
                cjroneSignature.setFileActUrl(newPDFPath);
                cjroneSignature.setCreateDate(new Date());
                cjroneSignature.setCreateId(getUserId());
                cjroneSignature.setType("惠残事项生活补贴");
                cjroneSignature.setTypeId(cjroneblLivingSubsidyEntity.getId());
                cjroneSignature.setFileName("living_allowance_" + cjroneblLivingSubsidyEntity.getIdCard() + ".pdf");
                cjroneSignature.setAccountId(getUserId().toString());
                cjroneSignature.setAccountName(getUser().getUsername());
                cjroneSignature.setStatus("1");

                cjroneSignatureService.save(cjroneSignature);
                matterApplicationEntity.setPhoto(cjroneSignature.getUrl());
                result_list.add(matterApplicationEntity);
            }
            else if ("护理补贴".equals(matterApplicationEntity.getMatterName())){

                //根据编号获得详细信息
                CjroneblNursingSubsidyEntity cjroneblNursingSubsidyEntity = cjroneblNursingSubsidyService.getById(matterApplicationEntity.getMatterId());

                // 原pdf模板路径
                String templatePath = cjroneProperties.getTempletePath()+"护理补助模板.pdf";
                // 生成的新文件路径
                String newPDFPath = cjroneProperties.getSignaturePath()+"nursing_subsidy_"+cjroneblNursingSubsidyEntity.getIdCard()+".pdf";

                // 获得待生成的实体文件
                Map<String, String> map =new HashMap<String, String>();
                Map<String, Object> mapparams =new HashMap<String, Object>();

                //获取年月日数据
                Calendar now = Calendar.getInstance();
                String nums[] = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};

                // 获得待生成的实体文件
                map.put("name", cjroneblNursingSubsidyEntity.getName());
                if (cjroneblNursingSubsidyEntity.getSex() == 1)
                    map.put("sex", "男");
                else
                    map.put("sex", "女");
                map.put("birthday", cjroneblNursingSubsidyEntity.getBirthday()==null?"":cjroneblNursingSubsidyEntity.getBirthday());
                map.put("nationality", cjroneblNursingSubsidyEntity.getNationality()==null?"":cjroneblNursingSubsidyEntity.getNationality());
                map.put("idCard", cjroneblNursingSubsidyEntity.getIdCard()==null?"":cjroneblNursingSubsidyEntity.getIdCard());
                map.put("disabilityCategory", cjroneblNursingSubsidyEntity.getDisabilityCategory()==null?"":cjroneblNursingSubsidyEntity.getDisabilityCategory());
                map.put("disabilityDegree", cjroneblNursingSubsidyEntity.getDisabilityDegree()==null?"":cjroneblNursingSubsidyEntity.getDisabilityDegree());
                map.put("disableId", cjroneblNursingSubsidyEntity.getDisableId()==null?"":cjroneblNursingSubsidyEntity.getDisableId());
                map.put("nativeAddress", cjroneblNursingSubsidyEntity.getNativeAddress()==null?"":cjroneblNursingSubsidyEntity.getNativeAddress());
                map.put("liveAddress", cjroneblNursingSubsidyEntity.getLiveAddress()==null?"":cjroneblNursingSubsidyEntity.getLiveAddress());
                map.put("mobilePhone", cjroneblNursingSubsidyEntity.getMobilePhone()==null?"":cjroneblNursingSubsidyEntity.getMobilePhone());
                map.put("bankName", cjroneblNursingSubsidyEntity.getBankName()==null?"":cjroneblNursingSubsidyEntity.getBankName());
                map.put("bankAccount", cjroneblNursingSubsidyEntity.getBankAccount()==null?"":cjroneblNursingSubsidyEntity.getBankAccount());
                map.put("guardianName", cjroneblNursingSubsidyEntity.getGuardianName()==null?"":cjroneblNursingSubsidyEntity.getGuardianName());
                map.put("guardianPhone", cjroneblNursingSubsidyEntity.getGuardianPhone()==null?"":cjroneblNursingSubsidyEntity.getGuardianPhone());
                map.put("sixMonth", cjroneblNursingSubsidyEntity.getSixMonth()==null?"":cjroneblNursingSubsidyEntity.getSixMonth());
                map.put("careType",cjroneblNursingSubsidyEntity.getCareType()==null?"":cjroneblNursingSubsidyEntity.getCareType());
                map.put("lifeStatus",cjroneblNursingSubsidyEntity.getLifeStatus()==null?"":cjroneblNursingSubsidyEntity.getLifeStatus());
                map.put("careMonths",cjroneblNursingSubsidyEntity.getCareMonths()==null?"":cjroneblNursingSubsidyEntity.getCareMonths().toString());
                map.put("applicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
                map.put("actuallySubsidy",cjroneblNursingSubsidyEntity.getActuallySubsidy());

                FileOutputStream out;
                int num = 1;//页数
                ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
                try {
                    out = new FileOutputStream(newPDFPath);// 输出流
                    Document doc = new Document();   //新建一个文档
                    PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                    doc.open();

                    for (int i = 0; i < num; i++) {
                        bos[i] = new ByteArrayOutputStream();
                        PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                        PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                        AcroFields form = stamper.getAcroFields(); //获取文本域
                        // BaseFont
                        // 1、使用iTextAsian.jar中的字体
                        // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                        // 2、使用Windows系统字体(TrueType)
                        // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                        // 3、使用资源字体(ClassPath)
                        // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                        BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                        java.util.Iterator<String> it = form.getFields().keySet().iterator();
                        while (it.hasNext()) {
                            String name = it.next();
                            System.out.println(name + ":"+map.get(name));
                            form.setFieldProperty(name,"textfont",font,null);
                            // form.addSubstitutionFont(font);
                            form.setField(name, map.get(name));
                        }

                        stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                        stamper.close();

                    }
                    PdfImportedPage page = null;
                    for (int i = 0; i < num; i++) {
                        page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                        copy.addPage(page);
                    }
                    doc.close();
                    out.close();
                } catch (IOException e) {
                    System.out.println("导出异常");
                } catch (DocumentException e) {
                    System.out.println("文档异常");
                }


                CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
                cjroneSignature.setUrl("/nursing_subsidy_" + cjroneblNursingSubsidyEntity.getIdCard() + ".pdf");
                cjroneSignature.setFileActUrl(newPDFPath);
                cjroneSignature.setCreateDate(new Date());
                cjroneSignature.setCreateId(getUserId());
                cjroneSignature.setType("惠残事项护理补贴");
                cjroneSignature.setTypeId(cjroneblNursingSubsidyEntity.getId());
                cjroneSignature.setFileName("nursing_subsidy_" + cjroneblNursingSubsidyEntity.getIdCard() + ".pdf");
                cjroneSignature.setAccountId(getUserId().toString());
                cjroneSignature.setAccountName(getUser().getUsername());
                cjroneSignature.setStatus("1");

                cjroneSignatureService.save(cjroneSignature);
                matterApplicationEntity.setPhoto(cjroneSignature.getUrl());
                result_list.add(matterApplicationEntity);
            }
            else if ("生活补助金".equals(matterApplicationEntity.getMatterName())){

                //根据编号获得生活补助金信息
                CjroneblLivingAllowanceEntity cjroneblLivingAllowanceEntity = cjroneblLivingAllowanceService.getById(matterApplicationEntity.getMatterId());

                Calendar now = Calendar.getInstance();

                // 原pdf模板路径
                String templatePath = cjroneProperties.getTempletePath()+"生活补助金模板.pdf";
                // 生成的新文件路径
                String newPDFPath = cjroneProperties.getSignaturePath()+"living_allowance_subsidy_"+cjroneblLivingAllowanceEntity.getIdCard()+".pdf";

                // 获得待生成的实体文件
                Map<String, String> map =new HashMap<String, String>();
                map.put("name", cjroneblLivingAllowanceEntity.getName()==null?"":cjroneblLivingAllowanceEntity.getName());
                if(cjroneblLivingAllowanceEntity.getSex()!=null){
                    if("0".equals(cjroneblLivingAllowanceEntity.getSex())){
                        map.put("sex","女");
                    }else{
                        map.put("sex","男");
                    }
                }else{
                    map.put("sex","");
                }
                map.put("birthday",cjroneblLivingAllowanceEntity.getBirthday()==null?"":cjroneblLivingAllowanceEntity.getBirthday());
                map.put("age",cjroneblLivingAllowanceEntity.getAge()==null?"":cjroneblLivingAllowanceEntity.getAge().toString());
                map.put("disabilityType",cjroneblLivingAllowanceEntity.getDisabilityType()==null?"":cjroneblLivingAllowanceEntity.getDisabilityType());
                map.put("disabilityDegree",cjroneblLivingAllowanceEntity.getDisabilityDegree()==null?"":cjroneblLivingAllowanceEntity.getDisabilityDegree());
                map.put("idCard",cjroneblLivingAllowanceEntity.getIdCard()==null?"":cjroneblLivingAllowanceEntity.getIdCard());
                map.put("disableId",cjroneblLivingAllowanceEntity.getDisableId()==null?"":cjroneblLivingAllowanceEntity.getDisableId());
                map.put("liveAddress",cjroneblLivingAllowanceEntity.getLiveAddress()==null?"":cjroneblLivingAllowanceEntity.getLiveAddress());
                map.put("telephone",cjroneblLivingAllowanceEntity.getTelephone()==null?"":cjroneblLivingAllowanceEntity.getTelephone());
                map.put("familyEconomy",cjroneblLivingAllowanceEntity.getFamilyEconomy()==null?"":cjroneblLivingAllowanceEntity.getFamilyEconomy());
                map.put("pension",cjroneblLivingAllowanceEntity.getPension()==null?"":cjroneblLivingAllowanceEntity.getPension());
                map.put("applyType",cjroneblLivingAllowanceEntity.getApplyType()==null?"":cjroneblLivingAllowanceEntity.getApplyType());
                map.put("subsidyMoney",cjroneblLivingAllowanceEntity.getSubsidyMoney()==null?"":cjroneblLivingAllowanceEntity.getSubsidyMoney());
                map.put("applyDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");


                FileOutputStream out;
                int num = 1;//页数
                ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
                try {
                    out = new FileOutputStream(newPDFPath);// 输出流
                    Document doc = new Document();   //新建一个文档
                    PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                    doc.open();

                    for (int i = 0; i < num; i++) {
                        bos[i] = new ByteArrayOutputStream();
                        System.out.println("templatePath--->"+templatePath);
                        PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                        PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                        AcroFields form = stamper.getAcroFields(); //获取文本域
                        // BaseFont
                        // 1、使用iTextAsian.jar中的字体
                        // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                        // 2、使用Windows系统字体(TrueType)
                        // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                        // 3、使用资源字体(ClassPath)
                        // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                        BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                        java.util.Iterator<String> it = form.getFields().keySet().iterator();
                        while (it.hasNext()) {
                            String name = it.next();
                            System.out.println(name + ":"+map.get(name));
                            form.setFieldProperty(name,"textfont",font,null);
                            // form.addSubstitutionFont(font);
                            form.setField(name, map.get(name));
                        }

                        stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                        stamper.close();

                    }
                    PdfImportedPage page = null;
                    for (int i = 0; i < num; i++) {
                        page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                        copy.addPage(page);
                    }
                    doc.close();
                    out.close();
                } catch (IOException e) {
                    System.out.println("导出异常");
                } catch (DocumentException e) {
                    System.out.println("文档异常");
                }

                CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
                cjroneSignature.setUrl("/living_allowance_subsidy_" + cjroneblLivingAllowanceEntity.getIdCard() + ".pdf");
                cjroneSignature.setFileActUrl(newPDFPath);
                cjroneSignature.setCreateDate(new Date());
                cjroneSignature.setCreateId(getUserId());
                cjroneSignature.setType("惠残事项生活补助金");
                cjroneSignature.setTypeId(cjroneblLivingAllowanceEntity.getId());
                cjroneSignature.setFileName("living_allowance_subsidy_" + cjroneblLivingAllowanceEntity.getIdCard() + ".pdf");
                cjroneSignature.setAccountId(getUserId().toString());
                cjroneSignature.setAccountName(getUser().getUsername());
                cjroneSignature.setStatus("1");

                cjroneSignatureService.save(cjroneSignature);
                matterApplicationEntity.setPhoto(cjroneSignature.getUrl());
                result_list.add(matterApplicationEntity);
            }
            else if ("职工基本医疗保险补助".equals(matterApplicationEntity.getMatterName())){

                //根据编号获得职工基本医疗保险补助
                CjroneblZgjbyiliaoEntity cjroneblZgjbyiliaoEntity =cjroneblZgjbyiliaoService.getById(matterApplicationEntity.getMatterId());

                Calendar now = Calendar.getInstance();

                // 原pdf模板路径
                String templatePath = cjroneProperties.getTempletePath()+"职工基本医疗保险补助模板.pdf";
                // 生成的新文件路径
                String newPDFPath = cjroneProperties.getSignaturePath()+"zgyiliao_"+cjroneblZgjbyiliaoEntity.getDisableId()+".pdf";

                // 获得待生成的实体文件
                Map<String, String> map =new HashMap<String, String>();
                map.put("name", cjroneblZgjbyiliaoEntity.getName()==null?"":cjroneblZgjbyiliaoEntity.getName());
                if(cjroneblZgjbyiliaoEntity.getSex()!=null){
                    if("0".equals(cjroneblZgjbyiliaoEntity.getSex())){
                        map.put("sex","女");
                    }else{
                        map.put("sex","男");
                    }
                }else{
                    map.put("sex","");
                }
                map.put("age",cjroneblZgjbyiliaoEntity.getAge()==null?"":cjroneblZgjbyiliaoEntity.getAge().toString());
                map.put("disableId",cjroneblZgjbyiliaoEntity.getDisableId()==null?"":cjroneblZgjbyiliaoEntity.getDisableId());
                map.put("telephone",cjroneblZgjbyiliaoEntity.getTelephone()==null?"":cjroneblZgjbyiliaoEntity.getTelephone());
                map.put("insuredStatus",cjroneblZgjbyiliaoEntity.getInsuredStatus()==null?"":cjroneblZgjbyiliaoEntity.getInsuredStatus());
                map.put("liveAddress",cjroneblZgjbyiliaoEntity.getLiveAddress()==null?"":cjroneblZgjbyiliaoEntity.getLiveAddress());
                map.put("otherSubsidy",cjroneblZgjbyiliaoEntity.getOtherSubsidy()==null?"":cjroneblZgjbyiliaoEntity.getOtherSubsidy());
                map.put("seTime2",cjroneblZgjbyiliaoEntity.getSeTime()==null?"":cjroneblZgjbyiliaoEntity.getSeTime());
                map.put("payMoney2",cjroneblZgjbyiliaoEntity.getPayMoney()==null?"":cjroneblZgjbyiliaoEntity.getPayMoney());
                map.put("insuredTypeY","基本医疗保险");
                map.put("applicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");



                FileOutputStream out;
                int num = 1;//页数
                ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
                try {
                    out = new FileOutputStream(newPDFPath);// 输出流
                    Document doc = new Document();   //新建一个文档
                    PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                    doc.open();

                    for (int i = 0; i < num; i++) {
                        bos[i] = new ByteArrayOutputStream();
                        System.out.println("templatePath--->"+templatePath);
                        PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                        PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                        AcroFields form = stamper.getAcroFields(); //获取文本域
                        // BaseFont
                        // 1、使用iTextAsian.jar中的字体
                        // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                        // 2、使用Windows系统字体(TrueType)
                        // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                        // 3、使用资源字体(ClassPath)
                        // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                        BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                        java.util.Iterator<String> it = form.getFields().keySet().iterator();
                        while (it.hasNext()) {
                            String name = it.next();
                            System.out.println(name + ":"+map.get(name));
                            form.setFieldProperty(name,"textfont",font,null);
                            // form.addSubstitutionFont(font);
                            form.setField(name, map.get(name));
                        }

                        stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                        stamper.close();

                    }
                    PdfImportedPage page = null;
                    for (int i = 0; i < num; i++) {
                        page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                        copy.addPage(page);
                    }
                    doc.close();
                    out.close();
                } catch (IOException e) {
                    System.out.println("导出异常");
                } catch (DocumentException e) {
                    System.out.println("文档异常");
                }

                CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
                cjroneSignature.setUrl("/zgyiliao_" + cjroneblZgjbyiliaoEntity.getDisableId() + ".pdf");
                cjroneSignature.setFileActUrl(newPDFPath);
                cjroneSignature.setCreateDate(new Date());
                cjroneSignature.setCreateId(getUserId());
                cjroneSignature.setType("惠残事项职工基本医疗保险");
                cjroneSignature.setTypeId(cjroneblZgjbyiliaoEntity.getId());
                cjroneSignature.setFileName("zgyiliao_" + cjroneblZgjbyiliaoEntity.getDisableId() + ".pdf");
                cjroneSignature.setAccountId(getUserId().toString());
                cjroneSignature.setAccountName(getUser().getUsername());
                cjroneSignature.setStatus("1");

                cjroneSignatureService.save(cjroneSignature);
                matterApplicationEntity.setPhoto(cjroneSignature.getUrl());
                result_list.add(matterApplicationEntity);
            }
            else if ("职工基本养老保险补助".equals(matterApplicationEntity.getMatterName())){

                //根据编号获得职工基本养老保险补助
                CjroneblZgjbyanglaoEntity cjroneblZgjbyanglaoEntity =cjroneblZgjbyanglaoService.getById(matterApplicationEntity.getMatterId());

                Calendar now = Calendar.getInstance();

                // 原pdf模板路径
                String templatePath = cjroneProperties.getTempletePath()+"职工基本养老保险补助模板.pdf";
                // 生成的新文件路径
                String newPDFPath = cjroneProperties.getSignaturePath()+"zgyanglao_"+cjroneblZgjbyanglaoEntity.getDisableId()+".pdf";

                // 获得待生成的实体文件
                Map<String, String> map =new HashMap<String, String>();
                map.put("name", cjroneblZgjbyanglaoEntity.getName()==null?"":cjroneblZgjbyanglaoEntity.getName());
                if(cjroneblZgjbyanglaoEntity.getSex()!=null){
                    if("0".equals(cjroneblZgjbyanglaoEntity.getSex())){
                        map.put("sex","女");
                    }else{
                        map.put("sex","男");
                    }
                }else{
                    map.put("sex","");
                }
                map.put("age",cjroneblZgjbyanglaoEntity.getAge()==null?"":cjroneblZgjbyanglaoEntity.getAge().toString());
                map.put("disableId",cjroneblZgjbyanglaoEntity.getDisableId()==null?"":cjroneblZgjbyanglaoEntity.getDisableId());
                map.put("telephone",cjroneblZgjbyanglaoEntity.getTelephone()==null?"":cjroneblZgjbyanglaoEntity.getTelephone());
                map.put("insuredStatus",cjroneblZgjbyanglaoEntity.getInsuredStatus()==null?"":cjroneblZgjbyanglaoEntity.getInsuredStatus());
                map.put("liveAddress",cjroneblZgjbyanglaoEntity.getLiveAddress()==null?"":cjroneblZgjbyanglaoEntity.getLiveAddress());
                map.put("otherSubsidy",cjroneblZgjbyanglaoEntity.getOtherSubsidy()==null?"":cjroneblZgjbyanglaoEntity.getOtherSubsidy());
                map.put("seTime1",cjroneblZgjbyanglaoEntity.getSeTime()==null?"":cjroneblZgjbyanglaoEntity.getSeTime());
                map.put("payMoney1",cjroneblZgjbyanglaoEntity.getPayMoney()==null?"":cjroneblZgjbyanglaoEntity.getPayMoney());
                map.put("insuredTypeYL","职工基本养老保险");
                map.put("applicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");



                FileOutputStream out;
                int num = 1;//页数
                ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
                try {
                    out = new FileOutputStream(newPDFPath);// 输出流
                    Document doc = new Document();   //新建一个文档
                    PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                    doc.open();

                    for (int i = 0; i < num; i++) {
                        bos[i] = new ByteArrayOutputStream();
                        System.out.println("templatePath--->"+templatePath);
                        PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                        PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                        AcroFields form = stamper.getAcroFields(); //获取文本域
                        // BaseFont
                        // 1、使用iTextAsian.jar中的字体
                        // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                        // 2、使用Windows系统字体(TrueType)
                        // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                        // 3、使用资源字体(ClassPath)
                        // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                        BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                        java.util.Iterator<String> it = form.getFields().keySet().iterator();
                        while (it.hasNext()) {
                            String name = it.next();
                            System.out.println(name + ":"+map.get(name));
                            form.setFieldProperty(name,"textfont",font,null);
                            // form.addSubstitutionFont(font);
                            form.setField(name, map.get(name));
                        }

                        stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                        stamper.close();

                    }
                    PdfImportedPage page = null;
                    for (int i = 0; i < num; i++) {
                        page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                        copy.addPage(page);
                    }
                    doc.close();
                    out.close();
                } catch (IOException e) {
                    System.out.println("导出异常");
                } catch (DocumentException e) {
                    System.out.println("文档异常");
                }

                CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
                cjroneSignature.setUrl("/zgyanglao_" + cjroneblZgjbyanglaoEntity.getDisableId() + ".pdf");
                cjroneSignature.setFileActUrl(newPDFPath);
                cjroneSignature.setCreateDate(new Date());
                cjroneSignature.setCreateId(getUserId());
                cjroneSignature.setType("惠残事项职工基本养老保险");
                cjroneSignature.setTypeId(cjroneblZgjbyanglaoEntity.getId());
                cjroneSignature.setFileName("zgyanglao_" + cjroneblZgjbyanglaoEntity.getDisableId() + ".pdf");
                cjroneSignature.setAccountId(getUserId().toString());
                cjroneSignature.setAccountName(getUser().getUsername());
                cjroneSignature.setStatus("1");

                cjroneSignatureService.save(cjroneSignature);
                matterApplicationEntity.setPhoto(cjroneSignature.getUrl());
                result_list.add(matterApplicationEntity);
            }
            else if ("城乡居民养老保险补助".equals(matterApplicationEntity.getMatterName())){
                //根据编号获得城乡居民养老保险补助
                CjroneblCxjmyanglaoEntity cjroneblCxjmyanglaoEntity = cjroneblCxjmyanglaoService.getById(matterApplicationEntity.getMatterId());

                Calendar now = Calendar.getInstance();

                // 原pdf模板路径
                String templatePath = cjroneProperties.getTempletePath()+"城乡居民养老保险补助模板.pdf";
                // 生成的新文件路径
                String newPDFPath = cjroneProperties.getSignaturePath()+"cxyanglao_"+cjroneblCxjmyanglaoEntity.getDisableId()+".pdf";

                // 获得待生成的实体文件
                Map<String, String> map =new HashMap<String, String>();
                map.put("name", cjroneblCxjmyanglaoEntity.getName()==null?"":cjroneblCxjmyanglaoEntity.getName());
                if(cjroneblCxjmyanglaoEntity.getSex()!=null){
                    if("0".equals(cjroneblCxjmyanglaoEntity.getSex())){
                        map.put("sex","女");
                    }else{
                        map.put("sex","男");
                    }
                }else{
                    map.put("sex","");
                }
                map.put("age",cjroneblCxjmyanglaoEntity.getAge()==null?"":cjroneblCxjmyanglaoEntity.getAge().toString());
                map.put("disableId",cjroneblCxjmyanglaoEntity.getDisableId()==null?"":cjroneblCxjmyanglaoEntity.getDisableId());
                map.put("telephone",cjroneblCxjmyanglaoEntity.getTelephone()==null?"":cjroneblCxjmyanglaoEntity.getTelephone());
                map.put("insuredStatus",cjroneblCxjmyanglaoEntity.getInsuredStatus()==null?"":cjroneblCxjmyanglaoEntity.getInsuredStatus());
                map.put("liveAddress",cjroneblCxjmyanglaoEntity.getLiveAddress()==null?"":cjroneblCxjmyanglaoEntity.getLiveAddress());
                map.put("otherSubsidy",cjroneblCxjmyanglaoEntity.getOtherSubsidy()==null?"":cjroneblCxjmyanglaoEntity.getOtherSubsidy());
                map.put("seTime1",cjroneblCxjmyanglaoEntity.getSeTime()==null?"":cjroneblCxjmyanglaoEntity.getSeTime());
                map.put("payMoney1",cjroneblCxjmyanglaoEntity.getPayMoney()==null?"":cjroneblCxjmyanglaoEntity.getPayMoney());
                map.put("insuredTypeYL","城乡居民养老保险");
                map.put("applicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");



                FileOutputStream out;
                int num = 1;//页数
                ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
                try {
                    out = new FileOutputStream(newPDFPath);// 输出流
                    Document doc = new Document();   //新建一个文档
                    PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                    doc.open();

                    for (int i = 0; i < num; i++) {
                        bos[i] = new ByteArrayOutputStream();
                        System.out.println("templatePath--->"+templatePath);
                        PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                        PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                        AcroFields form = stamper.getAcroFields(); //获取文本域
                        // BaseFont
                        // 1、使用iTextAsian.jar中的字体
                        // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                        // 2、使用Windows系统字体(TrueType)
                        // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                        // 3、使用资源字体(ClassPath)
                        // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                        BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                        java.util.Iterator<String> it = form.getFields().keySet().iterator();
                        while (it.hasNext()) {
                            String name = it.next();
                            System.out.println(name + ":"+map.get(name));
                            form.setFieldProperty(name,"textfont",font,null);
                            // form.addSubstitutionFont(font);
                            form.setField(name, map.get(name));
                        }

                        stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                        stamper.close();

                    }
                    PdfImportedPage page = null;
                    for (int i = 0; i < num; i++) {
                        page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                        copy.addPage(page);
                    }
                    doc.close();
                    out.close();
                } catch (IOException e) {
                    System.out.println("导出异常");
                } catch (DocumentException e) {
                    System.out.println("文档异常");
                }

                CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
                cjroneSignature.setUrl("/cxyanglao_" + cjroneblCxjmyanglaoEntity.getDisableId() + ".pdf");
                cjroneSignature.setFileActUrl(newPDFPath);
                cjroneSignature.setCreateDate(new Date());
                cjroneSignature.setCreateId(getUserId());
                cjroneSignature.setType("惠残事项城乡居民养老保险");
                cjroneSignature.setTypeId(cjroneblCxjmyanglaoEntity.getId());
                cjroneSignature.setFileName("zgyanglao_" + cjroneblCxjmyanglaoEntity.getDisableId() + ".pdf");
                cjroneSignature.setAccountId(getUserId().toString());
                cjroneSignature.setAccountName(getUser().getUsername());
                cjroneSignature.setStatus("1");

                cjroneSignatureService.save(cjroneSignature);
                matterApplicationEntity.setPhoto(cjroneSignature.getUrl());
                result_list.add(matterApplicationEntity);
            }
            else if ("城乡基本医疗保险补助".equals(matterApplicationEntity.getMatterName())){

                //根据编号获得职工基本医疗保险补助
                CjroneblCxjmyiliaoEntity cjroneblCxjmyiliaoEntity =cjroneblCxjmyiliaoService.getById(matterApplicationEntity.getMatterId());

                Calendar now = Calendar.getInstance();

                // 原pdf模板路径
                String templatePath = cjroneProperties.getTempletePath()+"城乡基本医疗保险补助模板.pdf";
                // 生成的新文件路径
                String newPDFPath = cjroneProperties.getSignaturePath()+"cxyiliao_"+cjroneblCxjmyiliaoEntity.getDisableId()+".pdf";

                // 获得待生成的实体文件
                Map<String, String> map =new HashMap<String, String>();
                map.put("name", cjroneblCxjmyiliaoEntity.getName()==null?"":cjroneblCxjmyiliaoEntity.getName());
                if(cjroneblCxjmyiliaoEntity.getSex()!=null){
                    if("0".equals(cjroneblCxjmyiliaoEntity.getSex())){
                        map.put("sex","女");
                    }else{
                        map.put("sex","男");
                    }
                }else{
                    map.put("sex","");
                }
                map.put("age",cjroneblCxjmyiliaoEntity.getAge()==null?"":cjroneblCxjmyiliaoEntity.getAge().toString());
                map.put("disableId",cjroneblCxjmyiliaoEntity.getDisableId()==null?"":cjroneblCxjmyiliaoEntity.getDisableId());
                map.put("telephone",cjroneblCxjmyiliaoEntity.getTelephone()==null?"":cjroneblCxjmyiliaoEntity.getTelephone());
                map.put("insuredStatus",cjroneblCxjmyiliaoEntity.getInsuredStatus()==null?"":cjroneblCxjmyiliaoEntity.getInsuredStatus());
                map.put("liveAddress",cjroneblCxjmyiliaoEntity.getLiveAddress()==null?"":cjroneblCxjmyiliaoEntity.getLiveAddress());
                map.put("otherSubsidy",cjroneblCxjmyiliaoEntity.getOtherSubsidy()==null?"":cjroneblCxjmyiliaoEntity.getOtherSubsidy());
                map.put("seTime2",cjroneblCxjmyiliaoEntity.getSeTime()==null?"":cjroneblCxjmyiliaoEntity.getSeTime());
                map.put("payMoney2",cjroneblCxjmyiliaoEntity.getPayMoney()==null?"":cjroneblCxjmyiliaoEntity.getPayMoney());
                map.put("insuredTypeY","城乡居民基本医疗保险");
                map.put("applicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");



                FileOutputStream out;
                int num = 1;//页数
                ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
                try {
                    out = new FileOutputStream(newPDFPath);// 输出流
                    Document doc = new Document();   //新建一个文档
                    PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                    doc.open();

                    for (int i = 0; i < num; i++) {
                        bos[i] = new ByteArrayOutputStream();
                        System.out.println("templatePath--->"+templatePath);
                        PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                        PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                        AcroFields form = stamper.getAcroFields(); //获取文本域
                        // BaseFont
                        // 1、使用iTextAsian.jar中的字体
                        // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                        // 2、使用Windows系统字体(TrueType)
                        // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                        // 3、使用资源字体(ClassPath)
                        // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                        BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                        java.util.Iterator<String> it = form.getFields().keySet().iterator();
                        while (it.hasNext()) {
                            String name = it.next();
                            System.out.println(name + ":"+map.get(name));
                            form.setFieldProperty(name,"textfont",font,null);
                            // form.addSubstitutionFont(font);
                            form.setField(name, map.get(name));
                        }

                        stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                        stamper.close();

                    }
                    PdfImportedPage page = null;
                    for (int i = 0; i < num; i++) {
                        page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                        copy.addPage(page);
                    }
                    doc.close();
                    out.close();
                } catch (IOException e) {
                    System.out.println("导出异常");
                } catch (DocumentException e) {
                    System.out.println("文档异常");
                }

                CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
                cjroneSignature.setUrl("/cxyiliao_" + cjroneblCxjmyiliaoEntity.getDisableId() + ".pdf");
                cjroneSignature.setFileActUrl(newPDFPath);
                cjroneSignature.setCreateDate(new Date());
                cjroneSignature.setCreateId(getUserId());
                cjroneSignature.setType("惠残事项城乡基本医疗保险补助");
                cjroneSignature.setTypeId(cjroneblCxjmyiliaoEntity.getId());
                cjroneSignature.setFileName("cxyiliao_" + cjroneblCxjmyiliaoEntity.getDisableId() + ".pdf");
                cjroneSignature.setAccountId(getUserId().toString());
                cjroneSignature.setAccountName(getUser().getUsername());
                cjroneSignature.setStatus("1");

                cjroneSignatureService.save(cjroneSignature);
                matterApplicationEntity.setPhoto(cjroneSignature.getUrl());
                result_list.add(matterApplicationEntity);
            }
            else if ("残疾人临时救助".equals(matterApplicationEntity.getMatterName())){

                //根据编号获得残疾人临时救助
                CjroneblTemporaryAssistanceEntity cjroneblTemporaryAssistanceEntity =cjroneblTemporaryAssistanceService.getById(matterApplicationEntity.getMatterId());

                Calendar now = Calendar.getInstance();

                // 原pdf模板路径
                String templatePath = cjroneProperties.getTempletePath()+"残疾人临时救助模板.pdf";
                // 生成的新文件路径
                String newPDFPath = cjroneProperties.getSignaturePath()+"temporary_assistance_"+cjroneblTemporaryAssistanceEntity.getIdCard()+".pdf";

                // 获得待生成的实体文件
                Map<String, String> map =new HashMap<String, String>();
                map.put("name", cjroneblTemporaryAssistanceEntity.getName()==null?"":cjroneblTemporaryAssistanceEntity.getName());
                if(cjroneblTemporaryAssistanceEntity.getSex()!=null){
                    if("0".equals(cjroneblTemporaryAssistanceEntity.getSex())){
                        map.put("sex","女");
                    }else{
                        map.put("sex","男");
                    }
                }else{
                    map.put("sex","");
                }
                map.put("disabilityType",cjroneblTemporaryAssistanceEntity.getDisabilityType()==null?"":cjroneblTemporaryAssistanceEntity.getDisabilityType());
                map.put("disabilityDegree",cjroneblTemporaryAssistanceEntity.getDisabilityDegree()==null?"":cjroneblTemporaryAssistanceEntity.getDisabilityDegree());
                map.put("disableId",cjroneblTemporaryAssistanceEntity.getDisableId()==null?"":cjroneblTemporaryAssistanceEntity.getDisableId());
                map.put("telephone",cjroneblTemporaryAssistanceEntity.getTelephone()==null?"":cjroneblTemporaryAssistanceEntity.getTelephone());
                map.put("liveAddress",cjroneblTemporaryAssistanceEntity.getLiveAddress()==null?"":cjroneblTemporaryAssistanceEntity.getLiveAddress());
                map.put("familyEconomy",cjroneblTemporaryAssistanceEntity.getFamilyEconomy()==null?"":cjroneblTemporaryAssistanceEntity.getFamilyEconomy());
                if("1".equals(cjroneblTemporaryAssistanceEntity.getMingzhenSubsidy())){
                    map.put("mingzhenSubsidy","是");
                }else{
                    map.put("mingzhenSubsidy","否");
                }

                map.put("payMoney",cjroneblTemporaryAssistanceEntity.getPayMoney()==null?"":cjroneblTemporaryAssistanceEntity.getPayMoney());
                map.put("subsidyMoney",cjroneblTemporaryAssistanceEntity.getSubsidyMoney()==null?"":cjroneblTemporaryAssistanceEntity.getSubsidyMoney());
                map.put("applyReason",cjroneblTemporaryAssistanceEntity.getApplyReason()==null?"":cjroneblTemporaryAssistanceEntity.getApplyReason());
                map.put("applyDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");



                FileOutputStream out;
                int num = 1;//页数
                ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
                try {
                    out = new FileOutputStream(newPDFPath);// 输出流
                    Document doc = new Document();   //新建一个文档
                    PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                    doc.open();

                    for (int i = 0; i < num; i++) {
                        bos[i] = new ByteArrayOutputStream();
                        System.out.println("templatePath--->"+templatePath);
                        PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                        PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                        AcroFields form = stamper.getAcroFields(); //获取文本域
                        // BaseFont
                        // 1、使用iTextAsian.jar中的字体
                        // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                        // 2、使用Windows系统字体(TrueType)
                        // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                        // 3、使用资源字体(ClassPath)
                        // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                        BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                        java.util.Iterator<String> it = form.getFields().keySet().iterator();
                        while (it.hasNext()) {
                            String name = it.next();
                            System.out.println(name + ":"+map.get(name));
                            form.setFieldProperty(name,"textfont",font,null);
                            // form.addSubstitutionFont(font);
                            form.setField(name, map.get(name));
                        }

                        stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                        stamper.close();

                    }
                    PdfImportedPage page = null;
                    for (int i = 0; i < num; i++) {
                        page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                        copy.addPage(page);
                    }
                    doc.close();
                    out.close();
                } catch (IOException e) {
                    System.out.println("导出异常");
                } catch (DocumentException e) {
                    System.out.println("文档异常");
                }

                CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
                cjroneSignature.setUrl("/temporary_assistance_" + cjroneblTemporaryAssistanceEntity.getIdCard() + ".pdf");
                cjroneSignature.setFileActUrl(newPDFPath);
                cjroneSignature.setCreateDate(new Date());
                cjroneSignature.setCreateId(getUserId());
                cjroneSignature.setType("惠残事项残疾人临时救助");
                cjroneSignature.setTypeId(cjroneblTemporaryAssistanceEntity.getId());
                cjroneSignature.setFileName("temporary_assistance_" + cjroneblTemporaryAssistanceEntity.getIdCard() + ".pdf");
                cjroneSignature.setAccountId(getUserId().toString());
                cjroneSignature.setAccountName(getUser().getUsername());
                cjroneSignature.setStatus("1");

                cjroneSignatureService.save(cjroneSignature);
                matterApplicationEntity.setPhoto(cjroneSignature.getUrl());
                result_list.add(matterApplicationEntity);
            }
            else if ("康复补助".equals(matterApplicationEntity.getMatterName())){

                //根据编号获得残疾人临时救助
                CjroneblRehabilitationSubsidyEntity cjroneblRehabilitationSubsidyEntity =cjroneblRehabilitationSubsidyService.getById(matterApplicationEntity.getMatterId());

                Calendar now = Calendar.getInstance();

                // 原pdf模板路径
                String templatePath = cjroneProperties.getTempletePath()+"康复补助模板.pdf";
                // 生成的新文件路径
                String newPDFPath = cjroneProperties.getSignaturePath()+"rehabilitation_subsidy_"+cjroneblRehabilitationSubsidyEntity.getIdCard()+".pdf";

                // 获得待生成的实体文件
                Map<String, String> map =new HashMap<String, String>();
                map.put("name", cjroneblRehabilitationSubsidyEntity.getName()==null?"":cjroneblRehabilitationSubsidyEntity.getName());
                if(cjroneblRehabilitationSubsidyEntity.getSex()!=null){
                    if("0".equals(cjroneblRehabilitationSubsidyEntity.getSex())){
                        map.put("sex","女");
                    }else{
                        map.put("sex","男");
                    }
                }else{
                    map.put("sex","");
                }
                map.put("disabilityType",cjroneblRehabilitationSubsidyEntity.getDisabilityType()==null?"":cjroneblRehabilitationSubsidyEntity.getDisabilityType());
                map.put("disabilityDegree",cjroneblRehabilitationSubsidyEntity.getDisabilityDegree()==null?"":cjroneblRehabilitationSubsidyEntity.getDisabilityDegree());
                map.put("disableId",cjroneblRehabilitationSubsidyEntity.getDisabileId()==null?"":cjroneblRehabilitationSubsidyEntity.getDisabileId());
                map.put("idCard",cjroneblRehabilitationSubsidyEntity.getIdCard()==null?"":cjroneblRehabilitationSubsidyEntity.getIdCard());
                map.put("telephone",cjroneblRehabilitationSubsidyEntity.getTelephone()==null?"":cjroneblRehabilitationSubsidyEntity.getTelephone());
                map.put("liveAddress",cjroneblRehabilitationSubsidyEntity.getLiveAddress()==null?"":cjroneblRehabilitationSubsidyEntity.getLiveAddress());
                map.put("familyEconomy",cjroneblRehabilitationSubsidyEntity.getFamilyEconomy()==null?"":cjroneblRehabilitationSubsidyEntity.getFamilyEconomy());

                map.put("applyDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");



                FileOutputStream out;
                int num = 1;//页数
                ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
                try {
                    out = new FileOutputStream(newPDFPath);// 输出流
                    Document doc = new Document();   //新建一个文档
                    PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                    doc.open();

                    for (int i = 0; i < num; i++) {
                        bos[i] = new ByteArrayOutputStream();
                        System.out.println("templatePath--->"+templatePath);
                        PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                        PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                        AcroFields form = stamper.getAcroFields(); //获取文本域
                        // BaseFont
                        // 1、使用iTextAsian.jar中的字体
                        // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                        // 2、使用Windows系统字体(TrueType)
                        // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                        // 3、使用资源字体(ClassPath)
                        // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                        BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                        java.util.Iterator<String> it = form.getFields().keySet().iterator();
                        while (it.hasNext()) {
                            String name = it.next();
                            System.out.println(name + ":"+map.get(name));
                            form.setFieldProperty(name,"textfont",font,null);
                            // form.addSubstitutionFont(font);
                            form.setField(name, map.get(name));
                        }

                        stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                        stamper.close();

                    }
                    PdfImportedPage page = null;
                    for (int i = 0; i < num; i++) {
                        page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                        copy.addPage(page);
                    }
                    doc.close();
                    out.close();
                } catch (IOException e) {
                    System.out.println("导出异常");
                } catch (DocumentException e) {
                    System.out.println("文档异常");
                }

                CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
                cjroneSignature.setUrl("/rehabilitation_subsidy_" + cjroneblRehabilitationSubsidyEntity.getIdCard() + ".pdf");
                cjroneSignature.setFileActUrl(newPDFPath);
                cjroneSignature.setCreateDate(new Date());
                cjroneSignature.setCreateId(getUserId());
                cjroneSignature.setType("惠残事项康复补助");
                cjroneSignature.setTypeId(cjroneblRehabilitationSubsidyEntity.getId());
                cjroneSignature.setFileName("rehabilitation_subsidy_" + cjroneblRehabilitationSubsidyEntity.getIdCard() + ".pdf");
                cjroneSignature.setAccountId(getUserId().toString());
                cjroneSignature.setAccountName(getUser().getUsername());
                cjroneSignature.setStatus("1");

                cjroneSignatureService.save(cjroneSignature);
                matterApplicationEntity.setPhoto(cjroneSignature.getUrl());
                result_list.add(matterApplicationEntity);
            }
            else if ("创业补助".equals(matterApplicationEntity.getMatterName())){

                //根据编号获得残疾人创业补助
                CjroneblBusinessGrantEntity cjroneblBusinessGrantEntity =cjroneblBusinessGrantService.getById(matterApplicationEntity.getMatterId());

                Calendar now = Calendar.getInstance();

                // 原pdf模板路径
                String templatePath = cjroneProperties.getTempletePath()+"创业模板.pdf";
                // 生成的新文件路径
                String newPDFPath = cjroneProperties.getSignaturePath()+"business_grant_"+cjroneblBusinessGrantEntity.getDisableId()+".pdf";

                // 获得待生成的实体文件
                Map<String, String> map =new HashMap<String, String>();
                map.put("name", cjroneblBusinessGrantEntity.getName()==null?"":cjroneblBusinessGrantEntity.getName());
                if(cjroneblBusinessGrantEntity.getSex()!=null){
                    if("0".equals(cjroneblBusinessGrantEntity.getSex())){
                        map.put("sex","女");
                    }else{
                        map.put("sex","男");
                    }
                }else{
                    map.put("sex","");
                }
                map.put("age",cjroneblBusinessGrantEntity.getAge()==null?"":cjroneblBusinessGrantEntity.getAge().toString());
                map.put("disableId",cjroneblBusinessGrantEntity.getDisableId()==null?"":cjroneblBusinessGrantEntity.getDisableId());
                map.put("telephone",cjroneblBusinessGrantEntity.getTelephone()==null?"":cjroneblBusinessGrantEntity.getTelephone());
                map.put("familyEconomy",cjroneblBusinessGrantEntity.getFamilyEconomy()==null?"":cjroneblBusinessGrantEntity.getFamilyEconomy());
                map.put("educationDegree",cjroneblBusinessGrantEntity.getEducationDegree()==null?"":cjroneblBusinessGrantEntity.getEducationDegree());
                map.put("liveAddress",cjroneblBusinessGrantEntity.getLiveAddress()==null?"":cjroneblBusinessGrantEntity.getLiveAddress());
                map.put("startTime",cjroneblBusinessGrantEntity.getStartTime()==null?"":cjroneblBusinessGrantEntity.getStartTime());
                map.put("manageAddress",cjroneblBusinessGrantEntity.getManageAddress()==null?"":cjroneblBusinessGrantEntity.getManageAddress());
                map.put("manageRange",cjroneblBusinessGrantEntity.getManageRange()==null?"":cjroneblBusinessGrantEntity.getManageRange());
                map.put("isCollege",cjroneblBusinessGrantEntity.getIsCollege()==null?"":cjroneblBusinessGrantEntity.getIsCollege());
                map.put("subsidyMoney",cjroneblBusinessGrantEntity.getSubsidyMoney()==null?"":cjroneblBusinessGrantEntity.getSubsidyMoney());
                map.put("subsidyReason",cjroneblBusinessGrantEntity.getSubsidyReason()==null?"":cjroneblBusinessGrantEntity.getSubsidyReason());
                map.put("isEmployment",cjroneblBusinessGrantEntity.getIsEmployment()==null?"":cjroneblBusinessGrantEntity.getIsEmployment());
                map.put("isFirsttime",cjroneblBusinessGrantEntity.getIsFirsttime()==null?"":cjroneblBusinessGrantEntity.getIsFirsttime());
                map.put("isSixManage",cjroneblBusinessGrantEntity.getIsSixManage()==null?"":cjroneblBusinessGrantEntity.getIsSixManage());


                map.put("applyDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");



                FileOutputStream out;
                int num = 1;//页数
                ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
                try {
                    out = new FileOutputStream(newPDFPath);// 输出流
                    Document doc = new Document();   //新建一个文档
                    PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                    doc.open();

                    for (int i = 0; i < num; i++) {
                        bos[i] = new ByteArrayOutputStream();
                        System.out.println("templatePath--->"+templatePath);
                        PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                        PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                        AcroFields form = stamper.getAcroFields(); //获取文本域
                        // BaseFont
                        // 1、使用iTextAsian.jar中的字体
                        // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                        // 2、使用Windows系统字体(TrueType)
                        // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                        // 3、使用资源字体(ClassPath)
                        // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                        BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                        java.util.Iterator<String> it = form.getFields().keySet().iterator();
                        while (it.hasNext()) {
                            String name = it.next();
                            System.out.println(name + ":"+map.get(name));
                            form.setFieldProperty(name,"textfont",font,null);
                            // form.addSubstitutionFont(font);
                            form.setField(name, map.get(name));
                        }

                        stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                        stamper.close();

                    }
                    PdfImportedPage page = null;
                    for (int i = 0; i < num; i++) {
                        page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                        copy.addPage(page);
                    }
                    doc.close();
                    out.close();
                } catch (IOException e) {
                    System.out.println("导出异常");
                } catch (DocumentException e) {
                    System.out.println("文档异常");
                }

                CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
                cjroneSignature.setUrl("/business_grant_" + cjroneblBusinessGrantEntity.getDisableId() + ".pdf");
                cjroneSignature.setFileActUrl(newPDFPath);
                cjroneSignature.setCreateDate(new Date());
                cjroneSignature.setCreateId(getUserId());
                cjroneSignature.setType("惠残事项创业补助");
                cjroneSignature.setTypeId(cjroneblBusinessGrantEntity.getId());
                cjroneSignature.setFileName("business_grant_" + cjroneblBusinessGrantEntity.getDisableId() + ".pdf");
                cjroneSignature.setAccountId(getUserId().toString());
                cjroneSignature.setAccountName(getUser().getUsername());
                cjroneSignature.setStatus("1");

                cjroneSignatureService.save(cjroneSignature);
                matterApplicationEntity.setPhoto(cjroneSignature.getUrl());
                result_list.add(matterApplicationEntity);
            }
            else if ("大学生补助".equals(matterApplicationEntity.getMatterName())){

                //根据编号获得大学生补助
                CjroneblCollegeeduEntity cjroneblCollegeeduEntity =cjroneblCollegeeduService.getById(matterApplicationEntity.getMatterId());

                Calendar now = Calendar.getInstance();

                // 原pdf模板路径
                String templatePath = cjroneProperties.getTempletePath()+"大学生补助模板.pdf";
                // 生成的新文件路径
                String newPDFPath = cjroneProperties.getSignaturePath()+"college_edu_"+cjroneblCollegeeduEntity.getDisableId()+".pdf";

                // 获得待生成的实体文件
                Map<String, String> map =new HashMap<String, String>();
                map.put("name", cjroneblCollegeeduEntity.getName()==null?"":cjroneblCollegeeduEntity.getName());
                if(cjroneblCollegeeduEntity.getSex()!=null){
                    if("0".equals(cjroneblCollegeeduEntity.getSex())){
                        map.put("sex","女");
                    }else{
                        map.put("sex","男");
                    }
                }else{
                    map.put("sex","");
                }
                map.put("birthday",cjroneblCollegeeduEntity.getBirthday()==null?"":cjroneblCollegeeduEntity.getBirthday());
                map.put("collegeName",cjroneblCollegeeduEntity.getCollegeName()==null?"":cjroneblCollegeeduEntity.getCollegeName());
                map.put("majorName",cjroneblCollegeeduEntity.getMajorName()==null?"":cjroneblCollegeeduEntity.getMajorName());
                map.put("collegeTime",cjroneblCollegeeduEntity.getCollegeTime()==null?"":cjroneblCollegeeduEntity.getCollegeTime());
                map.put("disableId",cjroneblCollegeeduEntity.getDisableId()==null?"":cjroneblCollegeeduEntity.getDisableId());
                map.put("tuition",cjroneblCollegeeduEntity.getTuition()==null?"":cjroneblCollegeeduEntity.getTuition());
                map.put("actuallyTuition",cjroneblCollegeeduEntity.getActuallyTuition()==null?"":cjroneblCollegeeduEntity.getActuallyTuition());
                map.put("accommodationFee",cjroneblCollegeeduEntity.getAccommodationFee()==null?"":cjroneblCollegeeduEntity.getAccommodationFee());
                map.put("actuallyAccommodationFee",cjroneblCollegeeduEntity.getActuallyAccommodationFee()==null?"":cjroneblCollegeeduEntity.getActuallyAccommodationFee());
                map.put("hukouNature",cjroneblCollegeeduEntity.getHukouNature()==null?"":cjroneblCollegeeduEntity.getHukouNature());
                map.put("familyCount",cjroneblCollegeeduEntity.getFamilyCount()==null?"":cjroneblCollegeeduEntity.getFamilyCount());
                map.put("liveAddress",cjroneblCollegeeduEntity.getLiveAddress()==null?"":cjroneblCollegeeduEntity.getLiveAddress());
                map.put("telephone",cjroneblCollegeeduEntity.getTelephone()==null?"":cjroneblCollegeeduEntity.getTelephone());
                map.put("postcode",cjroneblCollegeeduEntity.getPostcode()==null?"":cjroneblCollegeeduEntity.getPostcode());
                map.put("familyFinances",cjroneblCollegeeduEntity.getFamilyFinances()==null?"":cjroneblCollegeeduEntity.getFamilyFinances());
                map.put("familyIncome",cjroneblCollegeeduEntity.getFamilyIncome()==null?"":cjroneblCollegeeduEntity.getFamilyIncome());
                map.put("subsidyReason",cjroneblCollegeeduEntity.getSubsidyReason()==null?"":cjroneblCollegeeduEntity.getSubsidyReason());


                map.put("applyDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");



                FileOutputStream out;
                int num = 1;//页数
                ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
                try {
                    out = new FileOutputStream(newPDFPath);// 输出流
                    Document doc = new Document();   //新建一个文档
                    PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                    doc.open();

                    for (int i = 0; i < num; i++) {
                        bos[i] = new ByteArrayOutputStream();
                        System.out.println("templatePath--->"+templatePath);
                        PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                        PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                        AcroFields form = stamper.getAcroFields(); //获取文本域
                        // BaseFont
                        // 1、使用iTextAsian.jar中的字体
                        // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                        // 2、使用Windows系统字体(TrueType)
                        // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                        // 3、使用资源字体(ClassPath)
                        // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                        BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                        java.util.Iterator<String> it = form.getFields().keySet().iterator();
                        while (it.hasNext()) {
                            String name = it.next();
                            System.out.println(name + ":"+map.get(name));
                            form.setFieldProperty(name,"textfont",font,null);
                            // form.addSubstitutionFont(font);
                            form.setField(name, map.get(name));
                        }

                        stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                        stamper.close();

                    }
                    PdfImportedPage page = null;
                    for (int i = 0; i < num; i++) {
                        page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                        copy.addPage(page);
                    }
                    doc.close();
                    out.close();
                } catch (IOException e) {
                    System.out.println("导出异常");
                } catch (DocumentException e) {
                    System.out.println("文档异常");
                }

                CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
                cjroneSignature.setUrl("/college_edu_" + cjroneblCollegeeduEntity.getDisableId() + ".pdf");
                cjroneSignature.setFileActUrl(newPDFPath);
                cjroneSignature.setCreateDate(new Date());
                cjroneSignature.setCreateId(getUserId());
                cjroneSignature.setType("惠残事项大学生补助");
                cjroneSignature.setTypeId(cjroneblCollegeeduEntity.getId());
                cjroneSignature.setFileName("college_edu_" + cjroneblCollegeeduEntity.getDisableId() + ".pdf");
                cjroneSignature.setAccountId(getUserId().toString());
                cjroneSignature.setAccountName(getUser().getUsername());
                cjroneSignature.setStatus("1");

                cjroneSignatureService.save(cjroneSignature);
                matterApplicationEntity.setPhoto(cjroneSignature.getUrl());
                result_list.add(matterApplicationEntity);
            }

        });
        CjroneWelfareMatterApplicationEntity welfareMatterApplicationEntity = new CjroneWelfareMatterApplicationEntity();
        if (result_list.size() != 0){
            welfareMatterApplicationEntity = result_list.get(0);
        }
        return R.ok().put("signTotal",result_list.size()).put("matterId",welfareMatterApplicationEntity.getMatterId()).put("matterName",welfareMatterApplicationEntity.getMatterName()).put("fileUrl",welfareMatterApplicationEntity.getPhoto());
    }


    /**
     * 大学生补助PDF
     */
    @Login
    @GetMapping("/printCollegeeduPDF/{id}")
    @ApiOperation("大学生补助PDF")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "token", value = "登录token", required = false, paramType = "header")
    })
    public R printCollegeeduPDF(@PathVariable("id") Integer id,@LoginUser UserEntity user) throws IOException {
        System.out.print("id is :"+id);
        Map<String, Object> tmp_params = new HashMap<>();
        tmp_params.put("type","惠残事项大学生补助");
        tmp_params.put("status","1");
        tmp_params.put("type_id",id);
        List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
        if (alive_list.size()>0) {
            CjroneSignatureEntity cjroneSignatureEntity = alive_list.get(0);
            return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", cjroneSignatureEntity.getFileName()).put("signId",cjroneSignatureEntity.getId());

        } else{

            //根据编号获得大学生补助
            CjroneblCollegeeduEntity cjroneblCollegeeduEntity =cjroneblCollegeeduService.getById(id);

            Calendar now = Calendar.getInstance();

            // 原pdf模板路径
            String templatePath = cjroneProperties.getTempletePath()+"大学生补助模板.pdf";
            // 生成的新文件路径
            String newPDFPath = cjroneProperties.getSignaturePath()+"college_edu_"+cjroneblCollegeeduEntity.getDisableId()+".pdf";

            // 获得待生成的实体文件
            Map<String, String> map =new HashMap<String, String>();
            map.put("name", cjroneblCollegeeduEntity.getName()==null?"":cjroneblCollegeeduEntity.getName());
            if(cjroneblCollegeeduEntity.getSex()!=null){
                if("0".equals(cjroneblCollegeeduEntity.getSex())){
                    map.put("sex","女");
                }else{
                    map.put("sex","男");
                }
            }else{
                map.put("sex","");
            }
            map.put("birthday",cjroneblCollegeeduEntity.getBirthday()==null?"":cjroneblCollegeeduEntity.getBirthday());
            map.put("collegeName",cjroneblCollegeeduEntity.getCollegeName()==null?"":cjroneblCollegeeduEntity.getCollegeName());
            map.put("majorName",cjroneblCollegeeduEntity.getMajorName()==null?"":cjroneblCollegeeduEntity.getMajorName());
            map.put("collegeTime",cjroneblCollegeeduEntity.getCollegeTime()==null?"":cjroneblCollegeeduEntity.getCollegeTime());
            map.put("disableId",cjroneblCollegeeduEntity.getDisableId()==null?"":cjroneblCollegeeduEntity.getDisableId());
            map.put("tuition",cjroneblCollegeeduEntity.getTuition()==null?"":cjroneblCollegeeduEntity.getTuition());
            map.put("actuallyTuition",cjroneblCollegeeduEntity.getActuallyTuition()==null?"":cjroneblCollegeeduEntity.getActuallyTuition());
            map.put("accommodationFee",cjroneblCollegeeduEntity.getAccommodationFee()==null?"":cjroneblCollegeeduEntity.getAccommodationFee());
            map.put("actuallyAccommodationFee",cjroneblCollegeeduEntity.getActuallyAccommodationFee()==null?"":cjroneblCollegeeduEntity.getActuallyAccommodationFee());
            map.put("hukouNature",cjroneblCollegeeduEntity.getHukouNature()==null?"":cjroneblCollegeeduEntity.getHukouNature());
            map.put("familyCount",cjroneblCollegeeduEntity.getFamilyCount()==null?"":cjroneblCollegeeduEntity.getFamilyCount());
            map.put("liveAddress",cjroneblCollegeeduEntity.getLiveAddress()==null?"":cjroneblCollegeeduEntity.getLiveAddress());
            map.put("telephone",cjroneblCollegeeduEntity.getTelephone()==null?"":cjroneblCollegeeduEntity.getTelephone());
            map.put("postcode",cjroneblCollegeeduEntity.getPostcode()==null?"":cjroneblCollegeeduEntity.getPostcode());
            map.put("familyFinances",cjroneblCollegeeduEntity.getFamilyFinances()==null?"":cjroneblCollegeeduEntity.getFamilyFinances());
            map.put("familyIncome",cjroneblCollegeeduEntity.getFamilyIncome()==null?"":cjroneblCollegeeduEntity.getFamilyIncome());
            map.put("subsidyReason",cjroneblCollegeeduEntity.getSubsidyReason()==null?"":cjroneblCollegeeduEntity.getSubsidyReason());


            map.put("applyDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");



            FileOutputStream out;
            int num = 1;//页数
            ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
            try {
                out = new FileOutputStream(newPDFPath);// 输出流
                Document doc = new Document();   //新建一个文档
                PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                doc.open();

                for (int i = 0; i < num; i++) {
                    bos[i] = new ByteArrayOutputStream();
                    System.out.println("templatePath--->"+templatePath);
                    PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                    PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                    AcroFields form = stamper.getAcroFields(); //获取文本域
                    // BaseFont
                    // 1、使用iTextAsian.jar中的字体
                    // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                    // 2、使用Windows系统字体(TrueType)
                    // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                    // 3、使用资源字体(ClassPath)
                    // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                    BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                    java.util.Iterator<String> it = form.getFields().keySet().iterator();
                    while (it.hasNext()) {
                        String name = it.next();
                        System.out.println(name + ":"+map.get(name));
                        form.setFieldProperty(name,"textfont",font,null);
                        // form.addSubstitutionFont(font);
                        form.setField(name, map.get(name));
                    }

                    stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                    stamper.close();

                }
                PdfImportedPage page = null;
                for (int i = 0; i < num; i++) {
                    page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                    copy.addPage(page);
                }
                doc.close();
                out.close();
            } catch (IOException e) {
                System.out.println("导出异常");
            } catch (DocumentException e) {
                System.out.println("文档异常");
            }

            CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
            cjroneSignature.setUrl("/college_edu_" + cjroneblCollegeeduEntity.getDisableId() + ".pdf");
            cjroneSignature.setFileActUrl(newPDFPath);
            cjroneSignature.setCreateDate(new Date());
            cjroneSignature.setCreateId(getUserId());
            cjroneSignature.setType("惠残事项大学生补助");
            cjroneSignature.setTypeId(cjroneblCollegeeduEntity.getId());
            cjroneSignature.setFileName("college_edu_" + cjroneblCollegeeduEntity.getDisableId() + ".pdf");
            cjroneSignature.setAccountId(getUserId().toString());
            cjroneSignature.setAccountName(getUser().getUsername());
            cjroneSignature.setStatus("1");

            cjroneSignatureService.save(cjroneSignature);
            return R.ok().put("signTotal",1).put("matterId",id).put("matterName","大学生补助").put("fileUrl",cjroneSignature.getUrl());
        }

    }

    /**
     * 创业补助PDF
     */
    @Login
    @GetMapping("/printBusinessGrantPDF/{id}")
    @ApiOperation("创业补助PDF")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "token", value = "登录token", required = false, paramType = "header")
    })
    public R printBusinessGrantPDF(@PathVariable("id") Integer id,@LoginUser UserEntity user) throws IOException {
        System.out.print("id is :"+id);
        Map<String, Object> tmp_params = new HashMap<>();
        tmp_params.put("type","惠残事项创业补助");
        tmp_params.put("status","1");
        tmp_params.put("type_id",id);
        List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
        if (alive_list.size()>0) {
            CjroneSignatureEntity cjroneSignatureEntity = alive_list.get(0);
            return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", cjroneSignatureEntity.getFileName()).put("signId",cjroneSignatureEntity.getId());

        } else{

            //根据编号获得残疾人创业补助
            CjroneblBusinessGrantEntity cjroneblBusinessGrantEntity =cjroneblBusinessGrantService.getById(id);

            Calendar now = Calendar.getInstance();

            // 原pdf模板路径
            String templatePath = cjroneProperties.getTempletePath()+"创业模板.pdf";
            // 生成的新文件路径
            String newPDFPath = cjroneProperties.getSignaturePath()+"business_grant_"+cjroneblBusinessGrantEntity.getDisableId()+".pdf";

            // 获得待生成的实体文件
            Map<String, String> map =new HashMap<String, String>();
            map.put("name", cjroneblBusinessGrantEntity.getName()==null?"":cjroneblBusinessGrantEntity.getName());
            if(cjroneblBusinessGrantEntity.getSex()!=null){
                if("0".equals(cjroneblBusinessGrantEntity.getSex())){
                    map.put("sex","女");
                }else{
                    map.put("sex","男");
                }
            }else{
                map.put("sex","");
            }
            map.put("age",cjroneblBusinessGrantEntity.getAge()==null?"":cjroneblBusinessGrantEntity.getAge().toString());
            map.put("disableId",cjroneblBusinessGrantEntity.getDisableId()==null?"":cjroneblBusinessGrantEntity.getDisableId());
            map.put("telephone",cjroneblBusinessGrantEntity.getTelephone()==null?"":cjroneblBusinessGrantEntity.getTelephone());
            map.put("familyEconomy",cjroneblBusinessGrantEntity.getFamilyEconomy()==null?"":cjroneblBusinessGrantEntity.getFamilyEconomy());
            map.put("educationDegree",cjroneblBusinessGrantEntity.getEducationDegree()==null?"":cjroneblBusinessGrantEntity.getEducationDegree());
            map.put("liveAddress",cjroneblBusinessGrantEntity.getLiveAddress()==null?"":cjroneblBusinessGrantEntity.getLiveAddress());
            map.put("startTime",cjroneblBusinessGrantEntity.getStartTime()==null?"":cjroneblBusinessGrantEntity.getStartTime());
            map.put("manageAddress",cjroneblBusinessGrantEntity.getManageAddress()==null?"":cjroneblBusinessGrantEntity.getManageAddress());
            map.put("manageRange",cjroneblBusinessGrantEntity.getManageRange()==null?"":cjroneblBusinessGrantEntity.getManageRange());
            map.put("isCollege",cjroneblBusinessGrantEntity.getIsCollege()==null?"":cjroneblBusinessGrantEntity.getIsCollege());
            map.put("subsidyMoney",cjroneblBusinessGrantEntity.getSubsidyMoney()==null?"":cjroneblBusinessGrantEntity.getSubsidyMoney());
            map.put("subsidyReason",cjroneblBusinessGrantEntity.getSubsidyReason()==null?"":cjroneblBusinessGrantEntity.getSubsidyReason());
            map.put("isEmployment",cjroneblBusinessGrantEntity.getIsEmployment()==null?"":cjroneblBusinessGrantEntity.getIsEmployment());
            map.put("isFirsttime",cjroneblBusinessGrantEntity.getIsFirsttime()==null?"":cjroneblBusinessGrantEntity.getIsFirsttime());
            map.put("isSixManage",cjroneblBusinessGrantEntity.getIsSixManage()==null?"":cjroneblBusinessGrantEntity.getIsSixManage());


            map.put("applyDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");



            FileOutputStream out;
            int num = 1;//页数
            ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
            try {
                out = new FileOutputStream(newPDFPath);// 输出流
                Document doc = new Document();   //新建一个文档
                PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                doc.open();

                for (int i = 0; i < num; i++) {
                    bos[i] = new ByteArrayOutputStream();
                    System.out.println("templatePath--->"+templatePath);
                    PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                    PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                    AcroFields form = stamper.getAcroFields(); //获取文本域
                    // BaseFont
                    // 1、使用iTextAsian.jar中的字体
                    // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                    // 2、使用Windows系统字体(TrueType)
                    // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                    // 3、使用资源字体(ClassPath)
                    // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                    BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                    java.util.Iterator<String> it = form.getFields().keySet().iterator();
                    while (it.hasNext()) {
                        String name = it.next();
                        System.out.println(name + ":"+map.get(name));
                        form.setFieldProperty(name,"textfont",font,null);
                        // form.addSubstitutionFont(font);
                        form.setField(name, map.get(name));
                    }

                    stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                    stamper.close();

                }
                PdfImportedPage page = null;
                for (int i = 0; i < num; i++) {
                    page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                    copy.addPage(page);
                }
                doc.close();
                out.close();
            } catch (IOException e) {
                System.out.println("导出异常");
            } catch (DocumentException e) {
                System.out.println("文档异常");
            }

            CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
            cjroneSignature.setUrl("/business_grant_" + cjroneblBusinessGrantEntity.getDisableId() + ".pdf");
            cjroneSignature.setFileActUrl(newPDFPath);
            cjroneSignature.setCreateDate(new Date());
            cjroneSignature.setCreateId(getUserId());
            cjroneSignature.setType("惠残事项创业补助");
            cjroneSignature.setTypeId(cjroneblBusinessGrantEntity.getId());
            cjroneSignature.setFileName("business_grant_" + cjroneblBusinessGrantEntity.getDisableId() + ".pdf");
            cjroneSignature.setAccountId(getUserId().toString());
            cjroneSignature.setAccountName(getUser().getUsername());
            cjroneSignature.setStatus("1");

            cjroneSignatureService.save(cjroneSignature);
            return R.ok().put("signTotal",1).put("matterId",id).put("matterName","创业补助").put("fileUrl",cjroneSignature.getUrl());
        }

    }

    /**
     * 康复补助PDF
     */
    @Login
    @GetMapping("/printRehabilitationSubsidyPDF/{id}")
    @ApiOperation("康复补助PDF")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "token", value = "登录token", required = false, paramType = "header")
    })
    public R printRehabilitationSubsidyPDF(@PathVariable("id") Integer id,@LoginUser UserEntity user) throws IOException {
        System.out.print("id is :"+id);
        Map<String, Object> tmp_params = new HashMap<>();
        tmp_params.put("type","惠残事项康复补助");
        tmp_params.put("status","1");
        tmp_params.put("type_id",id);
        List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
        if (alive_list.size()>0) {
            CjroneSignatureEntity cjroneSignatureEntity = alive_list.get(0);
            return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", cjroneSignatureEntity.getFileName()).put("signId",cjroneSignatureEntity.getId());

        } else{

            //根据编号获得残疾人临时救助
            CjroneblRehabilitationSubsidyEntity cjroneblRehabilitationSubsidyEntity =cjroneblRehabilitationSubsidyService.getById(id);

            Calendar now = Calendar.getInstance();

            // 原pdf模板路径
            String templatePath = cjroneProperties.getTempletePath()+"康复补助模板.pdf";
            // 生成的新文件路径
            String newPDFPath = cjroneProperties.getSignaturePath()+"rehabilitation_subsidy_"+cjroneblRehabilitationSubsidyEntity.getIdCard()+".pdf";

            // 获得待生成的实体文件
            Map<String, String> map =new HashMap<String, String>();
            map.put("name", cjroneblRehabilitationSubsidyEntity.getName()==null?"":cjroneblRehabilitationSubsidyEntity.getName());
            if(cjroneblRehabilitationSubsidyEntity.getSex()!=null){
                if("0".equals(cjroneblRehabilitationSubsidyEntity.getSex())){
                    map.put("sex","女");
                }else{
                    map.put("sex","男");
                }
            }else{
                map.put("sex","");
            }
            map.put("disabilityType",cjroneblRehabilitationSubsidyEntity.getDisabilityType()==null?"":cjroneblRehabilitationSubsidyEntity.getDisabilityType());
            map.put("disabilityDegree",cjroneblRehabilitationSubsidyEntity.getDisabilityDegree()==null?"":cjroneblRehabilitationSubsidyEntity.getDisabilityDegree());
            map.put("disableId",cjroneblRehabilitationSubsidyEntity.getDisabileId()==null?"":cjroneblRehabilitationSubsidyEntity.getDisabileId());
            map.put("idCard",cjroneblRehabilitationSubsidyEntity.getIdCard()==null?"":cjroneblRehabilitationSubsidyEntity.getIdCard());
            map.put("telephone",cjroneblRehabilitationSubsidyEntity.getTelephone()==null?"":cjroneblRehabilitationSubsidyEntity.getTelephone());
            map.put("liveAddress",cjroneblRehabilitationSubsidyEntity.getLiveAddress()==null?"":cjroneblRehabilitationSubsidyEntity.getLiveAddress());
            map.put("familyEconomy",cjroneblRehabilitationSubsidyEntity.getFamilyEconomy()==null?"":cjroneblRehabilitationSubsidyEntity.getFamilyEconomy());

            map.put("applyDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");



            FileOutputStream out;
            int num = 1;//页数
            ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
            try {
                out = new FileOutputStream(newPDFPath);// 输出流
                Document doc = new Document();   //新建一个文档
                PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                doc.open();

                for (int i = 0; i < num; i++) {
                    bos[i] = new ByteArrayOutputStream();
                    System.out.println("templatePath--->"+templatePath);
                    PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                    PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                    AcroFields form = stamper.getAcroFields(); //获取文本域
                    // BaseFont
                    // 1、使用iTextAsian.jar中的字体
                    // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                    // 2、使用Windows系统字体(TrueType)
                    // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                    // 3、使用资源字体(ClassPath)
                    // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                    BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                    java.util.Iterator<String> it = form.getFields().keySet().iterator();
                    while (it.hasNext()) {
                        String name = it.next();
                        System.out.println(name + ":"+map.get(name));
                        form.setFieldProperty(name,"textfont",font,null);
                        // form.addSubstitutionFont(font);
                        form.setField(name, map.get(name));
                    }

                    stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                    stamper.close();

                }
                PdfImportedPage page = null;
                for (int i = 0; i < num; i++) {
                    page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                    copy.addPage(page);
                }
                doc.close();
                out.close();
            } catch (IOException e) {
                System.out.println("导出异常");
            } catch (DocumentException e) {
                System.out.println("文档异常");
            }

            CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
            cjroneSignature.setUrl("/rehabilitation_subsidy_" + cjroneblRehabilitationSubsidyEntity.getIdCard() + ".pdf");
            cjroneSignature.setFileActUrl(newPDFPath);
            cjroneSignature.setCreateDate(new Date());
            cjroneSignature.setCreateId(getUserId());
            cjroneSignature.setType("惠残事项康复补助");
            cjroneSignature.setTypeId(cjroneblRehabilitationSubsidyEntity.getId());
            cjroneSignature.setFileName("rehabilitation_subsidy_" + cjroneblRehabilitationSubsidyEntity.getIdCard() + ".pdf");
            cjroneSignature.setAccountId(getUserId().toString());
            cjroneSignature.setAccountName(getUser().getUsername());
            cjroneSignature.setStatus("1");

            cjroneSignatureService.save(cjroneSignature);

            return R.ok().put("signTotal",1).put("matterId",id).put("matterName","康复补助").put("fileUrl",cjroneSignature.getUrl());

        }

    }


    /**
     * 残疾人临时救助PDF
     */
    @Login
    @GetMapping("/printTemporaryAssistancePDF/{id}")
    @ApiOperation("残疾人临时救助PDF")
    public R printTemporaryAssistancePDF(@PathVariable("id") Integer id,@LoginUser UserEntity user) throws IOException {
        System.out.print("id is :"+id);
        Map<String, Object> tmp_params = new HashMap<>();
        tmp_params.put("type","惠残事项残疾人临时救助");
        tmp_params.put("status","1");
        tmp_params.put("type_id",id);
        List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
        if (alive_list.size()>0) {
            CjroneSignatureEntity cjroneSignatureEntity = alive_list.get(0);
            return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", cjroneSignatureEntity.getFileName()).put("signId",cjroneSignatureEntity.getId());

        } else{

            //根据编号获得残疾人临时救助
            CjroneblTemporaryAssistanceEntity cjroneblTemporaryAssistanceEntity =cjroneblTemporaryAssistanceService.getById(id);

            Calendar now = Calendar.getInstance();

            // 原pdf模板路径
            String templatePath = cjroneProperties.getTempletePath()+"残疾人临时救助模板.pdf";
            // 生成的新文件路径
            String newPDFPath = cjroneProperties.getSignaturePath()+"temporary_assistance_"+cjroneblTemporaryAssistanceEntity.getIdCard()+".pdf";

            // 获得待生成的实体文件
            Map<String, String> map =new HashMap<String, String>();
            map.put("name", cjroneblTemporaryAssistanceEntity.getName()==null?"":cjroneblTemporaryAssistanceEntity.getName());
            if(cjroneblTemporaryAssistanceEntity.getSex()!=null){
                if("0".equals(cjroneblTemporaryAssistanceEntity.getSex())){
                    map.put("sex","女");
                }else{
                    map.put("sex","男");
                }
            }else{
                map.put("sex","");
            }
            map.put("disabilityType",cjroneblTemporaryAssistanceEntity.getDisabilityType()==null?"":cjroneblTemporaryAssistanceEntity.getDisabilityType());
            map.put("disabilityDegree",cjroneblTemporaryAssistanceEntity.getDisabilityDegree()==null?"":cjroneblTemporaryAssistanceEntity.getDisabilityDegree());
            map.put("disableId",cjroneblTemporaryAssistanceEntity.getDisableId()==null?"":cjroneblTemporaryAssistanceEntity.getDisableId());
            map.put("telephone",cjroneblTemporaryAssistanceEntity.getTelephone()==null?"":cjroneblTemporaryAssistanceEntity.getTelephone());
            map.put("liveAddress",cjroneblTemporaryAssistanceEntity.getLiveAddress()==null?"":cjroneblTemporaryAssistanceEntity.getLiveAddress());
            map.put("familyEconomy",cjroneblTemporaryAssistanceEntity.getFamilyEconomy()==null?"":cjroneblTemporaryAssistanceEntity.getFamilyEconomy());
            if("1".equals(cjroneblTemporaryAssistanceEntity.getMingzhenSubsidy())){
                map.put("mingzhenSubsidy","是");
            }else{
                map.put("mingzhenSubsidy","否");
            }

            map.put("payMoney",cjroneblTemporaryAssistanceEntity.getPayMoney()==null?"":cjroneblTemporaryAssistanceEntity.getPayMoney());
            map.put("subsidyMoney",cjroneblTemporaryAssistanceEntity.getSubsidyMoney()==null?"":cjroneblTemporaryAssistanceEntity.getSubsidyMoney());
            map.put("applyReason",cjroneblTemporaryAssistanceEntity.getApplyReason()==null?"":cjroneblTemporaryAssistanceEntity.getApplyReason());
            map.put("applyDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");



            FileOutputStream out;
            int num = 1;//页数
            ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
            try {
                out = new FileOutputStream(newPDFPath);// 输出流
                Document doc = new Document();   //新建一个文档
                PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                doc.open();

                for (int i = 0; i < num; i++) {
                    bos[i] = new ByteArrayOutputStream();
                    System.out.println("templatePath--->"+templatePath);
                    PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                    PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                    AcroFields form = stamper.getAcroFields(); //获取文本域
                    // BaseFont
                    // 1、使用iTextAsian.jar中的字体
                    // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                    // 2、使用Windows系统字体(TrueType)
                    // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                    // 3、使用资源字体(ClassPath)
                    // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                    BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                    java.util.Iterator<String> it = form.getFields().keySet().iterator();
                    while (it.hasNext()) {
                        String name = it.next();
                        System.out.println(name + ":"+map.get(name));
                        form.setFieldProperty(name,"textfont",font,null);
                        // form.addSubstitutionFont(font);
                        form.setField(name, map.get(name));
                    }

                    stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                    stamper.close();

                }
                PdfImportedPage page = null;
                for (int i = 0; i < num; i++) {
                    page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                    copy.addPage(page);
                }
                doc.close();
                out.close();
            } catch (IOException e) {
                System.out.println("导出异常");
            } catch (DocumentException e) {
                System.out.println("文档异常");
            }

            CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
            cjroneSignature.setUrl("/temporary_assistance_" + cjroneblTemporaryAssistanceEntity.getIdCard() + ".pdf");
            cjroneSignature.setFileActUrl(newPDFPath);
            cjroneSignature.setCreateDate(new Date());
            cjroneSignature.setCreateId(getUserId());
            cjroneSignature.setType("惠残事项残疾人临时救助");
            cjroneSignature.setTypeId(cjroneblTemporaryAssistanceEntity.getId());
            cjroneSignature.setFileName("temporary_assistance_" + cjroneblTemporaryAssistanceEntity.getIdCard() + ".pdf");
            cjroneSignature.setAccountId(getUserId().toString());
            cjroneSignature.setAccountName(getUser().getUsername());
            cjroneSignature.setStatus("1");

            cjroneSignatureService.save(cjroneSignature);
            return R.ok().put("signTotal",1).put("matterId",id).put("matterName","残疾人临时救助").put("fileUrl",cjroneSignature.getUrl());
        }

    }

    /**
     * 城乡基本医疗保险补助PDF
     */
    @Login
    @GetMapping("/printCxjmyiliaoPDF/{id}")
    @ApiOperation("城乡基本医疗保险补助PDF")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "token", value = "登录token", required = false, paramType = "header")
    })
    public R printCxjmyiliaoPDF(@PathVariable("id") Integer id,@LoginUser UserEntity user) throws IOException {
        System.out.print("id is :"+id);
        Map<String, Object> tmp_params = new HashMap<>();
        tmp_params.put("type","惠残事项城乡基本医疗保险补助");
        tmp_params.put("status","1");
        tmp_params.put("type_id",id);
        List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
        if (alive_list.size()>0) {
            CjroneSignatureEntity cjroneSignatureEntity = alive_list.get(0);
            return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", cjroneSignatureEntity.getFileName()).put("signId",cjroneSignatureEntity.getId());

        } else{

            //根据编号获得职工基本医疗保险补助
            CjroneblCxjmyiliaoEntity cjroneblCxjmyiliaoEntity =cjroneblCxjmyiliaoService.getById(id);

            Calendar now = Calendar.getInstance();

            // 原pdf模板路径
            String templatePath = cjroneProperties.getTempletePath()+"城乡基本医疗保险补助模板.pdf";
            // 生成的新文件路径
            String newPDFPath = cjroneProperties.getSignaturePath()+"cxyiliao_"+cjroneblCxjmyiliaoEntity.getDisableId()+".pdf";

            // 获得待生成的实体文件
            Map<String, String> map =new HashMap<String, String>();
            map.put("name", cjroneblCxjmyiliaoEntity.getName()==null?"":cjroneblCxjmyiliaoEntity.getName());
            if(cjroneblCxjmyiliaoEntity.getSex()!=null){
                if("0".equals(cjroneblCxjmyiliaoEntity.getSex())){
                    map.put("sex","女");
                }else{
                    map.put("sex","男");
                }
            }else{
                map.put("sex","");
            }
            map.put("age",cjroneblCxjmyiliaoEntity.getAge()==null?"":cjroneblCxjmyiliaoEntity.getAge().toString());
            map.put("disableId",cjroneblCxjmyiliaoEntity.getDisableId()==null?"":cjroneblCxjmyiliaoEntity.getDisableId());
            map.put("telephone",cjroneblCxjmyiliaoEntity.getTelephone()==null?"":cjroneblCxjmyiliaoEntity.getTelephone());
            map.put("insuredStatus",cjroneblCxjmyiliaoEntity.getInsuredStatus()==null?"":cjroneblCxjmyiliaoEntity.getInsuredStatus());
            map.put("liveAddress",cjroneblCxjmyiliaoEntity.getLiveAddress()==null?"":cjroneblCxjmyiliaoEntity.getLiveAddress());
            map.put("otherSubsidy",cjroneblCxjmyiliaoEntity.getOtherSubsidy()==null?"":cjroneblCxjmyiliaoEntity.getOtherSubsidy());
            map.put("seTime2",cjroneblCxjmyiliaoEntity.getSeTime()==null?"":cjroneblCxjmyiliaoEntity.getSeTime());
            map.put("payMoney2",cjroneblCxjmyiliaoEntity.getPayMoney()==null?"":cjroneblCxjmyiliaoEntity.getPayMoney());
            map.put("insuredTypeY","城乡居民基本医疗保险");
            map.put("applicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");



            FileOutputStream out;
            int num = 1;//页数
            ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
            try {
                out = new FileOutputStream(newPDFPath);// 输出流
                Document doc = new Document();   //新建一个文档
                PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                doc.open();

                for (int i = 0; i < num; i++) {
                    bos[i] = new ByteArrayOutputStream();
                    System.out.println("templatePath--->"+templatePath);
                    PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                    PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                    AcroFields form = stamper.getAcroFields(); //获取文本域
                    // BaseFont
                    // 1、使用iTextAsian.jar中的字体
                    // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                    // 2、使用Windows系统字体(TrueType)
                    // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                    // 3、使用资源字体(ClassPath)
                    // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                    BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                    java.util.Iterator<String> it = form.getFields().keySet().iterator();
                    while (it.hasNext()) {
                        String name = it.next();
                        System.out.println(name + ":"+map.get(name));
                        form.setFieldProperty(name,"textfont",font,null);
                        // form.addSubstitutionFont(font);
                        form.setField(name, map.get(name));
                    }

                    stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                    stamper.close();

                }
                PdfImportedPage page = null;
                for (int i = 0; i < num; i++) {
                    page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                    copy.addPage(page);
                }
                doc.close();
                out.close();
            } catch (IOException e) {
                System.out.println("导出异常");
            } catch (DocumentException e) {
                System.out.println("文档异常");
            }

            CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
            cjroneSignature.setUrl("/cxyiliao_" + cjroneblCxjmyiliaoEntity.getDisableId() + ".pdf");
            cjroneSignature.setFileActUrl(newPDFPath);
            cjroneSignature.setCreateDate(new Date());
            cjroneSignature.setCreateId(getUserId());
            cjroneSignature.setType("惠残事项城乡基本医疗保险补助");
            cjroneSignature.setTypeId(cjroneblCxjmyiliaoEntity.getId());
            cjroneSignature.setFileName("cxyiliao_" + cjroneblCxjmyiliaoEntity.getDisableId() + ".pdf");
            cjroneSignature.setAccountId(getUserId().toString());
            cjroneSignature.setAccountName(getUser().getUsername());
            cjroneSignature.setStatus("1");

            cjroneSignatureService.save(cjroneSignature);
            return R.ok().put("signTotal",1).put("matterId",id).put("matterName","城乡基本医疗保险补助").put("fileUrl",cjroneSignature.getUrl());
        }
    }

    /**
     * 城乡居民养老保险补助PDF
     */
    @Login
    @GetMapping("/printCxjmyanglaoPDF/{id}")
    @ApiOperation("城乡居民养老保险补助PDF")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "token", value = "登录token", required = false, paramType = "header")
    })
    public R printCxjmyanglaoPDF(@PathVariable("id") Integer id,@LoginUser UserEntity user) throws IOException {
        System.out.print("id is :"+id);
        Map<String, Object> tmp_params = new HashMap<>();
        tmp_params.put("type","惠残事项城乡居民养老保险补助");
        tmp_params.put("status","1");
        tmp_params.put("type_id",id);
        List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
        if (alive_list.size()>0) {
            CjroneSignatureEntity cjroneSignatureEntity = alive_list.get(0);
            return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", cjroneSignatureEntity.getFileName()).put("signId",cjroneSignatureEntity.getId());

        } else{
            //根据编号获得城乡居民养老保险补助
            CjroneblCxjmyanglaoEntity cjroneblCxjmyanglaoEntity = cjroneblCxjmyanglaoService.getById(id);

            Calendar now = Calendar.getInstance();

            // 原pdf模板路径
            String templatePath = cjroneProperties.getTempletePath()+"城乡居民养老保险补助模板.pdf";
            // 生成的新文件路径
            String newPDFPath = cjroneProperties.getSignaturePath()+"cxyanglao_"+cjroneblCxjmyanglaoEntity.getDisableId()+".pdf";

            // 获得待生成的实体文件
            Map<String, String> map =new HashMap<String, String>();
            map.put("name", cjroneblCxjmyanglaoEntity.getName()==null?"":cjroneblCxjmyanglaoEntity.getName());
            if(cjroneblCxjmyanglaoEntity.getSex()!=null){
                if("0".equals(cjroneblCxjmyanglaoEntity.getSex())){
                    map.put("sex","女");
                }else{
                    map.put("sex","男");
                }
            }else{
                map.put("sex","");
            }
            map.put("age",cjroneblCxjmyanglaoEntity.getAge()==null?"":cjroneblCxjmyanglaoEntity.getAge().toString());
            map.put("disableId",cjroneblCxjmyanglaoEntity.getDisableId()==null?"":cjroneblCxjmyanglaoEntity.getDisableId());
            map.put("telephone",cjroneblCxjmyanglaoEntity.getTelephone()==null?"":cjroneblCxjmyanglaoEntity.getTelephone());
            map.put("insuredStatus",cjroneblCxjmyanglaoEntity.getInsuredStatus()==null?"":cjroneblCxjmyanglaoEntity.getInsuredStatus());
            map.put("liveAddress",cjroneblCxjmyanglaoEntity.getLiveAddress()==null?"":cjroneblCxjmyanglaoEntity.getLiveAddress());
            map.put("otherSubsidy",cjroneblCxjmyanglaoEntity.getOtherSubsidy()==null?"":cjroneblCxjmyanglaoEntity.getOtherSubsidy());
            map.put("seTime1",cjroneblCxjmyanglaoEntity.getSeTime()==null?"":cjroneblCxjmyanglaoEntity.getSeTime());
            map.put("payMoney1",cjroneblCxjmyanglaoEntity.getPayMoney()==null?"":cjroneblCxjmyanglaoEntity.getPayMoney());
            map.put("insuredTypeYL","城乡居民养老保险");
            map.put("applicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");



            FileOutputStream out;
            int num = 1;//页数
            ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
            try {
                out = new FileOutputStream(newPDFPath);// 输出流
                Document doc = new Document();   //新建一个文档
                PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                doc.open();

                for (int i = 0; i < num; i++) {
                    bos[i] = new ByteArrayOutputStream();
                    System.out.println("templatePath--->"+templatePath);
                    PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                    PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                    AcroFields form = stamper.getAcroFields(); //获取文本域
                    // BaseFont
                    // 1、使用iTextAsian.jar中的字体
                    // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                    // 2、使用Windows系统字体(TrueType)
                    // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                    // 3、使用资源字体(ClassPath)
                    // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                    BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                    java.util.Iterator<String> it = form.getFields().keySet().iterator();
                    while (it.hasNext()) {
                        String name = it.next();
                        System.out.println(name + ":"+map.get(name));
                        form.setFieldProperty(name,"textfont",font,null);
                        // form.addSubstitutionFont(font);
                        form.setField(name, map.get(name));
                    }

                    stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                    stamper.close();

                }
                PdfImportedPage page = null;
                for (int i = 0; i < num; i++) {
                    page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                    copy.addPage(page);
                }
                doc.close();
                out.close();
            } catch (IOException e) {
                System.out.println("导出异常");
            } catch (DocumentException e) {
                System.out.println("文档异常");
            }

            CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
            cjroneSignature.setUrl("/cxyanglao_" + cjroneblCxjmyanglaoEntity.getDisableId() + ".pdf");
            cjroneSignature.setFileActUrl(newPDFPath);
            cjroneSignature.setCreateDate(new Date());
            cjroneSignature.setCreateId(getUserId());
            cjroneSignature.setType("惠残事项城乡居民养老保险");
            cjroneSignature.setTypeId(cjroneblCxjmyanglaoEntity.getId());
            cjroneSignature.setFileName("zgyanglao_" + cjroneblCxjmyanglaoEntity.getDisableId() + ".pdf");
            cjroneSignature.setAccountId(getUserId().toString());
            cjroneSignature.setAccountName(getUser().getUsername());
            cjroneSignature.setStatus("1");

            cjroneSignatureService.save(cjroneSignature);
            return R.ok().put("signTotal",1).put("matterId",id).put("matterName","城乡居民养老保险补助").put("fileUrl",cjroneSignature.getUrl());
        }

    }

    /**
     * 职工基本养老保险补助PDF
     */
    @Login
    @GetMapping("/printZgjbyanglaoPDF/{id}")
    @ApiOperation("职工基本养老保险补助PDF")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "token", value = "登录token", required = false, paramType = "header")
    })
    public R printZgjbyanglaoPDF(@PathVariable("id") Integer id,@LoginUser UserEntity user) throws IOException {
        System.out.print("id is :"+id);
        Map<String, Object> tmp_params = new HashMap<>();
        tmp_params.put("type","惠残事项职工基本养老保险补助");
        tmp_params.put("status","1");
        tmp_params.put("type_id",id);
        List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
        if (alive_list.size()>0) {
            CjroneSignatureEntity cjroneSignatureEntity = alive_list.get(0);
            return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", cjroneSignatureEntity.getFileName()).put("signId",cjroneSignatureEntity.getId());

        } else{
            //根据编号获得职工基本养老保险补助
            CjroneblZgjbyanglaoEntity cjroneblZgjbyanglaoEntity =cjroneblZgjbyanglaoService.getById(id);

            Calendar now = Calendar.getInstance();

            // 原pdf模板路径
            String templatePath = cjroneProperties.getTempletePath()+"职工基本养老保险补助模板.pdf";
            // 生成的新文件路径
            String newPDFPath = cjroneProperties.getSignaturePath()+"zgyanglao_"+cjroneblZgjbyanglaoEntity.getDisableId()+".pdf";

            // 获得待生成的实体文件
            Map<String, String> map =new HashMap<String, String>();
            map.put("name", cjroneblZgjbyanglaoEntity.getName()==null?"":cjroneblZgjbyanglaoEntity.getName());
            if(cjroneblZgjbyanglaoEntity.getSex()!=null){
                if("0".equals(cjroneblZgjbyanglaoEntity.getSex())){
                    map.put("sex","女");
                }else{
                    map.put("sex","男");
                }
            }else{
                map.put("sex","");
            }
            map.put("age",cjroneblZgjbyanglaoEntity.getAge()==null?"":cjroneblZgjbyanglaoEntity.getAge().toString());
            map.put("disableId",cjroneblZgjbyanglaoEntity.getDisableId()==null?"":cjroneblZgjbyanglaoEntity.getDisableId());
            map.put("telephone",cjroneblZgjbyanglaoEntity.getTelephone()==null?"":cjroneblZgjbyanglaoEntity.getTelephone());
            map.put("insuredStatus",cjroneblZgjbyanglaoEntity.getInsuredStatus()==null?"":cjroneblZgjbyanglaoEntity.getInsuredStatus());
            map.put("liveAddress",cjroneblZgjbyanglaoEntity.getLiveAddress()==null?"":cjroneblZgjbyanglaoEntity.getLiveAddress());
            map.put("otherSubsidy",cjroneblZgjbyanglaoEntity.getOtherSubsidy()==null?"":cjroneblZgjbyanglaoEntity.getOtherSubsidy());
            map.put("seTime1",cjroneblZgjbyanglaoEntity.getSeTime()==null?"":cjroneblZgjbyanglaoEntity.getSeTime());
            map.put("payMoney1",cjroneblZgjbyanglaoEntity.getPayMoney()==null?"":cjroneblZgjbyanglaoEntity.getPayMoney());
            map.put("insuredTypeYL","职工基本养老保险");
            map.put("applicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");



            FileOutputStream out;
            int num = 1;//页数
            ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
            try {
                out = new FileOutputStream(newPDFPath);// 输出流
                Document doc = new Document();   //新建一个文档
                PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                doc.open();

                for (int i = 0; i < num; i++) {
                    bos[i] = new ByteArrayOutputStream();
                    System.out.println("templatePath--->"+templatePath);
                    PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                    PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                    AcroFields form = stamper.getAcroFields(); //获取文本域
                    // BaseFont
                    // 1、使用iTextAsian.jar中的字体
                    // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                    // 2、使用Windows系统字体(TrueType)
                    // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                    // 3、使用资源字体(ClassPath)
                    // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                    BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                    java.util.Iterator<String> it = form.getFields().keySet().iterator();
                    while (it.hasNext()) {
                        String name = it.next();
                        System.out.println(name + ":"+map.get(name));
                        form.setFieldProperty(name,"textfont",font,null);
                        // form.addSubstitutionFont(font);
                        form.setField(name, map.get(name));
                    }

                    stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                    stamper.close();

                }
                PdfImportedPage page = null;
                for (int i = 0; i < num; i++) {
                    page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                    copy.addPage(page);
                }
                doc.close();
                out.close();
            } catch (IOException e) {
                System.out.println("导出异常");
            } catch (DocumentException e) {
                System.out.println("文档异常");
            }

            CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
            cjroneSignature.setUrl("/zgyanglao_" + cjroneblZgjbyanglaoEntity.getDisableId() + ".pdf");
            cjroneSignature.setFileActUrl(newPDFPath);
            cjroneSignature.setCreateDate(new Date());
            cjroneSignature.setCreateId(getUserId());
            cjroneSignature.setType("惠残事项职工基本养老保险");
            cjroneSignature.setTypeId(cjroneblZgjbyanglaoEntity.getId());
            cjroneSignature.setFileName("zgyanglao_" + cjroneblZgjbyanglaoEntity.getDisableId() + ".pdf");
            cjroneSignature.setAccountId(getUserId().toString());
            cjroneSignature.setAccountName(getUser().getUsername());
            cjroneSignature.setStatus("1");
            cjroneSignatureService.save(cjroneSignature);

            return R.ok().put("signTotal",1).put("matterId",id).put("matterName","职工基本养老保险补助").put("fileUrl",cjroneSignature.getUrl());

        }

    }

    /**
     * 职工基本医疗保险补助PDF
     */
    @Login
    @GetMapping("/printZgjbyiliaoPDF/{id}")
    @ApiOperation("职工基本医疗保险补助PDF")
    public R printZgjbyiliaoPDF(@PathVariable("id") Integer id,@LoginUser UserEntity user) throws IOException {
        System.out.print("id is :"+id);
        Map<String, Object> tmp_params = new HashMap<>();
        tmp_params.put("type","惠残事项职工基本医疗保险补助");
        tmp_params.put("status","1");
        tmp_params.put("type_id",id);
        List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
        if (alive_list.size()>0) {
            CjroneSignatureEntity cjroneSignatureEntity = alive_list.get(0);
            return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", cjroneSignatureEntity.getFileName()).put("signId",cjroneSignatureEntity.getId());

        } else{
            //根据编号获得职工基本医疗保险补助
            CjroneblZgjbyiliaoEntity cjroneblZgjbyiliaoEntity =cjroneblZgjbyiliaoService.getById(id);

            Calendar now = Calendar.getInstance();

            // 原pdf模板路径
            String templatePath = cjroneProperties.getTempletePath()+"职工基本医疗保险补助模板.pdf";
            // 生成的新文件路径
            String newPDFPath = cjroneProperties.getSignaturePath()+"zgyiliao_"+cjroneblZgjbyiliaoEntity.getDisableId()+".pdf";

            // 获得待生成的实体文件
            Map<String, String> map =new HashMap<String, String>();
            map.put("name", cjroneblZgjbyiliaoEntity.getName()==null?"":cjroneblZgjbyiliaoEntity.getName());
            if(cjroneblZgjbyiliaoEntity.getSex()!=null){
                if("0".equals(cjroneblZgjbyiliaoEntity.getSex())){
                    map.put("sex","女");
                }else{
                    map.put("sex","男");
                }
            }else{
                map.put("sex","");
            }
            map.put("age",cjroneblZgjbyiliaoEntity.getAge()==null?"":cjroneblZgjbyiliaoEntity.getAge().toString());
            map.put("disableId",cjroneblZgjbyiliaoEntity.getDisableId()==null?"":cjroneblZgjbyiliaoEntity.getDisableId());
            map.put("telephone",cjroneblZgjbyiliaoEntity.getTelephone()==null?"":cjroneblZgjbyiliaoEntity.getTelephone());
            map.put("insuredStatus",cjroneblZgjbyiliaoEntity.getInsuredStatus()==null?"":cjroneblZgjbyiliaoEntity.getInsuredStatus());
            map.put("liveAddress",cjroneblZgjbyiliaoEntity.getLiveAddress()==null?"":cjroneblZgjbyiliaoEntity.getLiveAddress());
            map.put("otherSubsidy",cjroneblZgjbyiliaoEntity.getOtherSubsidy()==null?"":cjroneblZgjbyiliaoEntity.getOtherSubsidy());
            map.put("seTime2",cjroneblZgjbyiliaoEntity.getSeTime()==null?"":cjroneblZgjbyiliaoEntity.getSeTime());
            map.put("payMoney2",cjroneblZgjbyiliaoEntity.getPayMoney()==null?"":cjroneblZgjbyiliaoEntity.getPayMoney());
            map.put("insuredTypeY","基本医疗保险");
            map.put("applicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");



            FileOutputStream out;
            int num = 1;//页数
            ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
            try {
                out = new FileOutputStream(newPDFPath);// 输出流
                Document doc = new Document();   //新建一个文档
                PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                doc.open();

                for (int i = 0; i < num; i++) {
                    bos[i] = new ByteArrayOutputStream();
                    System.out.println("templatePath--->"+templatePath);
                    PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                    PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                    AcroFields form = stamper.getAcroFields(); //获取文本域
                    // BaseFont
                    // 1、使用iTextAsian.jar中的字体
                    // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                    // 2、使用Windows系统字体(TrueType)
                    // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                    // 3、使用资源字体(ClassPath)
                    // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                    BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                    java.util.Iterator<String> it = form.getFields().keySet().iterator();
                    while (it.hasNext()) {
                        String name = it.next();
                        System.out.println(name + ":"+map.get(name));
                        form.setFieldProperty(name,"textfont",font,null);
                        // form.addSubstitutionFont(font);
                        form.setField(name, map.get(name));
                    }

                    stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                    stamper.close();

                }
                PdfImportedPage page = null;
                for (int i = 0; i < num; i++) {
                    page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                    copy.addPage(page);
                }
                doc.close();
                out.close();
            } catch (IOException e) {
                System.out.println("导出异常");
            } catch (DocumentException e) {
                System.out.println("文档异常");
            }

            CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
            cjroneSignature.setUrl("/zgyiliao_" + cjroneblZgjbyiliaoEntity.getDisableId() + ".pdf");
            cjroneSignature.setFileActUrl(newPDFPath);
            cjroneSignature.setCreateDate(new Date());
            cjroneSignature.setCreateId(getUserId());
            cjroneSignature.setType("惠残事项职工基本医疗保险");
            cjroneSignature.setTypeId(cjroneblZgjbyiliaoEntity.getId());
            cjroneSignature.setFileName("zgyiliao_" + cjroneblZgjbyiliaoEntity.getDisableId() + ".pdf");
            cjroneSignature.setAccountId(getUserId().toString());
            cjroneSignature.setAccountName(getUser().getUsername());
            cjroneSignature.setStatus("1");

            cjroneSignatureService.save(cjroneSignature);

            return R.ok().put("signTotal",1).put("matterId",id).put("matterName","职工基本医疗保险补助").put("fileUrl",cjroneSignature.getUrl());

        }

    }

    /**
     * 生活补助金PDF
     */
    @Login
    @GetMapping("/printLiveAllowancePDF/{id}")
    @ApiOperation("生活补助金PDF")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "token", value = "登录token", required = false, paramType = "header")
    })
    public R printLiveAllowancePDF(@PathVariable("id") Integer id,@LoginUser UserEntity user) throws IOException {
        System.out.print("id is :"+id);
        Map<String, Object> tmp_params = new HashMap<>();
        tmp_params.put("type","惠残事项生活补助金");
        tmp_params.put("status","1");
        tmp_params.put("type_id",id);
        List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
        if (alive_list.size()>0) {
            CjroneSignatureEntity cjroneSignatureEntity = alive_list.get(0);
            return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", cjroneSignatureEntity.getFileName()).put("signId",cjroneSignatureEntity.getId());

        } else{
            //根据编号获得生活补助金信息
            CjroneblLivingAllowanceEntity cjroneblLivingAllowanceEntity = cjroneblLivingAllowanceService.getById(id);

            Calendar now = Calendar.getInstance();

            // 原pdf模板路径
            String templatePath = cjroneProperties.getTempletePath()+"生活补助金模板.pdf";
            // 生成的新文件路径
            String newPDFPath = cjroneProperties.getSignaturePath()+"living_allowance_subsidy_"+cjroneblLivingAllowanceEntity.getIdCard()+".pdf";

            // 获得待生成的实体文件
            Map<String, String> map =new HashMap<String, String>();
            map.put("name", cjroneblLivingAllowanceEntity.getName()==null?"":cjroneblLivingAllowanceEntity.getName());
            if(cjroneblLivingAllowanceEntity.getSex()!=null){
                if("0".equals(cjroneblLivingAllowanceEntity.getSex())){
                    map.put("sex","女");
                }else{
                    map.put("sex","男");
                }
            }else{
                map.put("sex","");
            }
            map.put("birthday",cjroneblLivingAllowanceEntity.getBirthday()==null?"":cjroneblLivingAllowanceEntity.getBirthday());
            map.put("age",cjroneblLivingAllowanceEntity.getAge()==null?"":cjroneblLivingAllowanceEntity.getAge().toString());
            map.put("disabilityType",cjroneblLivingAllowanceEntity.getDisabilityType()==null?"":cjroneblLivingAllowanceEntity.getDisabilityType());
            map.put("disabilityDegree",cjroneblLivingAllowanceEntity.getDisabilityDegree()==null?"":cjroneblLivingAllowanceEntity.getDisabilityDegree());
            map.put("idCard",cjroneblLivingAllowanceEntity.getIdCard()==null?"":cjroneblLivingAllowanceEntity.getIdCard());
            map.put("disableId",cjroneblLivingAllowanceEntity.getDisableId()==null?"":cjroneblLivingAllowanceEntity.getDisableId());
            map.put("liveAddress",cjroneblLivingAllowanceEntity.getLiveAddress()==null?"":cjroneblLivingAllowanceEntity.getLiveAddress());
            map.put("telephone",cjroneblLivingAllowanceEntity.getTelephone()==null?"":cjroneblLivingAllowanceEntity.getTelephone());
            map.put("familyEconomy",cjroneblLivingAllowanceEntity.getFamilyEconomy()==null?"":cjroneblLivingAllowanceEntity.getFamilyEconomy());
            map.put("pension",cjroneblLivingAllowanceEntity.getPension()==null?"":cjroneblLivingAllowanceEntity.getPension());
            map.put("applyType",cjroneblLivingAllowanceEntity.getApplyType()==null?"":cjroneblLivingAllowanceEntity.getApplyType());
            map.put("subsidyMoney",cjroneblLivingAllowanceEntity.getSubsidyMoney()==null?"":cjroneblLivingAllowanceEntity.getSubsidyMoney());
            map.put("applyDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");


            FileOutputStream out;
            int num = 1;//页数
            ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
            try {
                out = new FileOutputStream(newPDFPath);// 输出流
                Document doc = new Document();   //新建一个文档
                PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                doc.open();

                for (int i = 0; i < num; i++) {
                    bos[i] = new ByteArrayOutputStream();
                    System.out.println("templatePath--->"+templatePath);
                    PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                    PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                    AcroFields form = stamper.getAcroFields(); //获取文本域
                    // BaseFont
                    // 1、使用iTextAsian.jar中的字体
                    // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                    // 2、使用Windows系统字体(TrueType)
                    // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                    // 3、使用资源字体(ClassPath)
                    // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                    BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                    java.util.Iterator<String> it = form.getFields().keySet().iterator();
                    while (it.hasNext()) {
                        String name = it.next();
                        System.out.println(name + ":"+map.get(name));
                        form.setFieldProperty(name,"textfont",font,null);
                        // form.addSubstitutionFont(font);
                        form.setField(name, map.get(name));
                    }

                    stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                    stamper.close();

                }
                PdfImportedPage page = null;
                for (int i = 0; i < num; i++) {
                    page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                    copy.addPage(page);
                }
                doc.close();
                out.close();
            } catch (IOException e) {
                System.out.println("导出异常");
            } catch (DocumentException e) {
                System.out.println("文档异常");
            }

            CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
            cjroneSignature.setUrl("/living_allowance_subsidy_" + cjroneblLivingAllowanceEntity.getIdCard() + ".pdf");
            cjroneSignature.setFileActUrl(newPDFPath);
            cjroneSignature.setCreateDate(new Date());
            cjroneSignature.setCreateId(getUserId());
            cjroneSignature.setType("惠残事项生活补助金");
            cjroneSignature.setTypeId(cjroneblLivingAllowanceEntity.getId());
            cjroneSignature.setFileName("living_allowance_subsidy_" + cjroneblLivingAllowanceEntity.getIdCard() + ".pdf");
            cjroneSignature.setAccountId(getUserId().toString());
            cjroneSignature.setAccountName(getUser().getUsername());
            cjroneSignature.setStatus("1");

            cjroneSignatureService.save(cjroneSignature);
            return R.ok().put("signTotal",1).put("matterId",id).put("matterName","生活补助金").put("fileUrl",cjroneSignature.getUrl());

        }


    }


    /**
     * 护理补贴PDF
     */
    @Login
    @GetMapping("/printNursePDF/{id}")
    @ApiOperation("护理补贴PDF")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "token", value = "登录token", required = false, paramType = "header")
    })
    public R printNursePDF(@PathVariable("id") Integer id,@LoginUser UserEntity user) throws IOException {
        System.out.print("id is :"+id);
        Map<String, Object> tmp_params = new HashMap<>();
        tmp_params.put("type","惠残事项护理补贴");
        tmp_params.put("status","1");
        tmp_params.put("type_id",id);
        List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
        if (alive_list.size()>0) {
            CjroneSignatureEntity cjroneSignatureEntity = alive_list.get(0);
            return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", cjroneSignatureEntity.getFileName()).put("signId",cjroneSignatureEntity.getId());

        } else{
            //根据编号获得详细信息
            CjroneblNursingSubsidyEntity cjroneblNursingSubsidyEntity = cjroneblNursingSubsidyService.getById(id);

            // 原pdf模板路径
            String templatePath = cjroneProperties.getTempletePath()+"护理补助模板.pdf";
            // 生成的新文件路径
            String newPDFPath = cjroneProperties.getSignaturePath()+"nursing_subsidy_"+cjroneblNursingSubsidyEntity.getIdCard()+".pdf";

            // 获得待生成的实体文件
            Map<String, String> map =new HashMap<String, String>();
            Map<String, Object> mapparams =new HashMap<String, Object>();

            //获取年月日数据
            Calendar now = Calendar.getInstance();
            String nums[] = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};

            // 获得待生成的实体文件
            map.put("name", cjroneblNursingSubsidyEntity.getName());
            if (cjroneblNursingSubsidyEntity.getSex() == 1)
                map.put("sex", "男");
            else
                map.put("sex", "女");
            map.put("birthday", cjroneblNursingSubsidyEntity.getBirthday()==null?"":cjroneblNursingSubsidyEntity.getBirthday());
            map.put("nationality", cjroneblNursingSubsidyEntity.getNationality()==null?"":cjroneblNursingSubsidyEntity.getNationality());
            map.put("idCard", cjroneblNursingSubsidyEntity.getIdCard()==null?"":cjroneblNursingSubsidyEntity.getIdCard());
            map.put("disabilityCategory", cjroneblNursingSubsidyEntity.getDisabilityCategory()==null?"":cjroneblNursingSubsidyEntity.getDisabilityCategory());
            map.put("disabilityDegree", cjroneblNursingSubsidyEntity.getDisabilityDegree()==null?"":cjroneblNursingSubsidyEntity.getDisabilityDegree());
            map.put("disableId", cjroneblNursingSubsidyEntity.getDisableId()==null?"":cjroneblNursingSubsidyEntity.getDisableId());
            map.put("nativeAddress", cjroneblNursingSubsidyEntity.getNativeAddress()==null?"":cjroneblNursingSubsidyEntity.getNativeAddress());
            map.put("liveAddress", cjroneblNursingSubsidyEntity.getLiveAddress()==null?"":cjroneblNursingSubsidyEntity.getLiveAddress());
            map.put("mobilePhone", cjroneblNursingSubsidyEntity.getMobilePhone()==null?"":cjroneblNursingSubsidyEntity.getMobilePhone());
            map.put("bankName", cjroneblNursingSubsidyEntity.getBankName()==null?"":cjroneblNursingSubsidyEntity.getBankName());
            map.put("bankAccount", cjroneblNursingSubsidyEntity.getBankAccount()==null?"":cjroneblNursingSubsidyEntity.getBankAccount());
            map.put("guardianName", cjroneblNursingSubsidyEntity.getGuardianName()==null?"":cjroneblNursingSubsidyEntity.getGuardianName());
            map.put("guardianPhone", cjroneblNursingSubsidyEntity.getGuardianPhone()==null?"":cjroneblNursingSubsidyEntity.getGuardianPhone());
            map.put("sixMonth", cjroneblNursingSubsidyEntity.getSixMonth()==null?"":cjroneblNursingSubsidyEntity.getSixMonth());
            map.put("careType",cjroneblNursingSubsidyEntity.getCareType()==null?"":cjroneblNursingSubsidyEntity.getCareType());
            map.put("lifeStatus",cjroneblNursingSubsidyEntity.getLifeStatus()==null?"":cjroneblNursingSubsidyEntity.getLifeStatus());
            map.put("careMonths",cjroneblNursingSubsidyEntity.getCareMonths()==null?"":cjroneblNursingSubsidyEntity.getCareMonths().toString());
            map.put("applicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
            map.put("actuallySubsidy",cjroneblNursingSubsidyEntity.getActuallySubsidy());

            FileOutputStream out;
            int num = 1;//页数
            ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
            try {
                out = new FileOutputStream(newPDFPath);// 输出流
                Document doc = new Document();   //新建一个文档
                PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                doc.open();

                for (int i = 0; i < num; i++) {
                    bos[i] = new ByteArrayOutputStream();
                    PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                    PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                    AcroFields form = stamper.getAcroFields(); //获取文本域
                    // BaseFont
                    // 1、使用iTextAsian.jar中的字体
                    // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                    // 2、使用Windows系统字体(TrueType)
                    // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                    // 3、使用资源字体(ClassPath)
                    // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                    BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                    java.util.Iterator<String> it = form.getFields().keySet().iterator();
                    while (it.hasNext()) {
                        String name = it.next();
                        System.out.println(name + ":"+map.get(name));
                        form.setFieldProperty(name,"textfont",font,null);
                        // form.addSubstitutionFont(font);
                        form.setField(name, map.get(name));
                    }

                    stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                    stamper.close();

                }
                PdfImportedPage page = null;
                for (int i = 0; i < num; i++) {
                    page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                    copy.addPage(page);
                }
                doc.close();
                out.close();
            } catch (IOException e) {
                System.out.println("导出异常");
            } catch (DocumentException e) {
                System.out.println("文档异常");
            }


            CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
            cjroneSignature.setUrl("/nursing_subsidy_" + cjroneblNursingSubsidyEntity.getIdCard() + ".pdf");
            cjroneSignature.setFileActUrl(newPDFPath);
            cjroneSignature.setCreateDate(new Date());
            cjroneSignature.setCreateId(getUserId());
            cjroneSignature.setType("惠残事项护理补贴");
            cjroneSignature.setTypeId(cjroneblNursingSubsidyEntity.getId());
            cjroneSignature.setFileName("nursing_subsidy_" + cjroneblNursingSubsidyEntity.getIdCard() + ".pdf");
            cjroneSignature.setAccountId(getUserId().toString());
            cjroneSignature.setAccountName(getUser().getUsername());
            cjroneSignature.setStatus("1");

            cjroneSignatureService.save(cjroneSignature);
            return R.ok().put("signTotal",1).put("matterId",id).put("matterName","护理补贴").put("fileUrl",cjroneSignature.getUrl());

        }

    }


    /**
     * 生活补贴PDF
     */
    @Login
    @GetMapping("/printLivingSubsidyPDF/{id}")
    @ApiOperation("生活补贴PDF")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "token", value = "登录token", required = false, paramType = "header")
    })
    public R printLivingSubsidyPDF(@PathVariable("id") Integer id,@LoginUser UserEntity user) throws IOException {
        System.out.print("id is :"+id);
        Map<String, Object> tmp_params = new HashMap<>();
        tmp_params.put("type","惠残事项生活补贴");
        tmp_params.put("status","1");
        tmp_params.put("type_id",id);
        DataDisabilityCertificateEntity dd=dataDisabilityCertificateService.getByIDCard(user.getPassword());
        List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
        if (alive_list.size()>0) {
            CjroneSignatureEntity cjroneSignatureEntity = alive_list.get(0);
            return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", cjroneSignatureEntity.getFileName()).put("signId",cjroneSignatureEntity.getId());

        } else{

            //根据编号获得生活补贴信息
            CjroneblLivingSubsidyEntity cjroneblLivingSubsidyEntity = cjroneblLivingSubsidyService.getById(id);

            Calendar now = Calendar.getInstance();

            // 原pdf模板路径
            String templatePath = cjroneProperties.getTempletePath()+"生活补助模板.pdf";
            // 生成的新文件路径
            String newPDFPath = cjroneProperties.getSignaturePath()+"living_allowance_"+cjroneblLivingSubsidyEntity.getIdCard()+".pdf";

            // 获得待生成的实体文件
            Map<String, String> map =new HashMap<String, String>();
            map.put("name", cjroneblLivingSubsidyEntity.getName()==null?"":cjroneblLivingSubsidyEntity.getName());
            if(cjroneblLivingSubsidyEntity.getSex()!=null){
                if("0".equals(cjroneblLivingSubsidyEntity.getSex())){
                    map.put("sex","女");
                }else{
                    map.put("sex","男");
                }
            }else{
                map.put("sex","");
            }
            map.put("birthday",cjroneblLivingSubsidyEntity.getBirthday()==null?"":cjroneblLivingSubsidyEntity.getBirthday());
            map.put("natinality",dd.getNationality()==null?"":dd.getNationality());
            map.put("nativeZhenName",dd.getJiedao()==null?"":dd.getJiedao());
            map.put("nativeCunName",dd.getShequ()==null?"":dd.getShequ());
            map.put("idCard",cjroneblLivingSubsidyEntity.getIdCard()==null?"":cjroneblLivingSubsidyEntity.getIdCard());
            map.put("disabilityType",cjroneblLivingSubsidyEntity.getDisabilityType()==null?"":cjroneblLivingSubsidyEntity.getDisabilityType());
            map.put("disabilityDegree",cjroneblLivingSubsidyEntity.getDisabilityDegree()==null?"":cjroneblLivingSubsidyEntity.getDisabilityDegree());
            map.put("disableId",cjroneblLivingSubsidyEntity.getDisableId()==null?"":cjroneblLivingSubsidyEntity.getDisableId());
            map.put("nativeAddress",cjroneblLivingSubsidyEntity.getNativeAddress()==null?"":cjroneblLivingSubsidyEntity.getNativeAddress());
            map.put("liveAddress",cjroneblLivingSubsidyEntity.getLiveAddress()==null?"":cjroneblLivingSubsidyEntity.getLiveAddress());
            map.put("telephone",cjroneblLivingSubsidyEntity.getTelephone()==null?"":cjroneblLivingSubsidyEntity.getTelephone());
            map.put("bankName",cjroneblLivingSubsidyEntity.getBankName()==null?"":cjroneblLivingSubsidyEntity.getBankName());
            map.put("bankAccount",cjroneblLivingSubsidyEntity.getBankAccount()==null?"":cjroneblLivingSubsidyEntity.getBankAccount());
            map.put("guardianName",cjroneblLivingSubsidyEntity.getGuardianName()==null?"":cjroneblLivingSubsidyEntity.getGuardianName());
            map.put("guardianPhone",cjroneblLivingSubsidyEntity.getGuardianPhone()==null?"":cjroneblLivingSubsidyEntity.getGuardianPhone());
            if("1".equals(cjroneblLivingSubsidyEntity.getFamilyEconomy())){
                map.put("fe1","√");
            }else if("2".equals(cjroneblLivingSubsidyEntity.getFamilyEconomy())){
                map.put("fe2","√");
            }else if("3".equals(cjroneblLivingSubsidyEntity.getFamilyEconomy())){
                map.put("fe3","√");
            }else if("4".equals(cjroneblLivingSubsidyEntity.getFamilyEconomy())){
                map.put("fe4","√");
            }
            map.put("income",cjroneblLivingSubsidyEntity.getIncome()==null?"":cjroneblLivingSubsidyEntity.getIncome());
            map.put("applyDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
            map.put("zhen1","√");
            map.put("mz2","√");
            //开始关联补助金额
            Map<String, Object> mapparams =new HashMap<String, Object>();
            mapparams.put("type","生活补贴");
            List<CjroneTwoSubsidyStandardsEntity> cjroneTwoSubsidyStandardsEntityList=cjroneTwoSubsidyStandardsService.queryByMap(mapparams);
            if(cjroneTwoSubsidyStandardsEntityList!=null&&cjroneTwoSubsidyStandardsEntityList.size()>0){
                map.put("subsidymoney",cjroneTwoSubsidyStandardsEntityList.get(0).getMoney().toString());
            }
            else{
                map.put("subsidymoney","无数据");
            }
            map.put("qcl2","√");


            FileOutputStream out;
            int num = 2;//页数
            ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
            try {
                out = new FileOutputStream(newPDFPath);// 输出流
                Document doc = new Document();   //新建一个文档
                PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                doc.open();

                for (int i = 0; i < num; i++) {
                    bos[i] = new ByteArrayOutputStream();
                    System.out.println("templatePath--->"+templatePath);
                    PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                    PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                    AcroFields form = stamper.getAcroFields(); //获取文本域
                    // BaseFont
                    // 1、使用iTextAsian.jar中的字体
                    // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                    // 2、使用Windows系统字体(TrueType)
                    // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                    // 3、使用资源字体(ClassPath)
                    // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                    BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                    java.util.Iterator<String> it = form.getFields().keySet().iterator();
                    while (it.hasNext()) {
                        String name = it.next();
                        System.out.println(name + ":"+map.get(name));
                        form.setFieldProperty(name,"textfont",font,null);
                        // form.addSubstitutionFont(font);
                        form.setField(name, map.get(name));
                    }

                    stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                    stamper.close();

                }
                PdfImportedPage page = null;
                for (int i = 0; i < num; i++) {
                    page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                    copy.addPage(page);
                }
                doc.close();
                out.close();
            } catch (IOException e) {
                System.out.println("导出异常");
            } catch (DocumentException e) {
                System.out.println("文档异常");
            }

            CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
            cjroneSignature.setUrl("/living_allowance_" + cjroneblLivingSubsidyEntity.getIdCard() + ".pdf");
            cjroneSignature.setFileActUrl(newPDFPath);
            cjroneSignature.setCreateDate(new Date());
            cjroneSignature.setCreateId(getUserId());
            cjroneSignature.setType("惠残事项生活补贴");
            cjroneSignature.setTypeId(cjroneblLivingSubsidyEntity.getId());
            cjroneSignature.setFileName("living_allowance_" + cjroneblLivingSubsidyEntity.getIdCard() + ".pdf");
            cjroneSignature.setAccountId(getUserId().toString());
            cjroneSignature.setAccountName(getUser().getUsername());
            cjroneSignature.setStatus("1");

            cjroneSignatureService.save(cjroneSignature);
            return R.ok().put("signTotal",1).put("matterId",id).put("matterName","生活补贴").put("fileUrl",cjroneSignature.getUrl());

        }


    }


    /**
     * 残疾人子女教育补贴 pdf
     */
    @Login
    @GetMapping("/printChildeduPDF/{id}")
    @ApiOperation("残疾人子女教育补贴PDF")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "token", value = "登录token", required = false, paramType = "header")
    })
    public R printChildeduPDF(@PathVariable("id") Integer id,@LoginUser UserEntity user) throws IOException {
        System.out.print("id is :"+id);

        Map<String, Object> tmp_params = new HashMap<>();
        tmp_params.put("type","惠残事项残疾人子女教育补贴");
        tmp_params.put("status","1");
        tmp_params.put("type_id",id);
        List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);

        if (alive_list.size()>0) {
            CjroneSignatureEntity cjroneSignatureEntity = alive_list.get(0);
            return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", cjroneSignatureEntity.getFileName()).put("signId",cjroneSignatureEntity.getId());

        }else {

            //根据编号获得实体
            CjroneblChildeduEntity cjroneblChildeduEntity =cjroneblChildeduService.getById(id);

            Calendar now = Calendar.getInstance();

            // 原pdf模板路径
            String templatePath = cjroneProperties.getTempletePath()+"高中补助模板.pdf";
            // 生成的新文件路径
            String newPDFPath = cjroneProperties.getSignaturePath()+"child_edu_"+cjroneblChildeduEntity.getIdCard()+".pdf";

            // 获得待生成的实体文件
            Map<String, String> map =new HashMap<String, String>();
            map.put("name", cjroneblChildeduEntity.getName()==null?"":cjroneblChildeduEntity.getName());
            map.put("birthday",cjroneblChildeduEntity.getBirthday()==null?"":cjroneblChildeduEntity.getBirthday());
            map.put("disableId",cjroneblChildeduEntity.getDisableId()==null?"":cjroneblChildeduEntity.getDisableId());
            map.put("idCard",cjroneblChildeduEntity.getIdCard()==null?"":cjroneblChildeduEntity.getIdCard());
            map.put("telephone",cjroneblChildeduEntity.getTelephoe()==null?"":cjroneblChildeduEntity.getTelephoe());
            map.put("liveAddress",cjroneblChildeduEntity.getLiveAddress()==null?"":cjroneblChildeduEntity.getLiveAddress());
            map.put("admissionTime",cjroneblChildeduEntity.getAdmissionTime()==null?"":cjroneblChildeduEntity.getAdmissionTime());
            map.put("currentSchool",cjroneblChildeduEntity.getCurrentSchool()==null?"":cjroneblChildeduEntity.getCurrentSchool());
            map.put("grade",cjroneblChildeduEntity.getGrade()==null?"":cjroneblChildeduEntity.getGrade());
            map.put("subsidyMoney",cjroneblChildeduEntity.getSubsidyMoney()==null?"":cjroneblChildeduEntity.getSubsidyMoney().toString());
            map.put("applicantType",cjroneblChildeduEntity.getApplicantType()==null?"":cjroneblChildeduEntity.getApplicantType());
            map.put("fatherName",cjroneblChildeduEntity.getFatherName()==null?"":cjroneblChildeduEntity.getFatherName());
            map.put("motherName",cjroneblChildeduEntity.getMotherName()==null?"":cjroneblChildeduEntity.getMotherName());
            map.put("motherDisid",cjroneblChildeduEntity.getMotherDisid()==null?"":cjroneblChildeduEntity.getMotherDisid());
            map.put("FatherDisid",cjroneblChildeduEntity.getFatherDisid()==null?"":cjroneblChildeduEntity.getFatherDisid());


            map.put("createTime",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");



            FileOutputStream out;
            int num = 1;//页数
            ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
            try {
                out = new FileOutputStream(newPDFPath);// 输出流
                Document doc = new Document();   //新建一个文档
                PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                doc.open();

                for (int i = 0; i < num; i++) {
                    bos[i] = new ByteArrayOutputStream();
                    System.out.println("templatePath--->"+templatePath);
                    PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                    PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                    AcroFields form = stamper.getAcroFields(); //获取文本域
                    // BaseFont
                    // 1、使用iTextAsian.jar中的字体
                    // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                    // 2、使用Windows系统字体(TrueType)
                    // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                    // 3、使用资源字体(ClassPath)
                    // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                    BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                    java.util.Iterator<String> it = form.getFields().keySet().iterator();
                    while (it.hasNext()) {
                        String name = it.next();
                        System.out.println(name + ":"+map.get(name));
                        form.setFieldProperty(name,"textfont",font,null);
                        // form.addSubstitutionFont(font);
                        form.setField(name, map.get(name));
                    }

                    stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                    stamper.close();

                }
                PdfImportedPage page = null;
                for (int i = 0; i < num; i++) {
                    page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                    copy.addPage(page);
                }
                doc.close();
                out.close();
            } catch (IOException e) {
                System.out.println("导出异常");
            } catch (DocumentException e) {
                System.out.println("文档异常");
            }

            CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
            cjroneSignature.setUrl("/child_edu_" + cjroneblChildeduEntity.getIdCard() + ".pdf");
            cjroneSignature.setFileActUrl(newPDFPath);
            cjroneSignature.setCreateDate(new Date());
            cjroneSignature.setCreateId(getUserId());
            cjroneSignature.setType("惠残事项残疾人子女教育补贴");
            cjroneSignature.setTypeId(cjroneblChildeduEntity.getId());
            cjroneSignature.setFileName("child_edu_" + cjroneblChildeduEntity.getIdCard() + ".pdf");
            cjroneSignature.setAccountId(getUserId().toString());
            cjroneSignature.setAccountName(getUser().getUsername());
            cjroneSignature.setStatus("1");

            cjroneSignatureService.save(cjroneSignature);

            return R.ok().put("signTotal",1).put("matterId",id).put("matterName","残疾人子女教育补贴").put("fileUrl",cjroneSignature.getUrl());

        }



    }






    //==============================以下接口是老得接口=======================================
    /*
     *  康复事项列表
     */
 /*   @Login
    @GetMapping("getRehabilitationSubsidyCategoryList")
    @ApiOperation("获得康复事项列表")*/
    public R getRehabilitationSubsidyCategoryList(){

        List<DataRehabilitationSubsidyCategoryEntity> rsList= dataRehabilitationSubsidyCategoryService.getRehabilitationSubsidyCategoryList(null);
        return R.ok().put("list", rsList);

    }


    /*
     *  自动筛选事项
     */
/*    @Login
    @GetMapping("getAutoFilterMatter")
    @ApiOperation("自动筛选惠残事项接口")*/
    public R GetAutoFilterMatter()
    {
        return R.ok();
    }



   /* @PostMapping("saveRehabilitationSubsidy")
    @ApiOperation("保存康复补助接口")
    public R saveRehabilitationSubsidy(){
        return R.ok();
    }

    @PostMapping("saveLoveBus")
    @ApiOperation("保存爱心公交卡接口")
    public R saveLoveBus(){
        return R.ok();
    }

    @PostMapping("saveNursingSubsidy")
    @ApiOperation("保存护理补贴接口")
    public R saveNursingSubsidy(){
        return R.ok();
    }

    @PostMapping("saveLivingSubsidy")
    @ApiOperation("保存生活补贴接口")
    public R saveLivingSubsidy(){
        return R.ok();
    }

    @PostMapping("saveResidentInsurance")
    @ApiOperation("保存养老保险补贴接口")
    public R saveResidentInsurance(){
        return R.ok();
    }*/

    /**
     * 保存
     */
/*    @Login
    @PostMapping("/saveWelfare")
    @ApiOperation("保存惠残事项申请接口")*/
    public R saveWelfare(@RequestBody CjroneWelfareMatterApplicationEntity cjroneWelfareMatterApplication,@LoginUser UserEntity user){
        cjroneWelfareMatterApplication.setCreateId(user.getUserId());
        cjroneWelfareMatterApplication.setCreateTime(DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN));
        cjroneWelfareMatterApplication.setApplicationTime(DateUtils.format(new Date(), DateUtils.DATE_TIME_PATTERN));
        //cjroneWelfareMatterApplicationService.save(cjroneWelfareMatterApplication);
        Map<String,String> resultMap=appWelfareMattersService.saveWel(cjroneWelfareMatterApplication);
        return R.ok().put("kf",resultMap.get("kf")).put("sh",resultMap.get("sh")).put("hl",resultMap.get("hl"));
    }


    /**
     * 我的惠残事项申请列表
     */
/*    @Login
    @GetMapping("/listWelfare")
    @ApiOperation("我的惠残事项申请列表")*/
    public R listWelfare(@LoginUser UserEntity user){

        Map<String, Object> params=new HashMap<>();
        Map<String, Object> key=new HashMap<>();
        key.put("idCard",user.getPassword());
        params.put("key",key);
        PageUtils page = cjroneWelfareMatterApplicationService.queryPage(params);

        return R.ok().put("list", page.getList());
    }


    /**
     * 福利事项详情
     */
/*    @Login
    @GetMapping("/infoWelfare")
    @ApiOperation("惠残事项详情")*/
    public R infoWelfare(@RequestParam("id") Integer id ,@RequestParam("mattername") String mattername){
        //根据不同的 mattername 返回不同的福利事项申请详情
        if(mattername=="爱心公交卡"){

        }
        CjroneWelfareMatterApplicationEntity cjroneWelfareMatterApplication = cjroneWelfareMatterApplicationService.getById(id);

        return R.ok().put("cjroneWelfareMatterApplication", cjroneWelfareMatterApplication);
    }

    /**
     * 生成护理补贴电子签章 pdf
     */
/*    @Login
    @RequestMapping("/printHLPDF/{id}")*/
    public R printHLPDF(@PathVariable("id") Integer id,@LoginUser UserEntity user) throws IOException {
        Map<String, Object> tmp_params = new HashMap<>();
        tmp_params.put("type","惠残事项护理补贴");
        tmp_params.put("status","1");
        tmp_params.put("type_id",id);
        List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
        if (alive_list.size()>0) {
            CjroneSignatureEntity cjroneSignatureEntity = alive_list.get(0);
            if ("电子公章".equals("电子公章")){
                String tmp_file_name = System.currentTimeMillis()+"_"+cjroneSignatureEntity.getFileName();
                Files.copy(new File(cjroneSignatureEntity.getFileActUrl()).toPath(),new File(cjroneProperties.getTempFilePath()+tmp_file_name).toPath());

                return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", tmp_file_name).put("signId",cjroneSignatureEntity.getId());

            }else {
                return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", cjroneSignatureEntity.getFileName()).put("signId",cjroneSignatureEntity.getId());
            }
        }else {

            //根据编号获得详细信息
            CjroneNursingSubsidyEntity cjroneNursingSubsidyEntity = cjroneNursingSubsidyService.getById(id);

            // 原pdf模板路径
            String templatePath = cjroneProperties.getTempletePath()+"护理补助模板.pdf";
            // 生成的新文件路径
            String newPDFPath = cjroneProperties.getSignaturePath()+"nursing_subsidy_"+cjroneNursingSubsidyEntity.getIdCard()+".pdf";

            // 获得待生成的实体文件
            Map<String, String> map =new HashMap<String, String>();

            if(cjroneNursingSubsidyEntity.getCareType()!=null){
                String aaa[] = cjroneNursingSubsidyEntity.getCareType().substring(1, cjroneNursingSubsidyEntity.getCareType().length() - 1).split(",");
                String careTypeName = null;
                for (String s : aaa) {
                    if (s.replace("\"", "").equals("1")) {
                        if (careTypeName == null) {
                            careTypeName = "居家安养 ";
                        } else {
                            careTypeName = careTypeName + "居家安养 ";
                        }
                    } else if (s.replace("\"", "").equals("2")) {
                        if (careTypeName == null) {
                            careTypeName = "日间照料 ";
                        } else {
                            careTypeName = careTypeName + "日间照料 ";
                        }
                    } else if (s.replace("\"", "").equals("3")) {
                        if (careTypeName == null) {
                            careTypeName = "集中托养 ";
                        } else {
                            careTypeName = careTypeName + "集中托养 ";
                        }
                    } else if (s.replace("\"", "").equals("4")) {
                        if (careTypeName == null) {
                            careTypeName = "项目服务 ";
                        } else {
                            careTypeName = careTypeName + "项目服务 ";
                        }
                    }
                }
                cjroneNursingSubsidyEntity.setCareType(careTypeName);
            }
            else{
                cjroneNursingSubsidyEntity.setCareType("无");
            }

            //获取年月日数据
            Calendar now = Calendar.getInstance();
            String nums[] = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};

            // 获得待生成的实体文件
            map.put("name", cjroneNursingSubsidyEntity.getName());
            if (cjroneNursingSubsidyEntity.getSex() == 1)
                map.put("sex", "男");
            else
                map.put("sex", "女");
            map.put("birthday", cjroneNursingSubsidyEntity.getBirthday()==null?"":cjroneNursingSubsidyEntity.getBirthday());
            map.put("mobilePhone", cjroneNursingSubsidyEntity.getMobilePhone()==null?"":cjroneNursingSubsidyEntity.getMobilePhone());
            map.put("idCard", cjroneNursingSubsidyEntity.getIdCard()==null?"":cjroneNursingSubsidyEntity.getIdCard());
            if(cjroneNursingSubsidyEntity.getDisabilityCategory()==null){
                map.put("disabilityType", "");
            }
            else{
                if("1".equals(cjroneNursingSubsidyEntity.getDisabilityCategory())){
                    map.put("disabilityType", "视力残疾");
                }
                else if("2".equals(cjroneNursingSubsidyEntity.getDisabilityCategory())){
                    map.put("disabilityType", "听力残疾");
                }
                else if("3".equals(cjroneNursingSubsidyEntity.getDisabilityCategory())){
                    map.put("disabilityType", "智力残疾");
                }
                else if("4".equals(cjroneNursingSubsidyEntity.getDisabilityCategory())){
                    map.put("disabilityType", "精神残疾");
                }
                else if("5".equals(cjroneNursingSubsidyEntity.getDisabilityCategory())){
                    map.put("disabilityType", "肢体（神经系统疾病致残）");
                }
                else if("6".equals(cjroneNursingSubsidyEntity.getDisabilityCategory())){
                    map.put("disabilityType", "言语残疾");
                }
                else if("7".equals(cjroneNursingSubsidyEntity.getDisabilityCategory())){
                    map.put("disabilityType", "肢体（非神经系统疾病致残）");
                }
                else{
                    map.put("disabilityType", "多重残疾");
                }
            }
            map.put("disabilityDegree", cjroneNursingSubsidyEntity.getDisabilityDegree()==null?"无等":nums[Integer.parseInt(cjroneNursingSubsidyEntity.getDisabilityDegree())] + "级");
            map.put("disableId", cjroneNursingSubsidyEntity.getDisableId()==null?"":cjroneNursingSubsidyEntity.getDisableId());
            map.put("nativeAddress", cjroneNursingSubsidyEntity.getNativePlace()==null?"":cjroneNursingSubsidyEntity.getNativePlace());
            map.put("bankName", cjroneNursingSubsidyEntity.getBankName()==null?"":cjroneNursingSubsidyEntity.getBankName());
            map.put("bankAccount", cjroneNursingSubsidyEntity.getBankAccount()==null?"":cjroneNursingSubsidyEntity.getBankAccount());

            map.put("guardianName", cjroneNursingSubsidyEntity.getGuardianName()==null?"":cjroneNursingSubsidyEntity.getGuardianName());
            map.put("guardianPhone", cjroneNursingSubsidyEntity.getGuardianPhone()==null?"":cjroneNursingSubsidyEntity.getGuardianPhone());

            map.put("careServiceType",cjroneNursingSubsidyEntity.getCareType()==null?"":cjroneNursingSubsidyEntity.getCareType());
            map.put("degree",cjroneNursingSubsidyEntity.getDisabilityDegree()==null?"":nums[Integer.parseInt(cjroneNursingSubsidyEntity.getDisabilityDegree())]);

            map.put("applicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");

            FileOutputStream out;
            int num = 1;//页数
            ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
            try {
                out = new FileOutputStream(newPDFPath);// 输出流
                Document doc = new Document();   //新建一个文档
                PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                doc.open();

                for (int i = 0; i < num; i++) {
                    bos[i] = new ByteArrayOutputStream();
                    PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                    PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                    AcroFields form = stamper.getAcroFields(); //获取文本域
                    // BaseFont
                    // 1、使用iTextAsian.jar中的字体
                    // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                    // 2、使用Windows系统字体(TrueType)
                    // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                    // 3、使用资源字体(ClassPath)
                    // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                    BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                    java.util.Iterator<String> it = form.getFields().keySet().iterator();
                    while (it.hasNext()) {
                        String name = it.next();
                        System.out.println(name + ":"+map.get(name));
                        form.setFieldProperty(name,"textfont",font,null);
                        // form.addSubstitutionFont(font);
                        form.setField(name, map.get(name));
                    }

                    stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                    stamper.close();

                }
                PdfImportedPage page = null;
                for (int i = 0; i < num; i++) {
                    page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                    copy.addPage(page);
                }
                doc.close();
                out.close();
            } catch (IOException e) {
                System.out.println("导出异常");
            } catch (DocumentException e) {
                System.out.println("文档异常");
            }



            // 生成电子签章pdf 并保存
            //String filePath = pdfUtils.pdfNursingSubsidyApplyToSignature(cjroneProperties.getTempletePath(), cjroneProperties.getSignaturePath(), nsEntity);
            CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
            cjroneSignature.setUrl("/nursing_subsidy_" + cjroneNursingSubsidyEntity.getIdCard()+".pdf");
            cjroneSignature.setFileActUrl(newPDFPath);
            cjroneSignature.setCreateDate(new Date());
            cjroneSignature.setCreateId(getUserId());
            cjroneSignature.setType("惠残事项护理补贴");
            cjroneSignature.setTypeId(cjroneNursingSubsidyEntity.getId());
            cjroneSignature.setFileName("nursing_subsidy_"+cjroneNursingSubsidyEntity.getIdCard()+".pdf");
            cjroneSignature.setAccountId(getUserId().toString());
            cjroneSignature.setAccountName(getUser().getUsername());
            cjroneSignature.setStatus("1");

            cjroneSignatureService.save(cjroneSignature);
            // return R.ok().put("fileName", "signature_"+ PinYinUtil.getFullSpell(cjroneDisabilityHospital.getName())+".pdf").put("signId",cjroneSignature.getId());
            return R.ok().put("fileUrl", cjroneSignature.getUrl()).put("fileName", cjroneSignature.getFileName()).put("signId",cjroneSignature.getId());

        }

    }

    /**
     * 生成生活补贴电子签章 pdf
     */
/*    @Login
    @RequestMapping("/printSHPDF/{id}")*/
    public R printSHPDF(@PathVariable("id") Integer id,@LoginUser UserEntity user) throws IOException {
        System.out.print("id is :"+id);

        Map<String, Object> tmp_params = new HashMap<>();
        tmp_params.put("type","惠残事项生活补贴");
        tmp_params.put("status","1");
        tmp_params.put("type_id",id);
        List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
        if (alive_list.size()>0) {
            CjroneSignatureEntity cjroneSignatureEntity = alive_list.get(0);
            if ("电子公章".equals("电子公章")){
                String tmp_file_name = System.currentTimeMillis()+"_"+cjroneSignatureEntity.getFileName();
                Files.copy(new File(cjroneSignatureEntity.getFileActUrl()).toPath(),new File(cjroneProperties.getTempFilePath()+tmp_file_name).toPath());

                return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", tmp_file_name).put("signId",cjroneSignatureEntity.getId());

            }else {
                return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", cjroneSignatureEntity.getFileName()).put("signId",cjroneSignatureEntity.getId());
            }
        }else {


            //根据编号获得详细信息
            CjroneLivingAllowanceEntity cjroneLivingAllowance = cjroneLivingAllowanceService.getById(id);
            String nums[] = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
            Calendar now = Calendar.getInstance();

            // 原pdf模板路径
            String templatePath = cjroneProperties.getTempletePath()+"生活补助模板.pdf";
            // 生成的新文件路径
            String newPDFPath = cjroneProperties.getSignaturePath()+"living_allowance_"+cjroneLivingAllowance.getIdCard()+".pdf";

            // 获得待生成的实体文件
            Map<String, String> map =new HashMap<String, String>();
            map.put("name", cjroneLivingAllowance.getName()==null?"":cjroneLivingAllowance.getName());
            if (cjroneLivingAllowance.getSex() == 1)
                map.put("sex", "男");
            else
                map.put("sex", "女");
            map.put("birthday", cjroneLivingAllowance.getBirthday()==null?"":cjroneLivingAllowance.getBirthday());
            map.put("mobile", cjroneLivingAllowance.getMobilePhone()==null?"":cjroneLivingAllowance.getMobilePhone());
            map.put("idCard", cjroneLivingAllowance.getIdCard()==null?"":cjroneLivingAllowance.getIdCard());

            if(cjroneLivingAllowance.getDisabilityCategory()==null){
                map.put("disabilityType", "");
            }
            else{
                if("1".equals(cjroneLivingAllowance.getDisabilityCategory())){
                    map.put("disabilityType", "视力残疾");
                }
                else if("2".equals(cjroneLivingAllowance.getDisabilityCategory())){
                    map.put("disabilityType", "听力残疾");
                }
                else if("3".equals(cjroneLivingAllowance.getDisabilityCategory())){
                    map.put("disabilityType", "智力残疾");
                }
                else if("4".equals(cjroneLivingAllowance.getDisabilityCategory())){
                    map.put("disabilityType", "精神残疾");
                }
                else if("5".equals(cjroneLivingAllowance.getDisabilityCategory())){
                    map.put("disabilityType", "肢体（神经系统疾病致残）");
                }
                else if("6".equals(cjroneLivingAllowance.getDisabilityCategory())){
                    map.put("disabilityType", "言语残疾");
                }
                else if("7".equals(cjroneLivingAllowance.getDisabilityCategory())){
                    map.put("disabilityType", "肢体（非神经系统疾病致残）");
                }
                else{
                    map.put("disabilityType", "多重残疾");
                }
            }
            map.put("disabilityDegree", cjroneLivingAllowance.getDisabilityDegree()==null?"无等":nums[Integer.parseInt(cjroneLivingAllowance.getDisabilityDegree())] + "级");
            map.put("disableId", cjroneLivingAllowance.getDisableId()==null?"":cjroneLivingAllowance.getDisableId());
            map.put("nativeAddress", cjroneLivingAllowance.getNativePlace()==null?"":cjroneLivingAllowance.getNativePlace());
            map.put("bankName", cjroneLivingAllowance.getBankName()==null?"":cjroneLivingAllowance.getBankName());
            map.put("bankAccount", cjroneLivingAllowance.getBankAccount()==null?"":cjroneLivingAllowance.getBankAccount());
            map.put("guardianName", cjroneLivingAllowance.getGuardianName()==null?"":cjroneLivingAllowance.getGuardianName());
            map.put("guardianPhone", cjroneLivingAllowance.getGuardianPhone()==null?"":cjroneLivingAllowance.getGuardianPhone());
            map.put("economicSituation", cjroneLivingAllowance.getFamilyEconoCondition()==null?"":cjroneLivingAllowance.getFamilyEconoCondition());
            map.put("applicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
            //map.put("zhenApplicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
            //map.put("shiApplicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");

            FileOutputStream out;
            int num = 1;//页数
            ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
            try {
                out = new FileOutputStream(newPDFPath);// 输出流
                Document doc = new Document();   //新建一个文档
                PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                doc.open();

                for (int i = 0; i < num; i++) {
                    bos[i] = new ByteArrayOutputStream();
                    PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                    PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                    AcroFields form = stamper.getAcroFields(); //获取文本域
                    // BaseFont
                    // 1、使用iTextAsian.jar中的字体
                    // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                    // 2、使用Windows系统字体(TrueType)
                    // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                    // 3、使用资源字体(ClassPath)
                    // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                    BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                    java.util.Iterator<String> it = form.getFields().keySet().iterator();
                    while (it.hasNext()) {
                        String name = it.next();
                        System.out.println(name + ":"+map.get(name));
                        form.setFieldProperty(name,"textfont",font,null);
                        // form.addSubstitutionFont(font);
                        form.setField(name, map.get(name));
                    }

                    stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                    stamper.close();

                }
                PdfImportedPage page = null;
                for (int i = 0; i < num; i++) {
                    page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                    copy.addPage(page);
                }
                doc.close();
                out.close();
            } catch (IOException e) {
                System.out.println("导出异常");
            } catch (DocumentException e) {
                System.out.println("文档异常");
            }

            // 生成电子签章pdf 并保存
            //String filePath = pdfUtils.pdfLivingAllowanceApplyToSignature(cjroneProperties.getTempletePath(), cjroneProperties.getSignaturePath(), livingentity);
            CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
            cjroneSignature.setUrl("/living_allowance_" + cjroneLivingAllowance.getIdCard()+".pdf");
            cjroneSignature.setFileActUrl(newPDFPath);
            cjroneSignature.setCreateDate(new Date());
            cjroneSignature.setCreateId(getUserId());
            cjroneSignature.setType("惠残事项生活补贴");
            cjroneSignature.setTypeId(cjroneLivingAllowance.getId());
            cjroneSignature.setFileName("living_allowance_"+cjroneLivingAllowance.getIdCard()+".pdf");
            cjroneSignature.setAccountId(getUserId().toString());
            cjroneSignature.setAccountName(getUser().getUsername());
            cjroneSignature.setStatus("1");

            cjroneSignatureService.save(cjroneSignature);
            // return R.ok().put("fileName", "signature_"+ PinYinUtil.getFullSpell(cjroneDisabilityHospital.getName())+".pdf").put("signId",cjroneSignature.getId());
            return R.ok().put("fileUrl", cjroneSignature.getUrl()).put("fileName", cjroneSignature.getFileName()).put("signId",cjroneSignature.getId());

        }
    }

    /**
     * 生成康复补助电子签章 pdf
     */
/*    @Login
    @RequestMapping("/printKFPDF/{id}")*/
    public R printKFPDF(@PathVariable("id") Integer id,@LoginUser UserEntity user) throws IOException {
        Map<String, Object> tmp_params = new HashMap<>();
        tmp_params.put("type","惠残事项康复补助");
        tmp_params.put("status","1");
        tmp_params.put("type_id",id);
        List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
        if (alive_list.size()>0) {
            CjroneSignatureEntity cjroneSignatureEntity = alive_list.get(0);
            if ("电子公章".equals("电子公章")){
                String tmp_file_name = System.currentTimeMillis()+"_"+cjroneSignatureEntity.getFileName();
                Files.copy(new File(cjroneSignatureEntity.getFileActUrl()).toPath(),new File(cjroneProperties.getTempFilePath()+tmp_file_name).toPath());

                return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", tmp_file_name).put("signId",cjroneSignatureEntity.getId());

            }else {
                return R.ok().put("fileUrl", cjroneSignatureEntity.getUrl()).put("fileName", cjroneSignatureEntity.getFileName()).put("signId",cjroneSignatureEntity.getId());
            }
        }else {

            //根据编号获得详细信息
            CjroneRehabilitationSubsidyEntity cjroneRehabilitationSubsidyEntity = cjroneRehabilitationSubsidService.getById(id);

            Calendar now = Calendar.getInstance();
            String nums[] = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};

            // 原pdf模板路径
            String templatePath = cjroneProperties.getTempletePath()+"康复补助模板.pdf";
            // 生成的新文件路径
            String newPDFPath = cjroneProperties.getSignaturePath()+"rehabilitation_subsidy_"+cjroneRehabilitationSubsidyEntity.getIdCard()+".pdf";

            // 获得待生成的实体文件
            Map<String, String> map =new HashMap<String, String>();
            map.put("name",cjroneRehabilitationSubsidyEntity.getName()==null?"":cjroneRehabilitationSubsidyEntity.getName());
            if (cjroneRehabilitationSubsidyEntity.getSex() == 1)
                map.put("sex","男");
            else
                map.put("sex","女");
            map.put("nationality",cjroneRehabilitationSubsidyEntity.getNationality()==null?"":cjroneRehabilitationSubsidyEntity.getNationality());
            map.put("birthday",cjroneRehabilitationSubsidyEntity.getBirthday()==null?"":cjroneRehabilitationSubsidyEntity.getBirthday());
            map.put("idCard",cjroneRehabilitationSubsidyEntity.getIdCard()==null?"":cjroneRehabilitationSubsidyEntity.getIdCard());
            map.put("disableId",cjroneRehabilitationSubsidyEntity.getDisableId()==null?"":cjroneRehabilitationSubsidyEntity.getDisableId());
            if(cjroneRehabilitationSubsidyEntity.getDisabilityCategory()==null){
                map.put("disabilityType", "");
            }
            else{
                if("1".equals(cjroneRehabilitationSubsidyEntity.getDisabilityCategory())){
                    map.put("disabilityType", "视力残疾");
                }
                else if("2".equals(cjroneRehabilitationSubsidyEntity.getDisabilityCategory())){
                    map.put("disabilityType", "听力残疾");
                }
                else if("3".equals(cjroneRehabilitationSubsidyEntity.getDisabilityCategory())){
                    map.put("disabilityType", "智力残疾");
                }
                else if("4".equals(cjroneRehabilitationSubsidyEntity.getDisabilityCategory())){
                    map.put("disabilityType", "精神残疾");
                }
                else if("5".equals(cjroneRehabilitationSubsidyEntity.getDisabilityCategory())){
                    map.put("disabilityType", "肢体（神经系统疾病致残）");
                }
                else if("6".equals(cjroneRehabilitationSubsidyEntity.getDisabilityCategory())){
                    map.put("disabilityType", "言语残疾");
                }
                else if("7".equals(cjroneRehabilitationSubsidyEntity.getDisabilityCategory())){
                    map.put("disabilityType", "肢体（非神经系统疾病致残）");
                }
                else{
                    map.put("disabilityType", "多重残疾");
                }
            }
            map.put("disabilityDegree",cjroneRehabilitationSubsidyEntity.getDisabilityDegree()==null?"未评":nums[Integer.parseInt(cjroneRehabilitationSubsidyEntity.getDisabilityDegree())] + "级");
            map.put("presentAddress",cjroneRehabilitationSubsidyEntity.getPresentAddress()==null?"":cjroneRehabilitationSubsidyEntity.getPresentAddress());
            map.put("mobilePhone",cjroneRehabilitationSubsidyEntity.getMobilePhone()==null?"":cjroneRehabilitationSubsidyEntity.getMobilePhone());
            map.put("economicSituation",cjroneRehabilitationSubsidyEntity.getFamilyEconoCondition()==null?"":cjroneRehabilitationSubsidyEntity.getFamilyEconoCondition());
            map.put("medicalInsurance",cjroneRehabilitationSubsidyEntity.getMedicalInsurance()==null?"":cjroneRehabilitationSubsidyEntity.getMedicalInsurance());
            map.put("rehabilitationNeedsProject",cjroneRehabilitationSubsidyEntity.getRehabilitationProjectName()==null?"":cjroneRehabilitationSubsidyEntity.getRehabilitationProjectName());
            map.put("applicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
            map.put("year",now.get(Calendar.YEAR)+"");
            // rsEntity.setZhenApplicationDate(now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
            //rsEntity.setShiApplicationDate(now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
            //rsEntity.setServiceOrganizationRecord("根据康复需求评估得到项目实施，康复专项补贴：￥_____ 元");
            // rsEntity.setServiceOrganizationRecordDate(now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");

            FileOutputStream out;
            int num = 1;//页数
            ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
            try {
                out = new FileOutputStream(newPDFPath);// 输出流
                Document doc = new Document();   //新建一个文档
                PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                doc.open();

                for (int i = 0; i < num; i++) {
                    bos[i] = new ByteArrayOutputStream();
                    PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                    PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                    AcroFields form = stamper.getAcroFields(); //获取文本域
                    // BaseFont
                    // 1、使用iTextAsian.jar中的字体
                    // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                    // 2、使用Windows系统字体(TrueType)
                    // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                    // 3、使用资源字体(ClassPath)
                    // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                    BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                    java.util.Iterator<String> it = form.getFields().keySet().iterator();
                    while (it.hasNext()) {
                        String name = it.next();
                        System.out.println(name + ":"+map.get(name));
                        form.setFieldProperty(name,"textfont",font,null);
                        // form.addSubstitutionFont(font);
                        form.setField(name, map.get(name));
                    }

                    stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                    stamper.close();

                }
                PdfImportedPage page = null;
                for (int i = 0; i < num; i++) {
                    page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                    copy.addPage(page);
                }
                doc.close();
                out.close();
            } catch (IOException e) {
                System.out.println("导出异常");
            } catch (DocumentException e) {
                System.out.println("文档异常");
            }


            // 生成电子签章pdf 并保存
            //String filePath = pdfUtils.pdfRehabilitationSubsidyApplyToSignature(cjroneProperties.getTempletePath(), cjroneProperties.getSignaturePath(), rsEntity);
            CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
            cjroneSignature.setUrl("/rehabilitation_subsidy_" + cjroneRehabilitationSubsidyEntity.getIdCard()+".pdf");
            cjroneSignature.setFileActUrl(newPDFPath);
            cjroneSignature.setCreateDate(new Date());
            cjroneSignature.setCreateId(getUserId());
            cjroneSignature.setType("惠残事项康复补助");
            cjroneSignature.setTypeId(cjroneRehabilitationSubsidyEntity.getId());
            cjroneSignature.setFileName("rehabilitation_subsidy_"+cjroneRehabilitationSubsidyEntity.getIdCard()+".pdf");
            cjroneSignature.setAccountId(getUserId().toString());
            cjroneSignature.setAccountName(getUser().getUsername());
            cjroneSignature.setStatus("1");

            cjroneSignatureService.save(cjroneSignature);
            // return R.ok().put("fileName", "signature_"+ PinYinUtil.getFullSpell(cjroneDisabilityHospital.getName())+".pdf").put("signId",cjroneSignature.getId());
            return R.ok().put("fileUrl", cjroneSignature.getUrl()).put("fileName", cjroneSignature.getFileName()).put("signId",cjroneSignature.getId());


        }

    }


    /**
     * 生成电子签章 pdf
     */
/*    @Login
    @RequestMapping("/printThreePDF/{idCard}")*/
    public R printThreePDF(@PathVariable("idCard") String idCard,@LoginUser UserEntity user) throws UnsupportedEncodingException {
        Map<String, Object> params = new HashMap<>();
        params.put("id_card",idCard);
        params.put("sign_status",'1');
        List<CjroneWelfareMatterApplicationEntity> matterApplicationEntities = (List<CjroneWelfareMatterApplicationEntity>) cjroneWelfareMatterApplicationService.listByMap(params);
        List<CjroneWelfareMatterApplicationEntity> result_list = new ArrayList<>();
        matterApplicationEntities.forEach(matterApplicationEntity -> {
            if ("生活补贴".equals(matterApplicationEntity.getMatterName())) {


                //根据编号获得详细信息
                CjroneLivingAllowanceEntity cjroneLivingAllowance = cjroneLivingAllowanceService.getById(matterApplicationEntity.getMatterId());
                //获得残疾证信息
                DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity = disabilityCertificateApplicationService.getByIDCard(cjroneLivingAllowance.getIdCard());

                String nums[] = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
                Calendar now = Calendar.getInstance();

                // 原pdf模板路径
                String templatePath = cjroneProperties.getTempletePath()+"生活补助模板.pdf";
                // 生成的新文件路径
                String newPDFPath = cjroneProperties.getSignaturePath()+"living_allowance_"+cjroneLivingAllowance.getIdCard()+".pdf";

                // 获得待生成的实体文件
                Map<String, String> map =new HashMap<String, String>();
                map.put("name", cjroneLivingAllowance.getName()==null?"":cjroneLivingAllowance.getName());
                if (cjroneLivingAllowance.getSex() == 1)
                    map.put("sex", "男");
                else
                    map.put("sex", "女");
                map.put("birthday", cjroneLivingAllowance.getBirthday()==null?"":cjroneLivingAllowance.getBirthday());
                map.put("mobile", cjroneLivingAllowance.getMobilePhone()==null?"":cjroneLivingAllowance.getMobilePhone());
                map.put("idCard", cjroneLivingAllowance.getIdCard()==null?"":cjroneLivingAllowance.getIdCard());

                if(cjroneLivingAllowance.getDisabilityCategory()==null){
                    map.put("disabilityType", "");
                }
                else{
                    if("1".equals(cjroneLivingAllowance.getDisabilityCategory())){
                        map.put("disabilityType", "视力残疾");
                    }
                    else if("2".equals(cjroneLivingAllowance.getDisabilityCategory())){
                        map.put("disabilityType", "听力残疾");
                    }
                    else if("3".equals(cjroneLivingAllowance.getDisabilityCategory())){
                        map.put("disabilityType", "智力残疾");
                    }
                    else if("4".equals(cjroneLivingAllowance.getDisabilityCategory())){
                        map.put("disabilityType", "精神残疾");
                    }
                    else if("5".equals(cjroneLivingAllowance.getDisabilityCategory())){
                        map.put("disabilityType", "肢体（神经系统疾病致残）");
                    }
                    else if("6".equals(cjroneLivingAllowance.getDisabilityCategory())){
                        map.put("disabilityType", "言语残疾");
                    }
                    else if("7".equals(cjroneLivingAllowance.getDisabilityCategory())){
                        map.put("disabilityType", "肢体（非神经系统疾病致残）");
                    }
                    else{
                        map.put("disabilityType", "多重残疾");
                    }
                }
                map.put("disabilityDegree", cjroneLivingAllowance.getDisabilityDegree()==null?"无等":nums[Integer.parseInt(cjroneLivingAllowance.getDisabilityDegree())] + "级");
                map.put("disableId", cjroneLivingAllowance.getDisableId()==null?"":cjroneLivingAllowance.getDisableId());
                // 从残疾证表中获得详细的镇街道，村社区地址
                if(disabilityCertificateApplicationEntity!=null)
                    map.put("nativeAddress", disabilityCertificateApplicationEntity.getNativeZhenName()==null?"":disabilityCertificateApplicationEntity.getNativeZhenName()+disabilityCertificateApplicationEntity.getNativeCunName());
                else
                    map.put("nativeAddress", cjroneLivingAllowance.getNativePlace()==null?"":cjroneLivingAllowance.getNativePlace());
                map.put("bankName", cjroneLivingAllowance.getBankName()==null?"":cjroneLivingAllowance.getBankName());
                map.put("bankAccount", cjroneLivingAllowance.getBankAccount()==null?"":cjroneLivingAllowance.getBankAccount());
                map.put("guardianName", cjroneLivingAllowance.getGuardianName()==null?"":cjroneLivingAllowance.getGuardianName());
                map.put("guardianPhone", cjroneLivingAllowance.getGuardianPhone()==null?"":cjroneLivingAllowance.getGuardianPhone());
                map.put("economicSituation", cjroneLivingAllowance.getFamilyEconoCondition()==null?"":cjroneLivingAllowance.getFamilyEconoCondition());
                map.put("applicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
                //map.put("zhenApplicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
                //map.put("shiApplicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
                //开始关联补助金额
                Map<String, Object> mapparams =new HashMap<String, Object>();
                mapparams.put("type","生活补贴");
                List<CjroneTwoSubsidyStandardsEntity> cjroneTwoSubsidyStandardsEntityList=cjroneTwoSubsidyStandardsService.queryByMap(mapparams);
                if(cjroneTwoSubsidyStandardsEntityList!=null&&cjroneTwoSubsidyStandardsEntityList.size()>0){
                    map.put("subsidymoney",cjroneTwoSubsidyStandardsEntityList.get(0).getMoney().toString());
                }
                else{
                    map.put("subsidymoney","无数据");
                }


                FileOutputStream out;
                int num = 1;//页数
                ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
                try {
                    out = new FileOutputStream(newPDFPath);// 输出流
                    Document doc = new Document();   //新建一个文档
                    PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                    doc.open();

                    for (int i = 0; i < num; i++) {
                        bos[i] = new ByteArrayOutputStream();
                        PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                        PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                        AcroFields form = stamper.getAcroFields(); //获取文本域
                        // BaseFont
                        // 1、使用iTextAsian.jar中的字体
                        // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                        // 2、使用Windows系统字体(TrueType)
                        // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                        // 3、使用资源字体(ClassPath)
                        // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                        BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                        java.util.Iterator<String> it = form.getFields().keySet().iterator();
                        while (it.hasNext()) {
                            String name = it.next();
                            System.out.println(name + ":"+map.get(name));
                            form.setFieldProperty(name,"textfont",font,null);
                            // form.addSubstitutionFont(font);
                            form.setField(name, map.get(name));
                        }

                        stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                        stamper.close();

                    }
                    PdfImportedPage page = null;
                    for (int i = 0; i < num; i++) {
                        page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                        copy.addPage(page);
                    }
                    doc.close();
                    out.close();
                } catch (IOException e) {
                    System.out.println("导出异常");
                } catch (DocumentException e) {
                    System.out.println("文档异常");
                }

                CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
                cjroneSignature.setUrl("/living_allowance_" + cjroneLivingAllowance.getIdCard() + ".pdf");
                cjroneSignature.setFileActUrl(newPDFPath);
                cjroneSignature.setCreateDate(new Date());
                cjroneSignature.setCreateId(user.getUserId());
                cjroneSignature.setType("惠残事项生活补贴");
                cjroneSignature.setTypeId(cjroneLivingAllowance.getId());
                cjroneSignature.setFileName("living_allowance_" + cjroneLivingAllowance.getIdCard() + ".pdf");
                cjroneSignature.setAccountId(user.getUserId().toString());
                cjroneSignature.setAccountName(user.getUsername());
                cjroneSignature.setStatus("1");

                cjroneSignatureService.save(cjroneSignature);
                matterApplicationEntity.setPhoto(cjroneSignature.getUrl());
                result_list.add(matterApplicationEntity);
            }
            else if ("康复补助".equals(matterApplicationEntity.getMatterName())) {


                //根据编号获得详细信息
                CjroneRehabilitationSubsidyEntity cjroneRehabilitationSubsidyEntity = cjroneRehabilitationSubsidService.getById(matterApplicationEntity.getMatterId());

                Calendar now = Calendar.getInstance();
                String nums[] = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};

                // 原pdf模板路径
                String templatePath = cjroneProperties.getTempletePath()+"康复补助模板.pdf";
                // 生成的新文件路径
                String newPDFPath = cjroneProperties.getSignaturePath()+"rehabilitation_subsidy_"+cjroneRehabilitationSubsidyEntity.getIdCard()+".pdf";

                // 获得待生成的实体文件
                Map<String, String> map =new HashMap<String, String>();
                map.put("name",cjroneRehabilitationSubsidyEntity.getName()==null?"":cjroneRehabilitationSubsidyEntity.getName());
                if (cjroneRehabilitationSubsidyEntity.getSex() == 1)
                    map.put("sex","男");
                else
                    map.put("sex","女");
                map.put("nationality",cjroneRehabilitationSubsidyEntity.getNationality()==null?"":cjroneRehabilitationSubsidyEntity.getNationality());
                map.put("birthday",cjroneRehabilitationSubsidyEntity.getBirthday()==null?"":cjroneRehabilitationSubsidyEntity.getBirthday());
                map.put("idCard",cjroneRehabilitationSubsidyEntity.getIdCard()==null?"":cjroneRehabilitationSubsidyEntity.getIdCard());
                map.put("disableId",cjroneRehabilitationSubsidyEntity.getDisableId()==null?"":cjroneRehabilitationSubsidyEntity.getDisableId());
                if(cjroneRehabilitationSubsidyEntity.getDisabilityCategory()==null){
                    map.put("disabilityType", "");
                }
                else{
                    if("1".equals(cjroneRehabilitationSubsidyEntity.getDisabilityCategory())){
                        map.put("disabilityType", "视力残疾");
                    }
                    else if("2".equals(cjroneRehabilitationSubsidyEntity.getDisabilityCategory())){
                        map.put("disabilityType", "听力残疾");
                    }
                    else if("3".equals(cjroneRehabilitationSubsidyEntity.getDisabilityCategory())){
                        map.put("disabilityType", "智力残疾");
                    }
                    else if("4".equals(cjroneRehabilitationSubsidyEntity.getDisabilityCategory())){
                        map.put("disabilityType", "精神残疾");
                    }
                    else if("5".equals(cjroneRehabilitationSubsidyEntity.getDisabilityCategory())){
                        map.put("disabilityType", "肢体（神经系统疾病致残）");
                    }
                    else if("6".equals(cjroneRehabilitationSubsidyEntity.getDisabilityCategory())){
                        map.put("disabilityType", "言语残疾");
                    }
                    else if("7".equals(cjroneRehabilitationSubsidyEntity.getDisabilityCategory())){
                        map.put("disabilityType", "肢体（非神经系统疾病致残）");
                    }
                    else{
                        map.put("disabilityType", "多重残疾");
                    }
                }
                map.put("disabilityDegree",cjroneRehabilitationSubsidyEntity.getDisabilityDegree()==null?"未评":nums[Integer.parseInt(cjroneRehabilitationSubsidyEntity.getDisabilityDegree())] + "级");
                map.put("presentAddress",cjroneRehabilitationSubsidyEntity.getPresentAddress()==null?"":cjroneRehabilitationSubsidyEntity.getPresentAddress());
                map.put("mobilePhone",cjroneRehabilitationSubsidyEntity.getMobilePhone()==null?"":cjroneRehabilitationSubsidyEntity.getMobilePhone());
                map.put("economicSituation",cjroneRehabilitationSubsidyEntity.getFamilyEconoCondition()==null?"":cjroneRehabilitationSubsidyEntity.getFamilyEconoCondition());
                map.put("medicalInsurance",cjroneRehabilitationSubsidyEntity.getMedicalInsurance()==null?"":cjroneRehabilitationSubsidyEntity.getMedicalInsurance());
                map.put("rehabilitationNeedsProject",cjroneRehabilitationSubsidyEntity.getRehabilitationProjectName()==null?"":cjroneRehabilitationSubsidyEntity.getRehabilitationProjectName());
                map.put("applicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
                map.put("year",now.get(Calendar.YEAR)+"");
                // rsEntity.setZhenApplicationDate(now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
                //rsEntity.setShiApplicationDate(now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");
                //rsEntity.setServiceOrganizationRecord("根据康复需求评估得到项目实施，康复专项补贴：￥_____ 元");
                // rsEntity.setServiceOrganizationRecordDate(now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");

                FileOutputStream out;
                int num = 1;//页数
                ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
                try {
                    out = new FileOutputStream(newPDFPath);// 输出流
                    Document doc = new Document();   //新建一个文档
                    PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                    doc.open();

                    for (int i = 0; i < num; i++) {
                        bos[i] = new ByteArrayOutputStream();
                        PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                        PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                        AcroFields form = stamper.getAcroFields(); //获取文本域
                        // BaseFont
                        // 1、使用iTextAsian.jar中的字体
                        // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                        // 2、使用Windows系统字体(TrueType)
                        // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                        // 3、使用资源字体(ClassPath)
                        // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                        BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                        java.util.Iterator<String> it = form.getFields().keySet().iterator();
                        while (it.hasNext()) {
                            String name = it.next();
                            System.out.println(name + ":"+map.get(name));
                            form.setFieldProperty(name,"textfont",font,null);
                            // form.addSubstitutionFont(font);
                            form.setField(name, map.get(name));
                        }

                        stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                        stamper.close();

                    }
                    PdfImportedPage page = null;
                    for (int i = 0; i < num; i++) {
                        page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                        copy.addPage(page);
                    }
                    doc.close();
                    out.close();
                } catch (IOException e) {
                    System.out.println("导出异常");
                } catch (DocumentException e) {
                    System.out.println("文档异常");
                }

                CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
                cjroneSignature.setUrl("/rehabilitation_subsidy_" + cjroneRehabilitationSubsidyEntity.getIdCard() + ".pdf");
                cjroneSignature.setFileActUrl(newPDFPath);
                cjroneSignature.setCreateDate(new Date());
                cjroneSignature.setCreateId(user.getUserId());
                cjroneSignature.setType("惠残事项康复补助");
                cjroneSignature.setTypeId(cjroneRehabilitationSubsidyEntity.getId());
                cjroneSignature.setFileName("rehabilitation_subsidy_" + cjroneRehabilitationSubsidyEntity.getIdCard() + ".pdf");
                cjroneSignature.setAccountId(user.getUserId().toString());
                cjroneSignature.setAccountName(user.getUsername());
                cjroneSignature.setStatus("1");

                cjroneSignatureService.save(cjroneSignature);
                matterApplicationEntity.setPhoto(cjroneSignature.getUrl());
                result_list.add(matterApplicationEntity);
            }
            else if ("护理补贴".equals(matterApplicationEntity.getMatterName())) {

                //根据编号获得详细信息
                CjroneNursingSubsidyEntity cjroneNursingSubsidyEntity = cjroneNursingSubsidyService.getById(matterApplicationEntity.getMatterId());
                //获得残疾证信息
                DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity = disabilityCertificateApplicationService.getByIDCard(cjroneNursingSubsidyEntity.getIdCard());


                // 原pdf模板路径
                String templatePath = cjroneProperties.getTempletePath()+"护理补助模板.pdf";
                // 生成的新文件路径
                String newPDFPath = cjroneProperties.getSignaturePath()+"nursing_subsidy_"+cjroneNursingSubsidyEntity.getIdCard()+".pdf";

                // 获得待生成的实体文件
                Map<String, String> map =new HashMap<String, String>();
                Map<String, Object> mapparams =new HashMap<String, Object>();

                if(cjroneNursingSubsidyEntity.getCareType()!=null){
                    mapparams.put("isConcentratedCare",0);
                    String aaa[] = cjroneNursingSubsidyEntity.getCareType().substring(1, cjroneNursingSubsidyEntity.getCareType().length() - 1).split(",");
                    String careTypeName = null;
                    for (String s : aaa) {
                        if (s.replace("\"", "").equals("1")) {
                            if (careTypeName == null) {
                                careTypeName = "居家安养 ";
                            } else {
                                careTypeName = careTypeName + "居家安养 ";
                            }
                        } else if (s.replace("\"", "").equals("2")) {
                            if (careTypeName == null) {
                                careTypeName = "日间照料 ";
                            } else {
                                careTypeName = careTypeName + "日间照料 ";
                            }
                        } else if (s.replace("\"", "").equals("3")) {
                            if (careTypeName == null) {
                                careTypeName = "集中托养 ";
                            } else {
                                careTypeName = careTypeName + "集中托养 ";
                            }
                            mapparams.put("isConcentratedCare",1);
                        } else if (s.replace("\"", "").equals("4")) {
                            if (careTypeName == null) {
                                careTypeName = "项目服务 ";
                            } else {
                                careTypeName = careTypeName + "项目服务 ";
                            }
                        }
                    }
                    cjroneNursingSubsidyEntity.setCareType(careTypeName);
                }
                else{
                    cjroneNursingSubsidyEntity.setCareType("无");
                }

                //获取年月日数据
                Calendar now = Calendar.getInstance();
                String nums[] = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};

                // 获得待生成的实体文件
                map.put("name", cjroneNursingSubsidyEntity.getName());
                if (cjroneNursingSubsidyEntity.getSex() == 1)
                    map.put("sex", "男");
                else
                    map.put("sex", "女");
                map.put("birthday", cjroneNursingSubsidyEntity.getBirthday()==null?"":cjroneNursingSubsidyEntity.getBirthday());
                map.put("mobilePhone", cjroneNursingSubsidyEntity.getMobilePhone()==null?"":cjroneNursingSubsidyEntity.getMobilePhone());
                map.put("idCard", cjroneNursingSubsidyEntity.getIdCard()==null?"":cjroneNursingSubsidyEntity.getIdCard());
                if(cjroneNursingSubsidyEntity.getDisabilityCategory()==null){
                    map.put("disabilityType", "");
                    mapparams.put("disabilityType","");
                }
                else{
                    if("1".equals(cjroneNursingSubsidyEntity.getDisabilityCategory())){
                        map.put("disabilityType", "视力残疾");
                        mapparams.put("disabilityType","视力");
                    }
                    else if("2".equals(cjroneNursingSubsidyEntity.getDisabilityCategory())){
                        map.put("disabilityType", "听力残疾");
                        mapparams.put("disabilityType","听力");
                    }
                    else if("3".equals(cjroneNursingSubsidyEntity.getDisabilityCategory())){
                        map.put("disabilityType", "智力残疾");
                        mapparams.put("disabilityType","智力");
                    }
                    else if("4".equals(cjroneNursingSubsidyEntity.getDisabilityCategory())){
                        map.put("disabilityType", "精神残疾");
                        mapparams.put("disabilityType","精神");
                    }
                    else if("5".equals(cjroneNursingSubsidyEntity.getDisabilityCategory())){
                        map.put("disabilityType", "肢体（神经系统疾病致残）");
                        mapparams.put("disabilityType","肢体");
                    }
                    else if("6".equals(cjroneNursingSubsidyEntity.getDisabilityCategory())){
                        map.put("disabilityType", "言语残疾");
                        mapparams.put("disabilityType","言语");
                    }
                    else if("7".equals(cjroneNursingSubsidyEntity.getDisabilityCategory())){
                        map.put("disabilityType", "肢体（非神经系统疾病致残）");
                        mapparams.put("disabilityType","肢体");
                    }
                    else{
                        map.put("disabilityType", "多重残疾");
                    }
                }
                map.put("disabilityDegree", cjroneNursingSubsidyEntity.getDisabilityDegree()==null?"无等":nums[Integer.parseInt(cjroneNursingSubsidyEntity.getDisabilityDegree())] + "级");
                map.put("disableId", cjroneNursingSubsidyEntity.getDisableId()==null?"":cjroneNursingSubsidyEntity.getDisableId());
                if(disabilityCertificateApplicationEntity!=null)
                    map.put("nativeAddress", disabilityCertificateApplicationEntity.getNativeZhenName()==null?"":disabilityCertificateApplicationEntity.getNativeZhenName()+disabilityCertificateApplicationEntity.getNativeCunName());
                else
                    map.put("nativeAddress", cjroneNursingSubsidyEntity.getNativePlace()==null?"":cjroneNursingSubsidyEntity.getNativePlace());
                map.put("bankName", cjroneNursingSubsidyEntity.getBankName()==null?"":cjroneNursingSubsidyEntity.getBankName());
                map.put("bankAccount", cjroneNursingSubsidyEntity.getBankAccount()==null?"":cjroneNursingSubsidyEntity.getBankAccount());

                map.put("guardianName", cjroneNursingSubsidyEntity.getGuardianName()==null?"":cjroneNursingSubsidyEntity.getGuardianName());
                map.put("guardianPhone", cjroneNursingSubsidyEntity.getGuardianPhone()==null?"":cjroneNursingSubsidyEntity.getGuardianPhone());

                map.put("careServiceType",cjroneNursingSubsidyEntity.getCareType()==null?"":cjroneNursingSubsidyEntity.getCareType());
                map.put("degree",cjroneNursingSubsidyEntity.getDisabilityDegree()==null?"":nums[Integer.parseInt(cjroneNursingSubsidyEntity.getDisabilityDegree())]);

                map.put("applicationDate",now.get(Calendar.YEAR)+"年"+(now.get(Calendar.MONTH) + 1) +"月"+ now.get(Calendar.DAY_OF_MONTH)+"日");

                //开始关联补助金额
                mapparams.put("type","护理补贴");
                mapparams.put("disabilityDegree",cjroneNursingSubsidyEntity.getDisabilityDegree());
                List<CjroneTwoSubsidyStandardsEntity> cjroneTwoSubsidyStandardsEntityList=cjroneTwoSubsidyStandardsService.queryByMap(mapparams);
                if(cjroneTwoSubsidyStandardsEntityList!=null&&cjroneTwoSubsidyStandardsEntityList.size()>0){
                    map.put("subsidymoney",cjroneTwoSubsidyStandardsEntityList.get(0).getMoney().toString());
                }
                else{
                    map.put("subsidymoney","无数据");
                }

                FileOutputStream out;
                int num = 1;//页数
                ByteArrayOutputStream bos[] = new ByteArrayOutputStream[num];
                try {
                    out = new FileOutputStream(newPDFPath);// 输出流
                    Document doc = new Document();   //新建一个文档
                    PdfCopy copy = new PdfCopy(doc, out); //用于保存原页面内容,然后输出
                    doc.open();

                    for (int i = 0; i < num; i++) {
                        bos[i] = new ByteArrayOutputStream();
                        PdfReader reader = new PdfReader(templatePath);// 读取pdf模板
                        PdfStamper stamper = new PdfStamper(reader, bos[i]); //生成输出流
                        AcroFields form = stamper.getAcroFields(); //获取文本域
                        // BaseFont
                        // 1、使用iTextAsian.jar中的字体
                        // BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
                        // 2、使用Windows系统字体(TrueType)
                        // BaseFont.createFont("C:/WINDOWS/Fonts/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);    
                        // 3、使用资源字体(ClassPath)
                        // BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);   
                        BaseFont font = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);

                        java.util.Iterator<String> it = form.getFields().keySet().iterator();
                        while (it.hasNext()) {
                            String name = it.next();
                            System.out.println(name + ":"+map.get(name));
                            form.setFieldProperty(name,"textfont",font,null);
                            // form.addSubstitutionFont(font);
                            form.setField(name, map.get(name));
                        }

                        stamper.setFormFlattening(true);// 如果为false那么生成的PDF文件还能编辑，一定要设为true
                        stamper.close();

                    }
                    PdfImportedPage page = null;
                    for (int i = 0; i < num; i++) {
                        page = copy.getImportedPage(new PdfReader(bos[i].toByteArray()), i + 1);
                        copy.addPage(page);
                    }
                    doc.close();
                    out.close();
                } catch (IOException e) {
                    System.out.println("导出异常");
                } catch (DocumentException e) {
                    System.out.println("文档异常");
                }

                CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
                cjroneSignature.setUrl("/nursing_subsidy_" + cjroneNursingSubsidyEntity.getIdCard() + ".pdf");
                cjroneSignature.setFileActUrl(newPDFPath);
                cjroneSignature.setCreateDate(new Date());
                cjroneSignature.setCreateId(user.getUserId());
                cjroneSignature.setType("惠残事项护理补贴");
                cjroneSignature.setTypeId(cjroneNursingSubsidyEntity.getId());
                cjroneSignature.setFileName("nursing_subsidy_" + cjroneNursingSubsidyEntity.getIdCard() + ".pdf");
                cjroneSignature.setAccountId(user.getUserId().toString());
                cjroneSignature.setAccountName(user.getUsername());
                cjroneSignature.setStatus("1");

                cjroneSignatureService.save(cjroneSignature);
                matterApplicationEntity.setPhoto(cjroneSignature.getUrl());
                result_list.add(matterApplicationEntity);
            }
        });
        CjroneWelfareMatterApplicationEntity welfareMatterApplicationEntity = new CjroneWelfareMatterApplicationEntity();
        if (result_list.size() != 0){
            welfareMatterApplicationEntity = result_list.get(0);
        }
        return R.ok().put("signTotal",result_list.size()).put("matterId",welfareMatterApplicationEntity.getMatterId()).put("matterName",welfareMatterApplicationEntity.getMatterName()).put("fileUrl",welfareMatterApplicationEntity.getPhoto());
    }


}
