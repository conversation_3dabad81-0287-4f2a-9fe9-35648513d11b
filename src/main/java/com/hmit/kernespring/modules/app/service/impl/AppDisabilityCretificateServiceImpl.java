package com.hmit.kernespring.modules.app.service.impl;

import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.modules.app.dao.AppDisabilityCretificateDao;
import com.hmit.kernespring.modules.app.service.AppDisabilityCretificateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("appDisabilityCretificateServiceImpl")
public class AppDisabilityCretificateServiceImpl implements AppDisabilityCretificateService {

    @Autowired
    AppDisabilityCretificateDao appDisabilityCretificateDao;

    /**
     * 获得残疾类别列表
     * @param params
     * @return
     */
    @Override
    public PageUtils GetAssessmentCateList(Map<String, Object> params) {
    return null;
    }

    @Override
    public List<Map<String, Object>> getApplyList(Map<String, Object> params) {
        return appDisabilityCretificateDao.getApplyList(params);
    }

    @Override
    public Integer getQueueNum(Map<String, Object> params) {
        return appDisabilityCretificateDao.getQueueNum(params);
    }

}
