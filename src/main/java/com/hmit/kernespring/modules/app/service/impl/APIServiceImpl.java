package com.hmit.kernespring.modules.app.service.impl;

import com.google.gson.Gson;
import com.hmit.kernespring.common.utils.CjroneApp;
import com.hmit.kernespring.common.utils.CjroneAppUtil;
import com.hmit.kernespring.common.utils.MD5;
import com.hmit.kernespring.modules.app.service.APIService;
import com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity;
import com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity;
import com.hmit.kernespring.modules.sys.service.SysConfigService;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service("APIService")
public class APIServiceImpl implements APIService {

    @Autowired
    private SysConfigService sysConfigService;

    protected static final Logger logger = LoggerFactory.getLogger(APIServiceImpl.class);

    /*@Override
    public Map<String, Object> getSecretFrist() {

    }*/

    @Async("taskExecutor")
    public void t1(){
        for(int i=0;i<10;i++){

            logger.info("1111");
        }
    }

    @Async("taskExecutor")
    public void t2(){
        for(int i=0;i<10;i++){
            logger.info("2222");
        }
    }

    @Async("taskExecutor")
    public void t3(){
        for(int i=0;i<10;i++){
            logger.info("3333");
        }
    }

    @Override
    @Async("taskExecutor")
    public void asyncblfddbr(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException {
        for(int i=0;i<10 ;i++) {
            Map<String, Object> doFdDbInfo = this.queryFDDBRInfo(dataDisabilityCertificateEntities.get(i).getIdCard(), null);
            System.out.println("是否个体工商户--------->" + doFdDbInfo);
            logger.info("是否个体工商户++++++++++++++++++++++++" + i);
            //Thread.sleep(500);
        }
    }

    @Override
    @Async("taskExecutor")
    public void asyncblhh(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException {
        for(int i=0;i<21000;i++) {
            Map<String, Object> dohhInfo = this.queryHHInfo(dataDisabilityCertificateEntities.get(i).getIdCard(), null);
            System.out.println("火化--------->"+dohhInfo);
            logger.info("火化++++++++++++++++++++++++++++++++++"+i);
            //Thread.sleep(300);
        }
    }

    @Override
    @Async("taskExecutor")
    public void asyncblfx(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException {
        for(int i=0;i<21000;i++) {
            Map<String, Object> dofxInfo = this.queryFXInfo(dataDisabilityCertificateEntities.get(i).getIdCard(), null);
            System.out.println("服刑信息--------->"+dofxInfo);
            logger.info("服刑信息++++++++++++++++++++++++++++++"+i);
            //Thread.sleep(500);
        }
    }

    @Override
    @Async("taskExecutor")
    public void asyncblhj(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException {
        for(int i=0;i<21000;i++) {
            Map<String, Object> dohjInfo = this.queryHuJiInfo(dataDisabilityCertificateEntities.get(i).getIdCard(), null);
            System.out.println("户籍信息--------->"+dohjInfo);
            logger.info("户籍信息++++++++++++++++++++++++++++++"+i);
            //Thread.sleep(0);
        }
    }

    @Override
    @Async("taskExecutor")
    public void asyncblhjqc(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException {
        for(int i=0;i<21000;i++) {
            Map<String, Object> dohjqcInfo = this.queryHuJiQCInfo(dataDisabilityCertificateEntities.get(i).getIdCard(), null);
            System.out.println("户籍迁出信息--------->"+dohjqcInfo);
            logger.info("户籍迁出信息+++++++++++++++++++++++++"+i);
            //Thread.sleep(300);
        }
    }

    @Override
    @Async("taskExecutor")
    public void asyncdzzz(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException {
        for(int i=0;i<10;i++) {
            DataDisabilityCertificateEntity de=dataDisabilityCertificateEntities.get(i);
            Map<String, Object> dodzzzInfo = this.queryCardIdPho(de.getIdCard(),de.getName(),de.getNationality(),de.getBirthday() ,null);
            System.out.println("电子证照--------->"+dodzzzInfo);
            logger.info("电子证照+++++++++++++++++++++++++"+i);
            //Thread.sleep(500);
        }
    }

    @Override
    @Async("taskExecutor")
    public void asyncdbjz(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException {
        for(int i=0;i<10;i++) {
            DataDisabilityCertificateEntity de=dataDisabilityCertificateEntities.get(i);
            LocalDate today = LocalDate.now();
            String year = Integer.valueOf(today.getYear()).toString();
            String month = today.getMonth().toString();

            Map<String, Object> dodzzzInfo = this.queryDIBAOInfo(de.getIdCard(),year,month,null);
            System.out.println("低保救助--------->"+dodzzzInfo);
            logger.info("低保救助+++++++++++++++++++++++++"+i);
            //Thread.sleep(300);
        }
    }

    @Override
    @Async("taskExecutor")
    public void asyncgdxj(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException {
        for(int i=0;i<10;i++) {
            DataDisabilityCertificateEntity de=dataDisabilityCertificateEntities.get(i);

            Map<String, Object> dodzzzInfo = this.queryDXSInfo(de.getIdCard(),de.getName(),null);
            System.out.println("高等学籍--------->"+dodzzzInfo);
            logger.info("高等学籍+++++++++++++++++++++++++"+i);
            //Thread.sleep(300);
        }
    }

    @Override
    @Async("taskExecutor")
    public void asyncqyyl(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException {
        for(int i=0;i<52000;i++) {
            DataDisabilityCertificateEntity de=dataDisabilityCertificateEntities.get(i);

            Map<String, Object> dodzzzInfo = this.queryQYYLInfo(de.getIdCard(),de.getName(),null);
            System.out.println("企业养老--------->"+dodzzzInfo);
            logger.info("企业养老+++++++++++++++++++++++++"+i);
            //Thread.sleep(300);
        }
    }

    @Override
    @Async("taskExecutor")
    public void asyncxsxx(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException {
        for(int i=0;i<10;i++) {
            DataDisabilityCertificateEntity de=dataDisabilityCertificateEntities.get(i);

            Map<String, Object> dodzzzInfo = this.queryZJSInfo(de.getIdCard(),de.getName(),null);
            System.out.println("学生信息--------->"+dodzzzInfo);
            logger.info("学生信息+++++++++++++++++++++++++"+i);
            //Thread.sleep(300);
        }
    }

    @Override
    @Async("taskExecutor")
    public void asynctk(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException {
        for(int i=0;i<10500;i++) {
            DataDisabilityCertificateEntity de=dataDisabilityCertificateEntities.get(i);

            Map<String, Object> dodzzzInfo = this.queryTKInfo(de.getIdCard(),de.getName(),null);
            System.out.println("特困信息--------->"+dodzzzInfo);
            logger.info("特困信息+++++++++++++++++++++++++"+i);
            //Thread.sleep(300);
        }
    }

    @Override
    @Async("taskExecutor")
    public void asyncdead(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException {
        for(int i=0;i<10;i++) {
            DataDisabilityCertificateEntity de=dataDisabilityCertificateEntities.get(i);

            Map<String, Object> dodzzzInfo = this.queryDeadInfo(de.getIdCard(),de.getName(),null);
            System.out.println("死亡信息--------->"+dodzzzInfo);
            logger.info("死亡信息+++++++++++++++++++++++++"+i);
            //Thread.sleep(300);
        }
    }

    @Override
    @Async("taskExecutor")
    public void asyncylbx(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException {
        for(int i=0;i<10;i++) {
            DataDisabilityCertificateEntity de=dataDisabilityCertificateEntities.get(i);

            Map<String, Object> dodzzzInfo = this.queryYLBXInfo(de.getIdCard(),de.getName(),null);
            System.out.println("医疗保险信息--------->"+dodzzzInfo);
            logger.info("医疗保险信息+++++++++++++++++++++++++"+i);
            //Thread.sleep(300);
        }
    }

    @Override
    @Async("taskExecutor")
    public void asynclhjy(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException {
        for(int i=0;i<31000;i++) {
            DataDisabilityCertificateEntity de=dataDisabilityCertificateEntities.get(i);

            Map<String, Object> dodzzzInfo = this.queryLHJYInfo(de.getIdCard(),de.getName(),null);
            System.out.println("灵活就业信息--------->"+dodzzzInfo);
            logger.info("灵活就业信息+++++++++++++++++++++++++"+i);
            //Thread.sleep(300);
        }
    }

    // 医疗保险参保人员信息
    @Override
    @Async("taskExecutor")
    public void asynylbxcb(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException {
        for(int i=0;i<10;i++) {
            DataDisabilityCertificateEntity de=dataDisabilityCertificateEntities.get(i);

            Map<String, Object> dodzzzInfo = this.queryYiLiaoBaoXian(de.getName(),de.getIdCard(),null);
            System.out.println("医疗保险参保人员信息--------->"+dodzzzInfo);
            logger.info("医疗保险参保人员信息+++++++++++++++++++++++++"+i);
            //Thread.sleep(300);
        }
    }

    // 市治安户籍迁出
    @Override
    @Async("taskExecutor")
    public void asynyshihujiqc(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException {
        for(int i=0;i<10;i++) {
            DataDisabilityCertificateEntity de=dataDisabilityCertificateEntities.get(i);

            Map<String, Object> dodzzzInfo = this.queryShiHuJiQC(de.getIdCard(),null);
            System.out.println("市治安户籍迁出--------->"+dodzzzInfo);
            logger.info("市治安户籍迁出+++++++++++++++++++++++++"+i);
            //Thread.sleep(300);
        }
    }

    // 省户口本
    @Override
    @Async("taskExecutor")
    public void asynyshenhukou(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException {
        for(int i=0;i<60000;i++) {
            DataDisabilityCertificateEntity de=dataDisabilityCertificateEntities.get(i);

            Map<String, Object> dodzzzInfo = this.queryHuKouBenInfo(de.getIdCard(),null);
            System.out.println("省户口本--------->"+dodzzzInfo);
            logger.info("省户口本+++++++++++++++++++++++++"+i);
            //Thread.sleep(300);
        }
    }

    // 社会保险个人参保信息
    @Override
    @Async("taskExecutor")
    public void asynyshebao(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException {
        for(int i=0;i<10;i++) {
            DataDisabilityCertificateEntity de=dataDisabilityCertificateEntities.get(i);

            Map<String, Object> dodzzzInfo = this.querySheBao(de.getName(),de.getIdCard(),null);
            System.out.println("社会保险个人参保--------->"+dodzzzInfo);
            logger.info("社会保险个人参保+++++++++++++++++++++++++"+i);
            //Thread.sleep(300);
        }
    }

    // 人口信息
    @Override
    @Async("taskExecutor")
    public void asynyrenkou(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException {
        for(int i=0;i<60000;i++) {
            DataDisabilityCertificateEntity de=dataDisabilityCertificateEntities.get(i);

            Map<String, Object> dodzzzInfo = this.queyuRenKou(de.getIdCard(),null);
            System.out.println("人口信息--------->"+dodzzzInfo);
            logger.info("人口信息+++++++++++++++++++++++++"+i);
            //Thread.sleep(300);
        }
    }

    // 特困信息
    @Override
    @Async("taskExecutor")
    public void asynytekun(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException {
        for(int i=0;i<10;i++) {
            DataDisabilityCertificateEntity de=dataDisabilityCertificateEntities.get(i);

            Map<String, Object> dodzzzInfo = this.queryTKInfo(de.getIdCard(),de.getName(),null);
            System.out.println("特困--------->"+dodzzzInfo);
            logger.info("特困+++++++++++++++++++++++++"+i);
            //Thread.sleep(300);
        }
    }

    // 殡葬
    @Override
    @Async("taskExecutor")
    public void asynybinzang(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException {
        for(int i=0;i<15000;i++) {
            DataDisabilityCertificateEntity de=dataDisabilityCertificateEntities.get(i);

            Map<String, Object> dodzzzInfo = this.queryBinZangInfo(de.getName(),de.getIdCard(),null);
            System.out.println("殡葬--------->"+dodzzzInfo);
            logger.info("殡葬+++++++++++++++++++++++++"+i);
            //Thread.sleep(300);
        }
    }

    // 流动人口
    @Override
    @Async("taskExecutor")
    public void asynyliudong(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException {
        for(int i=0;i<10;i++) {
            DataDisabilityCertificateEntity de=dataDisabilityCertificateEntities.get(i);

            Map<String, Object> dodzzzInfo = this.queryLiuDong(de.getIdCard(),de.getName(),null);
            System.out.println("流动人口-------->"+dodzzzInfo);
            logger.info("流动人口+++++++++++++++++++++++++"+i);
            //Thread.sleep(300);
        }
    }


    @Override
    public Map<String, Object> queryDIBAOInfo(String cardId, String year, String month, String additional) {


        Map<String, Object> result = new HashMap<>();
        additional = additional != null ? additional: "{}" ;
        String requestsec = sysConfigService.getValue("requestSecretNew");
        // 构造请求参数
        Long current_time = System.currentTimeMillis();
        String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY_NEW+requestsec+current_time)+"&appKey="+CjroneApp.APP_KEY_NEW+"&aHAP0015="+cardId+"&bHAX0114="+year+"&bHAX0115="+month+"&additional="+additional+"&datatype=json";
        result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_DIBAO_INFO_URL,params_vailidation);
        System.out.println("低保救助信息接口的返回数据如下：-===================================");
        System.out.println(result);
        return result;
    }

    @Override
    public Map<String, Object> queryQYYLInfo(String cardId, String name, String additional) {
       /* Map<String, Object> request_key_result = CjroneAppUtil.doOne(CjroneApp.APP_REQUEST_SECRET_URL, CjroneApp.APP_KEY, CjroneApp.APP_SECRET);
        System.out.println(request_key_result);
        Map<String, Object> result = new HashMap<>();
        if ("00".equals(request_key_result.get("code").toString()) && request_key_result.get("datas") != null){
            Map<String, Object> map = new Gson().fromJson(request_key_result.get("datas").toString(), Map.class);
            if (map.get("requestSecret") !=null){
                String appSecret = map.get("requestSecret") !=null ? map.get("requestSecret").toString():null ;
                additional = additional != null ? additional: "{}" ;
                // 构造请求参数
                Long current_time = System.currentTimeMillis();
                String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+appSecret+current_time)+"&appKey="+CjroneApp.APP_KEY+"&cardId="+cardId+"&name="+name+"&additional="+additional+"&datatype=json";
                result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_QYYL_INFO_URL,params_vailidation);
                System.out.println("企业养老保险接口的返回数据如下：-===================================");
                System.out.println(result);
            }
        }else {
            return request_key_result;
        }
        return result;*/

        Map<String, Object> result = new HashMap<>();
        additional = additional != null ? additional: "{}" ;
        String requestsec = sysConfigService.getValue("requestSecret");
        // 构造请求参数
        Long current_time = System.currentTimeMillis();
        String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+requestsec+current_time)+"&appKey="+CjroneApp.APP_KEY+"&cardId="+cardId+"&name="+name+"&additional="+additional+"&datatype=json";
        result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_QYYL_INFO_URL,params_vailidation);
        System.out.println("企业养老保险接口的返回数据如下：-===================================");
        System.out.println(result);
        return result;
    }

    @Override
    public Map<String, Object> queryDXSInfo(String cardId, String name, String additional) {
       /* Map<String, Object> request_key_result = CjroneAppUtil.doOne(CjroneApp.APP_REQUEST_SECRET_URL, CjroneApp.APP_KEY, CjroneApp.APP_SECRET);
        System.out.println(request_key_result);
        Map<String, Object> result = new HashMap<>();
        if ("00".equals(request_key_result.get("code").toString()) && request_key_result.get("datas") != null){
            Map<String, Object> map = new Gson().fromJson(request_key_result.get("datas").toString(), Map.class);
            if (map.get("requestSecret") !=null){
                String appSecret = map.get("requestSecret") !=null ? map.get("requestSecret").toString():null ;
                additional = additional != null ? additional: "{}" ;
                // 构造请求参数
                Long current_time = System.currentTimeMillis();
                String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+appSecret+current_time)+"&appKey="+CjroneApp.APP_KEY+"&zjhm="+cardId+"&xm="+name+"&additional="+additional+"&datatype=json";
                result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_DXS_INFO_URL,params_vailidation);
                System.out.println("大学生接口的返回数据如下：-===================================");
                System.out.println(result);
            }
        }else {
            return request_key_result;
        }
        return result;*/


        additional = additional != null ? additional: "{}" ;
        Map<String, Object> result = new HashMap<>();
        String requestsec = sysConfigService.getValue("requestSecretNew");
        // 构造请求参数
        Long current_time = System.currentTimeMillis();
        String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY_NEW+requestsec+current_time)+"&appKey="+CjroneApp.APP_KEY_NEW+"&zjhm="+cardId+"&xm="+name+"&additional="+additional+"&datatype=json";
        result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_DXS_INFO_URL,params_vailidation);
        System.out.println("大学生接口的返回数据如下：-===================================");
        System.out.println(result);
        return result;
    }

    @Override
    public Map<String, Object> queryZJSInfo(String cardId, String name, String additional) {
      /*  Map<String, Object> request_key_result = CjroneAppUtil.doOne(CjroneApp.APP_REQUEST_SECRET_URL, CjroneApp.APP_KEY, CjroneApp.APP_SECRET);
        System.out.println(request_key_result);
        Map<String, Object> result = new HashMap<>();
        if ("00".equals(request_key_result.get("code").toString()) && request_key_result.get("datas") != null){
            Map<String, Object> map = new Gson().fromJson(request_key_result.get("datas").toString(), Map.class);
            if (map.get("requestSecret") !=null){
                String appSecret = map.get("requestSecret") !=null ? map.get("requestSecret").toString():null ;
                additional = additional != null ? additional: "{}" ;
                // 构造请求参数
                Long current_time = System.currentTimeMillis();
                String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+appSecret+current_time)+"&appKey="+CjroneApp.APP_KEY+"&SFZJH="+cardId+"&XSXM="+name+"&additional="+additional+"&datatype=json";
                result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_ZJSXJ_INFO_URL,params_vailidation);
                System.out.println("学籍接口的返回数据如下：-===================================");
                System.out.println(result);
            }
        }else {
            return request_key_result;
        }
        return result;*/


        Map<String, Object> result = new HashMap<>();
        String requestsec = sysConfigService.getValue("requestSecretNew");
        additional = additional != null ? additional: "{}" ;
        // 构造请求参数
        Long current_time = System.currentTimeMillis();
        String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY_NEW+requestsec+current_time)+"&appKey="+CjroneApp.APP_KEY_NEW+"&SFZJH="+cardId+"&XSXM="+name+"&additional="+additional+"&datatype=json";
        result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_ZJSXJ_INFO_URL,params_vailidation);
        System.out.println("学籍接口的返回数据如下：-===================================");
        System.out.println(result);
        return result;
    }

    @Override
    public Map<String, Object> queryTKInfo(String cardId, String name, String additional) {
       /* Map<String, Object> request_key_result = CjroneAppUtil.doOne(CjroneApp.APP_REQUEST_SECRET_URL, CjroneApp.APP_KEY, CjroneApp.APP_SECRET);
        System.out.println(request_key_result);
        Map<String, Object> result = new HashMap<>();
        if ("00".equals(request_key_result.get("code").toString()) && request_key_result.get("datas") != null){
            Map<String, Object> map = new Gson().fromJson(request_key_result.get("datas").toString(), Map.class);
            if (map.get("requestSecret") !=null){
                String appSecret = map.get("requestSecret") !=null ? map.get("requestSecret").toString():null ;
                additional = additional != null ? additional: "{}" ;
                // 构造请求参数
                Long current_time = System.currentTimeMillis();
                String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+appSecret+current_time)+"&appKey="+CjroneApp.APP_KEY+"&cardId="+cardId+"&name="+name+"&additional="+additional+"&datatype=json";
                result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_TK,params_vailidation);
                System.out.println("特困接口的返回数据如下：-===================================");
                System.out.println(result);
            }
        }else {
            return request_key_result;
        }
        return result;*/


        Map<String, Object> result = new HashMap<>();
        String requestsec = sysConfigService.getValue("requestSecretNew");
        additional = additional != null ? additional: "{}" ;
        // 构造请求参数
        Long current_time = System.currentTimeMillis();
        String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY_NEW+requestsec+current_time)+"&appKey="+CjroneApp.APP_KEY_NEW+"&cardId="+cardId+"&name="+name+"&additional="+additional+"&datatype=json";
        result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_TK,params_vailidation);
        System.out.println("特困接口的返回数据如下：-===================================");
        System.out.println(result);
        return result;
    }

    @Override
    public Map<String, Object> queryDeadInfo(String cardId, String name, String additional) {
     /*   Map<String, Object> request_key_result = CjroneAppUtil.doOne(CjroneApp.APP_REQUEST_SECRET_URL, CjroneApp.APP_KEY, CjroneApp.APP_SECRET);
        System.out.println(request_key_result);
        Map<String, Object> result = new HashMap<>();
        if ("00".equals(request_key_result.get("code").toString()) && request_key_result.get("datas") != null){
            Map<String, Object> map = new Gson().fromJson(request_key_result.get("datas").toString(), Map.class);
            if (map.get("requestSecret") !=null){
                String appSecret = map.get("requestSecret") !=null ? map.get("requestSecret").toString():null ;
                additional = additional != null ? additional: "{}" ;
                // 构造请求参数
                Long current_time = System.currentTimeMillis();
                String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+appSecret+current_time)+"&appKey="+CjroneApp.APP_KEY+"&idCardCode="+cardId+"&name="+name+"&additional="+additional+"&datatype=json";
                result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_DEAD,params_vailidation);
                System.out.println("死亡接口的返回数据如下：-===================================");
                System.out.println(result);
            }
        }else {
            return request_key_result;
        }
        return result;*/



        Map<String, Object> result = new HashMap<>();
        String requestsec = sysConfigService.getValue("requestSecretNew");
        additional = additional != null ? additional: "{}" ;
        // 构造请求参数
        Long current_time = System.currentTimeMillis();
        String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY_NEW +requestsec+current_time)+"&appKey="+CjroneApp.APP_KEY_NEW +"&idCardCode="+cardId+"&name="+name+"&additional="+additional+"&datatype=json";
        result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_DEAD,params_vailidation);
        System.out.println("死亡接口的返回数据如下：-===================================");
        System.out.println(result);
        return result;
    }

    @Override
    public Map<String, Object> queryYLBXInfo(String cardId, String name, String additional) {
       /* Map<String, Object> request_key_result = CjroneAppUtil.doOne(CjroneApp.APP_REQUEST_SECRET_URL, CjroneApp.APP_KEY, CjroneApp.APP_SECRET);
        System.out.println(request_key_result);
        Map<String, Object> result = new HashMap<>();
        if ("00".equals(request_key_result.get("code").toString()) && request_key_result.get("datas") != null){
            Map<String, Object> map = new Gson().fromJson(request_key_result.get("datas").toString(), Map.class);
            if (map.get("requestSecret") !=null){
                String appSecret = map.get("requestSecret") !=null ? map.get("requestSecret").toString():null ;
                additional = additional != null ? additional: "{}" ;
                // 构造请求参数
                Long current_time = System.currentTimeMillis();
                String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+appSecret+current_time)+"&appKey="+CjroneApp.APP_KEY+"&cardId="+cardId+"&name="+name+"&additional="+additional+"&datatype=json";
                result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_YLBX,params_vailidation);
                System.out.println("医疗保险接口的返回数据如下：-===================================");
                System.out.println(result);
            }
        }else {
            return request_key_result;
        }
        return result;*/


        Map<String, Object> result = new HashMap<>();
        String requestsec = sysConfigService.getValue("requestSecret");
        additional = additional != null ? additional: "{}" ;
        // 构造请求参数
        Long current_time = System.currentTimeMillis();
        String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+requestsec+current_time)+"&appKey="+CjroneApp.APP_KEY+"&cardId="+cardId+"&name="+name+"&additional="+additional+"&datatype=json";
        result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_YLBX,params_vailidation);
        System.out.println("医疗保险接口的返回数据如下：-===================================");
        System.out.println(result);

        return result;
    }

    @Override
    public Map<String, Object> queryLHJYInfo(String cardId, String name, String additional) {
       /* Map<String, Object> request_key_result = CjroneAppUtil.doOne(CjroneApp.APP_REQUEST_SECRET_URL, CjroneApp.APP_KEY, CjroneApp.APP_SECRET);
        System.out.println(request_key_result);
        Map<String, Object> result = new HashMap<>();
        if ("00".equals(request_key_result.get("code").toString()) && request_key_result.get("datas") != null){
            Map<String, Object> map = new Gson().fromJson(request_key_result.get("datas").toString(), Map.class);
            if (map.get("requestSecret") !=null){
                String appSecret = map.get("requestSecret") !=null ? map.get("requestSecret").toString():null ;
                additional = additional != null ? additional: "{}" ;
                // 构造请求参数
                Long current_time = System.currentTimeMillis();
                String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+appSecret+current_time)+"&appKey="+CjroneApp.APP_KEY+"&aac002="+cardId+"&aac003="+name+"&additional="+additional+"&datatype=json";
                result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_LHJY,params_vailidation);
                System.out.println("灵活就业接口的返回数据如下：-===================================");
                System.out.println(result);
            }
        }else {
            return request_key_result;
        }
        return result;*/


        Map<String, Object> result = new HashMap<>();
        String requestsec = sysConfigService.getValue("requestSecret");
        additional = additional != null ? additional: "{}" ;
        // 构造请求参数
        Long current_time = System.currentTimeMillis();
        String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+requestsec+current_time)+"&appKey="+CjroneApp.APP_KEY+"&aac002="+cardId+"&aac003="+name+"&additional="+additional+"&datatype=json";
        result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_LHJY,params_vailidation);
        System.out.println("灵活就业接口的返回数据如下：-===================================");
        System.out.println(result);

        return result;
    }

    @Override
    public Map<String, Object> queryFDDBRInfo(String cardId, String additional)  {
        /*Map<String, Object> request_key_result = CjroneAppUtil.doOne(CjroneApp.APP_REQUEST_SECRET_URL, CjroneApp.APP_KEY, CjroneApp.APP_SECRET);
        System.out.println(request_key_result);
        Map<String, Object> result = new HashMap<>();
        if ("00".equals(request_key_result.get("code").toString()) && request_key_result.get("datas") != null){
            Map<String, Object> map = new Gson().fromJson(request_key_result.get("datas").toString(), Map.class);
            if (map.get("requestSecret") !=null){
                String appSecret = map.get("requestSecret") !=null ? map.get("requestSecret").toString():null ;
                additional = additional != null ? additional: "{}" ;
                // 构造请求参数
                Long current_time = System.currentTimeMillis();
                String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+appSecret+current_time)+"&appKey="+CjroneApp.APP_KEY+"&cardId="+cardId+"&additional="+additional+"&datatype=json";
                result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_FDDBR_INFO_URL,params_vailidation);
                System.out.println("法定人接口的返回数据如下：-===================================");
                System.out.println(result);
            }
        }else {
            return request_key_result;
        }
        return result;*/

        Map<String, Object> result = new HashMap<>();
        additional = additional != null ? additional: "{}" ;
        String requestsec = sysConfigService.getValue("requestSecretNew");
        // 构造请求参数
        Long current_time = System.currentTimeMillis();
        String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY_NEW+requestsec+current_time)+"&appKey="+CjroneApp.APP_KEY_NEW+"&cardId="+cardId+"&additional="+additional+"&datatype=json";
        result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_FDDBR_INFO_URL,params_vailidation);
        System.out.println("法定人接口的返回数据如下：-===================================");
        System.out.println(result);
        return result;
    }

    @Override
    public Map<String, Object> queryFMCARDInfo(String czrkcszmbh, String additional) {
        Map<String, Object> request_key_result = CjroneAppUtil.doOne(CjroneApp.APP_REQUEST_SECRET_URL, CjroneApp.APP_KEY, CjroneApp.APP_SECRET);
        System.out.println(request_key_result);
        Map<String, Object> result = new HashMap<>();
        if ("00".equals(request_key_result.get("code").toString()) && request_key_result.get("datas") != null){
            Map<String, Object> map = new Gson().fromJson(request_key_result.get("datas").toString(), Map.class);
            if (map.get("requestSecret") !=null){
                String appSecret = map.get("requestSecret") !=null ? map.get("requestSecret").toString():null ;
                additional = additional != null ? additional: "{}" ;
                // 构造请求参数
                Long current_time = System.currentTimeMillis();
                String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+appSecret+current_time)+"&appKey="+CjroneApp.APP_KEY+"&czrkcszmbh="+czrkcszmbh+"&additional="+additional+"&datatype=json";
                result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_FMCARD_INFO_URL,params_vailidation);
                System.out.println(result);
            }
        }else {
            return request_key_result;
        }
        return result;
    }

    @Override
    public Map<String, Object> queryHHInfo(String cardId, String additional){
       /* Map<String, Object> request_key_result = CjroneAppUtil.doOne(CjroneApp.APP_REQUEST_SECRET_URL, CjroneApp.APP_KEY, CjroneApp.APP_SECRET);
        System.out.println(request_key_result);
        Map<String, Object> result = new HashMap<>();
        if ("00".equals(request_key_result.get("code").toString()) && request_key_result.get("datas") != null){
            Map<String, Object> map = new Gson().fromJson(request_key_result.get("datas").toString(), Map.class);
            if (map.get("requestSecret") !=null){
                String appSecret = map.get("requestSecret") !=null ? map.get("requestSecret").toString():null ;
                additional = additional != null ? additional: "{}" ;
                // 构造请求参数
                Long current_time = System.currentTimeMillis();
                String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+appSecret+current_time)+"&appKey="+CjroneApp.APP_KEY+"&cardId="+cardId+"&additional="+additional+"&datatype=json";
                System.out.println("params_vailidation: "+params_vailidation);
                result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_HH_INFO_URL,params_vailidation);
                System.out.println(result);
                return result;
            }
        }else {
            return request_key_result;
        }
        return result;*/

        Map<String, Object> result = new HashMap<>();
        String requestsec = sysConfigService.getValue("requestSecret");
        additional = additional != null ? additional: "{}" ;
        // 构造请求参数
        Long current_time = System.currentTimeMillis();
        String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+requestsec+current_time)+"&appKey="+CjroneApp.APP_KEY+"&cardId="+cardId+"&additional="+additional+"&datatype=json";
        System.out.println("params_vailidation: "+params_vailidation);
        result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_HH_INFO_URL,params_vailidation);
        System.out.println(result);
        return result;
    }

    @Override
    public Map<String, Object> queryFXInfo(String cardID, String additional)  {
       /* Map<String, Object> request_key_result = CjroneAppUtil.doOne(CjroneApp.APP_REQUEST_SECRET_URL, CjroneApp.APP_KEY, CjroneApp.APP_SECRET);
        System.out.println(request_key_result);
        Map<String, Object> result = new HashMap<>();
        if ("00".equals(request_key_result.get("code").toString()) && request_key_result.get("datas") != null){
            Map<String, Object> map = new Gson().fromJson(request_key_result.get("datas").toString(), Map.class);
            if (map.get("requestSecret") !=null){
                String appSecret = map.get("requestSecret") !=null ? map.get("requestSecret").toString():null ;
                additional = additional != null ? additional: "{}" ;
                // 构造请求参数
                Long current_time = System.currentTimeMillis();
                String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+appSecret+current_time)+"&appKey="+CjroneApp.APP_KEY+"&cardID="+cardID+"&additional="+additional+"&datatype=json";
                result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_FX_INFO_URL,params_vailidation);
                System.out.println(result);
            }
        }else {
            return request_key_result;
        }
        return result;*/

        Map<String, Object> result = new HashMap<>();
        String requestsec = sysConfigService.getValue("requestSecret");
        additional = additional != null ? additional: "{}" ;
        // 构造请求参数
        Long current_time = System.currentTimeMillis();
        String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+requestsec+current_time)+"&appKey="+CjroneApp.APP_KEY+"&cardID="+cardID+"&additional="+additional+"&datatype=json";
        result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_FX_INFO_URL,params_vailidation);
        System.out.println(result);
        return result;
    }

    @Override
    public Map<String, Object> queryZAInfo(String gmsfhm, String additional)  {
        Map<String, Object> request_key_result = CjroneAppUtil.doOne(CjroneApp.APP_REQUEST_SECRET_URL, CjroneApp.APP_KEY, CjroneApp.APP_SECRET);
        System.out.println(request_key_result);
        Map<String, Object> result = new HashMap<>();
        if ("00".equals(request_key_result.get("code").toString()) && request_key_result.get("datas") != null){
            Map<String, Object> map = new Gson().fromJson(request_key_result.get("datas").toString(), Map.class);
            if (map.get("requestSecret") !=null){
                String appSecret = map.get("requestSecret") !=null ? map.get("requestSecret").toString():null ;
                additional = additional != null ? additional: "{}" ;
                // 构造请求参数
                Long current_time = System.currentTimeMillis();
                String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+appSecret+current_time)+"&appKey="+CjroneApp.APP_KEY+"&gmsfhm="+gmsfhm+"&additional="+additional+"&datatype=json";
                result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_ZA_INFO_URL,params_vailidation);
                System.out.println(result);
            }
        }else {
            return request_key_result;
        }
        return result;
    }

    @Override
    public Map<String, Object> queryCSZMInfo(String fxm, String mxm, String fsfz, String msfz, String additional)  {
        Map<String, Object> request_key_result = CjroneAppUtil.doOne(CjroneApp.APP_REQUEST_SECRET_URL, CjroneApp.APP_KEY, CjroneApp.APP_SECRET);
        System.out.println(request_key_result);
        Map<String, Object> result = new HashMap<>();
        if ("00".equals(request_key_result.get("code").toString()) && request_key_result.get("datas") != null){
            Map<String, Object> map = new Gson().fromJson(request_key_result.get("datas").toString(), Map.class);
            if (map.get("requestSecret") !=null){
                String appSecret = map.get("requestSecret") !=null ? map.get("requestSecret").toString():null ;
                additional = additional != null ? additional: "{}" ;
                // 构造请求参数
                Long current_time = System.currentTimeMillis();
                String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+appSecret+current_time)+"&appKey="+CjroneApp.APP_KEY+"&fxm="+fxm+"&mxm="+mxm+"&fsfz="+fsfz+"&msfz="+msfz+"&additional="+additional+"&datatype=json";
                result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_CSZM_INFO_URL,params_vailidation);
                System.out.println(result);
            }
        }else {
            return request_key_result;
        }
        return result;
    }

    @Override
    public Map<String, Object> queryCardIDInfo(String czrkxm, String czrkgmsfhm, String additional)  {
        Map<String, Object> request_key_result = CjroneAppUtil.doOne(CjroneApp.APP_REQUEST_SECRET_URL, CjroneApp.APP_KEY, CjroneApp.APP_SECRET);
        System.out.println(request_key_result);
        Map<String, Object> result = new HashMap<>();
        if ("00".equals(request_key_result.get("code").toString()) && request_key_result.get("datas") != null){
            Map<String, Object> map = new Gson().fromJson(request_key_result.get("datas").toString(), Map.class);
            if (map.get("requestSecret") !=null){
                String appSecret = map.get("requestSecret") !=null ? map.get("requestSecret").toString():null ;
                additional = additional != null ? additional: "{}" ;
                // 构造请求参数
                Long current_time = System.currentTimeMillis();
                String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+appSecret+current_time)+"&appKey="+CjroneApp.APP_KEY+"&czrkxm="+czrkxm+"&czrkgmsfhm="+czrkgmsfhm+"&additional="+additional+"&datatype=json";
                result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_CAERID_INFO_URL,params_vailidation);
                System.out.println(result);
            }
        }else {
            return request_key_result;
        }
        return result;
    }

    @Override
    public Map<String, Object> queryCardIDInfoOld(String czrkxm, String czrkgmsfhm, String additional) {
        Map<String, Object> request_key_result = CjroneAppUtil.doOne(CjroneApp.APP_REQUEST_SECRET_URL, CjroneApp.APP_KEY, CjroneApp.APP_SECRET);
        System.out.println(request_key_result);
        Map<String, Object> result = new HashMap<>();
        if ("00".equals(request_key_result.get("code").toString()) && request_key_result.get("datas") != null){
            Map<String, Object> map = new Gson().fromJson(request_key_result.get("datas").toString(), Map.class);
            if (map.get("requestSecret") !=null){
                String appSecret = map.get("requestSecret") !=null ? map.get("requestSecret").toString():null ;
                additional = additional != null ? additional: "{}" ;
                // 构造请求参数
                Long current_time = System.currentTimeMillis();
                String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+appSecret+current_time)+"&appKey="+CjroneApp.APP_KEY+"&czrkxm="+czrkxm+"&czrkgmsfhm="+czrkgmsfhm+"&additional="+additional+"&datatype=json";
                result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_CAERID_OLD_INFO_URL,params_vailidation);
                System.out.println(result);
            }
        }else {
            return request_key_result;
        }
        return result;
    }

    @Override
    public Map<String, Object> queryHuJiInfo(String cardId, String additional) {
       /* Map<String, Object> request_key_result = CjroneAppUtil.doOne(CjroneApp.APP_REQUEST_SECRET_URL, CjroneApp.APP_KEY, CjroneApp.APP_SECRET);
        System.out.println(request_key_result);
        Map<String, Object> result = new HashMap<>();
        if ("00".equals(request_key_result.get("code").toString()) && request_key_result.get("datas") != null){
            Map<String, Object> map = new Gson().fromJson(request_key_result.get("datas").toString(), Map.class);
            if (map.get("requestSecret") !=null){
                String appSecret = map.get("requestSecret") !=null ? map.get("requestSecret").toString():null ;
                additional = additional != null ? additional: "{}" ;
                // 构造请求参数
                Long current_time = System.currentTimeMillis();
                String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+appSecret+current_time)+"&appKey="+CjroneApp.APP_KEY+"&cardId="+cardId+"&additional="+additional+"&datatype=json";
                System.out.println(params_vailidation);
                result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_HUJI_INFO_URL,params_vailidation);
                System.out.println(result);
            }
        }else {
            return request_key_result;
        }
        return result;*/

        Map<String, Object> result = new HashMap<>();
        additional = additional != null ? additional: "{}" ;
        String requestsec = sysConfigService.getValue("requestSecret");
        // 构造请求参数
        Long current_time = System.currentTimeMillis();
        String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+requestsec+current_time)+"&appKey="+CjroneApp.APP_KEY+"&cardId="+cardId+"&additional="+additional+"&datatype=json";
        System.out.println(params_vailidation);
        result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_HUJI_INFO_URL,params_vailidation);
        System.out.println(result);
        return result;

    }

    // 获得户口本信息
    @Override
    public Map<String, Object> querrHuKouBenInfo(String czrkgmsfhm, String additional) {
        Map<String, Object> result = new HashMap<>();
        additional = additional != null ? additional: "{}" ;
        String requestsec = sysConfigService.getValue("requestSecretNew");
        // 构造请求参数
        Long current_time = System.currentTimeMillis();
        String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY_NEW+requestsec+current_time)+"&appKey="+CjroneApp.APP_KEY_NEW+"&czrkgmsfhm="+czrkgmsfhm+"&additional="+additional+"&datatype=json";
        System.out.println(params_vailidation);
        result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_HUKOUBEN_INFO_URL,params_vailidation);
        System.out.println("============省公安厅居民户口簿（个人）===============");
        System.out.println(result);
        return result;
    }

    @Override
    public Map<String, Object> queryHuJiQCInfo(String GMSFHM, String additional) {
       /* Map<String, Object> request_key_result = CjroneAppUtil.doOne(CjroneApp.APP_REQUEST_SECRET_URL, CjroneApp.APP_KEY, CjroneApp.APP_SECRET);
        System.out.println(request_key_result);
        Map<String, Object> result = new HashMap<>();
        if ("00".equals(request_key_result.get("code").toString()) && request_key_result.get("datas") != null){
            Map<String, Object> map = new Gson().fromJson(request_key_result.get("datas").toString(), Map.class);
            if (map.get("requestSecret") !=null){
                String appSecret = map.get("requestSecret") !=null ? map.get("requestSecret").toString():null ;
                additional = additional != null ? additional: "{}" ;
                // 构造请求参数
                Long current_time = System.currentTimeMillis();
                String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+appSecret+current_time)+"&appKey="+CjroneApp.APP_KEY+"&gmsfhm="+GMSFHM+"&additional="+additional+"&datatype=json";
                result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_HUJI_QC_INFO_URL,params_vailidation);
                System.out.println(result);
            }
        }else {
            return request_key_result;
        }
        return result;*/

        Map<String, Object> result = new HashMap<>();
        additional = additional != null ? additional: "{}" ;
        String requestsec = sysConfigService.getValue("requestSecret");
        // 构造请求参数
        Long current_time = System.currentTimeMillis();
        String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+requestsec+current_time)+"&appKey="+CjroneApp.APP_KEY+"&gmsfhm="+GMSFHM+"&additional="+additional+"&datatype=json";
        result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_HUJI_QC_INFO_URL,params_vailidation);
        System.out.println(result);
        return result;
    }

    @Override
    public Map<String, Object> queryCardIdPho(String CZRKGMSFHM,String CZRKXM,String CZRKMZ,String CZRKCSRQ,String additional) {
       /* Map<String, Object> request_key_result = CjroneAppUtil.doOne(CjroneApp.APP_REQUEST_SECRET_URL, CjroneApp.APP_KEY, CjroneApp.APP_SECRET);
        System.out.println(" queryCardIdPho request_key_result :" + request_key_result);
        Map<String, Object> result = new HashMap<>();
        if ("00".equals(request_key_result.get("code").toString()) && request_key_result.get("datas") != null){
            Map<String, Object> map = new Gson().fromJson(request_key_result.get("datas").toString(), Map.class);
            if (map.get("requestSecret") !=null){
                String appSecret = map.get("requestSecret") !=null ? map.get("requestSecret").toString():null ;
                additional = additional != null ? additional: "{}" ;
                // 构造请求参数
                Long current_time = System.currentTimeMillis();
                String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+appSecret+current_time)+"&appKey="+CjroneApp.APP_KEY+"&CZRKGMSFHM="+CZRKGMSFHM+"&CZRKXM="+CZRKXM+"&CZRKMZ="+CZRKMZ+"&CZRKCSRQ="+CZRKCSRQ+"&additional="+additional+"&datatype=json";
                System.out.println("queryCardIdPho params_vailidation"+params_vailidation);
                System.out.println("current url:---->"+CjroneApp.APP_CARD_PHO_INFO_URL);
                result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_CARD_PHO_INFO_URL,params_vailidation);
                System.out.println(result);
            }
        }else {
            return request_key_result;
        }
        return result;*/


        Map<String, Object> result = new HashMap<>();
        additional = additional != null ? additional: "{}" ;
        String requestsec = sysConfigService.getValue("requestSecretNew");
        // 构造请求参数
        Long current_time = System.currentTimeMillis();
        String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY_NEW+requestsec+current_time)+"&appKey="+CjroneApp.APP_KEY_NEW+"&CZRKGMSFHM="+CZRKGMSFHM+"&CZRKXM="+CZRKXM+"&CZRKMZ="+CZRKMZ+"&CZRKCSRQ="+CZRKCSRQ+"&additional="+additional+"&datatype=json";
        System.out.println("queryCardIdPho params_vailidation"+params_vailidation);
        System.out.println("current url:---->"+CjroneApp.APP_CARD_PHO_INFO_URL);
        result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_CARD_PHO_INFO_URL,params_vailidation);
        System.out.println(result);
        return result;

    }

    @Override
    public Map<String, Object> queryFamilyInfo(String zjhm, String additional) {
        Map<String, Object> request_key_result = CjroneAppUtil.doOne(CjroneApp.APP_REQUEST_SECRET_URL, CjroneApp.APP_KEY, CjroneApp.APP_SECRET);
        System.out.println(" queryCardIdPho request_key_result :" + request_key_result);
        Map<String, Object> result = new HashMap<>();
        if ("00".equals(request_key_result.get("code").toString()) && request_key_result.get("datas") != null){
            Map<String, Object> map = new Gson().fromJson(request_key_result.get("datas").toString(), Map.class);
            if (map.get("requestSecret") !=null){
                String appSecret = map.get("requestSecret") !=null ? map.get("requestSecret").toString():null ;
                additional = additional != null ? additional: "{}" ;
                // 构造请求参数
                Long current_time = System.currentTimeMillis();
                String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+appSecret+current_time)+"&appKey="+CjroneApp.APP_KEY+"&zjhm="+zjhm+"&additional="+additional+"&datatype=json";
                System.out.println("queryCardIdPho params_vailidation"+params_vailidation);
                System.out.println("current url:---->"+CjroneApp.APP_CARD_PHO_INFO_URL);
                result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_FAMILY_INFO_URL,params_vailidation);
                System.out.println(result);
            }
        }else {
            return request_key_result;
        }
        return result;
    }

    // 医疗保险参保人员信息
    @Override
    public Map<String, Object> queryYiLiaoBaoXian(String name, String cardId,String additional) {
        /*Map<String, Object> request_key_result = CjroneAppUtil.doOne(CjroneApp.APP_REQUEST_SECRET_URL, CjroneApp.APP_KEY, CjroneApp.APP_SECRET);
        System.out.println(" queryCardIdPho request_key_result :" + request_key_result);
        Map<String, Object> result = new HashMap<>();
        if ("00".equals(request_key_result.get("code").toString()) && request_key_result.get("datas") != null){
            Map<String, Object> map = new Gson().fromJson(request_key_result.get("datas").toString(), Map.class);
            if (map.get("requestSecret") !=null){
                String appSecret = map.get("requestSecret") !=null ? map.get("requestSecret").toString():null ;
                additional = additional != null ? additional: "{}" ;
                // 构造请求参数
                Long current_time = System.currentTimeMillis();
                String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+appSecret+current_time)+"&appKey="+CjroneApp.APP_KEY+"&name="+name+"&cardId="+cardId+"&additional="+additional+"&datatype=json";
                System.out.println("queryCardIdPho params_vailidation"+params_vailidation);
                result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_YLBXCB_INFO_URL,params_vailidation);
                System.out.println(result);
            }
        }else {
            return request_key_result;
        }
        return result;*/

        Map<String, Object> result = new HashMap<>();
        additional = additional != null ? additional: "{}" ;
        String requestsec = sysConfigService.getValue("requestSecretNew");
        // 构造请求参数
        Long current_time = System.currentTimeMillis();
        String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY_NEW+requestsec+current_time)+"&appKey="+CjroneApp.APP_KEY_NEW+"&psnName="+name+"&psnCertType=01"+"&certNo="+cardId+"&additional="+additional+"&datatype=json";
        System.out.println("queryCardIdPho params_vailidation"+params_vailidation);
        result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_YLBXCB_INFO_URL,params_vailidation);
        System.out.println(result);
        return result;
    }

    // 市治安户籍迁出
    @Override
    public Map<String, Object> queryShiHuJiQC(String cardId, String additional) {
      /*  Map<String, Object> request_key_result = CjroneAppUtil.doOne(CjroneApp.APP_REQUEST_SECRET_URL, CjroneApp.APP_KEY, CjroneApp.APP_SECRET);
        System.out.println(" queryCardIdPho request_key_result :" + request_key_result);
        Map<String, Object> result = new HashMap<>();
        if ("00".equals(request_key_result.get("code").toString()) && request_key_result.get("datas") != null){
            Map<String, Object> map = new Gson().fromJson(request_key_result.get("datas").toString(), Map.class);
            if (map.get("requestSecret") !=null){
                String appSecret = map.get("requestSecret") !=null ? map.get("requestSecret").toString():null ;
                additional = additional != null ? additional: "{}" ;
                // 构造请求参数
                Long current_time = System.currentTimeMillis();
                String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+appSecret+current_time)+"&appKey="+CjroneApp.APP_KEY+"&gmsfhm="+cardId+"&additional="+additional+"&datatype=json";
                System.out.println("queryCardIdPho params_vailidation"+params_vailidation);
                result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_SHIHUJIQC_INFO_URL,params_vailidation);
                System.out.println(result);
            }
        }else {
            return request_key_result;
        }
        return result;*/

        Map<String, Object> result = new HashMap<>();
        additional = additional != null ? additional: "{}" ;
        String requestsec = sysConfigService.getValue("requestSecretNew");
        // 构造请求参数
        Long current_time = System.currentTimeMillis();
        String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY_NEW+requestsec+current_time)+"&appKey="+CjroneApp.APP_KEY_NEW+"&gmsfhm="+cardId+"&additional="+additional+"&datatype=json";
        System.out.println("queryCardIdPho params_vailidation"+params_vailidation);
        result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_SHIHUJIQC_INFO_URL,params_vailidation);
        System.out.println(result);
        return result;
    }

    // 省户口本
    @Override
    public Map<String, Object> queryHuKouBenInfo(String cardId, String additional) {
        /*Map<String, Object> request_key_result = CjroneAppUtil.doOne(CjroneApp.APP_REQUEST_SECRET_URL, CjroneApp.APP_KEY, CjroneApp.APP_SECRET);
        System.out.println(" queryCardIdPho request_key_result :" + request_key_result);
        Map<String, Object> result = new HashMap<>();
        if ("00".equals(request_key_result.get("code").toString()) && request_key_result.get("datas") != null){
            Map<String, Object> map = new Gson().fromJson(request_key_result.get("datas").toString(), Map.class);
            if (map.get("requestSecret") !=null){
                String appSecret = map.get("requestSecret") !=null ? map.get("requestSecret").toString():null ;
                additional = additional != null ? additional: "{}" ;
                // 构造请求参数
                Long current_time = System.currentTimeMillis();
                String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+appSecret+current_time)+"&appKey="+CjroneApp.APP_KEY+"&czrkgmsfhm="+cardId+"&additional="+additional+"&datatype=json";
                System.out.println("queryCardIdPho params_vailidation"+params_vailidation);
                result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_SHENGHUKOU_INFO_URL,params_vailidation);
                System.out.println(result);
            }
        }else {
            return request_key_result;
        }
        return result;*/

        Map<String, Object> result = new HashMap<>();
        additional = additional != null ? additional: "{}" ;
        String requestsec = sysConfigService.getValue("requestSecret");
        // 构造请求参数
        Long current_time = System.currentTimeMillis();
        String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+requestsec+current_time)+"&appKey="+CjroneApp.APP_KEY+"&czrkgmsfhm="+cardId+"&additional="+additional+"&datatype=json";
        System.out.println("queryCardIdPho params_vailidation"+params_vailidation);
        result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_SHENGHUKOU_INFO_URL,params_vailidation);
        System.out.println(result);
        return result;
    }

    // 社会保险个人参保信息
    @Override
    public Map<String, Object> querySheBao(String name, String cardId, String additional) {
       /* Map<String, Object> request_key_result = CjroneAppUtil.doOne(CjroneApp.APP_REQUEST_SECRET_URL, CjroneApp.APP_KEY, CjroneApp.APP_SECRET);
        System.out.println(" queryCardIdPho request_key_result :" + request_key_result);
        Map<String, Object> result = new HashMap<>();
        if ("00".equals(request_key_result.get("code").toString()) && request_key_result.get("datas") != null){
            Map<String, Object> map = new Gson().fromJson(request_key_result.get("datas").toString(), Map.class);
            if (map.get("requestSecret") !=null){
                String appSecret = map.get("requestSecret") !=null ? map.get("requestSecret").toString():null ;
                additional = additional != null ? additional: "{}" ;
                // 构造请求参数
                Long current_time = System.currentTimeMillis();
                String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+appSecret+current_time)+"&appKey="+CjroneApp.APP_KEY+"&aac003"+name+"&AAC002="+cardId+"&additional="+additional+"&datatype=json";
                System.out.println("queryCardIdPho params_vailidation"+params_vailidation);
                result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_SHEBAO_INFO_URL,params_vailidation);
                System.out.println(result);
            }
        }else {
            return request_key_result;
        }
        return result;*/


        additional = additional != null ? additional: "{}" ;
        String requestsec = sysConfigService.getValue("requestSecretNew");
        Map<String, Object> result = new HashMap<>();
        // 构造请求参数
        Long current_time = System.currentTimeMillis();
        String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY_NEW+requestsec+current_time)+"&appKey="+CjroneApp.APP_KEY_NEW+"&aac003"+name+"&AAC002="+cardId+"&additional="+additional+"&datatype=json";
        System.out.println("queryCardIdPho params_vailidation"+params_vailidation);
        result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_SHEBAO_INFO_URL,params_vailidation);
        System.out.println(result);
        return result;
    }

    // 人口信息
    @Override
    public Map<String, Object> queyuRenKou(String cardId, String additional) {
        /*Map<String, Object> request_key_result = CjroneAppUtil.doOne(CjroneApp.APP_REQUEST_SECRET_URL, CjroneApp.APP_KEY, CjroneApp.APP_SECRET);
        System.out.println(" queryCardIdPho request_key_result :" + request_key_result);
        Map<String, Object> result = new HashMap<>();
        if ("00".equals(request_key_result.get("code").toString()) && request_key_result.get("datas") != null){
            Map<String, Object> map = new Gson().fromJson(request_key_result.get("datas").toString(), Map.class);
            if (map.get("requestSecret") !=null){
                String appSecret = map.get("requestSecret") !=null ? map.get("requestSecret").toString():null ;
                additional = additional != null ? additional: "{}" ;
                // 构造请求参数
                Long current_time = System.currentTimeMillis();
                String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+appSecret+current_time)+"&appKey="+CjroneApp.APP_KEY+"&cardId="+cardId+"&additional="+additional+"&datatype=json";
                System.out.println("queryCardIdPho params_vailidation"+params_vailidation);
                result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_RENKOU_INFO_URL,params_vailidation);
                System.out.println(result);
            }
        }else {
            return request_key_result;
        }
        return result;*/

        Map<String, Object> result = new HashMap<>();
        String requestsec = sysConfigService.getValue("requestSecret");
        additional = additional != null ? additional: "{}" ;
        // 构造请求参数
        Long current_time = System.currentTimeMillis();
        String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+requestsec+current_time)+"&appKey="+CjroneApp.APP_KEY+"&cardId="+cardId+"&additional="+additional+"&datatype=json";
        System.out.println("queryCardIdPho params_vailidation"+params_vailidation);
        result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_RENKOU_INFO_URL,params_vailidation);
        System.out.println(result);
        return result;
    }

    // 特困救助
    @Override
    public Map<String, Object> queryTKInfo(String cardId, String additional) {
       /* Map<String, Object> request_key_result = CjroneAppUtil.doOne(CjroneApp.APP_REQUEST_SECRET_URL, CjroneApp.APP_KEY, CjroneApp.APP_SECRET);
        System.out.println(" queryCardIdPho request_key_result :" + request_key_result);
        Map<String, Object> result = new HashMap<>();
        if ("00".equals(request_key_result.get("code").toString()) && request_key_result.get("datas") != null){
            Map<String, Object> map = new Gson().fromJson(request_key_result.get("datas").toString(), Map.class);
            if (map.get("requestSecret") !=null){
                String appSecret = map.get("requestSecret") !=null ? map.get("requestSecret").toString():null ;
                additional = additional != null ? additional: "{}" ;
                // 构造请求参数
                Long current_time = System.currentTimeMillis();
                String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+appSecret+current_time)+"&appKey="+CjroneApp.APP_KEY+"&idcode="+cardId+"&additional="+additional+"&datatype=json";
                System.out.println("queryCardIdPho params_vailidation"+params_vailidation);
                result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_TEKUN_INFO_URL,params_vailidation);
                System.out.println(result);
            }
        }else {
            return request_key_result;
        }*/


        Map<String, Object> result = new HashMap<>();
        String requestsec = sysConfigService.getValue("requestSecret");
        additional = additional != null ? additional: "{}" ;
        // 构造请求参数
        Long current_time = System.currentTimeMillis();
        String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+requestsec+current_time)+"&appKey="+CjroneApp.APP_KEY+"&cardId="+cardId+"&additional="+additional+"&datatype=json";
        System.out.println("queryCardIdPho params_vailidation"+params_vailidation);
        result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_TEKUN_INFO_URL,params_vailidation);
        System.out.println(result);
        return result;
    }

    // 殡葬
    @Override
    public Map<String, Object> queryBinZangInfo(String name, String cardId, String additional) {
      /*  Map<String, Object> request_key_result = CjroneAppUtil.doOne(CjroneApp.APP_REQUEST_SECRET_URL, CjroneApp.APP_KEY, CjroneApp.APP_SECRET);
        System.out.println(" queryCardIdPho request_key_result :" + request_key_result);
        Map<String, Object> result = new HashMap<>();
        if ("00".equals(request_key_result.get("code").toString()) && request_key_result.get("datas") != null){
            Map<String, Object> map = new Gson().fromJson(request_key_result.get("datas").toString(), Map.class);
            if (map.get("requestSecret") !=null){
                String appSecret = map.get("requestSecret") !=null ? map.get("requestSecret").toString():null ;
                additional = additional != null ? additional: "{}" ;
                // 构造请求参数
                Long current_time = System.currentTimeMillis();
                String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+appSecret+current_time)+"&appKey="+CjroneApp.APP_KEY+"&name="+name+"&id_card="+cardId+"&additional="+additional+"&datatype=json";
                System.out.println("queryCardIdPho params_vailidation"+params_vailidation);
                result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_BINZANG_INFO_URL,params_vailidation);
                System.out.println(result);
            }
        }else {
            return request_key_result;
        }
        return result;*/


        Map<String, Object> result = new HashMap<>();
        String requestsec = sysConfigService.getValue("requestSecret");
        additional = additional != null ? additional: "{}" ;
        // 构造请求参数
        Long current_time = System.currentTimeMillis();
        String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+requestsec+current_time)+"&appKey="+CjroneApp.APP_KEY+"&name="+name+"&id_card="+cardId+"&additional="+additional+"&datatype=json";
        System.out.println("queryCardIdPho params_vailidation"+params_vailidation);
        result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_BINZANG_INFO_URL,params_vailidation);
        System.out.println(result);
        return result;
    }

    // 流动人口
    @Override
    public Map<String, Object> queryLiuDong(String cardId,String name, String additional) {
       /* Map<String, Object> request_key_result = CjroneAppUtil.doOne(CjroneApp.APP_REQUEST_SECRET_URL, CjroneApp.APP_KEY, CjroneApp.APP_SECRET);
        System.out.println(" queryCardIdPho request_key_result :" + request_key_result);
        Map<String, Object> result = new HashMap<>();
        if ("00".equals(request_key_result.get("code").toString()) && request_key_result.get("datas") != null){
            Map<String, Object> map = new Gson().fromJson(request_key_result.get("datas").toString(), Map.class);
            if (map.get("requestSecret") !=null){
                String appSecret = map.get("requestSecret") !=null ? map.get("requestSecret").toString():null ;
                additional = additional != null ? additional: "{}" ;
                // 构造请求参数
                Long current_time = System.currentTimeMillis();
                String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+appSecret+current_time)+"&appKey="+CjroneApp.APP_KEY+"&cardId="+cardId+"&additional="+additional+"&datatype=json";
                System.out.println("queryCardIdPho params_vailidation"+params_vailidation);
                result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_LIUDONG_INFO_URL,params_vailidation);
                System.out.println(result);
            }
        }else {
            return request_key_result;
        }
        return result;
*/

        Map<String, Object> result = new HashMap<>();
        additional = additional != null ? additional: "{}" ;
        String requestsec = sysConfigService.getValue("requestSecretNew");
        // 构造请求参数
        Long current_time = System.currentTimeMillis();
        String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY_NEW+requestsec+current_time)+"&appKey="+CjroneApp.APP_KEY_NEW+"&cardId="+cardId+"&name="+name+"&additional="+additional+"&datatype=json";
        System.out.println("queryCardIdPho params_vailidation"+params_vailidation);
        result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_LIUDONG_INFO_URL,params_vailidation);
        System.out.println(result);
        return result;
    }



}
