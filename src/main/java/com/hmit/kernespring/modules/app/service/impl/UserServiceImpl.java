

package com.hmit.kernespring.modules.app.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.common.validator.Assert;
import com.hmit.kernespring.modules.app.form.LoginForm;
import com.hmit.kernespring.modules.app.dao.UserDao;
import com.hmit.kernespring.modules.app.entity.UserEntity;
import com.hmit.kernespring.modules.app.service.UserService;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;


@Service("userService")
public class UserServiceImpl extends ServiceImpl<UserDao, UserEntity> implements UserService {

	@Override
	public UserEntity queryByMobile(String mobile) {
		return baseMapper.selectOne(new QueryWrapper<UserEntity>().eq("mobile", mobile));
	}

	@Override
	public UserEntity queryByIdnum(String idnum) {
		return baseMapper.selectOne(new QueryWrapper<UserEntity>().eq("password", idnum));
	}


	@Override
	public long login(LoginForm form) {
		UserEntity user = queryByMobile(form.getMobile());
		Assert.isNull(user, "手机号或密码错误");

		//密码错误
		if(!user.getPassword().equals(DigestUtils.sha256Hex(form.getPassword()))){
			throw new RRException("手机号或密码错误");
		}

		return user.getUserId();
	}
}
