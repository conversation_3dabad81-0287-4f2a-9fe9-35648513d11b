package com.hmit.kernespring.modules.app.controller;


import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.hmit.kernespring.common.utils.*;
import com.hmit.kernespring.modules.app.service.APIService;
import com.hmit.kernespring.modules.data_management.dao.DataDisabilityCertificateDao;
import com.hmit.kernespring.modules.data_management.entity.*;
import com.hmit.kernespring.modules.data_management.service.DataDisabilityCertificateService;
import com.hmit.kernespring.modules.data_management.service.DataLowSecurityService;
import com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity;
import com.hmit.kernespring.modules.matter_application.service.DisabilityCertificateApplicationService;
import com.hmit.kernespring.modules.sys.entity.SysConfigEntity;
import com.hmit.kernespring.modules.sys.service.SysConfigService;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;


/**
 * Created by kernespring on 2019-04-13.
 */
@RestController
@RequestMapping("/subject")
public class SysSubjectController {
    @Autowired
    private APIService apiService;
    @Autowired
    private DataDisabilityCertificateService dataDisabilityCertificateService;
    @Autowired
    private DisabilityCertificateApplicationService disabilityCertificateApplicationService;
    @Autowired
    private DataDisabilityCertificateDao dataDisabilityCertificateDao;
    @Autowired
    private DataLowSecurityService dataLowSecurityService;
    @Autowired
    private SysConfigService sysConfigService;

    //@Autowired
    // SysSubjectService sysSubjectService ;

    // 首次获得数据密钥
    @GetMapping("getrefreshkey")
    public Map<String, Object> getrefreshkey(){
        Map<String, Object> request_key_result = CjroneAppUtil.doOne(CjroneApp.APP_REQUEST_SECRET_URL, CjroneApp.APP_KEY, CjroneApp.APP_SECRET);
        System.out.println(request_key_result);

        String requestSecret="";
        String refreshSecret="";
        Map<String, Object> result = new HashMap<>();
        if ("00".equals(request_key_result.get("code").toString()) && request_key_result.get("datas") != null) {
            Map<String, Object> map = new Gson().fromJson(request_key_result.get("datas").toString(), Map.class);
            if (map.get("requestSecret") != null) {
                 requestSecret = map.get("requestSecret") != null ? map.get("requestSecret").toString() : null;
            }
            if (map.get("refreshSecret") != null) {
                refreshSecret = map.get("refreshSecret") != null ? map.get("refreshSecret").toString() : null;
            }
        }

        // 保存 请求密钥和刷新密钥
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime requesttime = LocalDateTime.now().plusMinutes(13);
        String requesttimestr = requesttime.format(formatter);
        LocalDateTime refreshtime = LocalDateTime.now().plusHours(47);
        String refreshtimestr = refreshtime.format(formatter);


        SysConfigEntity sysConfigEntity = new SysConfigEntity();
        sysConfigEntity.setParamKey("requestSecret");
        sysConfigEntity.setParamValue(requestSecret);
        sysConfigEntity.setRemark(requesttimestr);
        sysConfigService.saveConfig(sysConfigEntity);
        SysConfigEntity sysConfigEntity2 = new SysConfigEntity();
        sysConfigEntity2.setParamKey("refreshSecret");
        sysConfigEntity2.setParamValue(refreshSecret);
        sysConfigEntity2.setRemark(refreshtimestr);
        sysConfigService.saveConfig(sysConfigEntity2);

        return request_key_result;
    }


    @GetMapping("import_year")
    public String import_year(){
        apiService.t1();
        apiService.t2();
        apiService.t3();

        return "hello world";
    }

    // 企业养老接口测试
    @GetMapping("bl_qyyl")
    public String qyyl(String idCard, String name){
        Map<String, Object> doFdDbInfo = apiService.queryQYYLInfo(idCard, name,null);
        System.out.println("返货的企业养老--------->"+doFdDbInfo);
        return new Gson().toJson(doFdDbInfo);
    }

    // 低保救助接口测试
    @GetMapping("bl_dbjz")
    public String dbjz(String idCard, String year, String month){

        List<DataLowSecurityEntity> dataLowSecurityEntityList = new ArrayList<>();

        // 1、 获取所有残疾人列表，遍历判断
        List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntityList = dataDisabilityCertificateService.list();
        for(DataDisabilityCertificateEntity d : dataDisabilityCertificateEntityList){
            Map<String,Object> dbjzInfo = apiService.queryDIBAOInfo(d.getIdCard(), year, month,null);
            System.out.println("返回的低保救助信息--------->"+dbjzInfo);
            DataLowSecurityEntity dataLowSecurityEntity = new DataLowSecurityEntity();
            dataLowSecurityEntity.setIdCard(d.getIdCard());
            dataLowSecurityEntity.setName(d.getName());
            dataLowSecurityEntity.setCareerStatus(new Gson().toJson(dbjzInfo));
            dataLowSecurityEntityList.add(dataLowSecurityEntity);
        }

        dataLowSecurityService.saveBatch(dataLowSecurityEntityList);

        return "ok";
    }


    @GetMapping("bl_dxsbz")
    public String bl_dxsbz(String idCard, String name){
        Map<String, Object> doFdDbInfo = apiService.queryDXSInfo(idCard, name,null);
        System.out.println("返货的大学生--------->"+doFdDbInfo);
        return new Gson().toJson(doFdDbInfo);
    }

    @GetMapping("bl_zjxx")
    public String bl_zjxx(String idCard, String name){
        Map<String, Object> doFdDbInfo = apiService.queryZJSInfo(idCard, name,null);
        System.out.println("返货的大学生--------->"+doFdDbInfo);
        return new Gson().toJson(doFdDbInfo);
    }

    // 短信接口测试
    @GetMapping("sms")
    public String sms(){
        String info = null;
        try{
            HttpClient httpclient = new HttpClient();
            String SerialNumber = "0000000"+String.valueOf(System.currentTimeMillis());
            PostMethod post = new PostMethod("http://ums.zj165.com:8888/sms/Api/Send.do");
            post.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET,"gbk");
            post.addParameter("SpCode", "001715");
            post.addParameter("LoginName", "canlian");
            post.addParameter("Password", "uTWFXMGWoNfhr7");
            post.addParameter("MessageContent", "你有一项编号为123456789的事务需要处理。");
            post.addParameter("UserNumber", "18368404744");
            post.addParameter("SerialNumber", SerialNumber);
            post.addParameter("ScheduleTime", "");
            post.addParameter("ExtendAccessNum", "");
            post.addParameter("f", "1");
            httpclient.executeMethod(post);
            info = new String(post.getResponseBody(),"gbk");
            System.out.println(info);

        }catch (Exception e) {
            e.printStackTrace();
        }
        return info;
    }


    // 短信接口测试
    @GetMapping("smsqf")
    public String smsqf(){
        String info = null;
        Integer sendcount=0;
        String content="在防控新冠肺炎非常时期，区残联向你表示问候!请你戴口罩、勤洗手、少外出、不聚集、多通风。遇急难可向村、社区和残联求助。";
        // String[] phoneList={"18368404744","13606848303","13777125858"};
        String[] phoneList={"18368404744","13606848303"};
        // String[] phoneList={"18368404744"};

        for (String phone:phoneList) {
            try{
                HttpClient httpclient = new HttpClient();
                String SerialNumber = "0000000"+String.valueOf(System.currentTimeMillis());
                PostMethod post = new PostMethod("http://ums.zj165.com:8888/sms/Api/Send.do");
                post.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET,"gbk");
                post.addParameter("SpCode", "001715");
                post.addParameter("LoginName", "canlian");
                post.addParameter("Password", "uTWFXMGWoNfhr7");
                post.addParameter("MessageContent", content);
                post.addParameter("UserNumber", phone);
                post.addParameter("SerialNumber", SerialNumber);
                post.addParameter("ScheduleTime", "");
                post.addParameter("ExtendAccessNum", "");
                post.addParameter("f", "1");
                httpclient.executeMethod(post);
                info = new String(post.getResponseBody(),"gbk");
                System.out.println(phone+"--------");
                System.out.println(info);
                sendcount++;
            }catch (Exception e) {
                e.printStackTrace();
            }
        }

        System.out.println("sendcount--------"+sendcount);

        return info;
    }


    // 短信接口测试
    @GetMapping("smsqfbyjiedao")
    public String smsqfbyjiedao(String jiedao){

        String info = null;
        Integer sendcount=0;
        String content="在防控新冠肺炎非常时期，区残联向你表示问候!请你戴口罩、勤洗手、少外出、不聚集、多通风。遇急难可向村、社区和残联求助。";

        //本人有电话
        List<DataDisabilityCertificateEntity> d1=dataDisabilityCertificateDao.querysend1(jiedao);

        for(DataDisabilityCertificateEntity ds1:d1){
            try{
                HttpClient httpclient = new HttpClient();
                String SerialNumber = "0000000"+String.valueOf(System.currentTimeMillis());
                PostMethod post = new PostMethod("http://ums.zj165.com:8888/sms/Api/Send.do");
                post.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET,"gbk");
                post.addParameter("SpCode", "001715");
                post.addParameter("LoginName", "canlian");
                post.addParameter("Password", "uTWFXMGWoNfhr7");
                post.addParameter("MessageContent", content);
                post.addParameter("UserNumber", ds1.getMobilePhone());
                post.addParameter("SerialNumber", SerialNumber);
                post.addParameter("ScheduleTime", "");
                post.addParameter("ExtendAccessNum", "");
                post.addParameter("f", "1");
                httpclient.executeMethod(post);
                info = new String(post.getResponseBody(),"gbk");
                System.out.println(ds1.getName()+"--------"+ds1.getMobilePhone()+"------");
                System.out.println(info);
                sendcount++;
            }catch (Exception e) {
                e.printStackTrace();
            }
        }

        System.out.println("+++++++++++++++++++++++++++++++++++++++");
        //监护人有电话
        List<DataDisabilityCertificateEntity> d2=dataDisabilityCertificateDao.querysend2(jiedao);

        for(DataDisabilityCertificateEntity ds2:d2){
            try{
                HttpClient httpclient = new HttpClient();
                String SerialNumber = "0000000"+String.valueOf(System.currentTimeMillis());
                PostMethod post = new PostMethod("http://ums.zj165.com:8888/sms/Api/Send.do");
                post.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET,"gbk");
                post.addParameter("SpCode", "001715");
                post.addParameter("LoginName", "canlian");
                post.addParameter("Password", "uTWFXMGWoNfhr7");
                post.addParameter("MessageContent", content);
                post.addParameter("UserNumber", ds2.getGuardianMobile());
                post.addParameter("SerialNumber", SerialNumber);
                post.addParameter("ScheduleTime", "");
                post.addParameter("ExtendAccessNum", "");
                post.addParameter("f", "1");
                httpclient.executeMethod(post);
                info = new String(post.getResponseBody(),"gbk");
                System.out.println(ds2.getName()+"-----jhr---"+ds2.getGuardianMobile()+"------");
                System.out.println(info);
                sendcount++;
            }catch (Exception e) {
                e.printStackTrace();
            }
        }

        System.out.println(jiedao+"-------d1 num is ----"+d1.size());
        System.out.println(jiedao+"-------d2 num is ----"+d2.size());
        System.out.println("sendcount is ===== " + sendcount );

        return "ok";
    }





    // 法定代表人接口测试
    @GetMapping("bl_cddbr")
    public String blfddbr(){
        Map<String, Object> doFdDbInfo = apiService.queryFDDBRInfo("330206194903144659", null);
        System.out.println("是否个体工商户--------->"+doFdDbInfo);
        return new Gson().toJson(doFdDbInfo);
    }

    //火化信息
    @GetMapping("bl_hh")
    public String blhh(){
        Map<String, Object> dohhInfo = apiService.queryHHInfo("330206194903144659", null);
        System.out.println("火化--------->"+dohhInfo);
        return new Gson().toJson(dohhInfo);
    }

    //服刑信息
    @GetMapping("bl_fx")
    public String blfx(){
        Map<String, Object> dofxInfo = apiService.queryFXInfo("330206194903144659", null);
        System.out.println("服刑信息--------->"+dofxInfo);
        return new Gson().toJson(dofxInfo);
    }

    //户籍信息
    @GetMapping("bl_hj")
    public String blhj(){
        Map<String, Object> dohjInfo = apiService.queryHuJiInfo("330206194903144659", null);
        System.out.println("户籍信息--------->"+dohjInfo);
        return new Gson().toJson(dohjInfo);
    }

    //户籍迁出
    @GetMapping("bl_hjqc")
    public String blhjqc(){
        Map<String, Object> dohjqcInfo = apiService.queryHuJiQCInfo("330206194903144659", null);
        System.out.println("户籍迁出信息--------->"+dohjqcInfo);
        return new Gson().toJson(dohjqcInfo);
    }


    //电子证照
   /* @GetMapping("bl_dzzz")
    public String bldzzz(){
        Map<String, Object> dohjqcInfo = apiService.queryCardIdPho()
        System.out.println("户籍迁出信息--------->"+dohjqcInfo);
        return new Gson().toJson(dohjqcInfo);
    }*/

    //综合增加接口调用量
    @GetMapping("bl_zj")
    public void blzj() throws InterruptedException {

        //获得残疾人列表
        Map<String,Object> paramsdis=new HashMap<>();
        List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities = (List<DataDisabilityCertificateEntity>) dataDisabilityCertificateService.listByMap(paramsdis);
        //List<DisabilityCertificateApplicationEntity> disabilityCertificateApplicationEntities=(List<DisabilityCertificateApplicationEntity>)disabilityCertificateApplicationService.listByMap(paramsdis);
         // 多线程调用接口
        //法定代表人
          apiService.asyncblfddbr(dataDisabilityCertificateEntities);
        //火化信息
         apiService.asyncblhh(dataDisabilityCertificateEntities);
        //服刑信息
         apiService.asyncblfx(dataDisabilityCertificateEntities);
        //户籍信息
         apiService.asyncblhj(dataDisabilityCertificateEntities);
        //户籍迁出
        apiService.asyncblhjqc(dataDisabilityCertificateEntities);
        //电子证照
        apiService.asyncdzzz(dataDisabilityCertificateEntities);






        //首次获得刷新密钥和请求密钥 http://10.68.138.194/gateway/app/refreshTokenByKey.htm


        // 便利调用接口，提高接口调用量
     /*   for(int i=0;i<dataDisabilityCertificateEntities.size();i++){
            Map<String, Object> doFdDbInfo = apiService.queryFDDBRInfo(dataDisabilityCertificateEntities.get(i).getIdCard(), null);
            System.out.println("是否个体工商户--------->"+doFdDbInfo);
            System.out.println("是否个体工商户++++++++++++++++++++++++"+i);

            Map<String, Object> dohhInfo = apiService.queryHHInfo(dataDisabilityCertificateEntities.get(i).getIdCard(), null);
            System.out.println("火化--------->"+dohhInfo);
            System.out.println("火化++++++++++++++++++++++++++++++++++"+i);

            Map<String, Object> dofxInfo = apiService.queryFXInfo(dataDisabilityCertificateEntities.get(i).getIdCard(), null);
            System.out.println("服刑信息--------->"+dofxInfo);
            System.out.println("服刑信息++++++++++++++++++++++++++++++"+i);

            Map<String, Object> dohjInfo = apiService.queryHuJiInfo(dataDisabilityCertificateEntities.get(i).getIdCard(), null);
            System.out.println("户籍信息--------->"+dohjInfo);
            System.out.println("户籍信息++++++++++++++++++++++++++++++"+i);

            Map<String, Object> dohjqcInfo = apiService.queryHuJiQCInfo(dataDisabilityCertificateEntities.get(i).getIdCard(), null);
            System.out.println("户籍迁出信息--------->"+dohjqcInfo);
            System.out.println("户籍迁出信息+++++++++++++++++++++++++"+i);

            System.out.println("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@第"+i+"轮");
        }*/
        /*dataDisabilityCertificateEntities.forEach(dataDisabilityCertificateEntity -> {
            Map<String, Object> doFdDbInfo = apiService.queryFDDBRInfo(dataDisabilityCertificateEntity.getIdCard(), null);
            System.out.println("是否个体工商户--------->"+doFdDbInfo);

            Map<String, Object> dohhInfo = apiService.queryHHInfo(dataDisabilityCertificateEntity.getIdCard(), null);
            System.out.println("火化--------->"+dohhInfo);

            Map<String, Object> dofxInfo = apiService.queryFXInfo(dataDisabilityCertificateEntity.getIdCard(), null);
            System.out.println("服刑信息--------->"+dofxInfo);

            Map<String, Object> dohjInfo = apiService.queryHuJiInfo(dataDisabilityCertificateEntity.getIdCard(), null);
            System.out.println("户籍信息--------->"+dohjInfo);

            Map<String, Object> dohjqcInfo = apiService.queryHuJiQCInfo(dataDisabilityCertificateEntity.getIdCard(), null);
            System.out.println("户籍迁出信息--------->"+dohjqcInfo);
        });*/
        System.out.println("===================gameover=======================");
    }


    @GetMapping("init_account")
    public String init_account(String action){
        System.out.println("进入"+action);
        /*sysSubjectService.init_account();*/
        if (action.equals("config")){
            String config = "{\"imageActionName\":\"uploadimage\",\"imageFieldName\":\"upfile\",\"imageMaxSize\":2048000,\"imageAllowFiles\":[\".png\",\".jpg\",\".jpeg\",\".gif\",\".bmp\"],\"imageCompressEnable\":true,\"imageCompressBorder\":1600,\"imageInsertAlign\":\"none\",\"imageUrlPrefix\":\"\",\"imagePathFormat\":\"\\/server\\/ueditor\\/upload\\/image\\/{yyyy}{mm}{dd}\\/{time}{rand:6}\",\"scrawlActionName\":\"uploadscrawl\",\"scrawlFieldName\":\"upfile\",\"scrawlPathFormat\":\"\\/server\\/ueditor\\/upload\\/image\\/{yyyy}{mm}{dd}\\/{time}{rand:6}\",\"scrawlMaxSize\":2048000,\"scrawlUrlPrefix\":\"\",\"scrawlInsertAlign\":\"none\",\"snapscreenActionName\":\"uploadimage\",\"snapscreenPathFormat\":\"\\/server\\/ueditor\\/upload\\/image\\/{yyyy}{mm}{dd}\\/{time}{rand:6}\",\"snapscreenUrlPrefix\":\"\",\"snapscreenInsertAlign\":\"none\",\"catcherLocalDomain\":[\"127.0.0.1\",\"localhost\",\"img.baidu.com\"],\"catcherActionName\":\"catchimage\",\"catcherFieldName\":\"source\",\"catcherPathFormat\":\"\\/server\\/ueditor\\/upload\\/image\\/{yyyy}{mm}{dd}\\/{time}{rand:6}\",\"catcherUrlPrefix\":\"\",\"catcherMaxSize\":2048000,\"catcherAllowFiles\":[\".png\",\".jpg\",\".jpeg\",\".gif\",\".bmp\"],\"videoActionName\":\"uploadvideo\",\"videoFieldName\":\"upfile\",\"videoPathFormat\":\"\\/server\\/ueditor\\/upload\\/video\\/{yyyy}{mm}{dd}\\/{time}{rand:6}\",\"videoUrlPrefix\":\"\",\"videoMaxSize\":102400000,\"videoAllowFiles\":[\".flv\",\".swf\",\".mkv\",\".avi\",\".rm\",\".rmvb\",\".mpeg\",\".mpg\",\".ogg\",\".ogv\",\".mov\",\".wmv\",\".mp4\",\".webm\",\".mp3\",\".wav\",\".mid\"],\"fileActionName\":\"uploadfile\",\"fileFieldName\":\"upfile\",\"filePathFormat\":\"\\/server\\/ueditor\\/upload\\/file\\/{yyyy}{mm}{dd}\\/{time}{rand:6}\",\"fileUrlPrefix\":\"\",\"fileMaxSize\":51200000,\"fileAllowFiles\":[\".png\",\".jpg\",\".jpeg\",\".gif\",\".bmp\",\".flv\",\".swf\",\".mkv\",\".avi\",\".rm\",\".rmvb\",\".mpeg\",\".mpg\",\".ogg\",\".ogv\",\".mov\",\".wmv\",\".mp4\",\".webm\",\".mp3\",\".wav\",\".mid\",\".rar\",\".zip\",\".tar\",\".gz\",\".7z\",\".bz2\",\".cab\",\".iso\",\".doc\",\".docx\",\".xls\",\".xlsx\",\".ppt\",\".pptx\",\".pdf\",\".txt\",\".md\",\".xml\"],\"imageManagerActionName\":\"listimage\",\"imageManagerListPath\":\"\\/server\\/ueditor\\/upload\\/image\\/\",\"imageManagerListSize\":20,\"imageManagerUrlPrefix\":\"\",\"imageManagerInsertAlign\":\"none\",\"imageManagerAllowFiles\":[\".png\",\".jpg\",\".jpeg\",\".gif\",\".bmp\"],\"fileManagerActionName\":\"listfile\",\"fileManagerListPath\":\"\\/server\\/ueditor\\/upload\\/file\\/\",\"fileManagerUrlPrefix\":\"\",\"fileManagerListSize\":20,\"fileManagerAllowFiles\":[\".png\",\".jpg\",\".jpeg\",\".gif\",\".bmp\",\".flv\",\".swf\",\".mkv\",\".avi\",\".rm\",\".rmvb\",\".mpeg\",\".mpg\",\".ogg\",\".ogv\",\".mov\",\".wmv\",\".mp4\",\".webm\",\".mp3\",\".wav\",\".mid\",\".rar\",\".zip\",\".tar\",\".gz\",\".7z\",\".bz2\",\".cab\",\".iso\",\".doc\",\".docx\",\".xls\",\".xlsx\",\".ppt\",\".pptx\",\".pdf\",\".txt\",\".md\",\".xml\"]}";
            return config;
        }else if (action.equals("uploadimages")){
            String result = "{\"original\":\"demo.jpg\",\"name\":\"demo.jpg\",\"url\":\"\\/ueditor\\/upload\\/image\\/demo.jpg\",\"size\":\"99697\",\"type\":\".jpg\",\"state\":\"SUCCESS\"}";

            return result ;
        }else{
            return  "";
        }
    }
    @PostMapping("/{url}")
    public String index(@PathVariable("url") String url) {
        System.out.println("zxcv" + url);
        if (url.equals("init_account")) {
            String result = "{\"original\":\"demo.jpg\",\"name\":\"demo.jpg\",\"url\":\"\\/ueditor\\/upload\\/image\\/demo.jpg\",\"size\":\"99697\",\"type\":\".jpg\",\"state\":\"SUCCESS\"}" ;
            return result;
        }
        return url;
    }
    @GetMapping("/query")
    public String query(String username, String password, String XN, String type, String SSNJ, String XZ){
        return "success";
    }
    @GetMapping("/doPostTest")
    public Object query(String czrkxm, String czrkgmsfhm, String additional, String appKey, String appSecret) throws Exception{
        System.out.println("params:"+czrkxm+" "+czrkgmsfhm+" "+additional);
        String url = "http://10.68.138.194/gateway/api/***************/dataSharing/vdjb19VHdIh27Rc5.htm" ;
        appKey = appKey !=null ? appKey:"6465b05327cc4a41897907bf81382c29" ;
        appSecret = appSecret !=null ? appSecret:"fe07f9be363c46449ffae97ae2ad2bda" ;
        czrkxm = czrkxm !=null ? czrkxm:"蒋仲侠" ;
        czrkgmsfhm = czrkgmsfhm !=null ? czrkgmsfhm:"330206198208141710" ;
        additional = "{\"powerMatters\":\"许可0000-00\",\"subPowerMatters\":\"许可0000-0101\",\"accesscardId\":\"33071918******784523\"}" ;
        Map<String, Object> result = CjroneAppUtil.doCardInfoQuery(url, appKey, appSecret, czrkxm, czrkgmsfhm, additional);
        System.out.println(result);
        return result;
    }
    @GetMapping("/doPostTestOne")
    public Object doPostTestOne(String czrkxm, String czrkgmsfhm, String additional, String appKey, String appSecret)throws Exception {
        System.out.println("啊啊啊啊"+appKey+" "+appSecret);
        String url_one = "http://10.68.138.194/gateway/app/refreshTokenByKey.htm" ;
        String appKey_one = appKey !=null ? appKey:"6465b05327cc4a41897907bf81382c29" ;
        String appSecret_one = appSecret !=null ? appSecret:"fe07f9be363c46449ffae97ae2ad2bda" ;
        Map<String, Object> result = CjroneAppUtil.doOne(url_one, appKey_one, appSecret_one);
        System.out.println(result);
        System.out.println(result.get("datas"));
        Map<String, Object> map = new Gson().fromJson(result.get("datas").toString(), Map.class);
        System.out.println(map);

        System.out.println("params:"+czrkxm+" "+czrkgmsfhm+" "+additional);
        String url = "http://10.68.138.194/gateway/api/***************/dataSharing/vdjb19VHdIh27Rc5.htm" ;
        appKey = appKey !=null ? appKey:"6465b05327cc4a41897907bf81382c29" ;
        appSecret = map.get("requestSecret") !=null ? map.get("requestSecret").toString():"fe07f9be363c46449ffae97ae2ad2bda" ;
        czrkxm = czrkxm !=null ? czrkxm:"蒋仲侠" ;
        czrkgmsfhm = czrkgmsfhm !=null ? czrkgmsfhm:"330206198208141710" ;
        additional = "{\"powerMatters\":\"许可0000-00\",\"subPowerMatters\":\"许可0000-0101\",\"accesscardId\":\"33071918******784523\"}" ;
        Map<String, Object> result_ap = CjroneAppUtil.doCardInfoQuery(url, appKey, appSecret, czrkxm, czrkgmsfhm, additional);
        System.out.println(result_ap);
        return result_ap;
    }
    @GetMapping("/doPostTestTwo")
    public Object doPostTestTwo(String czrkxm, String czrkgmsfhm, String additional, String appKey, String appSecret)throws Exception  {
        System.out.println("啊啊啊啊" + appKey + " " + appSecret);
        String url_one = "http://10.68.138.194/gateway/app/refreshTokenByKey.htm" ;
        String appKey_one = appKey != null ? appKey : "6465b05327cc4a41897907bf81382c29" ;
        String appSecret_one = appSecret != null ? appSecret : "fe07f9be363c46449ffae97ae2ad2bda" ;
        Map<String, Object> result = CjroneAppUtil.doOne(url_one, appKey_one, appSecret_one);
        System.out.println(result);
        System.out.println(result.get("datas"));
        Map<String, Object> map = new Gson().fromJson(result.get("datas").toString(), Map.class);
        System.out.println(map);
        return  map;

    }
    @GetMapping("/doOne")
    public Object doOne(String appKey, String appSecret)throws Exception {
        System.out.println("啊啊啊啊"+appKey+" "+appSecret);
        String url = "http://10.68.138.194/gateway/app/refreshTokenByKey.htm" ;
        appKey = appKey !=null ? appKey:"6465b05327cc4a41897907bf81382c29" ;
        appSecret = appSecret !=null ? appSecret:"fe07f9be363c46449ffae97ae2ad2bda" ;
        Map<String, Object> result = CjroneAppUtil.doOne(url, appKey, appSecret);
        System.out.println(result);
        return result;
    }
    @GetMapping("/doTwo")
    public Object doTwo(String appKey, String appSecret)throws Exception {
        String url = "http://10.68.138.194/gateway/app/refreshTokenBySec.htm" ;
        appKey = appKey !=null ? appKey:"6465b05327cc4a41897907bf81382c29" ;
        appSecret = appSecret !=null ? appSecret:"fe07f9be363c46449ffae97ae2ad2bda" ;
        Map<String, Object> result = CjroneAppUtil.doTwo(url, appKey, appSecret);
        System.out.println(result);
        return result;
    }





    // 省公安接口 获取省份证信息
    @GetMapping("/do获取省份证信息")
    public Object 获取省份证信息(String czrkxm, String czrkgmsfhm, String additional, String appKeyq, String appSecreqt)throws Exception {
        Map<String, Object> request_key_result = CjroneAppUtil.doOne(CjroneApp.APP_REQUEST_SECRET_URL, CjroneApp.APP_KEY, CjroneApp.APP_SECRET);
        System.out.println(request_key_result);
        Map<String, Object> result = new HashMap<>();
        if ("00".equals(request_key_result.get("code").toString()) && request_key_result.get("datas") != null){
            Map<String, Object> map = new Gson().fromJson(request_key_result.get("datas").toString(), Map.class);
            if (map.get("requestSecret") !=null){
                String appSecret = map.get("requestSecret") !=null ? map.get("requestSecret").toString():null ;
                additional = additional != null ? additional: "{}" ;
                // 构造请求参数
                Long current_time = System.currentTimeMillis();
                String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+appSecret+current_time)+"&appKey="+CjroneApp.APP_KEY+"&czrkxm="+czrkxm+"&czrkgmsfhm="+czrkgmsfhm+"&additional="+additional+"&datatype=json";
                result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_CAERID_INFO_URL,params_vailidation);
                System.out.println(result);
            }
        }
        return "获取数据异常";
    }

    // 出生证明 获取出生证明信息（根据父亲或者父亲身份证号码 获取孩子信息）
    @GetMapping("/doCszmInfo")
    public Object doCszmInfo(String fxm, String mxm, String fsfz, String msfz, String additional, String appKeyq, String appSecreqt)throws Exception {
        String is_childs = "暂无孩子信息";
        Map<String, Object> doCszmInfo = apiService.queryCSZMInfo(fxm,mxm,fsfz,msfz,additional);
        if ("00".equals(doCszmInfo.get("code").toString())) {
            if (doCszmInfo.get("datas") != null) {
                System.out.println("doCszmInfo: " + doCszmInfo);
                List<ApiZCszmEntity> list = new Gson().fromJson(doCszmInfo.get("datas").toString().replaceAll(" ", "").replaceAll(":", "").replaceAll("-", ""), new TypeToken<List<ApiZCszmEntity>>() {
                }.getType());
                if (list.size() != 0) {
                    System.out.println("当前存在" + list.size() + "条孩子信息---> ");
                    list.forEach(item -> {
                        System.out.println("孩子信息：" + new Gson().toJson(item));
                    });
                    return list;
                }
            }
        }else {
            is_childs = "获取数据异常";
        }
        return is_childs;
    }

    // 治安 获取治安信息
    @GetMapping("/doZaInfo")
    public Object doZaInfo(String gmsfhm, String additional, String appKeyq, String appSecreqt)throws Exception {
        Map<String, Object> doZaInfo = apiService.queryZAInfo(gmsfhm, null);
        String is_zh = "暂无治安信息";
        if ("00".equals(doZaInfo.get("code").toString())) {
            if(doZaInfo.get("datas") != null) {
                System.out.println("doZaInfo: " + doZaInfo);
                Map<String, Object> nnmap = new Gson().fromJson(doZaInfo.get("datas").toString(), Map.class);
                List<ApiZhiAnEntity> list = new Gson().fromJson(nnmap.get("datas").toString(), new TypeToken<List<ApiZhiAnEntity>>() {
                }.getType());
                if (list.size() != 0) {
                    System.out.println("当前存在" + list.size() + "条治安信息 第一条信息如下---> " + new Gson().toJson(list.get(0)));
                    return new Gson().toJson(list);
                }
            }
        }else {
            is_zh = "获取数据异常";
        }
        return is_zh;
    }

    // 服刑 获取服刑信息
    @GetMapping("/doFxInfo")
    public Object doFxInfo(String cardID, String additional, String appKeyq, String appSecreqt)throws Exception {
        // 获取服刑信息
        String is_fx = "暂无服刑信息";
        Map<String, Object> doFxInfo = apiService.queryFXInfo(cardID, null);
        if ("00".equals(doFxInfo.get("code").toString())) {
            if (doFxInfo.get("datas") != null) {
                System.out.println("doFxInfo: " + doFxInfo);
                List<ApiFuxEntity> list = new Gson().fromJson(doFxInfo.get("datas").toString(), new TypeToken<List<ApiFuxEntity>>() {
                }.getType());
                if (list.size() != 0) {
                    System.out.println("当前存在" + list.size() + "条服刑信息 第一条信息如下---> " + new Gson().toJson(list.get(0)));
                    return new Gson().toJson(list);
                }
            }
        } else{
            is_fx = "获取数据异常";
        }
        return is_fx;
    }

    // 火化信息 获取火化信息
    @GetMapping("/doHhInfo")
    public Object doHhInfo(String cardId, String additional, String appKeyq, String appSecreqt)throws Exception {
        DeadInfoEntity deadInfoEntity = new DeadInfoEntity();
        String is_hh = "暂无火化信息" ;
        // 验证是否死亡
        Map<String, Object> dead_result = apiService.queryHHInfo(cardId, null);
        if ("00".equals(dead_result.get("code").toString())) {
            if(dead_result.get("datas") != null) {
                List<DeadInfoEntity> dead_list = new Gson().fromJson(dead_result.get("datas").toString().replaceAll("=,", "=null,"), new TypeToken<List<DeadInfoEntity>>() {
                }.getType());
                if (dead_list.size() != 0) {
                    System.out.println("当前存在" + dead_list.size() + "条火化信息 第一条信息如下---> " + new Gson().toJson(dead_list.get(0)));
                    return new Gson().toJson(dead_list);
                }
            }
        }else {
            is_hh = "获取数据异常";
        }
        return is_hh;
    }

    // 公民父母身份证号码 获取公民父母身份证号码信息
    @GetMapping("/doFMSfzInfo")
    public Object doFMSfzInfo(String czrkcszmbh, String additional, String appKeyq, String appSecreqt)throws Exception {
        Map<String, Object> doFMSfzInfo = apiService.queryFMCARDInfo(czrkcszmbh, null);
        if ("00".equals(doFMSfzInfo.get("code").toString()) && doFMSfzInfo.get("datas") != null) {
            System.out.println("doFMSfzInfo: "+doFMSfzInfo);
            List<ApiFuxEntity> list = new Gson().fromJson(doFMSfzInfo.get("datas").toString(), new TypeToken<List<ApiFuxEntity>>() {
            }.getType());
            if (list.size() != 0) {
                System.out.println("当前存在" + list.size() + "条身份息 第一条信息如下---> "+ new Gson().toJson(list.get(0)));
                return new Gson().toJson(list);
            }else {
                return list;
            }
        }else {
            return doFMSfzInfo;
        }
    }
    // 公安接口身份证信息
    @GetMapping("/doCardInfo")
    public Object doCardInfo(String xm,String cardId, String additional, String appKeyq, String appSecreqt)throws Exception {
        Map<String, Object> hh_result = apiService.queryCardIDInfo(xm, cardId, null);
        String is_sfz = "暂无身份证信息";
        if ("00".equals(hh_result.get("code").toString())) {
            if (hh_result.get("datas") != null) {
                List<CardIdInfoEntity> list = new Gson().fromJson(hh_result.get("datas").toString(), new TypeToken<List<CardIdInfoEntity>>() {
                }.getType());
                if (list.size() != 0) {
                    CardIdInfoEntity.ELCLICENCESTRUCTBean.DATABean dATABean = list.get(0).getELC_LICENCE_STRUCT().getDATA();
                    if ("奉化市公安局".equals(dATABean.getCZRKQFJG())) {
                        is_sfz = "奉化户籍的身份证信息";
                    }else {
                        is_sfz = "非奉化户籍的身份证信息";
                    }
                }
            }
        }else {
            is_sfz = "获取数据异常";
        }
        return is_sfz;
    }
    // 公安接口户籍信息
    @GetMapping("/doHuJiInfo")
    public Object doHuJiInfo(String cardId, String additional)throws Exception {
        Map<String, Object> hh_result = apiService.queryHuJiInfo(cardId, null);
        String is_sfz = "暂无户籍信息";
        if ("00".equals(hh_result.get("code").toString())) {
            if (hh_result.get("datas") != null) {
                String data = hh_result.get("datas").toString();
                data = data.substring(1,data.length()-1);
                String data_new = data.substring(0,data.indexOf("registrationDate=")+27)+data.substring(data.indexOf("registrationDate=")+36,data.length());
                System.out.println(data_new);

                ApiHuJiEntity apiHuJiEntity = new Gson().fromJson(data_new.replaceAll(" ","").replaceAll("=,","=null,"), ApiHuJiEntity.class);
                if (apiHuJiEntity != null) {
                    System.out.println(new Gson().toJson(apiHuJiEntity));
                    if (apiHuJiEntity.getWhereToLocal() != null && apiHuJiEntity.getWhereToLocal().indexOf("浙江省奉化市") != -1){
                        System.out.println("当前是奉化户籍");
                        is_sfz = "当前是奉化户籍";
                    }
                }
            }
        }else {
            is_sfz = "获取数据异常";
        }
        return is_sfz;
    }
    // 公安接口户籍迁出信息
    @GetMapping("/doHuJiQCInfo")
    public Object doHuJiQCInfo(String cardId, String additional)throws Exception {
        Map<String, Object> hh_result = apiService.queryHuJiQCInfo(cardId, null);
        String is_sfz = "暂无户籍迁出信息";
        if ("00".equals(hh_result.get("code").toString())) {
            if (hh_result.get("datas") != null) {
                System.out.println(hh_result);
                List<CardIdInfoEntity> list = new Gson().fromJson(hh_result.get("datas").toString(), new TypeToken<List<CardIdInfoEntity>>() {
                }.getType());
                if (list.size() != 0) {
                    CardIdInfoEntity.ELCLICENCESTRUCTBean.DATABean dATABean = list.get(0).getELC_LICENCE_STRUCT().getDATA();
                    if ("奉化市公安局".equals(dATABean.getCZRKQFJG())) {
                        is_sfz = "奉化户籍的身份证信息";
                    }else {
                        is_sfz = "非奉化户籍的身份证信息";
                    }
                }
            }
        }else {
            is_sfz = "获取数据异常";
        }
        return is_sfz;
    }
     // 法定代表人信息 获取法定代表人信息
    @GetMapping("/doFdDbInfo")
    public Object 法定代表人信息(String cardId, String additional, String appKeyq, String appSecreqt)throws Exception {
        Map<String, Object> doFdDbInfo = apiService.queryFDDBRInfo(cardId, null);
        String is_fdDbr = "暂无法定发表人信息";
        if ("00".equals(doFdDbInfo.get("code").toString())) {
            System.out.println("doFdDbInfo: "+doFdDbInfo);
            if (doFdDbInfo.get("datas") != null) {
                List<ApiFdDbrEntity> list = new Gson().fromJson(doFdDbInfo.get("datas").toString(), new TypeToken<List<ApiFdDbrEntity>>() {
                }.getType());
                if (list.size() != 0) {
                    System.out.println("当前存在" + list.size() + "条法定代表人信息 第一条信息如下---> " + new Gson().toJson(list.get(0)));
                    return new Gson().toJson(list);
                }
            }
        }else {
            is_fdDbr = "获取数据异常";
        }
        return is_fdDbr;
    }

     // 法定代表人信息 获取法定代表人信息
    @GetMapping("/doFamilyInfo")
    public Object doFamilyInfo(String cardId, String additional, String appKeyq, String appSecreqt)throws Exception {
        Map<String, Object> doFdDbInfo = apiService.queryFamilyInfo(cardId, null);
        return doFdDbInfo;
    }

    // 电子照片 获取电子照片信息
    @GetMapping("/doCardIdPho")
    public Object cardIdPho(String CZRKGMSFHM,String CZRKXM,String CZRKMZ,String CZRKCSRQ,String additional,String appKeyq,String appSecreqt){
        Map<String, Object> request_key_result = CjroneAppUtil.doOne(CjroneApp.APP_REQUEST_SECRET_URL, CjroneApp.APP_KEY, CjroneApp.APP_SECRET);
        System.out.println(request_key_result);
        Map<String, Object> result = new HashMap<>();
        if ("00".equals(request_key_result.get("code").toString()) && request_key_result.get("datas") != null){
            Map<String, Object> map = new Gson().fromJson(request_key_result.get("datas").toString(), Map.class);
            if (map.get("requestSecret") !=null){
                String appSecret = map.get("requestSecret") !=null ? map.get("requestSecret").toString():null ;
                additional = additional != null ? additional: "{}" ;
                // 构造请求参数
                Long current_time = System.currentTimeMillis();
                String params_vailidation = "&requestTime="+current_time+"&sign="+ MD5.md5(CjroneApp.APP_KEY+appSecret+current_time)+"&appKey="+CjroneApp.APP_KEY+"&CZRKGMSFHM="+CZRKGMSFHM+"&CZRKXM="+CZRKXM+"&CZRKMZ="+CZRKMZ+"&CZRKCSRQ="+CZRKCSRQ+"&additional="+additional+"&datatype=json";
                result = CjroneAppUtil.doInfoQuery(CjroneApp.APP_CARD_PHO_INFO_URL,params_vailidation);
                System.out.println(result);
            }
        }
        return result;
    }

    // 公安接口身份证信息
    @GetMapping("/doCardInfoNew")
    public Object doCardInfoNew(String xm,String cardId, String additional, String appKeyq, String appSecreqt)throws Exception {
        Map<String, Object> hh_result = apiService.queryCardIDInfo(xm, cardId, null);
        String is_sfz = "暂无身份证信息";
        if ("00".equals(hh_result.get("code").toString())) {
            if (hh_result.get("datas") != null) {
                List<CardIdInfoEntity> list = new Gson().fromJson(hh_result.get("datas").toString(), new TypeToken<List<CardIdInfoEntity>>() {
                }.getType());
                if (list.size() != 0) {
                    CardIdInfoEntity.ELCLICENCESTRUCTBean.DATABean dATABean = list.get(0).getELC_LICENCE_STRUCT().getDATA();
                    if ("奉化市公安局".equals(dATABean.getCZRKQFJG())) {
                        is_sfz = "奉化户籍的身份证信息";
                    }else {
                        is_sfz = "非奉化户籍的身份证信息";
                    }
                }
            }
        }else {
            is_sfz = "获取数据异常";
        }
        return is_sfz;
    }


}
