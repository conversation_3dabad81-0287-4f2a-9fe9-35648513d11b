package com.hmit.kernespring.modules.app.service;

import com.hmit.kernespring.modules.data_management.entity.DataDisabilityCertificateEntity;
import com.hmit.kernespring.modules.data_management.service.DataDisabilityCertificateService;
import com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity;

import java.util.List;
import java.util.Map;

public interface APIService {
    // 首次获得刷新密钥和请求密钥
    //Map<String,Object> getSecretFrist();
    void t1();
    void t2();
    void t3();

    //异步多线程刷接口
    void asyncblfddbr(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException;
    void asyncblhh(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException;
    void asyncblfx(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException;
    void asyncblhj(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException;
    void asyncblhjqc(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException;
    void asyncdzzz(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException;
    void asyncdbjz(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException;
    void asyncgdxj(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException;
    void asyncqyyl(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException;
    void asyncxsxx(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException;
    void asynctk(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException;
    void asyncdead(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException;
    void asyncylbx(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException;
    void asynclhjy(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException;
    // 医疗保险参保人员信息
    void asynylbxcb(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException;
    // 市治安户籍迁出
    void asynyshihujiqc(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException;
    // 省户口本
    void asynyshenhukou(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException;
    // 社会保险个人参保信息
    void asynyshebao(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException;
    // 人口信息
    void asynyrenkou(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException;
    // 特困信息
    void asynytekun(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException;
    // 殡葬
    void asynybinzang(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException;
    // 流动人口
    void asynyliudong(List<DataDisabilityCertificateEntity> dataDisabilityCertificateEntities) throws InterruptedException;




    // 低保救助接口
    Map<String,Object> queryDIBAOInfo(String cardId, String year, String month, String additional);
    // 市人社企业养老保险信息
    Map<String, Object> queryQYYLInfo(String cardId,String name, String additional);
    //高等学院学籍
    Map<String, Object> queryDXSInfo(String cardId,String name, String additional);
    // 浙江省高等学校
    Map<String, Object> queryZJSInfo(String cardId,String name, String additional);
    // 特困接口
    Map<String,Object> queryTKInfo(String cardId,String name, String additional);
    // 卫健委死亡数据接口
    Map<String,Object> queryDeadInfo(String cardId,String name, String additional);
    // 医疗保险
    Map<String,Object> queryYLBXInfo(String cardId,String name, String additional);
    // 灵活就业
    Map<String,Object> queryLHJYInfo(String cardId,String name, String additional);

    // 获取法定代表人信息 -beilun
    Map<String, Object> queryFDDBRInfo(String cardId, String additional);
    // 公民父母身份证号码 获取公民父母身份证号码信息
    Map<String, Object> queryFMCARDInfo(String czrkcszmbh, String additional) ;
    // 火化信息 获取火化信息 -beilun
    Map<String, Object> queryHHInfo(String cardId, String additional);

    // 服刑 获取服刑信息 -beilun
    Map<String, Object> queryFXInfo(String cardID, String additional);
    // 治安 获取治安信息
    Map<String, Object> queryZAInfo(String gmsfhm, String additional) ;
    // 出生证明 获取出生证明信息
    Map<String, Object> queryCSZMInfo(String fxm, String mxm, String fsfz, String msfz, String additional) ;
    // 省公安接口 获取省份证信息（新）-beilun2
    Map<String, Object> queryCardIDInfo(String czrkxm, String czrkgmsfhm, String additional) ;
    // 省公安接口 获取省份证信息（老）
    Map<String, Object> queryCardIDInfoOld(String czrkxm, String czrkgmsfhm, String additional) ;
    // 获取户籍信息 -beilun
    Map<String, Object>  queryHuJiInfo(String cardId,String additional) ;
    // 省公安厅居民户口簿（个人）
    Map<String, Object> querrHuKouBenInfo(String czrkgmsfhm,String additional);

    // 获取户籍迁出信息 -beilun
    Map<String, Object> queryHuJiQCInfo(String GMSFHM,String additional) ;
    // 获取电子照片信息 -beilun
    Map<String, Object> queryCardIdPho(String CZRKGMSFHM,String CZRKXM,String CZRKMZ,String CZRKCSRQ,String additional) ;
    // 获取家庭信息
    Map<String, Object> queryFamilyInfo(String zjhm,String additional) ;
    // 医疗保险参保人员信息
    Map<String,Object> queryYiLiaoBaoXian(String name,String cardId,String additional);
    // 市治安户籍迁出
    Map<String,Object> queryShiHuJiQC(String cardId,String additional);
    // 省户口本
    Map<String,Object> queryHuKouBenInfo(String cardId,String additional);
    // 社会保险个人参保信息
    Map<String,Object> querySheBao(String name,String cardId,String additional);
    // 人口信息
    Map<String,Object> queyuRenKou(String cardId,String additional);
    // 宁波市特困人员救助供养认定
    Map<String,Object> queryTKInfo(String cardId,String additional);
    // 殡葬
    Map<String,Object> queryBinZangInfo(String name,String cardId,String additional);
    // 流动人口
    Map<String,Object> queryLiuDong(String cardId,String name,String additional);




}
