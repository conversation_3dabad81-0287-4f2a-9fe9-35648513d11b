package com.hmit.kernespring.modules.sign.bean;

public class PosBean {
	private int signType;
	private String posPage;
	private float posX;
	private float posY;
	private String key;
	private float scale;
	public int getSignType() {
		return signType;
	}
	public void setSignType(int signType) {
		this.signType = signType;
	}
	public String getPosPage() {
		return posPage;
	}
	public void setPosPage(String posPage) {
		this.posPage = posPage;
	}
	public float getPosX() {
		return posX;
	}
	public void setPosX(float posX) {
		this.posX = posX;
	}
	public float getPosY() {
		return posY;
	}
	public void setPosY(float posY) {
		this.posY = posY;
	}
	public String getKey() {
		return key;
	}
	public void setKey(String key) {
		this.key = key;
	}
	public float getScale() {
		return scale;
	}
	public void setScale(float scale) {
		this.scale = scale;
	}
	@Override
	public String toString() {
		return "PosBean [signType=" + signType + ", posPage=" + posPage
				+ ", posX=" + posX + ", posY=" + posY + ", key=" + key
				+ ", scale=" + scale + "]";
	}
	
}
