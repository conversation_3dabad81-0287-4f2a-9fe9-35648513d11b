package com.hmit.kernespring.modules.sign.servlet;

import com.hmit.kernespring.modules.sign.service.DocSignManage;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
//import org.apache.log4j.Logger;

import javax.servlet.ServletException;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 *
 * @Package cn.sign.servlet
 * @Description:
 * 完成功能：
 * 1、查询签署详情
 * 2、归档流程
 * 3、获取下载链接
 * 另：获取下载链接后可根据下载链接下载到本地存储在贵司自己的文件系统中，方便以后的文件查看和存储。根据远程路径下载文件的帮助类参考utils包下的DownFile类
 * <AUTHOR>
 * @date 2018年11月6日 下午4:48:10
 */
public class DocSigndetailServlet extends HttpServlet {
	//private static Logger logger = Logger.getLogger(DocSigndetailServlet.class);

	public void doGet(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		doPost(request, response);
	}

	public void doPost(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		request.setCharacterEncoding("utf-8");
		response.setContentType("text/html;charset=utf-8");
		PrintWriter out = response.getWriter();
		out.println("<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.01 Transitional//EN\">");
		out.println("<HTML>");
		out.println("  <HEAD><TITLE>签署详情</TITLE></HEAD>");
		out.println("  <BODY>");

		String docId = "";
		String flowId = "";

		// 从请求的cookie中获取docId和flowId

		Cookie[] cookies = request.getCookies();
		for (Cookie cookie : cookies) {
			if (cookie.getName().equals("docId")) {
				docId = cookie.getValue();
			}
			if (cookie.getName().equals("flowId")) {
				flowId = cookie.getValue();
			}
		}

		if(!(flowId.equals(""))&&flowId!=null){
			/*
			 * 1、查询签署详情
			 *
			 */
			DocSignManage docSign = new DocSignManage();
			JSONObject detail = docSign.detail(flowId);
			int flowStatus = detail.getInt("flowStatus");
			String docName = detail.getString("docName");

			JSONArray signDetailArray = detail.getJSONArray("signDetailList");
			JSONObject signDetail = (JSONObject) signDetailArray.get(0);
			int signStatus = signDetail.getInt("signStatus");

			//判断签署状态
			int archiveProcess = -1;//用于标记流程是否归档
			if(signStatus==2&&flowStatus==1){
				//签署完成，但未归档流程
				out.print("文档签署完成但未归档<br/>");
				//logger.info("文档签署完成但未归档<br/>");
				/**
				 * 2、流程归档
				 */
				archiveProcess = docSign.archiveProcess(flowId);

			}else if(signStatus==2&&flowStatus==2){
				//签署完成，且流程已归档
				archiveProcess=0;//将流程状态更改为已归档
				out.print("文档签署完成且已归档<br/>");
				//logger.info("文档签署完成且已归档<br/>");
			}else{
				//未签署完成
				out.print("文档未签署完成<br/>");
				//logger.info("文档未签署完成<br/>");
			}

			//判断文档归档状态
			JSONObject downJson = null;
			if(archiveProcess==0){
				out.print("文档已归档<br/>");
				//logger.info("文档已归档<br/>");
				/**
				 * 3、获取下载连接
				 */
				downJson = docSign.downUrl(flowId);
			}else{
				out.print("文档未归档<br/>");
				//logger.info("文档未归档<br/>");
			}

			//判断获取下载连接是否成功
			if(downJson.getInt("errCode")==0){
				out.print("获取文档下载地址成功");
				//logger.info("获取文档下载地址成功");
				//解析返回的数据
				JSONArray jsonArray = downJson.getJSONArray("data");
				for (int i=0;i<jsonArray.size();i++) {
					JSONObject downjson = (JSONObject) jsonArray.get(i);
					String docid = downjson.getString("docId");
					String docUrl = downjson.getString("docUrl");
					out.print(docid+"<a href = "+docUrl+">:下载链接</a><br/>");
					//logger.info(docid+"<a href = "+docUrl+">:下载链接</a><br/>");
				}
			}else{
				out.print("获取文档下载地址失败，errcode="+downJson.getInt("errCode")+",msg="+downJson.getInt("msg")+"<br/>");
				//logger.info("获取文档下载地址失败，errcode="+downJson.getInt("errCode")+",msg="+downJson.getInt("msg")+"<br/>");
			}
		}else{
			//未从cookie中获取到cookie
			out.print("获取flowId失败！！！<br/>");
			//logger.info("获取flowId失败！！！<br/>");
		}

		out.println("  </BODY>");
		out.println("</HTML>");
		out.flush();
		out.close();

	}
}
