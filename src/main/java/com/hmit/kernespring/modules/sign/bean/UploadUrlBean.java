package com.hmit.kernespring.modules.sign.bean;
/***
 * 
 * @Package cn.sign.bean
 * @Description: 获取文件上传地址返回结果bean
 * <AUTHOR>  
 * @date 2018年10月10日 下午6:08:43
 */
public class UploadUrlBean {
	
	private String filekey;
	private String uploadUrl;
	public String getFilekey() {
		return filekey;
	}
	public void setFilekey(String filekey) {
		this.filekey = filekey;
	}
	public String getUploadUrl() {
		return uploadUrl;
	}
	public void setUploadUrl(String uploadUrl) {
		this.uploadUrl = uploadUrl;
	}
	
	@Override
	public String toString() {
		return "UploadUrlBean [filekey=" + filekey + ", uploadUrl=" + uploadUrl
				+ "]";
	}
	
}
