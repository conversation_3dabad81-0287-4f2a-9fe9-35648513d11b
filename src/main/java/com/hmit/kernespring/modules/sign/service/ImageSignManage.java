package com.hmit.kernespring.modules.sign.service;

import com.hmit.kernespring.modules.sign.bean.PersonSignBean;
import com.hmit.kernespring.modules.sign.bean.ProcessBean;
import com.hmit.kernespring.modules.sign.bean.SignResultBean;
import com.hmit.kernespring.modules.sign.config.ProjectConfig;
import com.hmit.kernespring.modules.sign.utils.AlgorithmHelper;
import com.hmit.kernespring.modules.sign.utils.HttpHelper;
import net.sf.json.JSONObject;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;

/**
 * 
 * @Package cn.sign.service
 * @Description: 图片印章
 * 实现的接口：
 * 1、创建文档签署流程
 * 2、发起签署 
 * <AUTHOR>  
 * @date 2018年10月12日 下午1:47:12
 */

public class ImageSignManage {
	private static org.slf4j.Logger logger= LoggerFactory.getLogger(ImageSignManage.class);

	// private static Logger logger = Logger.getLogger(Sign.class);
	
	/**
	 * 
	 * @Description: 创建文档签署流程
	 * @param processBean 流程对象
	 * @return 流程ID：flowId
	 * date:2018年10月12日下午1:47:30
	 */
	public String persionImageSign(ProcessBean processBean,String fileKey){
		//创建请求body
		JSONObject json = new JSONObject();

		if(processBean.getInitiatorAccountId()!=null&&!(processBean.getInitiatorAccountId().equals(""))){
			json.put("accountId", processBean.getInitiatorAccountId());
			
		}
		json.put("alias", "个人图片印章"+System.currentTimeMillis());
		json.put("width", 111.4543);
		json.put("height", 100.4534);
		json.put("color", "RED");

		json.put("draw", false);

		json.put("fileKey", fileKey);

		String body = json.toString();
		
		//获取请求签名值
		AlgorithmHelper al = new AlgorithmHelper();
		String signature = al.getSignature(body, ProjectConfig.projectSecret,ProjectConfig.ALGORITHM, ProjectConfig.ENCODING);
				
		//设置请求头
		HttpHelper http = new HttpHelper();
		LinkedHashMap<String, String> postHeaders = http.getPOSTHeaders(ProjectConfig.projectId,ProjectConfig.projectSecret );
		
		//发送post请求
		String url = ProjectConfig.host + ProjectConfig.personImageYz;
		JSONObject processResult = http.sendPOST(url, body, postHeaders, ProjectConfig.ENCODING);
		//返回accountId sealId
		int errCode =  processResult.getInt("errCode");
		String msg =  processResult.getString("msg");
		String sealId="";
		if(errCode==0){
			logger.info("创建个人印章成功："+processResult.toString());
			JSONObject data = processResult.getJSONObject("data");
			sealId = data.getString("sealId");
			return sealId;
		}else{
			logger.info("创建个人印章失败："+processResult.toString());
			return null;
		}		
	}
	/**
	 * 
	 * @Description:发起个人签署 
	 * @param psb
	 * @return
	 * date:2018年10月12日下午5:34:54
	 */
	public SignResultBean PersonSign(PersonSignBean psb, Boolean isMultiple){
		//设置请求体
		// psb.setSealId("$********-b95d-469e-af90-1b71f2c73258$*********");
		JSONObject json = JSONObject.fromObject(psb);
		String body = json.toString();
		System.out.println("body--->"+body);
		//获取请求签名值
		AlgorithmHelper al = new AlgorithmHelper();
		String signature = al.getSignature(body, ProjectConfig.projectSecret,ProjectConfig.ALGORITHM, ProjectConfig.ENCODING);
		
		//设置请求头
		HttpHelper http = new HttpHelper();
		LinkedHashMap<String, String> postHeaders = http.getPOSTHeaders(ProjectConfig.projectId,ProjectConfig.projectSecret );
		
		//发送post请求
		String url = isMultiple ? ProjectConfig.host+ProjectConfig.handPersonSignTasks : ProjectConfig.host+ProjectConfig.handPersonSignTask;
		JSONObject obj = http.sendPOST(url, body, postHeaders, ProjectConfig.ENCODING);

		int errCode = obj.getInt("errCode");
		String msg = obj.getString("msg");
		//返回结果
		SignResultBean signResult = new SignResultBean();
		if(errCode==0){
			logger.info("发起个人手动签署- 成功："+obj.toString());
			JSONObject data = obj.getJSONObject("data");
			signResult.setSignUrl(data.getString("signUrl"));
			signResult.setSignShortUrl(data.getString("signShortUrl"));  //短链接
			signResult.setThirdOrderNo(data.getString("thirdOrderNo"));
			return signResult;
		}else{
			logger.info("发起个人手动签署-失败："+obj.toString());
			return null;
		}

	}



}
