package com.hmit.kernespring.modules.sign.service;

import com.hmit.kernespring.modules.sign.config.ProjectConfig;
import com.hmit.kernespring.modules.sign.utils.AlgorithmHelper;
import com.hmit.kernespring.modules.sign.utils.HttpHelper;
import net.sf.json.JSONObject;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;

/**
 * 
 * @Package cn.sign.service
 * @Description: 
 * 实现接口：
 * 1、获取文件签署详情
 * 2、归档流程
 * 3、获取下载链接
 * <AUTHOR>  
 * @date 2018年11月6日 下午6:34:56
 */
public class DocSignManage {
	private static org.slf4j.Logger logger= LoggerFactory.getLogger(DocSignManage.class);

	// private static Logger logger = Logger.getLogger(DocSignManage.class);
	/**
	 * 
	 * @Description:获取文件签署详情
	 * @param flowId
	 * @return
	 * date:2018年10月12日下午5:56:49
	 */
	public JSONObject detail(String flowId){
		//设置请求体
		JSONObject json =new JSONObject();
		json.put("flowId", flowId);
		String body = json.toString();
		
		//获取请求签名值
		AlgorithmHelper al = new AlgorithmHelper();
		String signature = al.getSignature(body, ProjectConfig.projectSecret,ProjectConfig.ALGORITHM, ProjectConfig.ENCODING);
		
		//设置请求头
		HttpHelper http = new HttpHelper();
		LinkedHashMap<String, String> postHeaders = http.getPOSTHeaders(ProjectConfig.projectId,ProjectConfig.projectSecret );
		
		//发送post请求
		String url = ProjectConfig.host+ProjectConfig.detail;
		JSONObject obj = http.sendPOST(url, body, postHeaders, ProjectConfig.ENCODING);
		int errCode = obj.getInt("errCode");
		String msg = obj.getString("msg");
		if(errCode==0){
			JSONObject job = obj.getJSONObject("data");
			logger.info("获取签署详情成功："+job.toString());
			return job;
		}else{
			logger.info("获取签署详情失败"+obj.toString());
			return null;
		}
	}
	/**
	 * 
	 * @Description:流程归档 
	 * @param flowId
	 * @return
	 * date:2018年10月12日下午5:56:49
	 */
	public int archiveProcess(String flowId){
		//设置请求体
		JSONObject json =new JSONObject();
		json.put("flowId", flowId);
		String body = json.toString();
		
		//获取请求签名值
		AlgorithmHelper al = new AlgorithmHelper();
		String signature = al.getSignature(body, ProjectConfig.projectSecret,ProjectConfig.ALGORITHM, ProjectConfig.ENCODING);
		
		//设置请求头
		HttpHelper http = new HttpHelper();
		LinkedHashMap<String, String> postHeaders = http.getPOSTHeaders(ProjectConfig.projectId,ProjectConfig.projectSecret );
		
		//发送post请求
		String url = ProjectConfig.host+ProjectConfig.archiveProcess;
		JSONObject obj = http.sendPOST(url, body, postHeaders, ProjectConfig.ENCODING);
		logger.info("文档归档："+obj.toString());
		int errCode = obj.getInt("errCode");
		return errCode;
	}
	/**
	 * 
	 * @Description: 获取下载地址
	 * @param flowId
	 * @return 
	 * date:2018年11月6日下午6:36:11
	 */
	public JSONObject downUrl(String flowId){
		//设置请求体
		JSONObject json =new JSONObject();
		json.put("flowId", flowId);
		String body = json.toString();
				
				
		//设置请求头
		HttpHelper http = new HttpHelper();
		LinkedHashMap<String, String> postHeaders = http.getPOSTHeaders(ProjectConfig.projectId,ProjectConfig.projectSecret );
		
		//发送post请求
		String url = ProjectConfig.host+ProjectConfig.down;
		JSONObject obj = http.sendPOST(url, body, postHeaders, ProjectConfig.ENCODING);
	
		return obj;
	}
	
}
