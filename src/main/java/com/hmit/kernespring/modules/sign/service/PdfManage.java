package com.hmit.kernespring.modules.sign.service;

import com.hmit.kernespring.modules.sign.bean.UploadUrlBean;
import com.hmit.kernespring.modules.sign.config.ProjectConfig;
import com.hmit.kernespring.modules.sign.utils.AlgorithmHelper;
import com.hmit.kernespring.modules.sign.utils.FileHelper;
import com.hmit.kernespring.modules.sign.utils.HttpHelper;
import net.sf.json.JSONObject;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 *
 * @Package cn.sign.service
 * @Description: 文档服务类，
 * 包含以下接口：
 * 1、获取文件上传地址
 * 2、根据上传地址上传文件
 * 3、使用上传的文件作为待签署文件（本地文件创建文档）
 * <AUTHOR>
 * @date 2018年10月11日 下午6:27:38
 */
public class PdfManage {
	// private static Logger logger = Logger.getLogger(PdfManage.class);
	private static org.slf4j.Logger logger= LoggerFactory.getLogger(PdfManage.class);

	/**
	 *
	 * @Description:
	 * @param filePath  上传文件的绝对路径
	 * @return UploadUrlBean；
	 * date:2018年10月10日下午6:11:20
	 */
	public UploadUrlBean uploadUrl(String filePath){
		//设置请求体
		FileHelper fileHelper = new FileHelper();
		Map<String, String> fileInfo = fileHelper.getFileInfo(filePath);
		JSONObject obj = new JSONObject();
		obj.put("fileName", fileInfo.get("FileName"));
		obj.put("fileSize", fileInfo.get("FileLength"));
		obj.put("contentType", "image/jpeg");  //application/pdf
		obj.put("contentMd5", fileHelper.getContentMD5(filePath));
		String body = obj.toString();
		logger.info("body:"+body);

		//获取请求签名值
		AlgorithmHelper al = new AlgorithmHelper();
		String signature = al.getSignature(body, ProjectConfig.projectSecret,ProjectConfig.ALGORITHM, ProjectConfig.ENCODING);

		//设置请求头
		HttpHelper http = new HttpHelper();
		LinkedHashMap<String, String> postHeaders = http.getPOSTHeaders(ProjectConfig.projectId,ProjectConfig.projectSecret );

		//发送post请求
		String url = ProjectConfig.host+ProjectConfig.uploadUrl;
		JSONObject uploadUrlResult = http.sendPOST(url, body, postHeaders, ProjectConfig.ENCODING);

		//返回POST请求结果
		UploadUrlBean uploadUrlBean = new UploadUrlBean();
		int errCode = (int)uploadUrlResult.get("errCode");
		String msg = (String)uploadUrlResult.get("msg");
		if(errCode==0){
			logger.info("获取文件上传路径成功："+uploadUrlResult.toString());
			JSONObject data = uploadUrlResult.getJSONObject("data");
			uploadUrlBean.setFilekey((String)data.get("fileKey"));
			uploadUrlBean.setUploadUrl((String)data.get("uploadUrl"));
			return uploadUrlBean;
		}else{
			logger.info("获取文件上传路径失败："+uploadUrlResult.toString());
			return null;
		}

	}
	/**
	 *
	 * @Description: 上传文件
	 * @param filePath  上传文件路径
	 * @param urlBean
	 * @return
	 * date:2018年10月11日下午6:13:20
	 */
	public  int uploadFile(String filePath,String uploadUrl){
		logger.info("上传文件路径："+filePath+",上传地址："+uploadUrl);
		HttpHelper http = new HttpHelper();
		FileHelper fileHelper = new FileHelper();
		LinkedHashMap<String, String> headers = http.getPUTHeaders(fileHelper.getContentMD5(filePath), ProjectConfig.contentTypePdf, ProjectConfig.ENCODING);
		logger.info("上传文件至签章服务器put请求头："+headers.toString());
		int putstatus = http.sendPUT(uploadUrl, filePath, headers);
		if(putstatus>=200&&putstatus<400){
			logger.info(filePath+"文件上传签章服务器成功");
		}else{
			logger.info(filePath+"文件上传签章服务器失败");
		}
		return putstatus;
	}

	/**
	 *
	 * @Description: 使用上传的文件作为待签署文件
	 * @param fileKey  由获取上传地址返回的filekey
	 * @return docId
	 * date:2018年10月12日上午11:39:35
	 */
	public String createDocByFileKey(String fileKey){
		//设置请求体
		JSONObject obj = new JSONObject();
		obj.put("fileKey", fileKey);
		String body = obj.toString();

		//获取请求签名值
		AlgorithmHelper al = new AlgorithmHelper();
		String signature = al.getSignature(body, ProjectConfig.projectSecret,ProjectConfig.ALGORITHM, ProjectConfig.ENCODING);

		//设置请求头
		HttpHelper http = new HttpHelper();
		LinkedHashMap<String, String> postHeaders = http.getPOSTHeaders(ProjectConfig.projectId,ProjectConfig.projectSecret );

		//发送post请求
		String url = ProjectConfig.host+ProjectConfig.createbyfilekey;
		JSONObject createDocResult = http.sendPOST(url, body, postHeaders, ProjectConfig.ENCODING);

		//返回docId
		int errCode =  createDocResult.getInt("errCode");
		String msg =  createDocResult.getString("msg");
		String docId = "";
		if(errCode==0){
			logger.info("本地文件创建文档成功："+createDocResult.toString());
			JSONObject data = createDocResult.getJSONObject("data");
			docId = data.getString("docId");
			return docId;
		}else{
			logger.info("本地文件创建文档失败："+createDocResult.toString());
			return null;
		}
	}



}
