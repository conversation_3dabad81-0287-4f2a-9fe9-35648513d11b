package com.hmit.kernespring.modules.sign.test;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;

public class DownTest {

	
	public static void main(String[] args) throws Exception{

	    String url = "https://esignoss.oss-cn-hangzhou.aliyuncs.com/1111563786/08384a95-b3e0-4952-9994-1894a79bbe16/a40e158c-e403-42e4-9e49-85f08ab38b5a.pdf?Expires=1554287935&OSSAccessKeyId=LTAIdvHfiVrzDKbE&Signature=%2B7HrwD5sHUP4VPys3fP2Gp83TEc%3D";
	    downLoadFromUrl(url, "1111111.pdf", "D:/");
	}

	/**
	 * 从网络Url中下载文件
	 * @param urlStr
	 * @param fileName
	 * @param savePath
	 * @throws IOException
	 */
	public static File downLoadFromUrl(String urlStr, String fileName, String savePath) throws IOException {
	    long startTime = System.currentTimeMillis();
	    URL url = new URL(urlStr);
	    HttpURLConnection conn = (HttpURLConnection)url.openConnection();
	    //设置超时间为3秒
	    conn.setConnectTimeout(3000);
	    //防止屏蔽程序抓取而返回403错误
	    conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");

	    //得到输入流
	    InputStream inputStream = conn.getInputStream();
	    System.out.println("花费时间1：" + (System.currentTimeMillis() - startTime) + "ms");
	    //获取自己数组
	    byte[] getData = readInputStream(inputStream);
	    System.out.println("花费时间2："+(System.currentTimeMillis()-startTime)+"ms");
	    //文件保存位置
	    File saveDir = new File(savePath);
	    if(!saveDir.exists()){
	        saveDir.mkdirs();
	    }
	    File file = new File(saveDir+File.separator+fileName);
	    if(!file.exists()){
	        file.createNewFile();
	    }
	    FileOutputStream fos = new FileOutputStream(file);
	    fos.write(getData);
	    if(fos!=null){
	        fos.close();
	    }
	    if(inputStream!=null){
	        inputStream.close();
	    }
	    long endTime = System.currentTimeMillis();
	    System.out.println("花费时间4："+(endTime-startTime)+"ms"+"  "+new String (fileName.getBytes(), "UTF-8"));
	    System.out.println("============");
	    return file;
	}

	/**
	 * 从输入流中获取字节数组
	 * @param inputStream
	 * @return
	 * @throws IOException
	 */
	public static  byte[] readInputStream(InputStream inputStream) throws IOException {
	    byte[] buffer = new byte[8096];
	    int len = 0;
	    ByteArrayOutputStream bos = new ByteArrayOutputStream();
	    while((len = inputStream.read(buffer)) != -1) {
	        bos.write(buffer, 0, len);
	    }
	    bos.close();
	    return bos.toByteArray();
	}
}
