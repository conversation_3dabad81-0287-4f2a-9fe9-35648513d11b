package com.hmit.kernespring.modules.sign.bean;

import java.util.List;
import java.util.Map;

/**
 * 
 * @Package cn.sign.bean
 * @Description: 单文档签署流程对象类
 * <AUTHOR>  
 * @date 2018年10月12日 下午1:49:16
 */
public class ProcessBean {
	private String businessScene;
	
	private String initiatorAccountId;
	private Long signValidity;
	private String signPlatform;
	private String noticeType;
	private String redirectUrl;
	private String docId;
	private List<Map<String,Object>> docList;
	private String docName;
	private Boolean encryption;

	private Boolean isMultiple;

	public Boolean isMultiple() {
		return isMultiple;
	}

	public void setMultiple(Boolean multiple) {
		isMultiple = multiple;
	}

	private String payer;

	public List<Map<String, Object>> getDocList() {
		return docList;
	}

	public void setDocList(List<Map<String, Object>> docList) {
		this.docList = docList;
	}

	public String getBusinessScene() {
		return businessScene;
	}
	public void setBusinessScene(String businessScene) {
		this.businessScene = businessScene;
	}
	public String getInitiatorAccountId() {
		return initiatorAccountId;
	}
	public void setInitiatorAccountId(String initiatorAccountId) {
		this.initiatorAccountId = initiatorAccountId;
	}
	public Long getSignValidity() {
		return signValidity;
	}
	public void setSignValidity(Long signValidity) {
		this.signValidity = signValidity;
	}
	public String getSignPlatform() {
		return signPlatform;
	}
	public void setSignPlatform(String signPlatform) {
		this.signPlatform = signPlatform;
	}
	public String getNoticeType() {
		return noticeType;
	}
	public void setNoticeType(String noticeType) {
		this.noticeType = noticeType;
	}
	public String getRedirectUrl() {
		return redirectUrl;
	}
	public void setRedirectUrl(String redirectUrl) {
		this.redirectUrl = redirectUrl;
	}
	public String getDocId() {
		return docId;
	}
	public void setDocId(String docId) {
		this.docId = docId;
	}
	public String getDocName() {
		return docName;
	}
	public void setDocName(String docName) {
		this.docName = docName;
	}
	public Boolean getEncryption() {
		return encryption;
	}
	public void setEncryption(Boolean encryption) {
		this.encryption = encryption;
	}
	public String getPayer() {
		return payer;
	}
	public void setPayer(String payer) {
		this.payer = payer;
	}
	@Override
	public String toString() {
		return "ProcessBean [businessScene=" + businessScene
				+ ", initiatorAccountId=" + initiatorAccountId
				+ ", signValidity=" + signValidity + ", signPlatform="
				+ signPlatform + ", noticeType=" + noticeType
				+ ", redirectUrl=" + redirectUrl + ", docId=" + docId
				+ ", docName=" + docName + ", encryption=" + encryption
				+ ", payer=" + payer + "]";
	}
	
}
