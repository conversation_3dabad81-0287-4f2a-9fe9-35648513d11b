package com.hmit.kernespring.modules.sign.service;

import com.google.gson.Gson;
import com.hmit.kernespring.modules.sign.bean.*;
import com.hmit.kernespring.modules.sign.config.ProjectConfig;
import com.hmit.kernespring.modules.sign.utils.AlgorithmHelper;
import com.hmit.kernespring.modules.sign.utils.HttpHelper;
import com.hmit.kernespring.modules.sys.entity.SysUserEntity;
import com.hmit.kernespring.modules.sys.service.SysUserService;
import net.sf.json.JSONObject;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 *
 * @Package cn.sign.service
 * @Description: 文档签署相关类
 * 实现的接口：
 * 1、创建文档签署流程
 * 2、发起签署
 * <AUTHOR>
 * @date 2018年10月12日 下午1:47:12
 */
@Service("signService")
public class Sign {
	private static org.slf4j.Logger logger= LoggerFactory.getLogger(Sign.class);

	@Autowired
	private SysUserService sysUserService;

	// private static Logger logger = Logger.getLogger(Sign.class);

	/**
	 *
	 * @Description: 创建文档签署流程
	 * @param processBean 流程对象
	 * @return 流程ID：flowId
	 * date:2018年10月12日下午1:47:30
	 */
	public String signProcess(ProcessBean processBean){
		//创建请求body
		JSONObject json = new JSONObject();
		if(processBean.getBusinessScene()!=null&&!(processBean.getBusinessScene().equals(""))){
			json.put("businessScene", processBean.getBusinessScene());
		}else{
			logger.info("业务场景名称不可为空");return "";
		}
		if(processBean.getDocId()!=null&&!(processBean.getDocId().equals(""))){
			json.put("docId", processBean.getDocId());
		}else{
			if (!processBean.isMultiple()){
				logger.info("docId不可为空");return "";
			}
		}
		if(processBean.getDocList() !=null&& processBean.getDocList().size() !=0 ){
			json.put("docList", processBean.getDocList());
		}else{
			if (processBean.isMultiple()){
				logger.info("docList不可为空");return "";
			}
		}
		if(processBean.getInitiatorAccountId()!=null&&!(processBean.getInitiatorAccountId().equals(""))){
			json.put("initiatorAccountId", processBean.getInitiatorAccountId());

		}
		if(processBean.getSignValidity()!=null){
			json.put("signValidity", processBean.getSignValidity());
		}
		if(processBean.getSignPlatform()!=null&&!(processBean.getSignPlatform().equals(""))){
			json.put("signPlatform", processBean.getSignPlatform());
		}
		if(processBean.getNoticeType()!=null&&!(processBean.getNoticeType().equals(""))){
			json.put("noticeType", processBean.getNoticeType());
		}
		if(processBean.getRedirectUrl()!=null&&!(processBean.getRedirectUrl().equals(""))){
			json.put("redirectUrl", processBean.getRedirectUrl());
		}

		if(processBean.getDocName()!=null&&!(processBean.getDocName().equals(""))){
			json.put("docName", processBean.getDocName());
		}
		if(processBean.getEncryption()!=null&&!(processBean.getEncryption().equals(""))){
			json.put("encryption", processBean.getEncryption());
		}
		if(processBean.getPayer()!=null&&!(processBean.getPayer().equals(""))){
			json.put("payer", processBean.getPayer());
		}
		String body = json.toString();

		//获取请求签名值
		AlgorithmHelper al = new AlgorithmHelper();
		String signature = al.getSignature(body, ProjectConfig.projectSecret,ProjectConfig.ALGORITHM, ProjectConfig.ENCODING);

		//设置请求头
		HttpHelper http = new HttpHelper();
		LinkedHashMap<String, String> postHeaders = http.getPOSTHeaders(ProjectConfig.projectId,ProjectConfig.projectSecret );

		//发送post请求
		String url = processBean.isMultiple() ? ProjectConfig.host + ProjectConfig.addProcesss:ProjectConfig.host+ ProjectConfig.addProcess;
		JSONObject processResult = http.sendPOST(url, body, postHeaders, ProjectConfig.ENCODING);

		//返回accountId
		int errCode =  processResult.getInt("errCode");
		String msg =  processResult.getString("msg");
		String flowId="";
		if(errCode==0){
			logger.info("创建签署流程成功："+processResult.toString());
			JSONObject data = processResult.getJSONObject("data");
			flowId = data.getString("flowId");
			return flowId;
		}else{
			logger.info("创建签署流程失败："+processResult.toString());
			return null;
		}
	}
	/**
	 *
	 * @Description:发起个人签署
	 * @param psb
	 * @return
	 * date:2018年10月12日下午5:34:54
	 */
	public SignResultBean PersonSign(PersonSignBean psb, Boolean isMultiple, Boolean isApp){
		System.out.println(psb.getSealId());
		psb.setThirdOrderNo(null);
		//设置请求体
		// psb.setSealId("$********-b95d-469e-af90-1b71f2c73258$*********");
		JSONObject json = JSONObject.fromObject(psb);
		String body = json.toString();
		System.out.println("body--->"+body);
		if (isApp){
			Map<String, Object> params = new HashMap<>();
			params.put("accountId",psb.getAccountId());
			params.put("flowId",psb.getFlowId());
			params.put("sealType","0");
			params.put("posList",psb.getPosList());
			body = new Gson().toJson(params);
		}else {
			// 获取个人图片印章 并设置
		}
		//获取请求签名值
		AlgorithmHelper al = new AlgorithmHelper();
		String signature = al.getSignature(body, ProjectConfig.projectSecret,ProjectConfig.ALGORITHM, ProjectConfig.ENCODING);

		//设置请求头
		HttpHelper http = new HttpHelper();
		LinkedHashMap<String, String> postHeaders = http.getPOSTHeaders(ProjectConfig.projectId,ProjectConfig.projectSecret );

		//发送post请求
		String url = isMultiple ? ProjectConfig.host+ProjectConfig.handPersonSignTasks : ProjectConfig.host+ProjectConfig.handPersonSignTask;
		JSONObject obj = http.sendPOST(url, body, postHeaders, ProjectConfig.ENCODING);

		int errCode = obj.getInt("errCode");
		String msg = obj.getString("msg");
		//返回结果
		SignResultBean signResult = new SignResultBean();
		if(errCode==0){
			logger.info("发起个人手动签署- 成功："+obj.toString());
			JSONObject data = obj.getJSONObject("data");
			signResult.setSignUrl(data.getString("signUrl"));
			signResult.setSignShortUrl(data.getString("signShortUrl"));  //短链接
			signResult.setThirdOrderNo(data.getString("thirdOrderNo"));
			return signResult;
		}else{
			logger.info("发起个人手动签署-失败："+obj.toString());
			return null;
		}

	}

	/**
	 * 将签字图片上传至签章服务器
	 * @param fileUrl
	 * @param pb
	 * @return
	 */
	public String BindPersonImages(String fileUrl, ProcessBean pb) {
		// 照片印章开始
		//将签字图片上传至签章服务器
		PdfManage pm= new PdfManage();
		UploadUrlBean uploadUrlBean_tmp = pm.uploadUrl(fileUrl);
		int uploadFile_tmp = pm.uploadFile(fileUrl, uploadUrlBean_tmp.getUploadUrl());
		System.out.println("uploadFile_tmp::"+uploadFile_tmp);
		if(uploadFile_tmp<200||uploadFile_tmp>400){//判断是否文件上传至签章服务器是否成功

		}
		System.out.println("图片印章上传文件filekey:"+uploadUrlBean_tmp.getFilekey());
		// 获取个人图片印章
		ProcessBean pb_tmp= new ProcessBean();
		pb_tmp.setInitiatorAccountId(pb.getInitiatorAccountId());
		ImageSignManage imageSignManage = new ImageSignManage();
		String sealIdTmp = imageSignManage.persionImageSign(pb_tmp,uploadUrlBean_tmp.getFilekey());
		System.out.println("sealId-------->:"+sealIdTmp);
		return sealIdTmp;
	}

	/**
	 * 修改手签人的个人信息
	 * @param entity
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public String updatePersonInfo(SysUserEntity entity) {

		Account account = new Account();
		String accountId = account.queryAccountIdByThirdId(entity.getUserId().toString());

		String result = account.updatePerson(accountId,entity);
		if(result != null){
			return result;
		}
		return null;
	}


	/**
	 *
	 * @param thirdId sysUser 表中的主键ID
	 * @param entity sysUser 实体
	 * @return
	 */
	public String personAccountId(String thirdId,SysUserEntity entity){
//		SysUserEntity entity = sysUserService.getById(thirdId);
		if(entity == null){
			return "0";
		}
		if(entity.getName()== null || entity.getEmail() == null|| entity.getIdNo()== null ||entity.getMobile() == null){
			return "2";
		}
		PersonBean personBean = new PersonBean();
		//根据账号 找以下信息 sys_user 表中的主键ID
		personBean.setThirdId(thirdId);
		//name
		personBean.setName(entity.getName());
		personBean.setIdType(19);//demo中默认设置为中国大陆居民身份证，如遇其他类型，请参考对接文档中证件号对应的类型值。
		//身份账号
		personBean.setIdNo(entity.getIdNo());
		personBean.setMobile(entity.getMobile());
		//email
		personBean.setEmail(entity.getEmail());
		// 创建账户
		Account account = new Account();
		ProcessBean pb= new ProcessBean();
		String accountId = account.addPerson(personBean);
		System.out.println(accountId);
		pb.setInitiatorAccountId(accountId);
		// 查询个人印章数量
		account.personYinZhangList(pb);
		return accountId;
	}


}
