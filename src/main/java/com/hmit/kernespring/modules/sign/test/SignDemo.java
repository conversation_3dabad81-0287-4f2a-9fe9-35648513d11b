package com.hmit.kernespring.modules.sign.test;

import com.hmit.kernespring.modules.sign.DB.PersonDb;
import com.hmit.kernespring.modules.sign.bean.*;
import com.hmit.kernespring.modules.sign.service.Account;
import com.hmit.kernespring.modules.sign.service.ImageSignManage;
import com.hmit.kernespring.modules.sign.service.PdfManage;
import com.hmit.kernespring.modules.sign.service.Sign;
import com.hmit.kernespring.modules.sys.service.SysUserService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;

public class SignDemo {
	@Autowired
	private SysUserService sysUserService;

	public static void main(String[] args) {
		/*// 个人账号
		ProcessBean pb= new ProcessBean();
		pb.setInitiatorAccountId("62bd5347655e4404b6e2fa9db7558c80");
//		pb.setInitiatorAccountId("218941804fde4058ad3ce1cb4acbf203");
//		UploadUrlBean upfile = upfile();
//		String accountId = testPersonAccount();
//		String docId = testcreateDocByFileKey( upfile.getFilekey());
//		String flowId = testSignProcess(accountId, docId);
//		PersonSignResultBean testPersonSign = testPersonSign(accountId, flowId);
//		System.err.println(testPersonSign.toString());
		// 个人 签名照片地址
		String fileUrl = "/Users/<USER>/Desktop/屏幕快照/屏幕快照 2019-07-21 上午11.15.15.png";
		// 创建个人图片印章 并绑定账号
		BindPersonImages(fileUrl, pb);
		Account account = new Account();
		// 查询个人印章数量
		account.personYinZhangList(pb);*/

		// account.delPerson(pb);

		ProcessBean pb= new ProcessBean();
		String accountId = personAccountId();
		pb.setInitiatorAccountId(accountId);
		String fileUrl = "/Users/<USER>/Desktop/sing/1030/lALPD4Bhs-bXT_rNATvNAU0_333_315.png";
		BindPersonImages(fileUrl, pb);
		Account account = new Account();
		// 查询个人印章数量
		account.personYinZhangList(pb);
// 269
		// {"thirdId":"295","name":"单巧波","idNo":"330283198609213722","idType":19,"mobile":"***********","email":"<EMAIL>"}
		/*PersonBean personBean = new PersonBean();
		personBean.setThirdId("1001");
		personBean.setName("街道测试员");
		personBean.setIdType(19);//demo中默认设置为中国大陆居民身份证，如遇其他类型，请参考对接文档中证件号对应的类型值。
		personBean.setIdNo("500241199311105916");
		personBean.setMobile("***********");
		personBean.setEmail("<EMAIL>");
		// 创建账户
		Account account = new Account();
		ProcessBean pb= new ProcessBean();
		String accountId = account.addPerson(personBean);
		System.out.println(accountId);
		pb.setInitiatorAccountId(accountId);
		// 查询个人印章数量
		account.personYinZhangList(pb);*/
		//删除账号
	/*	ProcessBean pb= new ProcessBean();
		pb.setInitiatorAccountId("2c8590d3ffc6433cb650a103953b859a");
		Account account = new Account();
		account.delPerson(pb);*/
		// account.delPersonYz("57869a0e4f684f249053b9c396e9ee10","f39ad190-5ce3-4f6c-ac6a-296f891b0896");
	}
	public static String BindPersonImages(String fileUrl, ProcessBean pb) {
		// 照片印章开始
        //将签字图片上传至签章服务器
		PdfManage pm= new PdfManage();
        UploadUrlBean uploadUrlBean_tmp = pm.uploadUrl(fileUrl);
        int uploadFile_tmp = pm.uploadFile(fileUrl, uploadUrlBean_tmp.getUploadUrl());
        System.out.println("uploadFile_tmp::"+uploadFile_tmp);
        if(uploadFile_tmp<200||uploadFile_tmp>400){//判断是否文件上传至签章服务器是否成功

        }
        System.out.println("图片印章上传文件filekey:"+uploadUrlBean_tmp.getFilekey());
        // 获取个人图片印章
        ProcessBean pb_tmp= new ProcessBean();
        pb_tmp.setInitiatorAccountId(pb.getInitiatorAccountId());
        ImageSignManage imageSignManage = new ImageSignManage();
        String sealIdTmp = imageSignManage.persionImageSign(pb_tmp,uploadUrlBean_tmp.getFilekey());
        System.out.println("sealId-------->:"+sealIdTmp);
        return sealIdTmp;
	}
	private static UploadUrlBean upfile() {
		String filePath = "d:\\test.pdf";
		PdfManage pdf = new PdfManage();
		UploadUrlBean uploadUrlBean = pdf.uploadUrl(filePath);
		int uploadFile = pdf.uploadFile(filePath, uploadUrlBean.getUploadUrl());
		return uploadUrlBean;
	}

	public static String personAccountId(){

		PersonBean personBean = new PersonBean();
		//根据账号 找以下信息 sys_user 表中的主键ID
		personBean.setThirdId("1030");
		//name
		personBean.setName("胡红艳");
		personBean.setIdType(19);//demo中默认设置为中国大陆居民身份证，如遇其他类型，请参考对接文档中证件号对应的类型值。
		//身份账号
		personBean.setIdNo("330206197810200328");
		personBean.setMobile("***********");
		//email
		personBean.setEmail("<EMAIL>");
		// 创建账户
		Account account = new Account();
		ProcessBean pb= new ProcessBean();
		String accountId = account.addPerson(personBean);
		System.out.println(accountId);
		pb.setInitiatorAccountId(accountId);
		// 查询个人印章数量
		account.personYinZhangList(pb);
		return accountId;
	}


	public static String testPersonAccount(){
		//创建personBean对象
		PersonBean personBean = new PersonBean();
		personBean.setThirdId(PersonDb.userid);
		personBean.setName(PersonDb.username);
		personBean.setIdType(19);
		personBean.setIdNo(PersonDb.idnum);
		personBean.setMobile(PersonDb.mobile);
		personBean.setEmail(PersonDb.email);
		//创建账户
		Account account = new Account();
		String accountId = account.addPerson(personBean);
		return accountId;
	}

	public static String testcreateDocByFileKey(String fileKey){
		String docId = "";
		PdfManage pm = new PdfManage();
		docId = pm.createDocByFileKey(fileKey);
		return docId;
	}


	public static String testSignProcess(String accountId,String docId){

		ProcessBean pb= new ProcessBean();
		pb.setBusinessScene("申请材料证明");
		pb.setInitiatorAccountId(accountId);
		pb.setSignPlatform("1");
		pb.setRedirectUrl("http://localhost:8080/DocSigndetailServlet");
		pb.setDocId(docId);
		pb.setEncryption(false);

		Sign sign = new Sign();
		String flowId = sign.signProcess(pb);
		return flowId;
	}

	public static SignResultBean testPersonSign(String accountId, String flowId){

		PosBean pos = new PosBean();
		pos.setPosPage("1");
		pos.setPosX(200.0f);
		pos.setPosY(300.0f);
		pos.setScale(159.0f);
		pos.setSignType(2);
		ArrayList<PosBean> list= new ArrayList<>();
		list.add(pos);
		PersonSignBean p = new PersonSignBean();
		p.setAccountId(accountId);
		p.setFlowId(flowId);
		p.setSealType("0,1");
		p.setThirdOrderNo("setThirdOrderNosetThirdOrderNo");
		p.setPosList(list);
		Sign sign = new Sign();
		SignResultBean personSign = sign.PersonSign(p,false,false);
		return personSign;

	}

}
