package com.hmit.kernespring.modules.sign.servlet;

import com.hmit.kernespring.modules.sign.DB.PersonDb;
import com.hmit.kernespring.modules.sign.bean.PersonBean;
import com.hmit.kernespring.modules.sign.service.Account;
//import org.apache.log4j.Logger;

import javax.servlet.ServletException;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 *
 * @Package cn.sign.servlet
 * @Description:
 * 完成功能：
 * 创建个人账户，使用的数据是采用政务服务网个人单点登录返回的个人信息
 * <AUTHOR>
 * @date 2018年11月6日 下午4:47:01
 */
public class PersonServlet extends HttpServlet {
	//private static Logger logger = Logger.getLogger(PersonServlet.class);

	public void doGet(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		doPost(request, response);


	}

	public void doPost(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		request.setCharacterEncoding("utf-8");
		response.setContentType("text/html;charset=utf-8");
		//logger.info("创建个人用户");

		//创建personBean对象  设置个人参数
		PersonBean personBean = new PersonBean();
		personBean.setThirdId(PersonDb.userid);
		personBean.setName(PersonDb.username);
		personBean.setIdType(19);//demo中默认设置为中国大陆居民身份证，如遇其他类型，请参考对接文档中证件号对应的类型值。
		personBean.setIdNo(PersonDb.idnum);
		personBean.setMobile(PersonDb.mobile);
		personBean.setEmail(PersonDb.email);
		//创建账户
		Account account = new Account();
		String accountId = account.addPerson(personBean);
		Cookie c1 = new Cookie("personAccountId", accountId);
		response.addCookie(c1);
		if(accountId!=null&&!accountId.equals("")){
			response.setHeader("refresh", "1;URL=uploadFile.html");
		}else{
			response.getWriter().write("创建个人用户失败！");
			response.getWriter().flush();
			response.getWriter().close();
		}

	}

}
