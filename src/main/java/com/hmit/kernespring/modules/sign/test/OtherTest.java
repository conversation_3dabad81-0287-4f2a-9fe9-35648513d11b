package com.hmit.kernespring.modules.sign.test;

import com.hmit.kernespring.modules.sign.service.DocSignManage;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

public class OtherTest {
	public static void main(String[] args) {
		DocSignManage sign = new DocSignManage();
//		System.out.println(sign.signdetail("163a7070612a4174a636c06b1d8bb32b"));
		JSONObject signdetail = sign.detail("77c96ba9dcde459db6e5fb1d907994e7");
		sign.archiveProcess("77c96ba9dcde459db6e5fb1d907994e7");
		JSONObject downUrl = sign.downUrl("77c96ba9dcde459db6e5fb1d907994e7");

		JSONArray data = downUrl.getJSONArray("data");
		int size = data.size();
		for(int i=0;i<size;i++){
			JSONObject down = (JSONObject) data.get(i);
			String docid = (String) down.get("docId");
			String docurl = (String) down.get("docUrl");
			
		} 
		
		JSONObject object = (JSONObject) data.get(0);
		System.out.println(object.toString());
		//System.out.println(downUrl.toString());

	}
}