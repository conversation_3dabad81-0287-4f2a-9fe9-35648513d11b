package com.hmit.kernespring.modules.sign.bean;

import java.util.ArrayList;

public class PersonSignBean {
	private String sealId;
	private String flowId;
	private String thirdOrderNo;
	private String accountId;

	public String getSealId() {
		return sealId;
	}

	public void setSealId(String sealId) {
		this.sealId = sealId;
	}

	private String sealType;
	private ArrayList<PosBean> posList;
	public String getFlowId() {
		return flowId;
	}
	public void setFlowId(String flowId) {
		this.flowId = flowId;
	}
	public String getThirdOrderNo() {
		return thirdOrderNo;
	}
	public void setThirdOrderNo(String thirdOrderNo) {
		this.thirdOrderNo = thirdOrderNo;
	}
	public String getAccountId() {
		return accountId;
	}
	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}
	public String getSealType() {
		return sealType;
	}
	public void setSealType(String sealType) {
		this.sealType = sealType;
	}
	public ArrayList<PosBean> getPosList() {
		return posList;
	}
	public void setPosList(ArrayList<PosBean> posList) {
		this.posList = posList;
	}
	@Override
	public String toString() {
		
		return "PersonSignBean [flowId=" + flowId + ", thirdOrderNo="
				+ thirdOrderNo + ", accountId=" + accountId + ", sealType="
				+ sealType + ", posList=" + posList + "]";
	}
	
}
