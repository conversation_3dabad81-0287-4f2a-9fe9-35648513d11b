package com.hmit.kernespring.modules.sign.servlet;

import com.hmit.kernespring.modules.sign.bean.*;
import com.hmit.kernespring.modules.sign.config.ProjectConfig;
import com.hmit.kernespring.modules.sign.service.PdfManage;
import com.hmit.kernespring.modules.sign.service.Sign;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileUploadException;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
//import org.apache.log4j.Logger;

import javax.servlet.ServletException;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/***
 *
 * @Package cn.sign.servlet
 * @Description:
 * 完成功能：
 * 1、文件上传到本地
 * 2、将本地文件上传到签章系统
 * 3、本地文件创建文档（将上传的文件作为待签署文件）
 * 4、创建签署流程
 * 5、发起个人用户签署
 * <AUTHOR>
 * @date 2018年11月6日 下午4:34:59
 */
public class UploadFileServlet extends HttpServlet {
	//private static Logger logger = Logger.getLogger(UploadFileServlet.class);

	public void doGet(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		doPost(request, response);

	}
	public void doPost(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		request.setCharacterEncoding("utf-8");
		response.setContentType("text/html;charset=utf-8");
		//上传文件参数配置
		DiskFileItemFactory factory = new DiskFileItemFactory();
		ServletFileUpload upload = new ServletFileUpload(factory);
		String filePath = request.getServletContext().getRealPath("./")+"files"+File.separator+"srcFile.pdf";
		List items = null;
		//获取上传文件
		try {
			items = upload.parseRequest(request);
			}catch (FileUploadException e) {
				e.printStackTrace();
			}
			// 解析request请求
			Iterator iter = items.iterator();
			while (iter.hasNext()) {
				FileItem item = (FileItem) iter.next();
				if (!item.isFormField()) {
					long size = item.getSize(); // 文件的大小，以字节为单位
					String fileName = item.getName();
					//logger.info("上传文件名称:"+fileName);
					File saveFile = new File(filePath); // 定义一个file指向一个具体的文件
					if(!saveFile.exists()){
						saveFile.createNewFile();
					}

					try {
						item.write(saveFile);// 把上传的内容写到一个文件中
						//logger.info(filePath+"文件保存成功");
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			};

		//获取accountId
		String personAccountId = "";

		Cookie[] cookies = request.getCookies();
		for (Cookie cookie : cookies) {

			if (cookie.getName().equals("personAccountId")) {
				personAccountId = cookie.getValue();
			}

		}
		if(personAccountId.equals("")){
			response.getWriter().write("请先创建账户");
			response.getWriter().flush();
			response.getWriter().close();
			return ;
		}

		PdfManage pm= new PdfManage();
		//将文件上传至签章服务器
		UploadUrlBean uploadUrlBean = pm.uploadUrl(filePath);
		int uploadFile = pm.uploadFile(filePath, uploadUrlBean.getUploadUrl());
		System.out.println("uploadFile::"+uploadFile);
		if(uploadFile<200||uploadFile>400){//判断是否文件上传至签章服务器是否成功
			response.getWriter().write("文件上传至签章服务器失败");
			response.getWriter().flush();
			response.getWriter().close();
			return ;
		}

		//本地文件创建文档
		String docId = pm.createDocByFileKey(uploadUrlBean.getFilekey());
		if(docId==null||docId.equals("")){//判断本地文件创建文档是否成功
			response.getWriter().write("本地文件创建文档失败");
			response.getWriter().flush();
			response.getWriter().close();
			return ;
		}
		/*
		 * 单个文档创建签署流程
		 */
		//准备数据
		ProcessBean pb= new ProcessBean();
		pb.setBusinessScene("申请材料证明");
		pb.setInitiatorAccountId(personAccountId);
		pb.setSignPlatform("1");
		//pb.setRedirectUrl("http://www.ctools.top");
		pb.setRedirectUrl("http://127.0.0.1:8080/JavaZwfwPersonDocSignDemo_war_exploded/DocSigndetailServlet");
		pb.setDocId(docId);
		pb.setEncryption(false);
		pb.setPayer(ProjectConfig.payer);

		/*
		 * 创建签署流程
		 */
		Sign sign = new Sign();
		String flowId = sign.signProcess(pb);
		if(flowId==null||flowId.equals("")){
			response.getWriter().write("创建签署流程失败");
			response.getWriter().flush();
			response.getWriter().close();
			return ;
		}

		/*
		 * 发起签署
		 */
		//设置签署类型和位置
		PosBean pos = new PosBean();
		//签署页码
		pos.setPosPage("1");
		//签署坐标X
		pos.setPosX(200.0f);
		//签署坐标Y
		pos.setPosY(300.0f);
		//印章大小，可为空
		pos.setScale(159.0f);
		//签署定位类型，1-关键字定位，2-坐标定位，3-骑缝章
		pos.setSignType(2);
		ArrayList<PosBean> list= new ArrayList<>();
		list.add(pos);
		String signUrl = "";

		//登录的个人用户，使用个人用户签署
		PersonSignBean p = new PersonSignBean();

		p.setAccountId(personAccountId);
		p.setFlowId(flowId);
		p.setSealType("0,1");
		p.setThirdOrderNo("setThirdOrderNosetThirdOrderNo");
		//p.setPosList(list);

		SignResultBean personSign = sign.PersonSign(p,false,false);
		signUrl = personSign.getSignUrl();
		if(signUrl==null||signUrl.equals("")){
			response.getWriter().write("获取签署链接失败！");
			response.getWriter().flush();
			response.getWriter().close();
			return ;
		}

		/**
		 * 将docId、flowId存放在cookie中，正式使用也可存放在数据库中与accountId进行对应
		 */
		Cookie c1 = new Cookie("docId", docId);
		Cookie c2 = new Cookie("flowId", flowId);
		response.addCookie(c1);
		response.addCookie(c2);

		/*
		 * 跳转到签署页面
		 */
		response.setHeader("refresh", "1;URL="+signUrl);

	}

}
