package com.hmit.kernespring.modules.sign.Controller;

import com.google.gson.Gson;
import com.hmit.kernespring.common.utils.HttpRequestUtil;
import com.hmit.kernespring.common.utils.R;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.app.entity.UserEntity;
import com.hmit.kernespring.modules.app.service.UserService;
import com.hmit.kernespring.modules.cjrone.entity.*;
import com.hmit.kernespring.modules.cjrone.service.*;
import com.hmit.kernespring.modules.cjrone_bl.entity.*;
import com.hmit.kernespring.modules.cjrone_bl.service.*;
import com.hmit.kernespring.modules.data_management.service.DataChildEducationSubsidyService;
import com.hmit.kernespring.modules.matter_application.entity.CjroneWelfareMatterApplicationEntity;
import com.hmit.kernespring.modules.matter_application.entity.DisabilityCertificateApplicationEntity;
import com.hmit.kernespring.modules.matter_application.service.CjroneWelfareMatterApplicationService;
import com.hmit.kernespring.modules.matter_application.service.DisabilityCertificateApplicationService;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import com.hmit.kernespring.modules.sign.DB.PersonDb;
import com.hmit.kernespring.modules.sign.bean.*;
import com.hmit.kernespring.modules.sign.config.ProjectConfig;
import com.hmit.kernespring.modules.sign.service.*;
import com.hmit.kernespring.modules.sys.controller.AbstractController;
import com.hmit.kernespring.modules.sys.entity.SysUserEntity;
import com.hmit.kernespring.modules.sys.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.*;

/**
 *
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-08 11:15:30
 */
@RestController
@RequestMapping("cjrone/cjroneSign")
@Slf4j
public class CjroneSignController extends AbstractController {
    @Autowired
    private CjroneNursingSubsidyService cjroneNursingSubsidyService;
    @Autowired
    private CjroneRehabilitationSubsidyService cjroneRehabilitationSubsidyService;
    @Autowired
    private CjroneblLivingSubsidyService cjroneblLivingSubsidyService;
    @Autowired
    private DataChildEducationSubsidyService dataChildEducationSubsidyService;
    @Autowired
    private CjroneWelfareMatterApplicationService cjroneWelfareMatterApplicationService;

    @Autowired
    private CjroneSignatureService cjroneSignatureService;
    @Autowired
    private DisabilityCertificateApplicationService disabilityCertificateApplicationService;
    @Autowired
    private CjroneDocumentService cjroneDocumentService;
    @Autowired
    private CjroneProperties cjroneProperties;
    @Autowired
    private SysOssService sysOssService;
    @Autowired
    private UserService userService;
    @Autowired
    private CjroneEmploymentSubsidyService cjroneEmploymentSubsidyService;
    @Autowired
    private CjroneblLivingAllowanceService cjroneblLivingAllowanceService;
    @Autowired
    private CjroneblZgjbyanglaoService cjroneblZgjbyanglaoService;
    @Autowired
    private CjroneblTemporaryAssistanceService cjroneblTemporaryAssistanceService;
    @Autowired
    private CjroneblRehabilitationSubsidyService cjroneblRehabilitationSubsidyService;
    @Autowired
    private CjroneblBusinessGrantService cjroneblBusinessGrantService;
    @Autowired
    private CjroneblCollegeeduService cjroneblCollegeeduService;
    @Autowired
    private CjroneblChildeduService cjroneblChildeduService;
    @Autowired
    private Love24Service love24Service;

    @Autowired
    private SysUserService sysUserService;


    //PC端电子签章-单文件
    @GetMapping("/signateFile")
    public void signateFile(@RequestParam("applyId") String applyId,@RequestParam("second") String second,@RequestParam("fileName") String fileName,@RequestParam("type") String type,@RequestParam("typeAction") String typeAction, HttpServletRequest request, HttpServletResponse response) throws Exception {
        String filePath = cjroneProperties.getSignaturePath()+fileName;

        //创建personBean对象  设置个人参数
        PersonBean personBean = new PersonBean();
        personBean.setThirdId(getUser() !=null ? getUser().getUserId().toString() : "99999" );
        personBean.setName(getUser() !=null ? getUser().getName() : "" );
        personBean.setIdType(19);//demo中默认设置为中国大陆居民身份证，如遇其他类型，请参考对接文档中证件号对应的类型值。
        personBean.setIdNo(getUser() !=null ? getUser().getIdNo() : "" );
        personBean.setMobile(getUser() !=null ? getUser().getMobile() : "" );
        personBean.setEmail(getUser() !=null ? getUser().getEmail() : "" );
        //创建账户
        Account account = new Account();
        System.out.println("personbean=================================================="+personBean);
        String accountId = account.addPerson(personBean);
        Cookie c11 = new Cookie("personAccountId", accountId);
        response.addCookie(c11);
        if(accountId!=null&&!accountId.equals("")){
        }
        System.out.println(accountId);
        log.error("accountId:{}", accountId);
        //获取accountId
        String personAccountId = accountId;
        /*Cookie[] cookies = request.getCookies();
        for (Cookie cookie : cookies) {

            if (cookie.getName().equals("personAccountId")) {
                personAccountId = cookie.getValue();
            }

        }*/
        System.out.println("personAccountId:"+personAccountId);
        if(personAccountId.equals("")){
            response.getWriter().write("请先创建账户");
            response.getWriter().flush();
            response.getWriter().close();
            return ;
        }

        PdfManage pm= new PdfManage();
        //将文件上传至签章服务器
        System.out.println("filePath=================================================="+filePath);
        UploadUrlBean uploadUrlBean = pm.uploadUrl(filePath);
        int uploadFile = pm.uploadFile(filePath, uploadUrlBean.getUploadUrl());
        System.out.println("uploadFile::"+uploadFile);
        if(uploadFile<200||uploadFile>400){//判断是否文件上传至签章服务器是否成功
            response.getWriter().write("文件上传至签章服务器失败");
            response.getWriter().flush();
            response.getWriter().close();
            return ;
        }
        //本地文件创建文档
        String docId = pm.createDocByFileKey(uploadUrlBean.getFilekey());
        if(docId==null||docId.equals("")){//判断本地文件创建文档是否成功
            response.getWriter().write("本地文件创建文档失败");
            response.getWriter().flush();
            response.getWriter().close();
            return ;
        }else {
            // 将电子签章保存到数据库 以备签章完毕数据回调
            CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
            cjroneSignature.setCreateDate(new Date());
            cjroneSignature.setCreateId(getUserId());
            // cjroneSignature.setType("残疾证申请残疾人手签");
            cjroneSignature.setType(type);
            cjroneSignature.setTypeId(Integer.parseInt(applyId));
            cjroneSignature.setFileName(docId+".pdf");
            cjroneSignature.setAccountId(accountId);
            cjroneSignature.setAccountName(personBean.getName());
            cjroneSignature.setStatus("2");  // 2 表示正在签字的文档
            cjroneSignatureService.save(cjroneSignature);
        }

        String sealId = null;
        /*// 照片印章开始
        //将签字图片上传至签章服务器
        UploadUrlBean uploadUrlBean_tmp = pm.uploadUrl("/Users/<USER>/Desktop/残疾人奉化/sign_xiongyonghong.jpg");
        int uploadFile_tmp = pm.uploadFile("/Users/<USER>/Desktop/残疾人奉化/sign_xiongyonghong.jpg", uploadUrlBean_tmp.getUploadUrl());
        System.out.println("uploadFile_tmp::"+uploadFile_tmp);
        if(uploadFile_tmp<200||uploadFile_tmp>400){//判断是否文件上传至签章服务器是否成功
            response.getWriter().write("文件上传至签章服务器失败");
            response.getWriter().flush();
            response.getWriter().close();
            return ;
        }
        System.out.println("图片印章上传文件filekey:"+uploadUrlBean_tmp.getFilekey());
        // 获取个人图片印章
        ProcessBean pb_tmp= new ProcessBean();
        pb_tmp.setInitiatorAccountId(personAccountId);
        ImageSignManage imageSignManage = new ImageSignManage();
        String sealIdTmp = imageSignManage.persionImageSign(pb_tmp,uploadUrlBean_tmp.getFilekey());
        if (sealIdTmp != null){
            sealId  = sealIdTmp;
        }
        System.out.println("sealId-------->:"+sealId);*/
        /*
         * 单个文档创建签署流程
         */
        //准备数据
        ProcessBean pb= new ProcessBean();
        pb.setBusinessScene("申请材料证明");
        pb.setInitiatorAccountId(personAccountId);
        pb.setSignPlatform("1");
        //pb.setRedirectUrl("http://www.ctools.top");  330902195004077920   ***********
        //pb.setRedirectUrl("http://www.cjrone.com/cjrone/cjrone/cjroneSign/docSigndetail");

        pb.setRedirectUrl("http://localhost:8090/cjrone/cjrone/cjroneSign/docSigndetail");
        //pb.setRedirectUrl("http://localhost:8090/cjrone/cjrone/cjroneSign/docSigndetail");
        pb.setDocId(docId);
        pb.setMultiple(false);
        pb.setEncryption(false);
        pb.setPayer(ProjectConfig.payer);

        /*
         * 创建签署流程
         */
        Sign sign = new Sign();
        String flowId = sign.signProcess(pb);
        if(flowId==null||flowId.equals("")){
            response.getWriter().write("创建签署流程失败");
            response.getWriter().flush();
            response.getWriter().close();
            return ;
        }

        /*
         * 发起签署
         */
        //设置签署类型和位置
        PosBean pos = new PosBean();
        //签署页码
        pos.setPosPage("1");
        //签署坐标X
        pos.setPosX(200.0f);
        //签署坐标Y
        pos.setPosY(300.0f);
        //印章大小，可为空
        pos.setScale(159.0f);

        //签署定位类型，1-关键字定位，2-坐标定位，3-骑缝章
        pos.setSignType(2);
        ArrayList<PosBean> list= new ArrayList<>();
        list.add(pos);
        String signUrl = "";

        //登录的个人用户，使用个人用户签署
        PersonSignBean p = new PersonSignBean();

        p.setAccountId(personAccountId);
        p.setFlowId(flowId);
        // p.setSealType("0,1");
        p.setSealType("0");
        p.setThirdOrderNo("setThirdOrderNosetThirdOrderNo");
        //p.setPosList(list);
        if (sealId != null){
            p.setSealId(sealId);
        }
        System.out.println("aaaaa->"+p.getSealId());
        SignResultBean personSign = sign.PersonSign(p,pb.isMultiple(),false);
        signUrl = personSign.getSignUrl();
        if(signUrl==null||signUrl.equals("")){
            response.getWriter().write("获取签署链接失败！");
            response.getWriter().flush();
            response.getWriter().close();
            return ;
        }

        /**
         * 将docId、flowId存放在cookie中，正式使用也可存放在数据库中与accountId进行对应
         */
        Cookie c1 = new Cookie("docId", docId);
        Cookie c2 = new Cookie("flowId", flowId);
        Cookie c3 = new Cookie("signType", type);  // 1pc端 2app
        Cookie c5 = new Cookie("typeAction", typeAction);  // 申请人手签------
        Cookie c6 = new Cookie("second", second);  // 申请人手签------
        Cookie c4=new Cookie("applyId",applyId);
        response.addCookie(c1);
        response.addCookie(c2);
        response.addCookie(c3);
        response.addCookie(c4);
        response.addCookie(c5);
        response.addCookie(c6);
        System.out.println("bcb--"+typeAction);

        /*
         * 跳转到签署页面
         */
        response.setHeader("refresh", "1;URL="+signUrl);
    }

    //移动端电子签章-单文件-用于残疾证
    @GetMapping("/signateAppFile")
    public void signateAppFile( @RequestParam("applyId") String applyId, @RequestParam("fileName") String fileName, @RequestParam("type") String type, HttpServletRequest request, HttpServletResponse response) throws Exception{

        DisabilityCertificateApplicationEntity applicationEntity = disabilityCertificateApplicationService.getById(applyId);
        UserEntity user=userService.queryByIdnum(applicationEntity.getIdCard());

        String filePath = cjroneProperties.getSignaturePath()+fileName;

        //创建personBean对象  设置个人参数
        PersonBean personBean = new PersonBean();
        personBean.setThirdId(applicationEntity.getId().toString());
        personBean.setName(applicationEntity.getName());
        personBean.setIdType(19);//demo中默认设置为中国大陆居民身份证，如遇其他类型，请参考对接文档中证件号对应的类型值。
        personBean.setIdNo(applicationEntity.getIdCard());
        personBean.setMobile(PersonDb.mobile);
        personBean.setEmail(PersonDb.email);
        //创建账户
        Account account = new Account();
        String accountId = account.addPerson(personBean);
        Cookie c11 = new Cookie("personAccountId", accountId);
        response.addCookie(c11);
        if(accountId!=null&&!accountId.equals("")){
        }
        System.out.println(accountId);
        log.error("accountId:{}", accountId);
        //获取accountId
        String personAccountId = accountId;
        /*Cookie[] cookies = request.getCookies();
        if(cookies!=null){
            for (Cookie cookie : cookies) {
                if (cookie.getName().equals("personAccountId")) {
                    personAccountId = cookie.getValue();
                }
            }
        }*/
        System.out.println("personAccountId:"+personAccountId);
        if(personAccountId.equals("")){
            response.getWriter().write("请先创建账户");
            response.getWriter().flush();
            response.getWriter().close();
            return ;
        }

        PdfManage pm= new PdfManage();
        //将文件上传至签章服务器
        UploadUrlBean uploadUrlBean = pm.uploadUrl(filePath);
        int uploadFile = pm.uploadFile(filePath, uploadUrlBean.getUploadUrl());
        System.out.println("uploadFile::"+uploadFile);
        if(uploadFile<200||uploadFile>400){//判断是否文件上传至签章服务器是否成功
            response.getWriter().write("文件上传至签章服务器失败");
            response.getWriter().flush();
            response.getWriter().close();
            return ;
        }

        //本地文件创建文档
        String docId = pm.createDocByFileKey(uploadUrlBean.getFilekey());
        if(docId==null||docId.equals("")){//判断本地文件创建文档是否成功
            response.getWriter().write("本地文件创建文档失败");
            response.getWriter().flush();
            response.getWriter().close();
            return ;
        }else {
            // 将电子签章保存到数据库 已备签章完毕数据回调
            CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
            cjroneSignature.setCreateDate(new Date());
            cjroneSignature.setCreateId(user.getUserId());
            // cjroneSignature.setType("残疾证申请残疾人手签");
            cjroneSignature.setType(type);
            cjroneSignature.setTypeId(Integer.parseInt(applyId));
            cjroneSignature.setFileName(docId+".pdf");
            cjroneSignature.setAccountId(accountId);
            cjroneSignature.setAccountName(personBean.getName());
            cjroneSignature.setStatus("2");
            cjroneSignatureService.save(cjroneSignature);
        }
        /*
         * 单个文档创建签署流程
         */
        //准备数据
        ProcessBean pb= new ProcessBean();
        pb.setBusinessScene("申请材料证明");
        pb.setInitiatorAccountId(personAccountId);
        pb.setSignPlatform("1");
        //pb.setRedirectUrl("http://www.ctools.top");
        // https://cl.fh.gov.cn/cjrone/cjrone/cjroneSign/docSigndetail
        //pb.setRedirectUrl("http://www.cjrone.com/cjrone/cjrone/cjroneSign/docSigndetail");
        pb.setRedirectUrl("http://localhost:8090/cjrone/cjrone/cjroneSign/docSigndetail");
        pb.setDocId(docId);
        pb.setMultiple(false);
        pb.setEncryption(false);
        pb.setPayer(ProjectConfig.payer);

        /*
         * 创建签署流程
         */
        Sign sign = new Sign();
        String flowId = sign.signProcess(pb);
        if(flowId==null||flowId.equals("")){
            response.getWriter().write("创建签署流程失败");
            response.getWriter().flush();
            response.getWriter().close();
            return ;
        }

        /*
         * 发起签署
         */
        //设置签署类型和位置
        PosBean pos = new PosBean();
        //签署页码
        pos.setPosPage("1");
        //签署坐标X
        pos.setPosX(340.0f);
        //签署坐标Y
        pos.setPosY(140.0f);
        //印章大小，可为空
        pos.setScale(159.0f);
        //签署定位类型，1-关键字定位，2-坐标定位，3-骑缝章
        pos.setSignType(2);
        ArrayList<PosBean> list= new ArrayList<>();
        list.add(pos);
        String signShortUrl = "";

        //登录的个人用户，使用个人用户签署
        PersonSignBean p = new PersonSignBean();

        p.setAccountId(personAccountId);
        p.setFlowId(flowId);
        // p.setSealType("0,1");
        p.setSealType("0");
        p.setThirdOrderNo("setThirdOrderNosetThirdOrderNo");
        p.setPosList(list);

        SignResultBean personSign = sign.PersonSign(p,pb.isMultiple(),true);
        signShortUrl =  personSign.getSignShortUrl();    // 获得短链接            //personSign.getSignUrl();
        if(signShortUrl==null||signShortUrl.equals("")){
            response.getWriter().write("获取签署链接失败！");
            response.getWriter().flush();
            response.getWriter().close();
            return ;
        }

        /**
         * 将docId、flowId存放在cookie中，正式使用也可存放在数据库中与accountId进行对应
         */
        Cookie c1 = new Cookie("docId", docId);
        Cookie c2 = new Cookie("flowId", flowId);
        Cookie c3 = new Cookie("mobileType", "1");  //移动端残疾证申请特有的返回标志 1表示残疾证，2表示福利事项
        Cookie c4=new Cookie("disabilityId",applyId);
        response.addCookie(c1);
        response.addCookie(c2);
        response.addCookie(c3);
        response.addCookie(c4);

        /*
         * 跳转到签署页面
         */
        response.setHeader("refresh", "1;URL="+signShortUrl);

    }

    @GetMapping("/signateMultipleFile")
    public void signateMultipleFile(@RequestParam("applyCardId") String applyCardId,@RequestParam("type") String type,@RequestParam("typeAction") String typeAction, HttpServletRequest request, HttpServletResponse response) throws Exception {
        UserEntity user = new UserEntity();
        if (getUserId() ==null){
            //获得用户id
            user = userService.queryByIdnum(applyCardId);
        } else {
            user.setUserId(getUserId());
        }

        Map<String, Object> tmp_params = new HashMap<>();
        tmp_params.put("id_card",applyCardId);
        tmp_params.put("idCard",applyCardId);
        Map<String,Object> result_map = cjroneWelfareMatterApplicationService.queryMattersIdByMap(tmp_params);
        System.out.println(result_map);
        System.out.println(!"0".equals(result_map.get("isKhbz").toString()));
        System.out.println(!"0".equals(result_map.get("isHlbt").toString()));
        System.out.println(!"0".equals(result_map.get("isShbt").toString()));
        System.out.println(!"0".equals(result_map.get("isjycybt").toString()));
        List<Map<String,Object>> filePathList = new ArrayList<>();
        // List<Map<String,Object>> applyIds = new ArrayList<>();
        if(!"0".equals(result_map.get("isjycybt").toString())){
            Map<String, Object> params = new HashMap<>();
            params.put("filePath",cjroneProperties.getSignaturePath()+"/employment_subsidy_"+applyCardId+".pdf");
            params.put("type","惠残事项就业创业补助");
            params.put("typeId",result_map.get("isjycybt"));
            filePathList.add(params);
        }
        if (!"0".equals(result_map.get("isKhbz").toString())){
            Map<String, Object> params = new HashMap<>();
            params.put("filePath",cjroneProperties.getSignaturePath()+"/rehabilitation_subsidy_"+applyCardId+".pdf");
            params.put("type","惠残事项康复补助");
            params.put("typeId",result_map.get("isKhbz"));
            filePathList.add(params);
        }
        if (!"0".equals(result_map.get("isHlbt").toString())){
            Map<String, Object> params = new HashMap<>();
            params.put("filePath",cjroneProperties.getSignaturePath()+"/nursing_subsidy_"+applyCardId+".pdf");
            params.put("type","惠残事项护理补贴");
            params.put("typeId",result_map.get("isHlbt"));
            filePathList.add(params);
        }
        if (!"0".equals(result_map.get("isShbt").toString())){
            Map<String, Object> params = new HashMap<>();
            params.put("filePath",cjroneProperties.getSignaturePath()+"/living_allowance_"+applyCardId+".pdf");
            params.put("type","惠残事项生活补贴");
            params.put("typeId",result_map.get("isShbt"));
            filePathList.add(params);
        }
        System.out.println("filePathList.size():"+filePathList.size());
        //创建personBean对象  设置个人参数
        PersonBean personBean = new PersonBean();
        personBean.setThirdId(getUser() !=null ? getUser().getUserId().toString() : "99999" );
        personBean.setName(getUser() !=null ? getUser().getName() : "" );
        personBean.setIdType(19);//demo中默认设置为中国大陆居民身份证，如遇其他类型，请参考对接文档中证件号对应的类型值。
        personBean.setIdNo(getUser() !=null ? getUser().getIdNo() : "" );
        personBean.setMobile(getUser() !=null ? getUser().getMobile() : "" );
        personBean.setEmail(getUser() !=null ? getUser().getEmail() : "" );
        //创建账户
        Account account = new Account();
        String accountId = account.addPerson(personBean);
        Cookie c11 = new Cookie("personAccountId", accountId);
        response.addCookie(c11);
        if(accountId!=null&&!accountId.equals("")){
        }
        System.out.println(accountId);
        log.error("accountId:{}", accountId);
        //获取accountId
        String personAccountId = accountId;

        /*Cookie[] cookies = request.getCookies();
        for (Cookie cookie : cookies) {

            if (cookie.getName().equals("personAccountId")) {
                personAccountId = cookie.getValue();
            }

        }*/
        System.out.println("personAccountId:"+personAccountId);
        if(personAccountId.equals("")){
            response.getWriter().write("请先创建账户");
            response.getWriter().flush();
            response.getWriter().close();
            return ;
        }
        List<Map<String,Object>> doc_list = new ArrayList<>();
        filePathList.forEach(item ->{
            PdfManage pm= new PdfManage();
            UploadUrlBean uploadUrlBean = pm.uploadUrl(item.get("filePath").toString());
            int uploadFile = pm.uploadFile(item.get("filePath").toString(), uploadUrlBean.getUploadUrl());
            System.out.println("uploadFile::"+uploadFile);
            if(uploadFile<200||uploadFile>400){//判断是否文件上传至签章服务器是否成功
                logger.error("文件上传至签章服务器失败");
                return ;
            }

            //本地文件创建文档
            String docId = pm.createDocByFileKey(uploadUrlBean.getFilekey());
            if(docId==null||docId.equals("")){//判断本地文件创建文档是否成功
                logger.error("本地文件创建文档失败");
                return ;
            }else {
                // 将电子签章保存到数据库 已备签章完毕数据回调
                CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
                cjroneSignature.setCreateDate(new Date());
                cjroneSignature.setCreateId(getUserId());
                cjroneSignature.setType(item.get("type").toString());
                cjroneSignature.setTypeId(Integer.parseInt(item.get("typeId").toString()));
                cjroneSignature.setFileName(docId+".pdf");
                cjroneSignature.setAccountId(accountId);
                cjroneSignature.setAccountName(personBean.getName());
                cjroneSignature.setStatus("2");
                cjroneSignatureService.save(cjroneSignature);
            }
            Map<String, Object> params = new HashMap<>();
            params.put("docId",docId);
            doc_list.add(params);
        });

        /*
         * 多个文档创建签署流程
         */
        //准备数据
        ProcessBean pb= new ProcessBean();
        pb.setBusinessScene("申请材料证明");
        pb.setInitiatorAccountId(personAccountId);
        pb.setSignPlatform("1");
        //pb.setRedirectUrl("http://www.ctools.top");
        //pb.setRedirectUrl("http://www.cjrone.com/cjrone/cjrone/cjroneSign/docSigndetail");
        pb.setRedirectUrl("http://localhost:8090/cjrone/cjrone/cjroneSign/docSigndetail");
        // pb.setDocId(docId);
        pb.setDocList(doc_list);
        pb.setMultiple(true);
        pb.setEncryption(false);
        pb.setPayer(ProjectConfig.payer);

        /*
         * 创建签署流程
         */
        Sign sign = new Sign();
        String flowId = sign.signProcess(pb);
        if(flowId==null||flowId.equals("")){
            response.getWriter().write("创建签署流程失败");
            response.getWriter().flush();
            response.getWriter().close();
            return ;
        }

        /*
         * 发起签署
         */
        //设置签署类型和位置
        PosBean pos = new PosBean();
        //签署页码
        pos.setPosPage("1");
        //签署坐标X
        pos.setPosX(200.0f);
        //签署坐标Y
        pos.setPosY(300.0f);
        //印章大小，可为空
        pos.setScale(159.0f);
        //签署定位类型，1-关键字定位，2-坐标定位，3-骑缝章
        pos.setSignType(2);
        ArrayList<PosBean> list= new ArrayList<>();
        list.add(pos);
        String signUrl = "";

        //登录的个人用户，使用个人用户签署
        PersonSignBean p = new PersonSignBean();

        p.setAccountId(personAccountId);
        p.setFlowId(flowId);
        // p.setSealType("0,1");
        p.setSealType("0");
        p.setThirdOrderNo("setThirdOrderNosetThirdOrderNo");
        //p.setPosList(list);

        SignResultBean personSign = sign.PersonSign(p,pb.isMultiple(),false);
        signUrl = personSign.getSignUrl();
        if(signUrl==null||signUrl.equals("")){
            response.getWriter().write("获取签署链接失败！");
            response.getWriter().flush();
            response.getWriter().close();
            return ;
        }

        /**
         * 将docId、flowId存放在cookie中，正式使用也可存放在数据库中与accountId进行对应
         */
        Cookie c1 = new Cookie("docList", "docList");
        Cookie c2 = new Cookie("flowId", flowId);
        Cookie c3 = new Cookie("signType", type);  // 1pc端 2app
        Cookie c5 = new Cookie("typeAction", typeAction);  // 1pc端 2app
        //Cookie c4=new Cookie("applyIds",new Gson().toJson(applyIds));
        Cookie c6=new Cookie("signTotalType","Multiple");
        response.addCookie(c1);
        response.addCookie(c2);
        response.addCookie(c3);
        //response.addCookie(c4);
        response.addCookie(c5);
        response.addCookie(c6);

        /*
         * 跳转到签署页面
         */
        response.setHeader("refresh", "1;URL="+signUrl);
    }


    //移动端多文件
    @GetMapping("/signateMobileMultipleFile")
    public void signateMobileMultipleFile(@RequestParam("applyCardId") String applyCardId,@RequestParam("type") String type,@RequestParam("typeAction") String typeAction, HttpServletRequest request, HttpServletResponse response) throws Exception {

        //获得用户id
        UserEntity user=userService.queryByIdnum(applyCardId);

        Map<String, Object> tmp_params = new HashMap<>();
        tmp_params.put("id_card",applyCardId);
        tmp_params.put("idCard",applyCardId);
        Map<String,Object> result_map = cjroneWelfareMatterApplicationService.queryMattersIdByMap(tmp_params);
        System.out.println(result_map);
        System.out.println(!"0".equals(result_map.get("isKhbz").toString()));
        System.out.println(!"0".equals(result_map.get("isHlbt").toString()));
        System.out.println(!"0".equals(result_map.get("isShbt").toString()));
        List<Map<String,Object>> filePathList = new ArrayList<>();
        // List<Map<String,Object>> applyIds = new ArrayList<>();
        if(!"0".equals(result_map.get("isjycybt").toString())){
            Map<String, Object> params = new HashMap<>();
            params.put("filePath",cjroneProperties.getSignaturePath()+"/employment_subsidy_"+applyCardId+".pdf");
            params.put("type","惠残事项就业创业补助");
            params.put("typeId",result_map.get("isjycybt"));
            filePathList.add(params);
        }
        if (!"0".equals(result_map.get("isKhbz").toString())){
            Map<String, Object> params = new HashMap<>();
            params.put("filePath",cjroneProperties.getSignaturePath()+"/rehabilitation_subsidy_"+applyCardId+".pdf");
            params.put("type","惠残事项康复补助");
            params.put("typeId",result_map.get("isKhbz"));
            filePathList.add(params);
        }
        if (!"0".equals(result_map.get("isHlbt").toString())){
            Map<String, Object> params = new HashMap<>();
            params.put("filePath",cjroneProperties.getSignaturePath()+"/nursing_subsidy_"+applyCardId+".pdf");
            params.put("type","惠残事项护理补贴");
            params.put("typeId",result_map.get("isHlbt"));
            filePathList.add(params);
        }
        if (!"0".equals(result_map.get("isShbt").toString())){
            Map<String, Object> params = new HashMap<>();
            params.put("filePath",cjroneProperties.getSignaturePath()+"/living_allowance_"+applyCardId+".pdf");
            params.put("type","惠残事项生活补贴");
            params.put("typeId",result_map.get("isShbt"));
            filePathList.add(params);
        }
        System.out.println("filePathList.size():"+filePathList.size());
        //创建personBean对象  设置个人参数
        PersonBean personBean = new PersonBean();
        personBean.setThirdId(user !=null ? user.getUserId().toString() : "99999" );
        personBean.setName(user !=null ? user.getUsername() : "" );
        personBean.setIdType(19);//demo中默认设置为中国大陆居民身份证，如遇其他类型，请参考对接文档中证件号对应的类型值。
        personBean.setIdNo(user !=null ? user.getPassword() :"" ); //
        personBean.setMobile(user !=null ? user.getMobile() : "" );
        personBean.setEmail("" ); //user !=null ? user.getEmail() :
        //创建账户
        Account account = new Account();
        String accountId = account.addPerson(personBean);
        Cookie c11 = new Cookie("personAccountId", accountId);
        response.addCookie(c11);
        if(accountId!=null&&!accountId.equals("")){
        }
        System.out.println(accountId);
        log.error("accountId:{}", accountId);
        //获取accountId
        String personAccountId = accountId;

        /*Cookie[] cookies = request.getCookies();
        for (Cookie cookie : cookies) {

            if (cookie.getName().equals("personAccountId")) {
                personAccountId = cookie.getValue();
            }

        }*/
        System.out.println("personAccountId:"+personAccountId);
        if(personAccountId.equals("")){
            response.getWriter().write("请先创建账户");
            response.getWriter().flush();
            response.getWriter().close();
            return ;
        }
        List<Map<String,Object>> doc_list = new ArrayList<>();
        filePathList.forEach(item ->{
            PdfManage pm= new PdfManage();
            UploadUrlBean uploadUrlBean = pm.uploadUrl(item.get("filePath").toString());
            int uploadFile = pm.uploadFile(item.get("filePath").toString(), uploadUrlBean.getUploadUrl());
            System.out.println("uploadFile::"+uploadFile);
            if(uploadFile<200||uploadFile>400){//判断是否文件上传至签章服务器是否成功
                logger.error("文件上传至签章服务器失败");
                return ;
            }

            //本地文件创建文档
            String docId = pm.createDocByFileKey(uploadUrlBean.getFilekey());
            if(docId==null||docId.equals("")){//判断本地文件创建文档是否成功
                logger.error("本地文件创建文档失败");
                return ;
            }{
                // 将电子签章保存到数据库 已备签章完毕数据回调
                CjroneSignatureEntity cjroneSignature = new CjroneSignatureEntity();
                cjroneSignature.setCreateDate(new Date());
                cjroneSignature.setCreateId(user.getUserId());
                cjroneSignature.setType(item.get("type").toString());
                cjroneSignature.setTypeId(Integer.parseInt(item.get("typeId").toString()));
                cjroneSignature.setFileName(docId+".pdf");
                cjroneSignature.setAccountId(accountId);
                cjroneSignature.setAccountName(personBean.getName());
                cjroneSignature.setStatus("2");
                cjroneSignatureService.save(cjroneSignature);
            }
            Map<String, Object> params = new HashMap<>();
            params.put("docId",docId);
            doc_list.add(params);
        });

        /*
         * 多个文档创建签署流程
         */
        //准备数据
        ProcessBean pb= new ProcessBean();
        pb.setBusinessScene("申请材料证明");
        pb.setInitiatorAccountId(personAccountId);
        pb.setSignPlatform("1");
        //pb.setRedirectUrl("http://www.ctools.top");
        //pb.setRedirectUrl("http://www.cjrone.com/cjrone/cjrone/cjroneSign/docSigndetail");
        pb.setRedirectUrl("http://localhost:8090/cjrone/cjrone/cjroneSign/docSigndetail");
        // pb.setDocId(docId);
        pb.setDocList(doc_list);
        pb.setMultiple(true);
        pb.setEncryption(false);
        pb.setPayer(ProjectConfig.payer);

        /*
         * 创建签署流程
         */
        Sign sign = new Sign();
        String flowId = sign.signProcess(pb);
        if(flowId==null||flowId.equals("")){
            response.getWriter().write("创建签署流程失败");
            response.getWriter().flush();
            response.getWriter().close();
            return ;
        }

        /*
         * 发起签署
         */
        //设置签署类型和位置
        PosBean pos = new PosBean();
        //签署页码
        pos.setPosPage("1");
        //签署坐标X
        pos.setPosX(200.0f);
        //签署坐标Y
        pos.setPosY(300.0f);
        //印章大小，可为空
        pos.setScale(159.0f);
        //签署定位类型，1-关键字定位，2-坐标定位，3-骑缝章
        pos.setSignType(2);
        ArrayList<PosBean> list= new ArrayList<>();
        list.add(pos);
        String signShortUrl = "";

        //登录的个人用户，使用个人用户签署
        PersonSignBean p = new PersonSignBean();

        p.setAccountId(personAccountId);
        p.setFlowId(flowId);
        // p.setSealType("0,1");
        p.setSealType("0");
        p.setThirdOrderNo("setThirdOrderNosetThirdOrderNo");
        //p.setPosList(list);

        SignResultBean personSign = sign.PersonSign(p,pb.isMultiple(),true);
        signShortUrl = personSign.getSignShortUrl();
        if(signShortUrl==null||signShortUrl.equals("")){
            response.getWriter().write("获取签署链接失败！");
            response.getWriter().flush();
            response.getWriter().close();
            return ;
        }

        /**
         * 将docId、flowId存放在cookie中，正式使用也可存放在数据库中与accountId进行对应
         */
        Cookie c1 = new Cookie("docList", "docList");
        Cookie c2 = new Cookie("flowId", flowId);
        Cookie c3 = new Cookie("signType", type);  // 1pc端 2app
        Cookie c5 = new Cookie("typeAction", typeAction);  // 1pc端 2app
        //Cookie c4=new Cookie("applyIds",new Gson().toJson(applyIds));
        Cookie c6=new Cookie("signTotalType","Multiple");
        Cookie c4 = new Cookie("mobileType", "2");  //移动端残疾证申请特有的返回标志 1表示残疾证，2表示福利事项
        response.addCookie(c1);
        response.addCookie(c2);
        response.addCookie(c3);
        response.addCookie(c4);
        response.addCookie(c5);
        response.addCookie(c6);

        /*
         * 跳转到签署页面
         */
        response.setHeader("refresh", "1;URL="+signShortUrl);
    }



    @GetMapping("/signateMultipleFile1")
    public void signateMultipleFile1(@RequestParam("applyCardId") String applyCardId, HttpServletRequest request, HttpServletResponse response) throws Exception {
        List<String> filePathList = new ArrayList<>();
        filePathList.add("/Users/<USER>/resources/src.pdf");
        filePathList.add("/Users/<USER>/resources/srcFile.pdf");
        //创建personBean对象  设置个人参数
        PersonBean personBean = new PersonBean();
        personBean.setThirdId(PersonDb.userid);
        personBean.setName(PersonDb.username);
        personBean.setIdType(19);//demo中默认设置为中国大陆居民身份证，如遇其他类型，请参考对接文档中证件号对应的类型值。
        personBean.setIdNo(PersonDb.idnum);
        personBean.setMobile(PersonDb.mobile);
        personBean.setEmail(PersonDb.email);
        //创建账户
        Account account = new Account();
        String accountId = account.addPerson(personBean);
        Cookie c11 = new Cookie("personAccountId", accountId);
        response.addCookie(c11);
        if(accountId!=null&&!accountId.equals("")){
        }
        System.out.println(accountId);
        log.error("accountId:{}", accountId);
        //获取accountId
        String personAccountId = accountId;

        /*Cookie[] cookies = request.getCookies();
        for (Cookie cookie : cookies) {

            if (cookie.getName().equals("personAccountId")) {
                personAccountId = cookie.getValue();
            }

        }*/
        System.out.println("personAccountId:"+personAccountId);
        if(personAccountId.equals("")){
            response.getWriter().write("请先创建账户");
            response.getWriter().flush();
            response.getWriter().close();
            return ;
        }
        List<Map<String,Object>> doc_list = new ArrayList<>();
        filePathList.forEach(filePath ->{
            PdfManage pm= new PdfManage();
            UploadUrlBean uploadUrlBean = pm.uploadUrl(filePath);
            int uploadFile = pm.uploadFile(filePath, uploadUrlBean.getUploadUrl());
            System.out.println("uploadFile::"+uploadFile);
            if(uploadFile<200||uploadFile>400){//判断是否文件上传至签章服务器是否成功
                logger.error("文件上传至签章服务器失败");
                return ;
            }

            //本地文件创建文档
            String docId = pm.createDocByFileKey(uploadUrlBean.getFilekey());
            if(docId==null||docId.equals("")){//判断本地文件创建文档是否成功
                logger.error("本地文件创建文档失败");
                return ;
            }
            Map<String, Object> params = new HashMap<>();
            params.put("docId",docId);
            doc_list.add(params);
        });

        /*
         * 多个文档创建签署流程
         */
        //准备数据
        ProcessBean pb= new ProcessBean();
        pb.setBusinessScene("申请材料证明");
        pb.setInitiatorAccountId(personAccountId);
        pb.setSignPlatform("1");
        //pb.setRedirectUrl("http://www.ctools.top");
        //pb.setRedirectUrl("http://www.cjrone.com/cjrone/cjrone/cjroneSign/docSigndetail");
        pb.setRedirectUrl("http://localhost:8090/cjrone/cjrone/cjroneSign/docSigndetail");
        // pb.setDocId(docId);
        pb.setDocList(doc_list);
        pb.setMultiple(true);
        pb.setEncryption(false);
        pb.setPayer(ProjectConfig.payer);

        /*
         * 创建签署流程
         */
        Sign sign = new Sign();
        String flowId = sign.signProcess(pb);
        if(flowId==null||flowId.equals("")){
            response.getWriter().write("创建签署流程失败");
            response.getWriter().flush();
            response.getWriter().close();
            return ;
        }

        /*
         * 发起签署
         */
        //设置签署类型和位置
        PosBean pos = new PosBean();
        //签署页码
        pos.setPosPage("1");
        //签署坐标X
        pos.setPosX(200.0f);
        //签署坐标Y
        pos.setPosY(300.0f);
        //印章大小，可为空
        pos.setScale(159.0f);
        //签署定位类型，1-关键字定位，2-坐标定位，3-骑缝章
        pos.setSignType(2);
        ArrayList<PosBean> list= new ArrayList<>();
        list.add(pos);
        String signUrl = "";

        //登录的个人用户，使用个人用户签署
        PersonSignBean p = new PersonSignBean();

        p.setAccountId(personAccountId);
        p.setFlowId(flowId);
        // p.setSealType("0,1");
        p.setSealType("0");
        p.setThirdOrderNo("setThirdOrderNosetThirdOrderNo");
        //p.setPosList(list);

        SignResultBean personSign = sign.PersonSign(p,pb.isMultiple(),false);
        signUrl = personSign.getSignUrl();
        if(signUrl==null||signUrl.equals("")){
            response.getWriter().write("获取签署链接失败！");
            response.getWriter().flush();
            response.getWriter().close();
            return ;
        }

        /**
         * 将docId、flowId存放在cookie中，正式使用也可存放在数据库中与accountId进行对应
         */
        Cookie c1 = new Cookie("docList", "docList");
        Cookie c2 = new Cookie("flowId", flowId);
        response.addCookie(c1);
        response.addCookie(c2);

        /*
         * 跳转到签署页面
         */
        response.setHeader("refresh", "1;URL="+signUrl);
    }

    @GetMapping("/docSigndetail")
    public void docSigndetail(HttpServletRequest request, HttpServletResponse response) throws Exception {
        request.setCharacterEncoding("utf-8");
        response.setContentType("text/html;charset=utf-8");
        List<Map<String,Object>> file_list = new ArrayList<>();

        String docId = "";
        String flowId = "";


        System.out.println("返回的response参数：" + new Gson().toJson(request.getCookies()));
        logger.info("返回的response参数：" + request.getCookies().toString());

        // 从请求的cookie中获取docId和flowId
        String mobileType = "0";  //移动端的跳转标志
        String disabilityId = "0";
        String applyId = null;
        String signType = null;
        String typeAction = null;
        String second = null;
        String signTotalType = null;
        String applyIds = null;
        Cookie[] cookies = request.getCookies();
        for (Cookie cookie : cookies) {
            if (cookie.getName().equals("docId")) {
                docId = cookie.getValue();
            }
            if (cookie.getName().equals("flowId")) {
                flowId = cookie.getValue();
            }
            if (cookie.getName().equals("mobileType")) {
                mobileType = cookie.getValue();
            }
            if (cookie.getName().equals("disabilityId")) {
                disabilityId = cookie.getValue();
            }
            if (cookie.getName().equals("signType")) {
                signType = cookie.getValue();
            }
            if (cookie.getName().equals("applyId")) {
                applyId = cookie.getValue();
            }
            if (cookie.getName().equals("typeAction")) {
                typeAction = cookie.getValue();
            }
            if (cookie.getName().equals("second")) {
                second = cookie.getValue();
            }
            if (cookie.getName().equals("signTotalType")) {
                signTotalType = cookie.getValue();
            }
            if (cookie.getName().equals("applyIds")) {
                applyIds = cookie.getValue();
            }
        }

        System.out.println("bcb2--"+typeAction);

        if (!(flowId.equals("")) && flowId != null) {
            /*
             * 1、查询签署详情
             *
             */
            DocSignManage docSign = new DocSignManage();
            JSONObject detail = docSign.detail(flowId);
            int flowStatus = detail.getInt("flowStatus");
            String docName = detail.getString("docName");

            JSONArray signDetailArray = detail.getJSONArray("signDetailList");
            JSONObject signDetail = (JSONObject) signDetailArray.get(0);
            int signStatus = signDetail.getInt("signStatus");

            //判断签署状态
            int archiveProcess = -1;//用于标记流程是否归档
            if (signStatus == 2 && flowStatus == 1) {
                //签署完成，但未归档流程
                logger.info("文档签署完成但未归档<br/>");
                /**
                 * 2、流程归档
                 */
                archiveProcess = docSign.archiveProcess(flowId);
            } else if (signStatus == 2 && flowStatus == 2) {
                //签署完成，且流程已归档
                archiveProcess = 0;//将流程状态更改为已归档
                logger.info("文档签署完成且已归档<br/>");
            } else {
                //未签署完成
                logger.info("文档未签署完成<br/>");
            }

            //判断文档归档状态
            JSONObject downJson = null;
            if (archiveProcess == 0) {
                logger.info("文档已归档<br/>");
                /**
                 * 3、获取下载连接
                 */
                downJson = docSign.downUrl(flowId);
            } else {
                logger.info("文档未归档<br/>");
            }

            //判断获取下载连接是否成功
            if (downJson.getInt("errCode") == 0) {
                logger.info("获取文档下载地址成功");
                //解析返回的数据
                JSONArray jsonArray = downJson.getJSONArray("data");
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject downjson = (JSONObject) jsonArray.get(i);
                    String docid = downjson.getString("docId");
                    String docUrl = downjson.getString("docUrl");
                    // HttpRequestUtil.downloadPicture(docUrl,"/Users/<USER>/resources/cjrone/signature/",docid + ".pdf");
                    Map<String, Object> params = new HashMap<>();
                    params.put("file_name", docid + ".pdf");
                    params.put("status", "2");
                    List<CjroneSignatureEntity> list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(params);
                    String save_path = null;
                    String url_path = null;
                    Map<String, Object> params_result = new HashMap<>();
                    if (list.size() > 0) {
                        CjroneSignatureEntity cjroneSignatureEntity = list.get(0);
                        params_result.put("matterId",cjroneSignatureEntity.getTypeId());
                        params_result.put("matterName",cjroneSignatureEntity.getType());
                       if ("惠残事项护理补贴".equals(cjroneSignatureEntity.getType())) {
                            Map<String, Object> tmp_params = new HashMap<>();
                            tmp_params.put("type", "惠残事项护理补贴");
                            tmp_params.put("status", "1");
                            tmp_params.put("type_id", cjroneSignatureEntity.getTypeId());
                            List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
                            alive_list.forEach(item -> {
                                item.setStatus("0");
                            });
                            if (alive_list.size() > 0) {
                                cjroneSignatureService.updateBatchById(alive_list);
                            }
                            CjroneNursingSubsidyEntity cjroneNursingSubsidy = cjroneNursingSubsidyService.getById(cjroneSignatureEntity.getTypeId());
                            if (cjroneNursingSubsidy != null) {
                                save_path = cjroneProperties.getSignaturePath() + cjroneNursingSubsidy.getIdCard() + "/惠残事项护理补贴/";
                                url_path = "/" + cjroneNursingSubsidy.getIdCard() + "/" + URLEncoder.encode("惠残事项护理补贴", "UTF-8") + "/";
                            } else {
                                save_path = cjroneProperties.getSignaturePath() + cjroneSignatureEntity.getTypeId() + "/hand/";
                                url_path = "/" + cjroneSignatureEntity.getTypeId() + "/hand/";
                            }
                        } else if ("惠残事项生活补贴".equals(cjroneSignatureEntity.getType())) {
                            Map<String, Object> tmp_params = new HashMap<>();
                            tmp_params.put("type", "惠残事项生活补贴");
                            tmp_params.put("status", "1");
                            tmp_params.put("type_id", cjroneSignatureEntity.getTypeId());
                            List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
                            alive_list.forEach(item -> {
                                item.setStatus("0");
                            });
                            if (alive_list.size() > 0) {
                                cjroneSignatureService.updateBatchById(alive_list);
                            }
                            CjroneblLivingSubsidyEntity cjroneblLivingSubsidyEntity = cjroneblLivingSubsidyService.getById(cjroneSignatureEntity.getTypeId());
                            if (cjroneblLivingSubsidyEntity != null) {
                                save_path = cjroneProperties.getSignaturePath() + cjroneblLivingSubsidyEntity.getIdCard() + "/惠残事项生活补贴/";
                                url_path = "/" + cjroneblLivingSubsidyEntity.getIdCard() + "/" + URLEncoder.encode("惠残事项生活补贴", "UTF-8") + "/";
                            } else {
                                save_path = cjroneProperties.getSignaturePath() + cjroneSignatureEntity.getTypeId() + "/hand/";
                                url_path = "/" + cjroneSignatureEntity.getTypeId() + "/hand/";
                            }
                        }else if ("惠残事项生活补助金".equals(cjroneSignatureEntity.getType())) {
                            Map<String, Object> tmp_params = new HashMap<>();
                            tmp_params.put("type", "惠残事项生活补助金");
                            tmp_params.put("status", "1");
                            tmp_params.put("type_id", cjroneSignatureEntity.getTypeId());
                            List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
                            alive_list.forEach(item -> {
                                item.setStatus("0");
                            });
                            if (alive_list.size() > 0) {
                                cjroneSignatureService.updateBatchById(alive_list);
                            }
                            CjroneblLivingAllowanceEntity cjroneblLivingAllowanceEntity=cjroneblLivingAllowanceService.getById(cjroneSignatureEntity.getTypeId());
                            if (cjroneblLivingAllowanceEntity != null) {
                                save_path = cjroneProperties.getSignaturePath() + cjroneblLivingAllowanceEntity.getIdCard() + "/惠残事项生活补助金/";
                                url_path = "/" + cjroneblLivingAllowanceEntity.getIdCard() + "/" + URLEncoder.encode("惠残事项生活补助金", "UTF-8") + "/";
                            } else {
                                save_path = cjroneProperties.getSignaturePath() + cjroneSignatureEntity.getTypeId() + "/hand/";
                                url_path = "/" + cjroneSignatureEntity.getTypeId() + "/hand/";
                            }
                        }else if("惠残事项职工基本养老保险补助".equals(cjroneSignatureEntity.getType())){
                            Map<String, Object> tmp_params = new HashMap<>();
                            tmp_params.put("type", "惠残事项职工基本养老保险补助");
                            tmp_params.put("status", "1");
                            tmp_params.put("type_id", cjroneSignatureEntity.getTypeId());
                            List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
                            alive_list.forEach(item -> {
                                item.setStatus("0");
                            });
                            if (alive_list.size() > 0) {
                                cjroneSignatureService.updateBatchById(alive_list);
                            }
                            CjroneblZgjbyanglaoEntity cjroneblZgjbyanglaoEntity=cjroneblZgjbyanglaoService.getById(cjroneSignatureEntity.getTypeId());
                            if (cjroneblZgjbyanglaoEntity != null) {
                                save_path = cjroneProperties.getSignaturePath() + cjroneblZgjbyanglaoEntity.getDisableId() + "/惠残事项职工基本养老保险补助/";
                                url_path = "/" + cjroneblZgjbyanglaoEntity.getDisableId() + "/" + URLEncoder.encode("惠残事项职工基本养老保险补助", "UTF-8") + "/";
                            } else {
                                save_path = cjroneProperties.getSignaturePath() + cjroneSignatureEntity.getTypeId() + "/hand/";
                                url_path = "/" + cjroneSignatureEntity.getTypeId() + "/hand/";
                            }
                        }else if("惠残事项城乡居民养老保险补助".equals(cjroneSignatureEntity.getType())){
                            Map<String, Object> tmp_params = new HashMap<>();
                            tmp_params.put("type", "惠残事项城乡居民养老保险补助");
                            tmp_params.put("status", "1");
                            tmp_params.put("type_id", cjroneSignatureEntity.getTypeId());
                            List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
                            alive_list.forEach(item -> {
                                item.setStatus("0");
                            });
                            if (alive_list.size() > 0) {
                                cjroneSignatureService.updateBatchById(alive_list);
                            }
                            CjroneblZgjbyanglaoEntity cjroneblZgjbyanglaoEntity=cjroneblZgjbyanglaoService.getById(cjroneSignatureEntity.getTypeId());
                            if (cjroneblZgjbyanglaoEntity != null) {
                                save_path = cjroneProperties.getSignaturePath() + cjroneblZgjbyanglaoEntity.getDisableId() + "/惠残事项城乡居民养老保险补助/";
                                url_path = "/" + cjroneblZgjbyanglaoEntity.getDisableId() + "/" + URLEncoder.encode("惠残事项城乡居民养老保险补助", "UTF-8") + "/";
                            } else {
                                save_path = cjroneProperties.getSignaturePath() + cjroneSignatureEntity.getTypeId() + "/hand/";
                                url_path = "/" + cjroneSignatureEntity.getTypeId() + "/hand/";
                            }
                        } else if("惠残事项职工基本医疗保险补助".equals(cjroneSignatureEntity.getType())){
                            Map<String, Object> tmp_params = new HashMap<>();
                            tmp_params.put("type", "惠残事项职工基本医疗保险补助");
                            tmp_params.put("status", "1");
                            tmp_params.put("type_id", cjroneSignatureEntity.getTypeId());
                            List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
                            alive_list.forEach(item -> {
                                item.setStatus("0");
                            });
                            if (alive_list.size() > 0) {
                                cjroneSignatureService.updateBatchById(alive_list);
                            }
                            CjroneblZgjbyanglaoEntity cjroneblZgjbyanglaoEntity=cjroneblZgjbyanglaoService.getById(cjroneSignatureEntity.getTypeId());
                            if (cjroneblZgjbyanglaoEntity != null) {
                                save_path = cjroneProperties.getSignaturePath() + cjroneblZgjbyanglaoEntity.getDisableId() + "/惠残事项职工基本医疗保险补助/";
                                url_path = "/" + cjroneblZgjbyanglaoEntity.getDisableId() + "/" + URLEncoder.encode("惠残事项职工基本医疗保险补助", "UTF-8") + "/";
                            } else {
                                save_path = cjroneProperties.getSignaturePath() + cjroneSignatureEntity.getTypeId() + "/hand/";
                                url_path = "/" + cjroneSignatureEntity.getTypeId() + "/hand/";
                            }
                        }else if("惠残事项城乡基本医疗保险补助".equals(cjroneSignatureEntity.getType())){
                            Map<String, Object> tmp_params = new HashMap<>();
                            tmp_params.put("type", "惠残事项城乡基本医疗保险补助");
                            tmp_params.put("status", "1");
                            tmp_params.put("type_id", cjroneSignatureEntity.getTypeId());
                            List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
                            alive_list.forEach(item -> {
                                item.setStatus("0");
                            });
                            if (alive_list.size() > 0) {
                                cjroneSignatureService.updateBatchById(alive_list);
                            }
                            CjroneblZgjbyanglaoEntity cjroneblZgjbyanglaoEntity=cjroneblZgjbyanglaoService.getById(cjroneSignatureEntity.getTypeId());
                            if (cjroneblZgjbyanglaoEntity != null) {
                                save_path = cjroneProperties.getSignaturePath() + cjroneblZgjbyanglaoEntity.getDisableId() + "/惠残事项城乡基本医疗保险补助/";
                                url_path = "/" + cjroneblZgjbyanglaoEntity.getDisableId() + "/" + URLEncoder.encode("惠残事项城乡基本医疗保险补助", "UTF-8") + "/";
                            } else {
                                save_path = cjroneProperties.getSignaturePath() + cjroneSignatureEntity.getTypeId() + "/hand/";
                                url_path = "/" + cjroneSignatureEntity.getTypeId() + "/hand/";
                            }
                        }
                        else if("惠残事项残疾人临时救助".equals(cjroneSignatureEntity.getType())){
                            Map<String, Object> tmp_params = new HashMap<>();
                            tmp_params.put("type", "惠残事项残疾人临时救助");
                            tmp_params.put("status", "1");
                            tmp_params.put("type_id", cjroneSignatureEntity.getTypeId());
                            List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
                            alive_list.forEach(item -> {
                                item.setStatus("0");
                            });
                            if (alive_list.size() > 0) {
                                cjroneSignatureService.updateBatchById(alive_list);
                            }
                            CjroneblTemporaryAssistanceEntity cjroneblTemporaryAssistanceEntity=cjroneblTemporaryAssistanceService.getById(cjroneSignatureEntity.getTypeId());
                            if (cjroneblTemporaryAssistanceEntity != null) {
                                save_path = cjroneProperties.getSignaturePath() + cjroneblTemporaryAssistanceEntity.getIdCard() + "/惠残事项残疾人临时救助/";
                                url_path = "/" + cjroneblTemporaryAssistanceEntity.getIdCard() + "/" + URLEncoder.encode("惠残事项残疾人临时救助", "UTF-8") + "/";
                            } else {
                                save_path = cjroneProperties.getSignaturePath() + cjroneSignatureEntity.getTypeId() + "/hand/";
                                url_path = "/" + cjroneSignatureEntity.getTypeId() + "/hand/";
                            }
                        }
                        else if("惠残事项康复补助".equals(cjroneSignatureEntity.getType())){
                            Map<String, Object> tmp_params = new HashMap<>();
                            tmp_params.put("type", "惠残事项康复补助");
                            tmp_params.put("status", "1");
                            tmp_params.put("type_id", cjroneSignatureEntity.getTypeId());
                            List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
                            alive_list.forEach(item -> {
                                item.setStatus("0");
                            });
                            if (alive_list.size() > 0) {
                                cjroneSignatureService.updateBatchById(alive_list);
                            }
                            CjroneblRehabilitationSubsidyEntity cjroneblRehabilitationSubsidyEntity=cjroneblRehabilitationSubsidyService.getById(cjroneSignatureEntity.getTypeId());
                            if (cjroneblRehabilitationSubsidyEntity != null) {
                                save_path = cjroneProperties.getSignaturePath() + cjroneblRehabilitationSubsidyEntity.getIdCard() + "/惠残事项康复补助/";
                                url_path = "/" + cjroneblRehabilitationSubsidyEntity.getIdCard() + "/" + URLEncoder.encode("惠残事项康复补助", "UTF-8") + "/";
                            } else {
                                save_path = cjroneProperties.getSignaturePath() + cjroneSignatureEntity.getTypeId() + "/hand/";
                                url_path = "/" + cjroneSignatureEntity.getTypeId() + "/hand/";
                            }
                        }
                        else if("惠残事项创业补助".equals(cjroneSignatureEntity.getType())){
                            Map<String, Object> tmp_params = new HashMap<>();
                            tmp_params.put("type", "惠残事项创业补助");
                            tmp_params.put("status", "1");
                            tmp_params.put("type_id", cjroneSignatureEntity.getTypeId());
                            List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
                            alive_list.forEach(item -> {
                                item.setStatus("0");
                            });
                            if (alive_list.size() > 0) {
                                cjroneSignatureService.updateBatchById(alive_list);
                            }
                            CjroneblBusinessGrantEntity cjroneblBusinessGrantEntity=cjroneblBusinessGrantService.getById(cjroneSignatureEntity.getTypeId());
                            if (cjroneblBusinessGrantEntity != null) {
                                save_path = cjroneProperties.getSignaturePath() + cjroneblBusinessGrantEntity.getDisableId() + "/惠残事项创业补助/";
                                url_path = "/" + cjroneblBusinessGrantEntity.getDisableId() + "/" + URLEncoder.encode("惠残事项创业补助", "UTF-8") + "/";
                            } else {
                                save_path = cjroneProperties.getSignaturePath() + cjroneSignatureEntity.getTypeId() + "/hand/";
                                url_path = "/" + cjroneSignatureEntity.getTypeId() + "/hand/";
                            }
                        }
                       else if("惠残事项大学生补助".equals(cjroneSignatureEntity.getType())){
                           Map<String, Object> tmp_params = new HashMap<>();
                           tmp_params.put("type", "惠残事项大学生补助");
                           tmp_params.put("status", "1");
                           tmp_params.put("type_id", cjroneSignatureEntity.getTypeId());
                           List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
                           alive_list.forEach(item -> {
                               item.setStatus("0");
                           });
                           if (alive_list.size() > 0) {
                               cjroneSignatureService.updateBatchById(alive_list);
                           }
                           CjroneblCollegeeduEntity cjroneblCollegeeduEntity=cjroneblCollegeeduService.getById(cjroneSignatureEntity.getTypeId());
                           if (cjroneblCollegeeduEntity != null) {
                               save_path = cjroneProperties.getSignaturePath() + cjroneblCollegeeduEntity.getDisableId() + "/惠残事项大学生补助/";
                               url_path = "/" + cjroneblCollegeeduEntity.getDisableId() + "/" + URLEncoder.encode("惠残事项大学生补助", "UTF-8") + "/";
                           } else {
                               save_path = cjroneProperties.getSignaturePath() + cjroneSignatureEntity.getTypeId() + "/hand/";
                               url_path = "/" + cjroneSignatureEntity.getTypeId() + "/hand/";
                           }
                       }
                       else if("残疾人子女教育补贴".equals(cjroneSignatureEntity.getType())){
                           Map<String, Object> tmp_params = new HashMap<>();
                           tmp_params.put("type", "惠残事项残疾人子女教育补贴");
                           tmp_params.put("status", "1");
                           tmp_params.put("type_id", cjroneSignatureEntity.getTypeId());
                           List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
                           alive_list.forEach(item -> {
                               item.setStatus("0");
                           });
                           if (alive_list.size() > 0) {
                               cjroneSignatureService.updateBatchById(alive_list);
                           }

                           CjroneblChildeduEntity cjroneblChildeduEntity=cjroneblChildeduService.getById(cjroneSignatureEntity.getTypeId());
                           if (cjroneblChildeduEntity != null) {
                               save_path = cjroneProperties.getSignaturePath() + cjroneblChildeduEntity.getDisableId() + "/惠残事项残疾人子女教育补贴/";
                               url_path = "/" + cjroneblChildeduEntity.getDisableId() + "/" + URLEncoder.encode("惠残事项残疾人子女教育补贴", "UTF-8") + "/";
                           } else {
                               save_path = cjroneProperties.getSignaturePath() + cjroneSignatureEntity.getTypeId() + "/hand/";
                               url_path = "/" + cjroneSignatureEntity.getTypeId() + "/hand/";
                           }
                       }
                       else if("惠残事项医疗救助".equals(cjroneSignatureEntity.getType())){
                           Map<String, Object> tmp_params = new HashMap<>();
                           tmp_params.put("type", "惠残事项医疗救助");
                           tmp_params.put("status", "1");
                           tmp_params.put("type_id", cjroneSignatureEntity.getTypeId());
                           List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
                           alive_list.forEach(item -> {
                               item.setStatus("0");
                           });
                           if (alive_list.size() > 0) {
                               cjroneSignatureService.updateBatchById(alive_list);
                           }
                           CjroneblBusinessGrantEntity cjroneblBusinessGrantEntity=cjroneblBusinessGrantService.getById(cjroneSignatureEntity.getTypeId());
                           if (cjroneblBusinessGrantEntity != null) {
                               save_path = cjroneProperties.getSignaturePath() + cjroneblBusinessGrantEntity.getDisableId() + "/惠残事项医疗救助/";
                               url_path = "/" + cjroneblBusinessGrantEntity.getDisableId() + "/" + URLEncoder.encode("惠残事项医疗救助", "UTF-8") + "/";
                           } else {
                               save_path = cjroneProperties.getSignaturePath() + cjroneSignatureEntity.getTypeId() + "/hand/";
                               url_path = "/" + cjroneSignatureEntity.getTypeId() + "/hand/";
                           }
                       }
                       else if("惠残事项住院补助".equals(cjroneSignatureEntity.getType())){
                           Map<String, Object> tmp_params = new HashMap<>();
                           tmp_params.put("type", "惠残事项住院补助");
                           tmp_params.put("status", "1");
                           tmp_params.put("type_id", cjroneSignatureEntity.getTypeId());
                           List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
                           alive_list.forEach(item -> {
                               item.setStatus("0");
                           });
                           if (alive_list.size() > 0) {
                               cjroneSignatureService.updateBatchById(alive_list);
                           }
                           CjroneblBusinessGrantEntity cjroneblBusinessGrantEntity=cjroneblBusinessGrantService.getById(cjroneSignatureEntity.getTypeId());
                           if (cjroneblBusinessGrantEntity != null) {
                               save_path = cjroneProperties.getSignaturePath() + cjroneblBusinessGrantEntity.getDisableId() + "/惠残事项住院补助/";
                               url_path = "/" + cjroneblBusinessGrantEntity.getDisableId() + "/" + URLEncoder.encode("惠残事项住院补助", "UTF-8") + "/";
                           } else {
                               save_path = cjroneProperties.getSignaturePath() + cjroneSignatureEntity.getTypeId() + "/hand/";
                               url_path = "/" + cjroneSignatureEntity.getTypeId() + "/hand/";
                           }
                       }
                       else if("惠残事项智慧爱心24小时".equals(cjroneSignatureEntity.getType())){
                           Map<String, Object> tmp_params = new HashMap<>();
                           tmp_params.put("type", "惠残事项智慧爱心24小时");
                           tmp_params.put("status", "1");
                           tmp_params.put("type_id", cjroneSignatureEntity.getTypeId());
                           List<CjroneSignatureEntity> alive_list = (List<CjroneSignatureEntity>) cjroneSignatureService.listByMap(tmp_params);
                           alive_list.forEach(item -> {
                               item.setStatus("0");
                           });
                           if (alive_list.size() > 0) {
                               cjroneSignatureService.updateBatchById(alive_list);
                           }

                           Love24Entity cjroneblChildeduEntity=love24Service.getById(cjroneSignatureEntity.getTypeId());
                           if (cjroneblChildeduEntity != null) {
                               save_path = cjroneProperties.getSignaturePath() + cjroneblChildeduEntity.getDisableId() + "/惠残事项智慧爱心24小时/";
                               url_path = "/" + cjroneblChildeduEntity.getDisableId() + "/" + URLEncoder.encode("惠残事项智慧爱心24小时", "UTF-8") + "/";
                           } else {
                               save_path = cjroneProperties.getSignaturePath() + cjroneSignatureEntity.getTypeId() + "/hand/";
                               url_path = "/" + cjroneSignatureEntity.getTypeId() + "/hand/";
                           }
                       }

                        // String save_path = cjroneProperties.getSignaturePath()+cjroneSignatureEntity.getTypeId()+"/hand/";
                        HttpRequestUtil.downloadPicture(docUrl, save_path, docid + ".pdf");

                        cjroneSignatureEntity.setFileActUrl(save_path + docid + ".pdf");
                        cjroneSignatureEntity.setUrl(url_path + docid + ".pdf");
                        cjroneSignatureEntity.setStatus("1");
                        cjroneSignatureService.updateById(cjroneSignatureEntity);
                    }
                    params_result.put("urlPath",url_path + docid + ".pdf");
                    file_list.add(params_result);
                    logger.info(docid + "<a href = " + docUrl + ">:下载链接</a><br/>");
                }
            } else {
                logger.info("获取文档下载地址失败，errcode=" + downJson.getInt("errCode") + ",msg=" + downJson.getInt("msg") + "<br/>");
            }
        } else {
            //未从cookie中获取到cookie
            logger.info("获取flowId失败！！！<br/>");
        }
        if (file_list.size() > 0) {
            System.out.println("kkkkkk--------->>>>>>"+mobileType);
            System.out.println("kkkkkk--------->>>>>>"+signType);
            System.out.println("kkkkkk--------->>>>>>"+typeAction);
            System.out.println("kkkkkk--------->>>>>>"+second);
            if ("1".equals(mobileType)) {
                // 移动端跳转  ---- 残疾证
                //先更新手签状态
                DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity = new DisabilityCertificateApplicationEntity();
                disabilityCertificateApplicationEntity.setId(Integer.parseInt(disabilityId));
                disabilityCertificateApplicationEntity.setSignStatus("2");
                disabilityCertificateApplicationEntity.setStatus("1");  //镇街道待审核
                disabilityCertificateApplicationService.updateSignStatusById(disabilityCertificateApplicationEntity);
                logger.info("状态更新====》<br/>" + disabilityId);
                response.sendRedirect("http://localhost:8090/app/record.html");
            } else if ("2".equals(mobileType)) {
                //移动端跳转 ---- 福利事项

                    final  String typeActionTmp = typeAction;
                    final  String secondTmp = second;
                    file_list.forEach(item ->{
                        System.out.println("kkkkk->"+item+","+typeActionTmp);
                        //更新手签状态
                        CjroneWelfareMatterApplicationEntity cjroneWelfareMatterApplication = new CjroneWelfareMatterApplicationEntity();
                        cjroneWelfareMatterApplication.setMatterId(Integer.parseInt(item.get("matterId").toString()));
                        String matterName = item.get("matterName").toString();
                        cjroneWelfareMatterApplication.setMatterName(matterName.substring(4, matterName.length()));
                        if ("申请人手签".equals(typeActionTmp)) {
                            cjroneWelfareMatterApplication.setSignStatus("2");
                            //cjroneWelfareMatterApplication.setStatus("8"); // 待镇街道审批
                        }
                        cjroneWelfareMatterApplicationService.updateSignStatusByMap(cjroneWelfareMatterApplication);

                    });

                response.sendRedirect("http://localhost:8090/app/record.html");
            } else {
                if (signType.indexOf("残疾证申请") != -1) {
                    //更新手签状态
                    DisabilityCertificateApplicationEntity disabilityCertificateApplicationEntity = new DisabilityCertificateApplicationEntity();
                    disabilityCertificateApplicationEntity.setId(Integer.parseInt(applyId));
                    if ("残疾证申请残疾人手签".equals(signType)) {
                        disabilityCertificateApplicationEntity.setSignStatus("2");
                    }
                    disabilityCertificateApplicationService.updateSignStatusById(disabilityCertificateApplicationEntity);
                }

                if (signType.indexOf("惠残事项") != -1) {
                    if (file_list.size() > 1){
                        final  String typeActionTmp = typeAction;
                        final  String secondTmp = second;
                        file_list.forEach(item ->{
                            System.out.println("kkkkk->"+item+","+typeActionTmp);
                            //更新手签状态
                            CjroneWelfareMatterApplicationEntity cjroneWelfareMatterApplication = new CjroneWelfareMatterApplicationEntity();
                            cjroneWelfareMatterApplication.setMatterId(Integer.parseInt(item.get("matterId").toString()));
                            String matterName = item.get("matterName").toString();
                            cjroneWelfareMatterApplication.setMatterName(matterName.substring(4, matterName.length()));
                            if ("申请人手签".equals(typeActionTmp)) {
                                cjroneWelfareMatterApplication.setSignStatus("2");
                                cjroneWelfareMatterApplication.setStatus("8"); // 待镇街道审批
                            } else if ("镇街道手签".equals(typeActionTmp)) {
                                cjroneWelfareMatterApplication.setSignStatus("4");
                                cjroneWelfareMatterApplication.setStatus("2"); // 待区残联审批
                            } else if ("区残联手签".equals(typeActionTmp) || "康复部手签".equals(typeActionTmp)) {
                                if (matterName.indexOf("生活") != -1 ||matterName.indexOf("护理") != -1){
                                    cjroneWelfareMatterApplication.setStatus("5"); // 子事项待民政审批
                                }else {
                                    cjroneWelfareMatterApplication.setStatus("1"); // 子事项通过
                                }
                                // cjroneWelfareMatterApplication.setStatus("1"); // 子事项通过
                                if (secondTmp.indexOf("经办") != -1){
                                    cjroneWelfareMatterApplication.setStatus(null);
                                    cjroneWelfareMatterApplication.setSignStatus("7");
                                } else {
                                    cjroneWelfareMatterApplication.setSignStatus("6");
                                }
                                // cjroneWelfareMatterApplication.setSignStatus("6");
                            } else if ("民政局手签".equals(typeActionTmp)) {
                                if (matterName.indexOf("生活") != -1 ||matterName.indexOf("护理") != -1){
                                    cjroneWelfareMatterApplication.setStatus("1"); // 子事项通过
                                }
                                if (secondTmp.indexOf("经办") != -1){
                                    cjroneWelfareMatterApplication.setStatus(null);
                                    cjroneWelfareMatterApplication.setSignStatus("9");
                                } else {
                                    cjroneWelfareMatterApplication.setSignStatus("8");
                                }
                                // cjroneWelfareMatterApplication.setSignStatus("8");
                            } else {
                                cjroneWelfareMatterApplication.setSignStatus("1");
                            }
                            cjroneWelfareMatterApplicationService.updateSignStatusByMap(cjroneWelfareMatterApplication);

                        });
                    } else {
                        // 改变状态代码在这里！！！！
                        //更新手签状态
                        CjroneWelfareMatterApplicationEntity cjroneWelfareMatterApplication = new CjroneWelfareMatterApplicationEntity();
                        cjroneWelfareMatterApplication.setMatterId(Integer.parseInt(applyId));
                        cjroneWelfareMatterApplication.setMatterName(signType.substring(4, signType.length()));
                        if ("申请人手签".equals(typeAction)) {
                            // 村社区帮助残疾人手签完成
                            if("大学生补助".equals(cjroneWelfareMatterApplication.getMatterName())){
                                cjroneWelfareMatterApplication.setStatus("7"); //总体状态为区残联负责人待审核
                                cjroneWelfareMatterApplication.setSignStatus("8"); //签字状态为完成手签
                                cjroneWelfareMatterApplication.setSignatureStatus("5");  //设置电子签章状态为区负责人待电子签章
                            }else{
                                cjroneWelfareMatterApplication.setStatus("2"); //总体状态为街道待审核
                                cjroneWelfareMatterApplication.setSignStatus("2"); //签字状态为街道待手签
                                cjroneWelfareMatterApplication.setSignatureStatus("2");  //设置电子签章状态为街道待电子签章
                            }

                        } else if ("镇街道手签".equals(typeAction)) {
                            // 镇街道手签完成
                            if("大学生补助".equals(cjroneWelfareMatterApplication.getMatterName())){
                                cjroneWelfareMatterApplication.setStatus("7"); //总体状态为区残联负责人待审核
                                cjroneWelfareMatterApplication.setSignStatus("8"); //签字状态为完成手签
                                cjroneWelfareMatterApplication.setSignatureStatus("5");  //设置电子签章状态为区负责人待电子签章
                            }else{
                                cjroneWelfareMatterApplication.setStatus("2"); //总体状态为街道待审核
                                if (cjroneWelfareMatterApplication.getMatterName().indexOf("生活补贴") != -1){
                                    cjroneWelfareMatterApplication.setSignStatus("4"); //签字状态为民政待手签
                                }else{
                                    cjroneWelfareMatterApplication.setSignStatus("6"); //签字状态为区残联待手签
                                }
                                cjroneWelfareMatterApplication.setSignatureStatus("2");  //设置电子签章状态为街道待电子签章
                            }

                        } else if ("区残联手签".equals(typeAction)) {
                            if (second.indexOf("经办") != -1){
                                // 经办人
                                cjroneWelfareMatterApplication.setStatus("7");  //区残联负责人待审核
                                if("职工基本养老保险补助".equals(cjroneWelfareMatterApplication.getMatterName())||"城乡居民养老保险补助".equals(cjroneWelfareMatterApplication.getMatterName())||"职工基本医疗保险补助".equals(cjroneWelfareMatterApplication.getMatterName())||"城乡基本医疗保险补助".equals(cjroneWelfareMatterApplication.getMatterName())||"住院补助".equals(cjroneWelfareMatterApplication.getMatterName())||"医疗救助".equals(cjroneWelfareMatterApplication.getMatterName())||"康复补助".equals(cjroneWelfareMatterApplication.getMatterName())){
                                    cjroneWelfareMatterApplication.setSignStatus("8"); //手签完成，区残联负责人不需要手签
                                }else{
                                    cjroneWelfareMatterApplication.setSignStatus("7"); //区残联负责人待手签
                                }
                                cjroneWelfareMatterApplication.setSignatureStatus("5"); //区残联负责人待电子盖章
                            } else {
                                // 负责人
                                cjroneWelfareMatterApplication.setStatus("7");  //区残联负责人待审核
                                cjroneWelfareMatterApplication.setSignStatus("8"); //区残联负责人已手签
                                cjroneWelfareMatterApplication.setSignatureStatus("5"); //民政负责人待电子盖章
                            }
                        } else if ("民政局手签".equals(typeAction)) {
                           /* if (cjroneWelfareMatterApplication.getMatterName().indexOf("生活") != -1){
                                cjroneWelfareMatterApplication.setStatus("1"); // 子事项通过
                            }*/
                            // cjroneWelfareMatterApplication.setSignStatus("8");
                            if (second.indexOf("经办") != -1){
                                // 经办人
                                cjroneWelfareMatterApplication.setStatus("5");  //民政负责人待审核
                                cjroneWelfareMatterApplication.setSignStatus("5"); //民政负责人待手签
                                cjroneWelfareMatterApplication.setSignatureStatus("4"); //民政负责人待电子盖章
                            } else {
                                // 负责人
                                cjroneWelfareMatterApplication.setStatus("5");  //民政负责人待审核
                                cjroneWelfareMatterApplication.setSignStatus("6"); //民政负责人已手签（区经办人待手签）
                                cjroneWelfareMatterApplication.setSignatureStatus("4"); //民政负责人待电子盖章
                            }
                        } else {
                            cjroneWelfareMatterApplication.setSignStatus("1");
                        }
                        cjroneWelfareMatterApplicationService.updateSignStatusByMap(cjroneWelfareMatterApplication);
                    }
                }
                logger.info("状态更新====》<br/>" + applyId);
                // openIE:https://cl.fh.gov.cn/cjrone/static/test.htm?fileName=1563756992239_6cc7b4bd0f754b2b9838f6eb74ab25ab.pdf,3237,2,qcl ***********
                response.sendRedirect("http://localhost:8090/cjrone/static/" + file_list.get(0).get("urlPath"));
                //response.sendRedirect("http://localhost:8090/cjrone/static/" + file_list.get(0).get("urlPath"));

                // response.sendRedirect("openIE:https://cl.fh.gov.cn/cjrone/static/test.htm?fileName=1563756992239_6cc7b4bd0f754b2b9838f6eb74ab25ab.pdf,3237,2,qcl");
                //response.sendRedirect("http://www.cjrone.com/cjrone/static/" + file_list.get(0).get("urlPath"));
            }
        }
    }

    @RequestMapping("/signPhoto")
    public R uploadSignPhoto(String fileUrl,String thirdId){
        SysUserEntity entity = sysUserService.getById(thirdId);
        Sign sign = new Sign();
        ProcessBean pb= new ProcessBean();
        String accountId = sign.personAccountId(thirdId,entity);
        if(accountId.equals("0")){
            return R.error().put("异常错误信息","sysUser表中没有ID为："+thirdId+"的人员");
        }
        if(accountId.equals("2")){
            return R.error().put("异常错误信息","sysUser表中ID为："+thirdId+"的人缺少相关的 姓名、身份证号、手机号、邮箱等信息");
        }
        pb.setInitiatorAccountId(accountId);
        sign.BindPersonImages(fileUrl,pb);

        Account account = new Account();
        // 查询个人印章数量
        String result = account.personYinZhangList(pb);
        return R.ok().put("result",result);
    }

    @RequestMapping("/updatePersonInfo")
    public R updatePersonInfo(String thirdId,String name,String idno,String mobile){

        SysUserEntity entity =  sysUserService.getById(thirdId);
        String result ="";
        if(Optional.ofNullable(entity).isPresent()){
            entity.setName(name);
            entity.setIdNo(!"".equals(idno)?idno:entity.getIdNo());
            entity.setMobile(mobile);
            sysUserService.updateById(entity);
            Sign sign = new Sign();
            result = sign.updatePersonInfo(entity);
        }

        if(result == null || "".equals(result)){
            return R.error().put("500","人员手签信息修改失败");
        }
        return R.ok().put("result",result);
    }


}
