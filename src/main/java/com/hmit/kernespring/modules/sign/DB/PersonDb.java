package com.hmit.kernespring.modules.sign.DB;
/***
 * 
 * @Package cn.sign.DB
 * @Description: 创建个人用户所需信息，用于模拟单点登录获取到的个人信息。
 * <AUTHOR>  
 * @date 2018年10月12日 上午10:04:49
 */
public class PersonDb {
	//用户在贵司系统中的用户表标识，不同用户不可重复
	public static final String userid = "1";//建议不为空，且每个用户的userid不唯一
	//个人姓名
	public static final String username = "熊永洪";//不可为空
	//个人身份证件号
	public static final String idnum = "500241199311105916";//不可为空
	//邮件
	public static final String email = "<EMAIL>";//建议不为空
	//手机号码
	public static final String mobile = "18067444337";//建议不为空
	//证件号类型    注意：个人单点登录返回的证件号类型与创建账户传入的证件号类型值不同，
	public static final String idtype = "1";
	
	
	//个人护照证件号
	public static final String passport = "";
	//个人港澳居民来往大陆通行证证件号
	public static final String permitlicense = "";
	//个人台湾居民来往内地通行证证件号
	public static final String taiwanlicense = "";
	//个人军官证证件号
	public static final String officerlicense = "";
	//外国人永久居留身份证（中国绿卡）证件号
	public static final String greencard = "";
	
}
