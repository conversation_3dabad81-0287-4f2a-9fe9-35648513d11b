package com.hmit.kernespring.modules.sign.servlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.PrintStream;

/**
 * 
 * @Package cn.sign.servlet
 * @Description:
 *  用于获取异步通知，本servlet仅有用于展示如果获取一步通知。
 *  如要配置异步通知地址，请联系杭州天谷技术对接人员
 * <AUTHOR>  
 * @date 2018年11月23日 上午10:36:39
 */
public class GetAsynJson extends HttpServlet {

	public void doGet(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		doPost(request, response);
		
	}

	public void doPost(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		System.out.println("电子签名开放服务回调信息~~~");
		request.setCharacterEncoding("utf-8");
        StringBuffer jb = new StringBuffer();
        String line = null;
        String result = "";
        try {
            //读取输入流到StringBuffer中
            BufferedReader reader = request.getReader();
            while ((line = reader.readLine()) != null){
            	jb.append(line);
            }
            System.out.println("异步消息为："+jb.toString());
          
            //先将服务器收到的JSON字符串打印到客户端，再将该字符串转换为JSON对象然后再转换成的JSON字符串打印到客户端
            PrintStream out = new PrintStream(response.getOutputStream());
            out.println(jb.toString());
            out.println(result);
        } catch (Exception e) { 
        	/*report an error*/ 
        }
  
	}

}
