package com.hmit.kernespring.modules.sign.utils;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * 
 * @Package cn.sign.utils
 * @Description: 
 * 		1、提供一个根据下载连接下载文件的帮助类，
 *		2、签署完成后可以根据下载连接将文件下载到对接平台文件系统
 * <AUTHOR>  
 * @date 2018年11月23日 上午9:57:02
 */
public class DownFile {
	 /** 
	   * 从网络Url中下载文件 
	   * @param urlStr 
	   */
	  public  static byte[] downLoadFromUrl(String urlStr) throws IOException{ 
	    System.out.println(urlStr);
		  URL url = new URL(urlStr);  
	    HttpURLConnection conn = (HttpURLConnection)url.openConnection();  
	        //设置超时间为3秒 
	    conn.setConnectTimeout(3*1000); 
	    //防止屏蔽程序抓取而返回403错误 
	    conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)"); 
	  
	    //得到输入流 
	    InputStream inputStream = conn.getInputStream();  
	    //获取自己数组 
	    byte[] getData = readInputStream(inputStream); 
	    inputStream.close();
	  return getData;
	  
	  } 
	  
	  /** 
	   * 从输入流中获取字节数组 
	   * @param inputStream 
	   * @return 
	   * @throws IOException 
	   */
	  public static byte[] readInputStream(InputStream inputStream) throws IOException {  
	    byte[] buffer = new byte[1024];  
	    int len = 0;  
	    ByteArrayOutputStream bos = new ByteArrayOutputStream();  
	    while((len = inputStream.read(buffer)) != -1) {  
	      bos.write(buffer, 0, len);  
	    }  
	    bos.close();  
	    return bos.toByteArray();  
	  }  
	  
	  /** 
	     * 根据byte数组，生成文件 
	     */  
	    public static void getFile(byte[] bfile, String filePath,String fileName) {  
	        BufferedOutputStream bos = null;  
	        FileOutputStream fos = null;  
	        File file = null;  
	        try {  
	            File dir = new File(filePath);  
	            if(!dir.exists()&&dir.isDirectory()){//判断文件目录是否存在  
	                dir.mkdirs();  
	            }  
	            file = new File(filePath+"\\"+fileName);  
	            fos = new FileOutputStream(file);  
	            bos = new BufferedOutputStream(fos);  
	            bos.write(bfile);  
	        } catch (Exception e) {  
	            e.printStackTrace();  
	        } finally {  
	            if (bos != null) {  
	                try {  
	                    bos.close();  
	                } catch (IOException e1) {  
	                    e1.printStackTrace();  
	                }  
	            }  
	            if (fos != null) {  
	                try {  
	                    fos.close();  
	                } catch (IOException e1) {  
	                    e1.printStackTrace();  
	                }  
	            }  
	        }  
	    }
	  
	  public static void main(String[] args) {
		  String url="https://esignweb.oss-cn-hangzhou.aliyuncs.com/1111563786/e942ffb7-7d11-4d28-8639-28d71c927208/5c19524a-27e7-484e-aff2-95805d5d0ac0.pdf?Expires=1542941712&OSSAccessKeyId=LTAIdv";
		  byte[] data;
		try {
			data = downLoadFromUrl(url);
			getFile(data, "D://", "down.pdf");
			  
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

	}
	  
}
