package com.hmit.kernespring.modules.sign.utils;

import org.apache.tomcat.util.codec.binary.Base64;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.LinkedHashMap;
import java.util.Map;


public class FileHelper {
	    /***
	     * 获取文件基本信息
	     * @param filePath
	     * @return
	     */
	    public static Map<String, String> getFileInfo(String filePath) {
	        Map<String, String> fileInfo = new LinkedHashMap<String, String>();
	        File file = new File(filePath);
	        fileInfo.put("FileName", file.getName());
	        fileInfo.put("FileLength", String.valueOf(file.length()));
	        return fileInfo;
	    }
	    /***
	     * 获取文件的Bytes
	     * 
	     * @param filePath
	     * @return
	     * @throws IOException
	     */
	    public static byte[] getBytes(String filePath) {
	        File file = new File(filePath);
	        FileInputStream fis = null;
	        byte[] buffer = null;
	        try {
	            fis = new FileInputStream(file);
	            buffer = new byte[(int) file.length()];
	            fis.read(buffer);
	            fis.close();
	        }
	        catch (FileNotFoundException e) {
	            e.printStackTrace();
	        }
	        catch (IOException e) {
	            e.printStackTrace();
	        }
	        finally {
	            try {
	                fis.close();
	            }
	            catch (IOException e) {
	                e.printStackTrace();
	            }
	        }
	        return buffer;
	    }
	    
	    /***
	     * 计算文件的Content-MD5
	     * 
	     * @param filePath
	     * @return
	     */
	    public static String getContentMD5(String filePath) {
		// 获取文件MD5的二进制数组（128位）
		byte[] bytes = getFileMD5Bytes128(filePath);
		// 对文件MD5的二进制数组进行base64编码（而不是对32位的16进制字符串进行编码）
		return new String(Base64.encodeBase64(bytes));
	    }
	    /***
	     * 获取文件MD5-二进制数组（128位）
	     * 
	     * @param filePath
	     * @return
	     * @throws IOException
	     */
	    public static byte[] getFileMD5Bytes128(String filePath) {
		FileInputStream fis = null;
		byte[] md5Bytes = null;
		try {
		    File file = new File(filePath);
		    fis = new FileInputStream(file);
		    MessageDigest md5 = MessageDigest.getInstance("MD5");
		    byte[] buffer = new byte[1024];
		    int length = -1;
		    while ((length = fis.read(buffer, 0, 1024)) != -1) {
			md5.update(buffer, 0, length);
		    }
		    md5Bytes = md5.digest();
		    fis.close();
		} catch (FileNotFoundException e) {
		    e.printStackTrace();
		} catch (NoSuchAlgorithmException e) {
		    e.printStackTrace();
		} catch (IOException e) {
		    e.printStackTrace();
		}
		return md5Bytes;
	    } 
}
