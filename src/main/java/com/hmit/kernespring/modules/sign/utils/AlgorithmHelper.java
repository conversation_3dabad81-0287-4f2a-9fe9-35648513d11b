package com.hmit.kernespring.modules.sign.utils;

import net.sf.json.JSONObject;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

public class AlgorithmHelper {
	 
    /***
     * 获取请求签名值
     * 
     * @param data
     *            加密前数据
     * @param key
     *            密钥
     * @param algorithm
     *            HmacMD5 HmacSHA1 HmacSHA256 HmacSHA384 HmacSHA512
     * @param encoding
     *            编码格式
     * @return HMAC加密后16进制字符串
     * @throws Exception
     */
    public  String getSignature(String data, String key, String algorithm, String encoding) {
	Mac mac = null;
	try {
	    mac = Mac.getInstance(algorithm);
	    SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(encoding), algorithm);
	    mac.init(secretKey);
	    mac.update(data.getBytes(encoding));
	} catch (NoSuchAlgorithmException e) {
	    e.printStackTrace();
	    return null;
	} catch (UnsupportedEncodingException e) {
	    e.printStackTrace();
	    return null;
	} catch (InvalidKeyException e) {
	    e.printStackTrace();
	    return null;
	}
	return byte2hex(mac.doFinal());
    }
    /***
     * 将byte[]转成16进制字符串
     * 
     * @param data
     * 
     * @return 16进制字符串
     */
    public  String byte2hex(byte[] data) {
	StringBuilder hash = new StringBuilder();
	String stmp;
	for (int n = 0; data != null && n < data.length; n++) {
	    stmp = Integer.toHexString(data[n] & 0XFF);
	    if (stmp.length() == 1)
		hash.append('0');
	    hash.append(stmp);
	}
	return hash.toString();
    }
    
    
	/***
	 * 将JSON字符串转成JSON对象
	 * 
	 * @param str
	 * @return JSON对象
	 */
	public JSONObject toJSONObject(String str) {
		JSONObject obj = JSONObject.fromObject(str);
		return obj;
	}
}
