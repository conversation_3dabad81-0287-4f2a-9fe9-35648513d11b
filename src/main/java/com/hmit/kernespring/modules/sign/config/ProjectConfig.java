package com.hmit.kernespring.modules.sign.config;
/**
 *
 * @Package cn.sign.config
 * @Description: 项目配置信息
 * <AUTHOR>
 * @date 2018年10月10日 下午6:33:00
 */
public class ProjectConfig {
    // 测试
	//public static final  String projectId = "4438789143";
	// 正式
	public static final  String projectId = "5111576170";

	//测试 应用秘钥
	//public static final  String projectSecret = "aa024504eff5b7d3890b6504ac840cea";

	// 正式
	public static final  String projectSecret = "203c4f5de2de3adc6261343638017f05";

	//正式环境请求地址
	public static final  String host = "https://o.tsign.cn/opentreaty-service";
	//模拟环境请求地址
	// public static final  String host = "https://smlo.tsign.cn/opentreaty-service";

	// 编码格式
	public static final String ENCODING = "UTF-8";
	//可为空
	public static final String payer = "";
	// 哈希算法
	public static final String ALGORITHM = "HmacSHA256";
	//上传文件类型
	public static final  String contentTypePdf = "image/jpeg";   //  application/pdf   image/jpeg
	//获取文件上传地址
	public static final  String uploadUrl = "/file/uploadurl";
	// 本地文件创建文档
	public static final  String createbyfilekey = "/doc/createbyfilekey";
	// 创建个人账户
	public static final  String person = "/account/create/person";
	// 更新个人账户信息
	public static final  String updatePerson = "/account/person/update";
	// 根据thirdId 查询 accountId信息
	public static final  String queryAccountId = "/account/idexchange";
	// 创建单个文档签署流程
	public static final  String addProcess = "/sign/contract/addProcess";
	// 创建多个文档签署流程
	public static final  String addProcesss = "/sign/contracts/addProcess";
	// 发起个人签署
	public static final  String handPersonSignTask = "/sign/contract/handPersonSignTask";
	// 发起个人签署
	public static final  String handPersonSignTasks = "/sign/contracts/handPersonSignTask";
	// 归档流程
	public static final  String archiveProcess = "/sign/contract/archiveProcess";
	// 查询文档签署详情
	public static final  String detail = "/sign/contract/detail";
	// 签署文件下载
	public static final  String down = "/sign/download";
	// 创建个人图片印章
	public static final  String personImageYz = "/seal/create/image/person";
	// 删除个人印章
	public static final  String delPersonYz= "/seal/create/image/person";
	// 查询个人印章
	public static final  String personYinZhang = "/seal/query";
	// 注销用户
	public static final  String delPerson = "/account/delete";
}
