package com.hmit.kernespring.modules.sign.service;

import com.hmit.kernespring.modules.sign.bean.PersonBean;
import com.hmit.kernespring.modules.sign.bean.ProcessBean;
import com.hmit.kernespring.modules.sign.config.ProjectConfig;
import com.hmit.kernespring.modules.sign.utils.HttpHelper;
import com.hmit.kernespring.modules.sys.entity.SysUserEntity;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;

public class Account {
	private static org.slf4j.Logger logger= LoggerFactory.getLogger(Account.class);

	//private static Logger logger = Logger.getLogger(Account.class);

	/**
	 *
	 * @Description:创建个人账号
	 * @param per
	 * @return accountId
	 * date:2018年10月12日下午1:24:14
	 */
	public String addPerson(PersonBean per){
		logger.info("个人bean:"+per.toString());
		//创建请求body
		JSONObject json = new JSONObject();
		logger.info(per.getThirdId());

		if((per.getThirdId())!=null&&!((per.getThirdId()).equals(""))){
			json.put("thirdId", per.getThirdId());
		}
		if(per.getName()!=null&&!(per.getName().equals(""))){
			json.put("name", per.getName());
		}else{
			logger.info("姓名不可为空");
			return "";
		}
		if(per.getIdNo()!=null&&!(per.getIdNo().equals(""))){
			json.put("idNo", per.getIdNo());
		}else{
			logger.info("证件号不可为空");
			return "";
		}
		if(per.getIdType()>=13&&per.getIdType()<=27){
			json.put("idType", per.getIdType());
		}else{
			logger.info("证件号类型不可为空,且要与证件号类型相符");
			return "";
		}
		if(per.getMobile()!=null&&!(per.getMobile().equals(""))){
			json.put("mobile", per.getMobile());
		}
		if(per.getEmail()!=null&&!(per.getEmail().equals(""))){
			json.put("email", per.getEmail());
		}
		String body = json.toString();//将json对象转换成String类型

		//设置请求头
		HttpHelper http = new HttpHelper();
		LinkedHashMap<String, String> postHeaders = http.getPOSTHeaders(ProjectConfig.projectId,ProjectConfig.projectSecret );

		//发送post请求
		String url = ProjectConfig.host+ProjectConfig.person;
		JSONObject accountResult = http.sendPOST(url, body, postHeaders, ProjectConfig.ENCODING);

		//返回accountId
		int errCode =  accountResult.getInt("errCode");
		String msg =  accountResult.getString("msg");
		String accountId="";
		if(errCode==0||errCode==1435206){
			logger.info("创建用户成功："+accountResult.toString());
			JSONObject data = accountResult.getJSONObject("data");
			accountId = data.getString("accountId");
			return accountId;
		}else{
			logger.info("创建用户失败："+accountResult.toString());
			return null;
		}
	}
	/**
	 *
	 * @Description:删除个人印章
	 * @param per
	 * @return accountId
	 * date:2018年10月12日下午1:24:14
	 */
	public String delPersonYz(String accountId,String sealId){
		logger.info("个人bean:"+accountId);
		//创建请求body
		JSONObject json = new JSONObject();
		json.put("accountId", accountId);
		json.put("sealId", sealId);

		String body = json.toString();//将json对象转换成String类型

		//设置请求头
		HttpHelper http = new HttpHelper();
		LinkedHashMap<String, String> postHeaders = http.getPOSTHeaders(ProjectConfig.projectId,ProjectConfig.projectSecret );

		//发送post请求
		String url = "https://openapi.esign.cn/v1/accounts/"+accountId+"/seals/"+sealId;
		JSONObject accountResult = http.sendDELETE(url, body, postHeaders, ProjectConfig.ENCODING);

		//返回accountId
		int errCode =  accountResult.getInt("errCode");
		if(errCode==0||errCode==1435206){
			logger.info("删除个人印章成功："+accountResult.toString());
			JSONObject data = accountResult.getJSONObject("data");
			accountId = data.getString("accountId");
			return accountId;
		}else{
			logger.info("删除个人印章："+accountResult.toString());
			return null;
		}
	}
	/**
	 *
	 * @Description:查询个人印章信息
	 * @param processBean
	 * @return accountId
	 * date:2019年07月20日 下午10:29:14
	 */
	public String personYinZhangList(ProcessBean processBean){
		logger.info("个人bean:"+processBean.toString());
		//创建请求body
		JSONObject json = new JSONObject();
		logger.info(processBean.getInitiatorAccountId());

		if((processBean.getInitiatorAccountId())!=null&&!((processBean.getInitiatorAccountId()).equals(""))){
			json.put("accountId", processBean.getInitiatorAccountId());
		}
		String body = json.toString();//将json对象转换成String类型

		//设置请求头
		HttpHelper http = new HttpHelper();
		LinkedHashMap<String, String> postHeaders = http.getPOSTHeaders(ProjectConfig.projectId,ProjectConfig.projectSecret );

		//发送post请求
		String url = ProjectConfig.host+ProjectConfig.personYinZhang;
		JSONObject accountResult = http.sendPOST(url, body, postHeaders, ProjectConfig.ENCODING);

		//返回accountId
		int errCode =  accountResult.getInt("errCode");
		String msg =  accountResult.getString("msg");
		String seals="";
		if(errCode==0||errCode==1435206){
			logger.info("查询个人印章成功："+accountResult.toString());
			JSONObject data = accountResult.getJSONObject("data");
			JSONArray myJsonArray = JSONArray.fromObject(data.getString("seals"));
			logger.info("当前个人印章数量："+myJsonArray.size());
			if(myJsonArray.size()>0){
				for(int i=0;i<myJsonArray.size();i++){
					JSONObject job = myJsonArray.getJSONObject(i);  // 遍历 jsonarray 数组，把每一个对象转成 json 对象
					System.out.println("sealId："+job.get("sealId")) ;  // 得到 每个对象中的属性值
					System.out.println("sealAlias："+job.get("sealAlias")) ;  // 得到 每个对象中的属性值
					System.out.println("sealUrl："+job.get("sealUrl")) ;  // 得到 每个对象中的属性值
				}
			}
			return seals;
		}else{
			logger.info("查询个人印章失败："+accountResult.toString());
			return null;
		}
	}


	/**
	 *
	 * @Description: 注销账号
	 * @param processBean
	 * @return accountId
	 * date:2019年07月30日 上午10:56:18
	 */
	public String delPerson(ProcessBean processBean){
		logger.info("个人bean:"+processBean.toString());
		//创建请求body
		JSONObject json = new JSONObject();
		logger.info(processBean.getInitiatorAccountId());

		if((processBean.getInitiatorAccountId())!=null&&!((processBean.getInitiatorAccountId()).equals(""))){
			json.put("accountId", processBean.getInitiatorAccountId());
		}
		String body = json.toString();//将json对象转换成String类型

		//设置请求头
		HttpHelper http = new HttpHelper();
		LinkedHashMap<String, String> postHeaders = http.getPOSTHeaders(ProjectConfig.projectId,ProjectConfig.projectSecret );

		//发送post请求
		String url = ProjectConfig.host+ProjectConfig.delPerson;
		JSONObject accountResult = http.sendPOST(url, body, postHeaders, ProjectConfig.ENCODING);

		//返回accountId
		int errCode =  accountResult.getInt("errCode");
		String msg =  accountResult.getString("msg");
		String seals="";
		if(errCode==0||errCode==1435206){
			logger.info("注销账号成功："+accountResult.toString());
			return accountResult.toString();
		}else{
			logger.info("注销账号失败："+accountResult.toString());
			return null;
		}
	}
	/**
	 *
	 * @Description:根据 thirdId 查询出 accountId
	 * @param ThirdId
	 * @return accountId
	 * date:2018年10月12日下午1:24:14
	 */
	public String queryAccountIdByThirdId(String ThirdId){
		logger.info("个人ThirdId:"+ThirdId);
		//创建请求body
		JSONObject json = new JSONObject();
		logger.info(ThirdId);
		json.put("thirdId", ThirdId);

		String body = json.toString();//将json对象转换成String类型

		//设置请求头
		HttpHelper http = new HttpHelper();
		LinkedHashMap<String, String> postHeaders = http.getPOSTHeaders(ProjectConfig.projectId,ProjectConfig.projectSecret );

		//发送post请求
		String url = ProjectConfig.host+ProjectConfig.queryAccountId;
		JSONObject accountResult = http.sendPOST(url, body, postHeaders, ProjectConfig.ENCODING);

		//返回accountId
		int errCode =  accountResult.getInt("errCode");
		String accountId="";
		if(errCode==0){
			logger.info("用户中心查询账号成功："+accountResult.toString());
			JSONObject data = accountResult.getJSONObject("data");
			accountId = data.getString("accountId");
			return accountId;
		}else{
			logger.info("用户中心查询账号失败："+accountResult.toString());
			return null;
		}
	}
	/**
	 *
	 * @Description:更新个人账号
	 * @param per
	 * @return accountId
	 * date:2018年10月12日下午1:24:14
	 */
	public String updatePerson(String accountId, SysUserEntity per){
		logger.info("个人bean:"+per.toString());
		//创建请求body
		JSONObject json = new JSONObject();
		logger.info(accountId);
		json.put("accountId", accountId);

		if(per.getName()!=null&&!(per.getName().equals(""))){
			json.put("name", per.getName());
		}else{
			logger.info("姓名不可为空");
			return "";
		}
		if(per.getMobile()!=null&&!(per.getMobile().equals(""))){
			json.put("mobile", per.getMobile());
		}
		if(per.getEmail()!=null&&!(per.getEmail().equals(""))){
			json.put("email", per.getEmail());
		}
		String body = json.toString();//将json对象转换成String类型

		//设置请求头
		HttpHelper http = new HttpHelper();
		LinkedHashMap<String, String> postHeaders = http.getPOSTHeaders(ProjectConfig.projectId,ProjectConfig.projectSecret );

		//发送post请求
		String url = ProjectConfig.host+ProjectConfig.updatePerson;
		JSONObject accountResult = http.sendPOST(url, body, postHeaders, ProjectConfig.ENCODING);

		//返回accountId
		int errCode =  accountResult.getInt("errCode");
		if(errCode==0){
			logger.info("更新用户信息成功："+accountResult.toString());
			return accountResult.toString();
		}else{
			logger.info("更新用户信息失败："+accountResult.toString());
			return null;
		}
	}



}
