

package com.hmit.kernespring.modules.oss.controller;

import com.google.gson.Gson;
import com.hmit.kernespring.common.exception.RRException;
import com.hmit.kernespring.common.utils.ConfigConstant;
import com.hmit.kernespring.common.utils.Constant;
import com.hmit.kernespring.common.utils.PageUtils;
import com.hmit.kernespring.common.utils.R;
import com.hmit.kernespring.common.validator.ValidatorUtils;
import com.hmit.kernespring.common.validator.group.AliyunGroup;
import com.hmit.kernespring.common.validator.group.QcloudGroup;
import com.hmit.kernespring.common.validator.group.QiniuGroup;
import com.hmit.kernespring.config.CjroneProperties;
import com.hmit.kernespring.modules.oss.cloud.CloudStorageConfig;
import com.hmit.kernespring.modules.oss.cloud.OSSFactory;
import com.hmit.kernespring.modules.oss.entity.SysOssEntity;
import com.hmit.kernespring.modules.oss.service.SysOssService;
import com.hmit.kernespring.modules.sys.service.SysConfigService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Stream;

/**
 * 文件上传
 *
 * @<NAME_EMAIL>
 */
@RestController
@RequestMapping("sys/oss")
public class SysOssController {
	@Autowired
	private SysOssService sysOssService;
	@Autowired
	private CjroneProperties cjroneProperties;
	@Autowired
    private SysConfigService sysConfigService;

    private final static String KEY = ConfigConstant.CLOUD_STORAGE_CONFIG_KEY;
	
	/**
	 * 列表
	 */
	@GetMapping("/list")
	@RequiresPermissions("sys:oss:all")
	public R list(@RequestParam Map<String, Object> params){
		PageUtils page = sysOssService.queryPage(params);

		return R.ok().put("page", page);
	}


    /**
     * 云存储配置信息
     */
    @GetMapping("/config")
    @RequiresPermissions("sys:oss:all")
    public R config(){
        CloudStorageConfig config = sysConfigService.getConfigObject(KEY, CloudStorageConfig.class);

        return R.ok().put("config", config);
    }


	/**
	 * 保存云存储配置信息
	 */
	@PostMapping("/saveConfig")
	@RequiresPermissions("sys:oss:all")
	public R saveConfig(@RequestBody CloudStorageConfig config){
		//校验类型
		ValidatorUtils.validateEntity(config);

		if(config.getType() == Constant.CloudService.QINIU.getValue()){
			//校验七牛数据
			ValidatorUtils.validateEntity(config, QiniuGroup.class);
		}else if(config.getType() == Constant.CloudService.ALIYUN.getValue()){
			//校验阿里云数据
			ValidatorUtils.validateEntity(config, AliyunGroup.class);
		}else if(config.getType() == Constant.CloudService.QCLOUD.getValue()){
			//校验腾讯云数据
			ValidatorUtils.validateEntity(config, QcloudGroup.class);
		}

        sysConfigService.updateValueByKey(KEY, new Gson().toJson(config));

		return R.ok();
	}
	

	/**
	 * 上传文件
	 */
	@PostMapping("/upload")
	@RequiresPermissions("sys:oss:all")
	public R upload(@RequestParam("file") MultipartFile file) throws Exception {
		if (file.isEmpty()) {
			throw new RRException("上传文件不能为空");
		}

		//上传文件
		String suffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
		String url = OSSFactory.build().uploadSuffix(file.getBytes(), suffix);

		//保存文件信息
		SysOssEntity ossEntity = new SysOssEntity();
		ossEntity.setUrl(url);
		ossEntity.setCreateDate(new Date());
		sysOssService.save(ossEntity);

		return R.ok().put("url", url);
	}
	/**
	 * 上传文件
	 */
	@PostMapping("/uploadNeB")
	// @RequiresPermissions("sys:oss:all")
	public R uploadNeB(@RequestParam("file") MultipartFile file) throws Exception {
		if (file.isEmpty()) {
			throw new RRException("上传文件不能为空");
		}

		//上传文件
        String originalFilename = file.getOriginalFilename();
        String fileExtension = "";
        String baseName = originalFilename;
        if (originalFilename != null && originalFilename.contains(".")) {
            fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
            baseName = originalFilename.substring(0, originalFilename.lastIndexOf("."));
        }
        String fileNewName = baseName + "_" + System.currentTimeMillis() + fileExtension;
        String file_path = cjroneProperties.getUploadPath()+fileNewName;
		File filePath = new File(file_path);
		if(!filePath.getParentFile().exists()){
			filePath.mkdirs();
		}
		try {
			file.transferTo(filePath);
		} catch (IllegalStateException | IOException e) {
			e.printStackTrace();
		}

		//保存文件信息
		SysOssEntity ossEntity = new SysOssEntity();
		ossEntity.setUrl(file_path);
		ossEntity.setCreateDate(new Date());
		sysOssService.save(ossEntity);

        return R.ok().put("url", fileNewName).put("docId", ossEntity.getId());
	}
	/**
	 * 上传文件
	 */
	@PostMapping("/uploadNe")
	public R uploadNe(@RequestParam("file") MultipartFile file, @RequestParam("extraParam") String extraParam) throws Exception {
		if (file.isEmpty()) {
			throw new RRException("上传文件不能为空");
		}
		String file_name = "" ;
		String signId = "" ;
		String[] params = extraParam.split(",");
		List<String> params_list = Arrays.asList(params);
		for(int i=0;i<params_list.size();i++){
			String[] params_tmp = params_list.get(i).split("=");
			if (params_tmp != null && "fileName".equals(params_tmp[0])){
				file_name = params_tmp[1];
			}else if (params_tmp != null && "signId".equals(params_tmp[0])){
				signId = params_tmp[1];
			}
		}
		System.out.println(file_name+" "+signId);
		//上传文件
		String file_path = cjroneProperties.getUploadPath() + file_name;
		System.out.println(file_path);
		File filePath = new File(file_path);
		if(!filePath.getParentFile().exists()){
			filePath.mkdirs();
		}
		try {
			file.transferTo(filePath);
		} catch (IllegalStateException | IOException e) {
			e.printStackTrace();
		}

		//保存文件信息
		SysOssEntity ossEntity = new SysOssEntity();
		ossEntity.setUrl(file_path);
		ossEntity.setCreateDate(new Date());
		sysOssService.save(ossEntity);

		return R.ok();
	}


	/**
	 * 删除
	 */
	@PostMapping("/delete")
	@RequiresPermissions("sys:oss:all")
	public R delete(@RequestBody Long[] ids){
		sysOssService.removeByIds(Arrays.asList(ids));

		return R.ok();
	}

}
