package com.hmit.kernespring.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;


/**
 * cjrone项目配置
 *
 * <AUTHOR>
 * @Date 2019/04/02 15:49
 */
@Component
@ConfigurationProperties(prefix = CjroneProperties.PREFIX)
public class CjroneProperties {

    public static final String PREFIX = "cjrone";
    private String uploadPath;

    private String downloadPath;
    private String downloadZipPath;
    private String signaturePath;
    private String templetePath;
    private String tempFilePath;

    public String getTempFilePath() {
        return tempFilePath;
    }

    public void setTempFilePath(String tempFilePath) {
        this.tempFilePath = tempFilePath;
    }

    public String getDownloadZipPath() {
        return downloadZipPath;
    }

    public void setDownloadZipPath(String downloadZipPath) {
        this.downloadZipPath = downloadZipPath;
    }

    public String getTempletePath() {
        return templetePath;
    }

    public void setTempletePath(String templetePath) {
        this.templetePath = templetePath;
    }

    public String getSignaturePath() {
        return signaturePath;
    }

    public void setSignaturePath(String signaturePath) {
        this.signaturePath = signaturePath;
    }

    public String getDownloadPath() {
        return downloadPath;
    }

    public void setDownloadPath(String downloadPath) {
        this.downloadPath = downloadPath;
    }

    public String getUploadPath() {
        return uploadPath;
    }

    public void setUploadPath(String uploadPath) {
        this.uploadPath = uploadPath;
    }
}
