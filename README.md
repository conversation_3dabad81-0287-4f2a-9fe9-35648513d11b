**项目结构** 
```
cjrone
├─db  项目SQL语句
│
├─common 公共模块
│  ├─aspect 系统日志
│  ├─exception 异常处理
│  ├─validator 后台校验
│  └─xss XSS过滤
│ 
├─config 配置信息
│ 
├─modules 功能模块
│  ├─app API接口模块(APP调用)
│  ├─job 定时任务模块
│  ├─oss 文件服务模块
│  └─sys 权限模块
│ 
├─BeansproutsApplication 项目启动类
│  
├──resources 
│  ├─mapper SQL对应的XML文件
│  └─static 静态资源

```
<br>

**技术选型：** 
- 核心框架：Spring Boot 2.1
- 安全框架：Apache Shiro 1.4
- 视图框架：Spring MVC 5.0
- 持久层框架：MyBatis 3.3
- 定时器：Quartz 2.3
- 数据库连接池：Druid 1.0
- 日志管理：SLF4J 1.7、Log4j
- 页面交互：Vue2.x 
<br> 

状态说明：
- 手签状态（SignStatus）：1、申请人待手签  2 镇街道待手签    4 民政经办人待手签  5 民政负责人  6 区经办人 7 区负责人 8 手签完成
- 签章状态（SignatureStatus）：1、无   2 街道待电子签章    4 民政负责人  5 区负责人  6 签章完成
- 审核状态（status）：1 申请人待手签 2 镇街道待审核  4 民政局经办人待审核  5 民政负责人待审核 6 区残联经办人待审核 7 区残联负责人待审核  8 通过
 9 街道退回 -社区  10 民政退回-街道  11 区残联-民政  12 区残联-街道  
 
 生活补助金流程状态说明：
 1、村社区/镇街道  申请   status = 1  SignStatus=1  SignatureStatus=1
 村社区手签 （申请人手签） status = 2  SignStatus=2  SignatureStatus=2
 
2、镇街道手签  status = 2  SignStatus=6  SignatureStatus=2
3、镇街道电子盖章   status = 2  SignStatus=6  SignatureStatus=5
4、镇街道审核  
（1）、 通过  status = 6  SignStatus=6  SignatureStatus=5
（2）、退回   status = 9  SignStatus=6  SignatureStatus=5

5、区残联经办人手签  status = 7  SignStatus=7  SignatureStatus=5
6、区残联负责人手签  status = 7  SignStatus=8  SignatureStatus=5
7、区残联负责人电子盖章 status = 7  SignStatus=8  SignatureStatus=6
8、区残联负责人审核   
（1）、通过  status = 8  SignStatus=8  SignatureStatus=6
（2）、退回  status = 12  SignStatus=8  SignatureStatus=6
