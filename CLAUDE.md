# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview
CJrone is a Spring Boot-based welfare management system for disabled persons (残疾人福利管理系统). It manages various disability-related subsidies, applications, and data synchronization.

## Build and Development Commands

### Build and Run
```bash
# Build the project
mvn clean package

# Run the application
java -jar target/cjrone.jar

# Run with specific profile
java -jar target/cjrone.jar --spring.profiles.active=prod
```

### Development
```bash
# Skip tests during build (default behavior)
mvn clean package -DskipTests

# Run Spring Boot application in development
mvn spring-boot:run

# Run with tests enabled (tests are skipped by default)
mvn clean package -DskipTests=false

# Docker build
mvn clean package docker:build

# Remote deployment (configured for ************)
mvn clean package wagon:upload-single wagon:sshexec
```

### Database
- Uses PostgreSQL as primary database
- Connection configured in `application-prod.yml`
- Database name: `cjrone-bl`
- MyBatis-Plus for ORM with XML mappers in `src/main/resources/mapper/`
- **MCP Database Access**: Use `mcp__cjrone-postgres__query` for direct database queries and index analysis

## Architecture

### Module Structure
The project follows a modular architecture under `src/main/java/com/hmit/kernespring/modules/`:

- **app/**: Mobile API module with JWT authentication
- **cjrone/**: Core disability welfare management (main regional implementation)
- **cjrone_bl/**: Different region-specific implementation (bl区域)
- **data_management/**: Data synchronization and external API integration
- **disability_certificate/**: Disability certificate management
- **job/**: Scheduled tasks and background jobs
- **matter_application/**: Welfare application workflow management
- **oss/**: Object Storage Service for file uploads
- **pdf/**: PDF generation and document processing
- **sign/**: Digital signature and electronic seal management
- **sys/**: System management (users, roles, permissions)

### Core Business Entities
Each welfare subsidy type follows the same pattern:
- **Entity**: Database table mapping (e.g., `CjroneRehabilitationSubsidyEntity`)
- **DAO**: Data access layer extending `BaseMapper`
- **Service**: Business logic interface extending `IService`
- **ServiceImpl**: Implementation with pagination, validation, and workflow
- **Controller**: REST API endpoints with CRUD operations

### Key Features
- **Workflow Management**: Multi-level approval process (申请→街道→民政→区残联)
- **Digital Signatures**: Electronic seal integration with hand-written signatures
- **Data Synchronization**: External API integration for government data
- **Excel Import/Export**: EasyPoi for batch data processing
- **PDF Generation**: iText and FreeMarker for document generation
- **Multi-Database Support**: PostgreSQL, MySQL, SQL Server, Oracle

### Status Management
The system uses multiple status fields:
- **status**: Approval workflow status (1-8 for different stages, 9-12 for rejections)
- **signStatus**: Hand signature status (1-8 for different signers)
- **signatureStatus**: Electronic seal status (1-6 for different stages)

### Authentication
- **Admin**: Apache Shiro with session-based authentication
- **Mobile API**: JWT tokens with 7-day expiration
- **Permissions**: Role-based access control

### File Storage
- Configurable upload paths in `application.yml`
- Support for local storage and cloud storage (Alibaba Cloud, Tencent Cloud, Qiniu)
- Separate paths for uploads, downloads, signatures, and temporary files

## Creating New Welfare Subsidy Types

To add a new welfare subsidy (惠残事项):

1. **Entity**: Create entity class with `@TableName` annotation
2. **DAO**: Create DAO interface extending `BaseMapper` 
3. **Service**: Create service interface extending `IService`
4. **ServiceImpl**: Implement business logic with pagination and workflow
5. **Controller**: Create REST controller with standard CRUD endpoints
6. **Mapper XML**: Create MyBatis XML file for complex queries

Reference existing implementations like `CjroneRehabilitationSubsidy` for patterns.

## Configuration Files
- `application.yml`: Base configuration
- `application-prod.yml`: Production database settings
- `pom.xml`: Maven dependencies and build configuration
- `Dockerfile`: Container deployment configuration

## Key Dependencies
- Spring Boot 2.1.3
- MyBatis-Plus 3.0.7
- Apache Shiro 1.4.0
- PostgreSQL Driver
- Druid Connection Pool
- EasyPoi for Excel processing
- iText for PDF generation
- Redis for caching
- Swagger for API documentation

## Testing and Code Quality

### Testing Framework
- **JUnit 4** with Spring Boot Test (`@SpringBootTest`, `@RunWith(SpringRunner.class)`)
- Tests are **skipped by default** in Maven build (configured in pom.xml)
- Test location: `src/test/java/com/hmit/kernespring/`
- Focus on integration tests rather than unit tests

### Running Tests
```bash
# Run tests (they are skipped by default)
mvn test -DskipTests=false

# Run specific test
mvn test -Dtest=DynamicDataSourceTest -DskipTests=false
```

### Code Quality
- **No formal linting tools** currently configured
- Uses **Lombok** to reduce boilerplate code
- **No code coverage** tools setup

## Deployment

### Docker Deployment
```bash
# Build Docker image
mvn clean package docker:build

# Run with Docker Compose
docker-compose up

# Manual Docker run
docker run -p 8080:8080 -e spring.profiles.active=dev cjrone/hm
```

### Environment Configuration
- **Development**: `application-dev.yml` (port 8090)
- **Test**: `application-test.yml`
- **Production**: `application-prod.yml`
- **Docker**: Exposes port 8080, overrides default 8090

## Git Commit Policy

**IMPORTANT**: When performing git commits, Claude Code must ONLY commit the current modification content, not all changes in the repository.

### Git Commit Rules
1. **Selective commits only**: Use `git add <specific-files>` instead of `git add .` or `git add -A`
2. **Check before commit**: Always run `git status` and `git diff` to review what will be committed
3. **Current task focus**: Only add files that are directly related to the current task or modification
4. **Exclude unrelated changes**: Do not include modifications from previous tasks or unrelated files
5. **Verify scope**: Ensure the commit scope matches the specific request or task being completed

This ensures clean, focused commits that contain only the intended changes.

## Database Tools and Performance

### MCP Database Connection
This project uses the `mcp__cjrone-postgres__query` MCP tool to access the PostgreSQL database directly. This is useful for:
- Checking current database indexes
- Analyzing query performance
- Monitoring database schema changes
- Debugging SQL queries

### Common Database Queries
```sql
-- Check indexes for a specific table
SELECT schemaname, tablename, indexname, indexdef 
FROM pg_indexes 
WHERE tablename = 'table_name' 
ORDER BY indexname;

-- Check all tables with id_card indexes
SELECT schemaname, tablename, indexname, indexdef 
FROM pg_indexes 
WHERE indexdef LIKE '%id_card%' 
ORDER BY tablename;

-- Monitor active database connections
SELECT count(*) as active_connections, state 
FROM pg_stat_activity 
GROUP BY state;
```
