# Qwen Code Context for `blcjrone`

## Project Overview

This repository contains a Java-based backend application named `cjrone` (likely a codename for a specific project, possibly related to '残疾人' - disabled persons). The project is built using the **Spring Boot 2.1** framework and follows a modular architecture.

### Key Technologies

- **Core Framework:** Spring Boot 2.1
- **Security:** Apache Shiro 1.4
- **Web Layer:** Spring MVC 5.0
- **Persistence:** MyBatis 3.3 (via MyBatis-Plus 3.0.7.1)
- **Database:** PostgreSQL (primary), with configurations suggesting support for Oracle and SQL Server.
- **Database Connection Pool:** Druid 1.1.13
- **Scheduling:** Quartz 2.3
- **Logging:** SLF4J, Logback
- **API Documentation:** Swagger 2.7
- **Build Tool:** Maven
- **Frontend Interaction:** Vue2.x (likely consumes the API provided by this backend)

### Architecture

The project structure is modular, organized under the `com.hmit.kernespring.modules` package. Key modules identified include:

- `app`: Likely contains API endpoints for mobile or external application consumption.
- `sys`: Handles system/user permissions and core functionalities.
- `job`: Manages scheduled tasks using Quartz.
- `oss`: Deals with file/object storage services.
- `sign`: Related to digital signatures or hand signatures.
- `pdf`: Handles PDF generation and manipulation.
- `matter_application`, `disability_certificate`, `data_management`, `cjrone`, `cjrone_bl`: These appear to be domain-specific modules related to the core business logic (e.g., applications, certificates, data management).

Common utilities and configurations are located in `com.hmit.kernespring.common`, `com.hmit.kernespring.config`, and `com.hmit.kernespring.datasource`.

The main application entry point is `BeansproutsApplication.java`.

## Configuration

- **Main Config:** `src/main/resources/application.yml` sets the base configuration, including server port (`8090`), context path (`/cjrone`), Jackson date formatting, multipart file size limits, Redis configuration (currently enabled on localhost:6379), and MyBatis-Plus settings. It also defines custom paths for uploads, downloads, signatures, etc.
- **Environment-Specific Config:** `src/main/resources/application-dev.yml` provides configurations for the `dev` profile, notably setting up the PostgreSQL database connection and local file system paths for development. Similar files exist for `test` and `prod`.
- **Database:** The application uses PostgreSQL as the primary database in development. The connection details are in `application-dev.yml`. MyBatis-Plus is configured to map database tables to Java entities and uses XML mappers located in `src/main/resources/mapper`.
- **Redis:** Redis caching is enabled and configured in `application.yml`. It's used for caching (controlled by `cjrone.redis.open` and `spring.redis.open`) and potentially for Shiro sessions (controlled by `cjrone.shiro.redis`).

## Building and Running

### Prerequisites

- Java 8 JDK
- Maven
- PostgreSQL database (configured as per `application-dev.yml`)
- Redis server (configured as per `application.yml`)
- Local directories for file storage (e.g., `D:/tmp/cjrone/upload/`) as defined in `application-dev.yml`.

### Commands

- **Build:** `mvn clean package`
  - This compiles the code, runs tests (though tests are currently skipped via `maven-surefire-plugin` configuration in `pom.xml`), and packages the application into a JAR file located in the `target` directory.
- **Run (Development):** `mvn spring-boot:run`
  - This starts the application using the default profile (likely `dev` as specified in `application.yml`). Make sure PostgreSQL and Redis are running.
- **Run (Packaged JAR):** `java -jar target/cjrone.jar --spring.profiles.active=dev`
  - This runs the packaged application JAR file, explicitly activating the `dev` profile.
- **Deployment (Remote):** The `pom.xml` includes a `wagon-maven-plugin` configuration that suggests deployment via `mvn clean package wagon:upload-single wagon:sshexec`. This would upload the JAR to a remote server and execute commands to stop the old process and start the new one (as defined in the plugin's `<commands>` section).
- **Docker Build:** The `pom.xml` includes a `docker-maven-plugin` configuration. You can build a Docker image using `mvn clean package docker:build`. The `Dockerfile` in the project root defines the image.

## Development Conventions

- **Language:** Java 8
- **Build Tool:** Maven
- **Modular Design:** Code is organized into functional modules under `src/main/java/com/hmit/kernespring/modules`.
- **Database Access:** MyBatis-Plus is used for ORM, with XML mappers in `src/main/resources/mapper`.
- **Security:** Apache Shiro is used for authentication and authorization.
- **Scheduling:** Quartz is used for defining and running scheduled jobs.
- **Configuration:** Externalized configuration using Spring Boot's `application.yml` and profile-specific files (`application-{profile}.yml`).
- **Logging:** SLF4J with Logback is used for logging.
- **API Documentation:** Swagger is integrated for API documentation.